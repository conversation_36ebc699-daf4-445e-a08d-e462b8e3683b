import 'package:flutter_test/flutter_test.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/presence_hours_parser_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';

void main() {
  group('PresenceHoursParserService', () {
    late PresenceHoursParserService service;

    setUp(() {
      service = PresenceHoursParserServiceImpl();
    });

    group('parsePresenceHours', () {
      test('should parse preset "Full-time (9 AM - 5 PM)" correctly', () {
        // Arrange
        const presenceHoursString = 'Full-time (9 AM - 5 PM)';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: $failure'),
          (config) {
            expect(config.zoneId, equals(zoneId));
            expect(config.isAutomaticValidationEnabled, true);
            expect(config.timeBlocks.length, equals(1));
            
            final timeBlock = config.timeBlocks.first;
            expect(timeBlock.startTime.hour, equals(9));
            expect(timeBlock.startTime.minute, equals(0));
            expect(timeBlock.endTime.hour, equals(17));
            expect(timeBlock.endTime.minute, equals(0));
            expect(timeBlock.activeDays, equals(DayOfWeek.weekdays));
          },
        );
      });

      test('should parse preset "Part-time (9 AM - 1 PM)" correctly', () {
        // Arrange
        const presenceHoursString = 'Part-time (9 AM - 1 PM)';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: $failure'),
          (config) {
            expect(config.zoneId, equals(zoneId));
            expect(config.isAutomaticValidationEnabled, true);
            expect(config.timeBlocks.length, equals(1));
            
            final timeBlock = config.timeBlocks.first;
            expect(timeBlock.startTime.hour, equals(9));
            expect(timeBlock.startTime.minute, equals(0));
            expect(timeBlock.endTime.hour, equals(13));
            expect(timeBlock.endTime.minute, equals(0));
            expect(timeBlock.activeDays, equals(DayOfWeek.weekdays));
          },
        );
      });

      test('should parse preset "Mornings (6 AM - 12 PM)" correctly', () {
        // Arrange
        const presenceHoursString = 'Mornings (6 AM - 12 PM)';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: $failure'),
          (config) {
            expect(config.zoneId, equals(zoneId));
            expect(config.isAutomaticValidationEnabled, true);
            expect(config.timeBlocks.length, equals(1));
            
            final timeBlock = config.timeBlocks.first;
            expect(timeBlock.startTime.hour, equals(6));
            expect(timeBlock.startTime.minute, equals(0));
            expect(timeBlock.endTime.hour, equals(12));
            expect(timeBlock.endTime.minute, equals(0));
            expect(timeBlock.activeDays, equals(DayOfWeek.all));
          },
        );
      });

      test('should parse preset "Weekends (10 AM - 4 PM)" correctly', () {
        // Arrange
        const presenceHoursString = 'Weekends (10 AM - 4 PM)';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected success but got failure: $failure'),
          (config) {
            expect(config.zoneId, equals(zoneId));
            expect(config.isAutomaticValidationEnabled, true);
            expect(config.timeBlocks.length, equals(1));
            
            final timeBlock = config.timeBlocks.first;
            expect(timeBlock.startTime.hour, equals(10));
            expect(timeBlock.startTime.minute, equals(0));
            expect(timeBlock.endTime.hour, equals(16));
            expect(timeBlock.endTime.minute, equals(0));
            expect(timeBlock.activeDays, equals(DayOfWeek.weekends));
          },
        );
      });

      test('should return failure for invalid presence hours string', () {
        // Arrange
        const presenceHoursString = 'Invalid format';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure.message, contains('Invalid presence hours format')),
          (config) => fail('Expected failure but got success'),
        );
      });

      test('should return failure for empty presence hours string', () {
        // Arrange
        const presenceHoursString = '';
        const zoneId = 'test-zone-id';

        // Act
        final result = service.parsePresenceHours(
          presenceHoursString: presenceHoursString,
          zoneId: zoneId,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure.message, contains('Invalid presence hours format')),
          (config) => fail('Expected failure but got success'),
        );
      });
    });

    group('PresenceHoursConfiguration', () {
      test('should check if automatic validation is active at specific time', () {
        // Arrange
        final config = PresenceHoursConfiguration(
          zoneId: 'test-zone',
          isAutomaticValidationEnabled: true,
          timeBlocks: [
            PresenceTimeBlock.fromTimeStrings(
              id: 'work_hours',
              startTime: '09:00',
              endTime: '17:00',
              activeDays: DayOfWeek.weekdays,
            ),
          ],
          createdAt: DateTime.now(),
        );

        // Act & Assert
        // Test during work hours on a weekday (Tuesday 10:00 AM)
        final workTime = DateTime(2024, 1, 2, 10, 0); // Tuesday
        expect(config.isActiveAt(workTime), true);

        // Test outside work hours on a weekday (Tuesday 8:00 AM)
        final beforeWork = DateTime(2024, 1, 2, 8, 0); // Tuesday
        expect(config.isActiveAt(beforeWork), false);

        // Test during work hours on a weekend (Saturday 10:00 AM)
        final weekendTime = DateTime(2024, 1, 6, 10, 0); // Saturday
        expect(config.isActiveAt(weekendTime), false);
      });

      test('should return false when automatic validation is disabled', () {
        // Arrange
        final config = PresenceHoursConfiguration(
          zoneId: 'test-zone',
          isAutomaticValidationEnabled: false,
          timeBlocks: [
            PresenceTimeBlock.fromTimeStrings(
              id: 'work_hours',
              startTime: '09:00',
              endTime: '17:00',
              activeDays: DayOfWeek.weekdays,
            ),
          ],
          createdAt: DateTime.now(),
        );

        // Act & Assert
        final workTime = DateTime(2024, 1, 2, 10, 0); // Tuesday 10:00 AM
        expect(config.isActiveAt(workTime), false);
      });
    });
  });
}
