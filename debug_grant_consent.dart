import 'package:firebase_auth/firebase_auth.dart';
import 'package:respublicaseguridad/features/notifications/data/repositories/notification_preferences_repository_impl.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_preferences_datasource.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Debug method to grant location consent for the current user
/// Add this to your app and call it once to fix the consent issue
Future<void> debugGrantLocationConsent() async {
  try {
    print('🔧 Debug: Granting location consent...');
    
    // Get current user
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      print('❌ No authenticated user found');
      return;
    }
    
    final userId = user.uid;
    print('👤 User ID: $userId');
    
    // Create repository instance
    final dataSource = NotificationPreferencesFirebaseDataSource(
      firestore: FirebaseFirestore.instance,
    );
    final repository = NotificationPreferencesRepositoryImpl(
      dataSource: dataSource,
    );
    
    // Grant location consent with proper timestamp
    final result = await repository.grantLocationTrackingConsent(
      userId,
      locationAccessDurationHours: 24.0, // 24 hours
    );
    
    result.fold(
      (failure) {
        print('❌ Failed to grant consent: ${failure.message}');
      },
      (_) {
        print('✅ Location consent granted successfully!');
        print('📋 The user should now receive notifications for nearby incidents.');
      },
    );
    
    // Verify the update
    final prefsResult = await repository.getUserPreferences(userId);
    prefsResult.fold(
      (failure) {
        print('❌ Failed to verify preferences: ${failure.message}');
      },
      (preferences) {
        print('\n📋 Verification:');
        print('  - Location notifications enabled: ${preferences.isLocationNotificationsEnabled}');
        print('  - Location tracking consented: ${preferences.isLocationTrackingConsented}');
        print('  - Consent granted at: ${preferences.locationConsentGrantedAt}');
        print('  - Access duration: ${preferences.locationAccessDurationHours} hours');
        print('  - Notification radius: ${preferences.notificationRadiusKm} km');
        
        if (preferences.locationConsentGrantedAt != null) {
          final expiresAt = preferences.locationConsentGrantedAt!.add(
            Duration(hours: preferences.locationAccessDurationHours.round()),
          );
          print('  - Consent expires at: $expiresAt');
          print('  - Is consent valid: ${DateTime.now().isBefore(expiresAt)}');
        }
      },
    );
    
  } catch (e) {
    print('❌ Error granting consent: $e');
  }
}

/// Alternative method using direct Firestore update
/// Use this if the repository method doesn't work
Future<void> debugGrantLocationConsentDirect() async {
  try {
    print('🔧 Debug: Granting location consent directly...');
    
    // Get current user
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      print('❌ No authenticated user found');
      return;
    }
    
    final userId = user.uid;
    print('👤 User ID: $userId');
    
    // Update directly in Firestore
    await FirebaseFirestore.instance
        .collection('notification_preferences')
        .doc(userId)
        .update({
      'locationConsentGrantedAt': FieldValue.serverTimestamp(),
      'isLocationTrackingConsented': true,
      'locationAccessDurationHours': 24,
      'updatedAt': FieldValue.serverTimestamp(),
    });
    
    print('✅ Location consent granted successfully via direct update!');
    
    // Verify the update
    final doc = await FirebaseFirestore.instance
        .collection('notification_preferences')
        .doc(userId)
        .get();
    
    if (doc.exists) {
      final data = doc.data()!;
      print('\n📋 Verification:');
      print('  - Location notifications enabled: ${data['isLocationNotificationsEnabled']}');
      print('  - Location tracking consented: ${data['isLocationTrackingConsented']}');
      print('  - Consent granted at: ${data['locationConsentGrantedAt']}');
      print('  - Access duration: ${data['locationAccessDurationHours']} hours');
      print('  - Notification radius: ${data['notificationRadiusKm']} km');
    }
    
  } catch (e) {
    print('❌ Error granting consent directly: $e');
  }
}
