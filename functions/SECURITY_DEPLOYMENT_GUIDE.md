# QR Validation Security Cloud Functions Deployment Guide

## Overview

This guide covers the deployment of production-level security Cloud Functions for QR validation. The implementation provides enterprise-grade security with AES-256-GCM encryption, HMAC-SHA256 integrity protection, and comprehensive threat detection.

## Security Features

### 🔐 Cryptographic Security
- **AES-256-GCM Encryption**: Payload encryption with authenticated encryption
- **HMAC-SHA256**: Message authentication with PBKDF2 key derivation
- **Secure Random Generation**: Cryptographically secure token and nonce generation
- **Key Rotation Support**: Version-aware key management system

### 🛡️ Attack Prevention
- **Replay Attack Prevention**: Advanced nonce-based protection with time windows
- **Timing Attack Mitigation**: Constant-time string comparisons
- **Rate Limiting**: Multi-tier rate limiting (user-level and IP-level)
- **Input Validation**: Comprehensive parameter validation and sanitization

### 📊 Monitoring & Auditing
- **Security Event Logging**: Comprehensive audit trail in Firestore
- **Threat Detection**: Real-time security threat identification and alerting
- **Performance Monitoring**: Processing time tracking and optimization
- **Compliance Logging**: Enterprise-grade audit logs for compliance requirements

## Prerequisites

### 1. Firebase Project Setup
```bash
# Ensure you have Firebase CLI installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project (if not already done)
firebase init
```

### 2. Required Firebase Services
- **Cloud Functions**: For security operations
- **Cloud Firestore**: For audit logging and token tracking
- **Realtime Database**: For session management and rate limiting
- **Authentication**: For user verification
- **App Check**: For additional client verification (recommended)

## Environment Configuration

### 1. Secret Management
Set up the required secrets using Firebase CLI:

```bash
# Set the QR validation secret (use a strong, randomly generated key)
firebase functions:secrets:set QR_VALIDATION_SECRET

# Set the encryption key (use a strong, randomly generated key)
firebase functions:secrets:set QR_ENCRYPTION_KEY

# Optional: Set key version for rotation
firebase functions:config:set qr.key_version="v1"
```

### 2. Environment Variables
```bash
# Set Node.js environment
firebase functions:config:set runtime.node_env="production"

# Optional: Configure custom settings
firebase functions:config:set qr.max_tokens_per_session="10"
firebase functions:config:set qr.token_lifetime_seconds="45"
```

## Deployment Steps

### 1. Build and Deploy
```bash
# Navigate to functions directory
cd functions

# Install dependencies
npm install

# Build TypeScript
npm run build

# Deploy all functions
firebase deploy --only functions

# Or deploy specific functions
firebase deploy --only functions:createSecureToken,functions:validateSecureToken
```

### 2. Verify Deployment
```bash
# Check function status
firebase functions:log

# Test function endpoints (replace with your project ID)
curl -X POST https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net/createSecureToken \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -d '{"sessionId":"test","zoneId":"test","purpose":"test","validatorUserId":"test"}'
```

## Security Configuration

### 1. Firestore Security Rules
Update your Firestore security rules to protect audit collections:

```javascript
// Add to firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Security audit logs - admin only
    match /security_audit/{document} {
      allow read, write: if false; // Only Cloud Functions can write
    }
    
    // Security threats - admin only
    match /security_threats/{document} {
      allow read, write: if false; // Only Cloud Functions can write
    }
    
    // Used tokens - Cloud Functions only
    match /used_tokens/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }
    
    // Consumed tokens - Cloud Functions only
    match /consumed_tokens/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }
  }
}
```

### 2. Realtime Database Security Rules
Update your Realtime Database rules:

```json
{
  "rules": {
    "rate_limits": {
      ".read": false,
      ".write": false
    },
    "secure_tokens": {
      ".read": false,
      ".write": false
    },
    "consumed_tokens": {
      ".read": false,
      ".write": false
    },
    "nonce_registry": {
      ".read": false,
      ".write": false
    }
  }
}
```

### 3. App Check Configuration (Recommended)
Enable App Check for additional security:

```bash
# Enable App Check in Firebase Console
# Configure reCAPTCHA for web
# Configure App Attest/DeviceCheck for mobile
```

## Client Integration

### 1. Update Dependencies
Add the cloud security service to your Flutter app:

```dart
// Replace existing security service imports
import 'package:respublicaseguridad/core/services/qr_validation_cloud_security_service.dart';

// Use the new service
final securityService = QRValidationCloudSecurityService.instance;
```

### 2. Migration from Client-Side Security
```dart
// OLD (deprecated)
final result = QRValidationSecurityService.instance.createSecureTokenData(...);

// NEW (production-ready)
final result = await QRValidationCloudSecurityService.instance.createSecureToken(...);
```

## Monitoring and Maintenance

### 1. Monitor Security Events
```bash
# View security audit logs
firebase firestore:query security_audit --limit 100

# Monitor security threats
firebase firestore:query security_threats --where severity==critical

# Check function performance
firebase functions:log --only createSecureToken,validateSecureToken
```

### 2. Key Rotation
```bash
# Generate new secrets
firebase functions:secrets:set QR_VALIDATION_SECRET_V2
firebase functions:secrets:set QR_ENCRYPTION_KEY_V2

# Update key version
firebase functions:config:set qr.key_version="v2"

# Deploy updated functions
firebase deploy --only functions
```

### 3. Performance Optimization
- Monitor function execution times in Firebase Console
- Adjust rate limiting thresholds based on usage patterns
- Scale function instances based on load

## Troubleshooting

### Common Issues

1. **Secret Access Errors**
   ```bash
   # Verify secrets are set
   firebase functions:secrets:access QR_VALIDATION_SECRET
   ```

2. **Permission Errors**
   ```bash
   # Check IAM permissions
   gcloud projects get-iam-policy YOUR_PROJECT_ID
   ```

3. **Function Timeout**
   - Increase function timeout in deployment configuration
   - Optimize database queries and encryption operations

### Debug Mode
Enable debug logging:
```bash
firebase functions:config:set debug.enabled="true"
firebase deploy --only functions
```

## Security Best Practices

1. **Regular Security Audits**: Review security_threats collection weekly
2. **Key Rotation**: Rotate encryption keys quarterly
3. **Rate Limit Tuning**: Adjust based on legitimate usage patterns
4. **Monitoring**: Set up alerts for critical security events
5. **Backup**: Regular backups of audit logs for compliance

## Support

For issues or questions:
1. Check Firebase Console logs
2. Review security_audit collection for detailed error information
3. Monitor security_threats collection for attack patterns
4. Contact development team with specific error messages and timestamps
