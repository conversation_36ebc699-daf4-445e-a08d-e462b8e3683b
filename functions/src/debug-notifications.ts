import { onCall } from "firebase-functions/v2/https";
import { getFirestore } from "firebase-admin/firestore";
import { logger } from "firebase-functions";
import { FCMNotificationService } from "./services/fcm-notification-service";

/**
 * Debug function to test notification system
 */
export const debugNotificationSystem = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { userId, testType = "all" } = request.data;

      if (!userId) {
        throw new Error("User ID required");
      }

      const firestore = getFirestore();
      const debugResults: any = {
        timestamp: new Date().toISOString(),
        userId,
        tests: {}
      };

      // Test 1: Check user preferences
      if (testType === "all" || testType === "preferences") {
        logger.info("Testing user preferences", { userId });

        const preferencesDoc = await firestore
          .collection("notification_preferences")
          .doc(userId)
          .get();

        debugResults.tests.preferences = {
          exists: preferencesDoc.exists,
          data: preferencesDoc.exists ? preferencesDoc.data() : null,
          issues: []
        };

        if (preferencesDoc.exists) {
          const prefs = preferencesDoc.data()!;

          if (!prefs.isLocationNotificationsEnabled) {
            debugResults.tests.preferences.issues.push("Location notifications disabled");
          }

          if (!prefs.isLocationTrackingConsented) {
            debugResults.tests.preferences.issues.push("Location tracking not consented");
          }

          if (!prefs.fcmToken) {
            debugResults.tests.preferences.issues.push("No FCM token found");
          }

          if (prefs.quietHours?.isEnabled) {
            debugResults.tests.preferences.issues.push("Quiet hours might be active");
          }
        } else {
          debugResults.tests.preferences.issues.push("User preferences not found");
        }
      }

      // Test 2: Check user location
      if (testType === "all" || testType === "location") {
        logger.info("Testing user location", { userId });

        const locationDoc = await firestore
          .collection("user_locations")
          .doc(userId)
          .get();

        debugResults.tests.location = {
          exists: locationDoc.exists,
          data: locationDoc.exists ? locationDoc.data() : null,
          issues: []
        };

        if (locationDoc.exists) {
          const location = locationDoc.data()!;
          const now = new Date();
          const locationTime = location.timestamp?.toDate();

          if (!locationTime) {
            debugResults.tests.location.issues.push("No location timestamp");
          } else {
            const ageMinutes = (now.getTime() - locationTime.getTime()) / (1000 * 60);
            if (ageMinutes > 30) {
              debugResults.tests.location.issues.push(`Location is ${Math.round(ageMinutes)} minutes old`);
            }
          }

          if (!location.latitude || !location.longitude) {
            debugResults.tests.location.issues.push("Invalid coordinates");
          }
        } else {
          debugResults.tests.location.issues.push("User location not found");
        }
      }

      // Test 3: Send test notification
      if (testType === "all" || testType === "notification") {
        logger.info("Testing notification sending", { userId });

        const preferencesDoc = await firestore
          .collection("notification_preferences")
          .doc(userId)
          .get();

        if (preferencesDoc.exists) {
          const prefs = preferencesDoc.data()!;

          if (prefs.fcmToken) {
            try {
              const testResult = await FCMNotificationService.sendTestNotification(
                userId,
                prefs.fcmToken
              );

              debugResults.tests.notification = {
                success: testResult.success,
                messageId: testResult.messageId,
                error: testResult.error,
                issues: testResult.success ? [] : [testResult.error || "Unknown error"]
              };
            } catch (error: any) {
              debugResults.tests.notification = {
                success: false,
                error: error.message,
                issues: [error.message]
              };
            }
          } else {
            debugResults.tests.notification = {
              success: false,
              issues: ["No FCM token available"]
            };
          }
        } else {
          debugResults.tests.notification = {
            success: false,
            issues: ["User preferences not found"]
          };
        }
      }

      // Test 4: Check recent incidents
      if (testType === "all" || testType === "incidents") {
        logger.info("Testing recent incidents", { userId });

        const recentIncidents = await firestore
          .collection("incidents")
          .where("status", "==", "community_visible")
          .orderBy("postedAt", "desc")
          .limit(5)
          .get();

        debugResults.tests.incidents = {
          count: recentIncidents.size,
          incidents: recentIncidents.docs.map(doc => ({
            id: doc.id,
            data: doc.data(),
            age: new Date().getTime() - doc.data().postedAt?.toDate()?.getTime()
          })),
          issues: recentIncidents.empty ? ["No recent incidents found"] : []
        };
      }

      // Test 5: Check notification logs
      if (testType === "all" || testType === "logs") {
        logger.info("Testing notification logs", { userId });

        const recentLogs = await firestore
          .collection("notification_logs")
          .where("userId", "==", userId)
          .orderBy("timestamp", "desc")
          .limit(10)
          .get();

        debugResults.tests.logs = {
          count: recentLogs.size,
          recentLogs: recentLogs.docs.map(doc => doc.data()),
          issues: recentLogs.empty ? ["No notification logs found"] : []
        };
      }

      logger.info("Debug test completed", { userId, results: debugResults });
      return debugResults;

    } catch (error) {
      logger.error("Error in debug test", { error, userId: request.data?.userId });
      throw error;
    }
  }
);

/**
 * Debug function to manually trigger incident notifications
 */
export const debugTriggerIncidentNotifications = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { incidentId, userId } = request.data;

      if (!incidentId || !userId) {
        throw new Error("Incident ID and User ID required");
      }

      const firestore = getFirestore();

      // Get incident data
      const incidentDoc = await firestore.collection("incidents").doc(incidentId).get();

      if (!incidentDoc.exists) {
        throw new Error("Incident not found");
      }

      const incidentData = incidentDoc.data()!;

      logger.info("Manually triggering incident notifications", {
        incidentId,
        userId,
        incidentType: incidentData.categoryKey,
        location: incidentData.location
      });

      return {
        incidentId,
        message: "Manual trigger initiated - check logs for results",
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error("Error manually triggering notifications", { error, incidentId: request.data?.incidentId });
      throw error;
    }
  }
);

/**
 * Debug function to update FCM token for a user
 */
export const debugUpdateFCMToken = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { userId, fcmToken } = request.data;

      if (!userId || !fcmToken) {
        throw new Error("User ID and FCM token required");
      }

      const firestore = getFirestore();

      // Update FCM token in notification preferences
      await firestore.collection("notification_preferences").doc(userId).set({
        fcmToken,
        fcmTokenUpdatedAt: new Date(),
      }, { merge: true });

      logger.info("FCM token updated via debug function", {
        userId,
        tokenPreview: fcmToken.substring(0, 20) + "..."
      });

      return {
        success: true,
        message: "FCM token updated successfully",
        userId,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error("Error updating FCM token", { error, userId: request.data?.userId });
      throw error;
    }
  }
);

/**
 * Debug function to set up complete notification preferences for a user
 */
export const debugSetupNotificationPreferences = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { userId, fcmToken } = request.data;

      if (!userId) {
        throw new Error("User ID required");
      }

      const firestore = getFirestore();

      // Create complete notification preferences
      const preferences: any = {
        userId,
        isLocationNotificationsEnabled: true,
        isLocationTrackingConsented: true,
        notificationRadiusKm: 2.0,
        enabledIncidentTypes: [], // Empty means all types enabled
        quietHours: {
          startTime: { hour: 22, minute: 0 }, // 10 PM
          endTime: { hour: 6, minute: 0 }, // 6 AM
          isEnabled: false, // Disabled by default
        },
        locationConsentGrantedAt: new Date(),
        locationAccessDurationHours: 24.0,
        priorityLevel: "medium",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add FCM token if provided
      if (fcmToken) {
        preferences.fcmToken = fcmToken;
        preferences.fcmTokenUpdatedAt = new Date();
      }

      // Save to Firestore
      await firestore.collection("notification_preferences").doc(userId).set(preferences);

      logger.info("Notification preferences set up via debug function", {
        userId,
        hasToken: !!fcmToken,
        tokenPreview: fcmToken ? fcmToken.substring(0, 20) + "..." : "none"
      });

      return {
        success: true,
        message: "Notification preferences set up successfully",
        userId,
        preferences,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error("Error setting up notification preferences", { error, userId: request.data?.userId });
      throw error;
    }
  }
);
