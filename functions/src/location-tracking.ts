import { onCall } from "firebase-functions/v2/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { getFirestore } from "firebase-admin/firestore";
import { logger } from "firebase-functions";

/**
 * Callable function to update user location
 * Called by the mobile app when user location changes
 */
export const updateUserLocation = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { userId, latitude, longitude, accuracy, timestamp } = request.data;

      // Validate input
      if (!userId || typeof latitude !== "number" || typeof longitude !== "number") {
        throw new Error("Invalid location data");
      }

      // Validate coordinates
      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        throw new Error("Invalid coordinates");
      }

      const firestore = getFirestore();

      // Check if user has location tracking consent
      const preferencesDoc = await firestore
        .collection("notification_preferences")
        .doc(userId)
        .get();

      if (!preferencesDoc.exists) {
        throw new Error("User preferences not found");
      }

      const preferences = preferencesDoc.data()!;
      if (!preferences.isLocationTrackingConsented) {
        throw new Error("Location tracking not consented");
      }

      // Get current location data
      const locationRef = firestore.collection("user_locations").doc(userId);
      const currentLocationDoc = await locationRef.get();

      const newLocationData = {
        latitude,
        longitude,
        accuracy: accuracy || 0,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
      };

      let shouldUpdate = true;

      // Check if location has changed significantly
      if (currentLocationDoc.exists) {
        const currentData = currentLocationDoc.data()!;
        const currentLocation = currentData.currentLocation;

        if (currentLocation) {
          const distance = calculateDistance(
            latitude,
            longitude,
            currentLocation.latitude,
            currentLocation.longitude
          );

          // Only update if moved more than 50 meters or more than 5 minutes have passed
          const timeDiff = Date.now() - currentData.lastLocationUpdate?.toDate()?.getTime() || 0;
          shouldUpdate = distance > 50 || timeDiff > 5 * 60 * 1000;
        }
      }

      if (shouldUpdate) {
        // Update location data
        const locationUpdate: any = {
          currentLocation: newLocationData,
          lastLocationUpdate: new Date(),
          isTrackingEnabled: true,
          accuracy: preferences.accuracy || "balanced",
          batteryOptimizationLevel: preferences.batteryOptimizationLevel || 0.5,
        };

        // Add to recent locations history
        if (currentLocationDoc.exists) {
          const currentData = currentLocationDoc.data()!;
          const recentLocations = currentData.recentLocations || [];
          
          // Add new location to history and keep only last 10
          recentLocations.unshift({
            location: newLocationData,
            timestamp: new Date(),
          });
          
          locationUpdate.recentLocations = recentLocations.slice(0, 10);
        } else {
          locationUpdate.recentLocations = [{
            location: newLocationData,
            timestamp: new Date(),
          }];
        }

        await locationRef.set(locationUpdate, { merge: true });

        // Check for nearby incidents after location update
        await checkNearbyIncidentsForUser(userId, newLocationData);

        logger.info("User location updated successfully", { 
          userId, 
          latitude, 
          longitude,
          accuracy 
        });
      }

      return {
        success: true,
        locationUpdated: shouldUpdate,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      logger.error("Error updating user location", { error });
      throw new Error(`Failed to update location: ${error}`);
    }
  }
);

/**
 * Scheduled function to clean up old location data
 * Runs daily to remove location data older than 7 days
 */
export const cleanupOldLocationData = onSchedule(
  {
    schedule: "0 2 * * *", // Run daily at 2 AM
    region: "us-central1",
  },
  async () => {
    try {
      const firestore = getFirestore();
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      logger.info("Starting location data cleanup", { cutoffDate: sevenDaysAgo });

      // Get all user locations
      const locationsSnapshot = await firestore
        .collection("user_locations")
        .get();

      let cleanedCount = 0;

      for (const doc of locationsSnapshot.docs) {
        const data = doc.data();
        const recentLocations = data.recentLocations || [];

        // Filter out old locations
        const filteredLocations = recentLocations.filter((entry: any) => {
          const entryDate = entry.timestamp?.toDate();
          return entryDate && entryDate > sevenDaysAgo;
        });

        // Update document if locations were removed
        if (filteredLocations.length !== recentLocations.length) {
          await doc.ref.update({
            recentLocations: filteredLocations,
          });
          cleanedCount++;
        }

        // Remove entire document if no recent locations and last update is old
        const lastUpdate = data.lastLocationUpdate?.toDate();
        if (filteredLocations.length === 0 && lastUpdate && lastUpdate < sevenDaysAgo) {
          await doc.ref.delete();
          cleanedCount++;
        }
      }

      logger.info("Location data cleanup completed", { 
        documentsProcessed: locationsSnapshot.docs.length,
        documentsUpdated: cleanedCount 
      });

    } catch (error) {
      logger.error("Error during location data cleanup", { error });
    }
  }
);

/**
 * Callable function to get user's location history
 */
export const getUserLocationHistory = onCall(
  {
    region: "us-central1",
  },
  async (request) => {
    try {
      const { userId, limit = 10 } = request.data;

      if (!userId) {
        throw new Error("User ID required");
      }

      const firestore = getFirestore();
      const locationDoc = await firestore
        .collection("user_locations")
        .doc(userId)
        .get();

      if (!locationDoc.exists) {
        return {
          success: true,
          locations: [],
          currentLocation: null,
        };
      }

      const data = locationDoc.data()!;
      const recentLocations = data.recentLocations || [];

      return {
        success: true,
        locations: recentLocations.slice(0, limit),
        currentLocation: data.currentLocation,
        lastUpdate: data.lastLocationUpdate,
        isTrackingEnabled: data.isTrackingEnabled,
      };

    } catch (error) {
      logger.error("Error getting user location history", { error });
      throw new Error(`Failed to get location history: ${error}`);
    }
  }
);

/**
 * Check for nearby incidents when user location is updated
 */
async function checkNearbyIncidentsForUser(
  userId: string,
  location: { latitude: number; longitude: number }
): Promise<void> {
  try {
    const firestore = getFirestore();

    // Get user preferences
    const preferencesDoc = await firestore
      .collection("notification_preferences")
      .doc(userId)
      .get();

    if (!preferencesDoc.exists) {
      return;
    }

    const preferences = preferencesDoc.data()!;
    
    // Check if notifications are enabled
    if (!preferences.isLocationNotificationsEnabled) {
      return;
    }

    // Check if in quiet hours
    if (isInQuietHours(preferences)) {
      return;
    }

    const radiusKm = preferences.notificationRadiusKm || 2.0;
    const radiusMeters = radiusKm * 1000;

    // Get recent incidents near the location
    const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);
    
    const incidentsSnapshot = await firestore
      .collection("incidents")
      .where("postedAt", ">=", sixHoursAgo)
      .where("visibilityStatus", "==", "visibleToCommunity")
      .where("isBlocked", "==", false)
      .get();

    for (const incidentDoc of incidentsSnapshot.docs) {
      const incident = incidentDoc.data();
      const incidentLocation = incident.location;

      if (!incidentLocation?.latitude || !incidentLocation?.longitude) {
        continue;
      }

      // Calculate distance
      const distance = calculateDistance(
        location.latitude,
        location.longitude,
        incidentLocation.latitude,
        incidentLocation.longitude
      );

      // Check if within radius
      if (distance <= radiusMeters) {
        // Check if user has already been notified about this incident
        const existingNotification = await firestore
          .collection("incident_notifications")
          .where("userId", "==", userId)
          .where("incidentId", "==", incidentDoc.id)
          .limit(1)
          .get();

        if (existingNotification.empty) {
          // Send notification (this would trigger the notification function)
          logger.info("Found nearby incident for user", {
            userId,
            incidentId: incidentDoc.id,
            distance,
            incidentType: incident.categoryKey,
          });

          // In a real implementation, this would trigger the notification
          // For now, we'll just log it
        }
      }
    }

  } catch (error) {
    logger.error("Error checking nearby incidents", { error, userId });
  }
}

/**
 * Helper function to check if user is in quiet hours
 */
function isInQuietHours(preferences: any): boolean {
  const quietHours = preferences.quietHours;
  if (!quietHours || !quietHours.isEnabled) {
    return false;
  }

  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTime = currentHour * 60 + currentMinute;

  const startTime = quietHours.startHour * 60 + quietHours.startMinute;
  const endTime = quietHours.endHour * 60 + quietHours.endMinute;

  if (startTime > endTime) {
    // Overnight quiet hours
    return currentTime >= startTime || currentTime <= endTime;
  } else {
    // Same day quiet hours
    return currentTime >= startTime && currentTime <= endTime;
  }
}

/**
 * Helper function to calculate distance between two points
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
}
