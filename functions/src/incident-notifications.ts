import { onDocumentCreated } from "firebase-functions/v2/firestore";
import { onCall } from "firebase-functions/v2/https";
import { getFirestore } from "firebase-admin/firestore";
import { logger } from "firebase-functions";
import { FCMNotificationService } from "./services/fcm-notification-service";

interface UserData {
  userId: string;
  fcmToken: string;
  location: { latitude: number; longitude: number };
  distance: number;
}

/**
 * Main function: Process new incident and send notifications
 */
export const processNewIncident = onDocumentCreated(
  {
    document: "incidents/{incidentId}",
    region: "us-central1",
  },
  async (event) => {
    const incidentId = event.params.incidentId;
    const incidentData = event.data?.data();

    if (!incidentData) {
      logger.error("No incident data", { incidentId });
      return;
    }

    logger.info("Processing incident", { incidentId, type: incidentData.categoryKey });

    // Validate incident location
    const location = incidentData.location;
    if (!location?.latitude || !location?.longitude) {
      logger.error("Invalid location", { incidentId });
      return;
    }

    try {
      // Find nearby users
      const users = await findNearbyUsers(location.latitude, location.longitude, incidentData.categoryKey);
      logger.info(`Found ${users.length} users`, { incidentId });

      // Send notifications
      for (const user of users) {
        await sendNotification(user, incidentData, incidentId);
      }

      logger.info("Processing complete", { incidentId });
    } catch (error) {
      logger.error("Processing failed", { error, incidentId });
    }
  }
);

/**
 * Find users near incident location
 */
async function findNearbyUsers(lat: number, lng: number, incidentType: string): Promise<UserData[]> {
  const firestore = getFirestore();
  const users: UserData[] = [];

  try {
    // Get users with notifications enabled
    const snapshot = await firestore
      .collection("notification_preferences")
      .where("isLocationNotificationsEnabled", "==", true)
      .where("isLocationTrackingConsented", "==", true)
      .get();

    logger.info(`Query found ${snapshot.size} users with notifications enabled`);

    for (const doc of snapshot.docs) {
      const prefs = doc.data();
      const userId = doc.id;

      // Check if user wants this incident type
      if (!wantsIncidentType(prefs, incidentType)) {
        continue;
      }

      // Check quiet hours
      if (isQuietHours(prefs)) {
        continue;
      }

      // Get user location
      const userLocation = await getUserLocation(userId, prefs);
      if (!userLocation) {
        continue;
      }

      // Calculate distance
      const distance = calculateDistance(lat, lng, userLocation.latitude, userLocation.longitude);
      const maxDistance = prefs.notificationRadiusKm || 3;

      if (distance <= maxDistance && prefs.fcmToken) {
        users.push({
          userId,
          fcmToken: prefs.fcmToken,
          location: userLocation,
          distance
        });
      }
    }

    logger.info(`Filtered to ${users.length} nearby users`);
    return users;

  } catch (error) {
    logger.error("Error finding users", { error });
    return [];
  }
}

/**
 * Check if user wants notifications for this incident type
 */
function wantsIncidentType(prefs: any, incidentType: string): boolean {
  if (!prefs.enabledIncidentTypes || prefs.enabledIncidentTypes.length === 0) {
    return true; // All types enabled if empty
  }
  return prefs.enabledIncidentTypes.includes(incidentType);
}

/**
 * Check if user is in quiet hours
 */
function isQuietHours(prefs: any): boolean {
  if (!prefs.quietHours?.isEnabled) {
    return false;
  }

  const now = new Date();
  const currentHour = now.getHours();
  const startHour = prefs.quietHours.startHour || 22;
  const endHour = prefs.quietHours.endHour || 6;

  if (startHour <= endHour) {
    return currentHour >= startHour && currentHour < endHour;
  } else {
    return currentHour >= startHour || currentHour < endHour;
  }
}

/**
 * Get user's current location
 */
async function getUserLocation(userId: string, prefs: any): Promise<{ latitude: number; longitude: number } | null> {
  const firestore = getFirestore();

  try {
    // Try user_locations collection first
    const locationDoc = await firestore.collection("user_locations").doc(userId).get();
    if (locationDoc.exists) {
      const data = locationDoc.data()!;
      if (data.latitude && data.longitude) {
        const age = Date.now() - data.timestamp?.toDate()?.getTime();
        if (age < 24 * 60 * 60 * 1000) { // Less than 24 hours old
          return { latitude: data.latitude, longitude: data.longitude };
        }
      }
    }

    // Fallback to lastKnownLocation in preferences
    if (prefs.lastKnownLocation?.latitude && prefs.lastKnownLocation?.longitude) {
      return {
        latitude: prefs.lastKnownLocation.latitude,
        longitude: prefs.lastKnownLocation.longitude
      };
    }

    return null;
  } catch (error) {
    logger.error("Error getting user location", { error, userId });
    return null;
  }
}

/**
 * Calculate distance between two points in kilometers
 */
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Send notification to user
 */
async function sendNotification(user: UserData, incidentData: any, incidentId: string): Promise<void> {
  try {
    // Use the same FCM service that works for test notifications
    const notificationData = {
      incidentId,
      incidentType: incidentData.categoryKey,
      severity: 'medium' as 'medium',
      distance: `${user.distance.toFixed(1)}km away`,
      latitude: user.location.latitude.toString(),
      longitude: user.location.longitude.toString(),
      timestamp: new Date().toISOString(),
      address: 'Incident Location'
    };

    const result = await FCMNotificationService.sendIncidentNotification(
      user.userId,
      user.fcmToken,
      notificationData,
      `${user.distance.toFixed(1)}km away`
    );

    if (result.success) {
      logger.info("Notification sent", { userId: user.userId, incidentId, distance: user.distance });
    } else {
      logger.error("Notification failed", { userId: user.userId, incidentId, error: result.error });
    }

  } catch (error) {
    logger.error("Failed to send notification", { error, userId: user.userId, incidentId });
  }
}



/**
 * Manual trigger function for testing
 */
export const triggerIncidentNotifications = onCall(
  { region: "us-central1" },
  async (request) => {
    const { incidentId } = request.data;

    if (!incidentId) {
      throw new Error("incidentId required");
    }

    const firestore = getFirestore();
    const doc = await firestore.collection("incidents").doc(incidentId).get();

    if (!doc.exists) {
      throw new Error("Incident not found");
    }

    const incidentData = doc.data()!;
    const location = incidentData.location;

    if (!location?.latitude || !location?.longitude) {
      throw new Error("Invalid incident location");
    }

    const users = await findNearbyUsers(location.latitude, location.longitude, incidentData.categoryKey);

    for (const user of users) {
      await sendNotification(user, incidentData, incidentId);
    }

    return {
      success: true,
      incidentId,
      notificationsSent: users.length
    };
  }
);
