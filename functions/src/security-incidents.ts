import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { geohashForLocation, distanceBetween } from 'geofire-common';
import * as _ from 'lodash';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// Types
interface IncidentData {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: {
    latitude: number;
    longitude: number;
  };
  zoneId: string;
  geohash: string;
  timestamp: admin.firestore.Timestamp;
  resolvedAt?: admin.firestore.Timestamp;
  status: 'active' | 'investigating' | 'resolved' | 'dismissed';
  description?: string;
  reporterId?: string;
  metadata?: Record<string, any>;
  mediaUrls?: string[];
  priority: number;
  isAnonymous: boolean;
  lastUpdated?: admin.firestore.Timestamp;
}

interface SummaryData {
  zoneId: string;
  totalIncidents: number;
  activeIncidents: number;
  highSeverityCount: number;
  mediumSeverityCount: number;
  lowSeverityCount: number;
  incidentsByType: Record<string, number>;
  lastUpdated: admin.firestore.Timestamp;
  timeframe: string;
  averageResponseTime: number;
  resolvedIncidents: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  hotspots: Array<{
    latitude: number;
    longitude: number;
    incidentCount: number;
    dominantType: string;
    radius: number;
  }>;
}

// Utility functions
function calculateThreatLevel(highCount: number, mediumCount: number, totalCount: number): string {
  const highRatio = highCount / Math.max(totalCount, 1);
  const mediumRatio = mediumCount / Math.max(totalCount, 1);
  
  if (highRatio >= 0.3 || highCount >= 5) return 'critical';
  if (highRatio >= 0.15 || highCount >= 3) return 'high';
  if (mediumRatio >= 0.4 || mediumCount >= 5) return 'medium';
  return 'low';
}

function findHotspots(incidents: IncidentData[], radiusKm: number = 0.5): Array<any> {
  const clusters: Array<{
    center: { lat: number; lng: number };
    incidents: IncidentData[];
  }> = [];

  incidents.forEach(incident => {
    const point = { lat: incident.location.latitude, lng: incident.location.longitude };
    
    // Find existing cluster within radius
    let cluster = clusters.find(c => 
      distanceBetween([c.center.lat, c.center.lng], [point.lat, point.lng]) <= radiusKm * 1000
    );
    
    if (cluster) {
      cluster.incidents.push(incident);
      // Recalculate center
      const avgLat = cluster.incidents.reduce((sum, i) => sum + i.location.latitude, 0) / cluster.incidents.length;
      const avgLng = cluster.incidents.reduce((sum, i) => sum + i.location.longitude, 0) / cluster.incidents.length;
      cluster.center = { lat: avgLat, lng: avgLng };
    } else {
      clusters.push({
        center: point,
        incidents: [incident]
      });
    }
  });

  // Convert to hotspots (only clusters with 2+ incidents)
  return clusters
    .filter(c => c.incidents.length >= 2)
    .map(c => {
      const typeCount = _.countBy(c.incidents, 'type');
      const dominantType = _.maxBy(Object.keys(typeCount), type => typeCount[type]) || 'unknown';
      
      return {
        latitude: c.center.lat,
        longitude: c.center.lng,
        incidentCount: c.incidents.length,
        dominantType,
        radius: radiusKm * 1000
      };
    });
}

// Cloud Functions

// 1. Incident Creation Trigger - Real-time summary updates
export const onIncidentCreated = functions.firestore
  .document('security_incidents/{incidentId}')
  .onCreate(async (snap, context) => {
    const incident = snap.data() as IncidentData;
    const incidentId = context.params.incidentId;
    
    try {
      // Update real-time summary
      await updateRealtimeSummary(incident.zoneId);
      
      // Send notifications for high-priority incidents
      if (incident.severity === 'high' || incident.severity === 'critical') {
        await sendHighPriorityNotification(incident, incidentId);
      }
      
      // Update trends
      await updateTrends(incident.zoneId, '5m');
      
      console.log(`Processed new incident: ${incidentId} in zone: ${incident.zoneId}`);
    } catch (error) {
      console.error('Error processing incident creation:', error);
    }
  });

// 2. Incident Update Trigger
export const onIncidentUpdated = functions.firestore
  .document('security_incidents/{incidentId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data() as IncidentData;
    const after = change.after.data() as IncidentData;
    const incidentId = context.params.incidentId;
    
    try {
      // If status changed to resolved
      if (before.status !== 'resolved' && after.status === 'resolved') {
        await updateRealtimeSummary(after.zoneId);
        await updateTrends(after.zoneId, '5m');
        
        // Calculate response time
        const responseTime = after.resolvedAt 
          ? after.resolvedAt.toMillis() - after.timestamp.toMillis()
          : 0;
        
        // Update analytics
        await updateResponseTimeMetrics(after.zoneId, responseTime);
      }
      
      console.log(`Updated incident: ${incidentId}`);
    } catch (error) {
      console.error('Error processing incident update:', error);
    }
  });

// 3. Scheduled Summary Updates
export const updateSummariesScheduled = functions.pubsub
  .schedule('every 5 minutes')
  .onRun(async (context) => {
    try {
      // Get all active zones
      const zonesSnapshot = await db.collection('zones').get();
      const updatePromises = zonesSnapshot.docs.map(doc => 
        updateRealtimeSummary(doc.id)
      );
      
      await Promise.all(updatePromises);
      console.log(`Updated summaries for ${zonesSnapshot.size} zones`);
    } catch (error) {
      console.error('Error in scheduled summary update:', error);
    }
  });

// 4. Trend Analysis Updates
export const updateTrendsScheduled = functions.pubsub
  .schedule('every 15 minutes')
  .onRun(async (context) => {
    try {
      const zonesSnapshot = await db.collection('zones').get();
      const updatePromises = zonesSnapshot.docs.map(doc => 
        Promise.all([
          updateTrends(doc.id, '15m'),
          updateTrends(doc.id, '1h'),
          updateTrends(doc.id, '1d')
        ])
      );
      
      await Promise.all(updatePromises);
      console.log(`Updated trends for ${zonesSnapshot.size} zones`);
    } catch (error) {
      console.error('Error in scheduled trend update:', error);
    }
  });

// Helper Functions

async function updateRealtimeSummary(zoneId: string): Promise<void> {
  const now = admin.firestore.Timestamp.now();
  const oneDayAgo = admin.firestore.Timestamp.fromMillis(now.toMillis() - 24 * 60 * 60 * 1000);
  
  // Get incidents from last 24 hours
  const incidentsSnapshot = await db.collection('security_incidents')
    .where('zoneId', '==', zoneId)
    .where('timestamp', '>=', oneDayAgo)
    .get();
  
  const incidents = incidentsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as IncidentData[];
  
  // Calculate metrics
  const totalIncidents = incidents.length;
  const activeIncidents = incidents.filter(i => i.status === 'active').length;
  const resolvedIncidents = incidents.filter(i => i.status === 'resolved').length;
  
  const severityCounts = _.countBy(incidents, 'severity');
  const highSeverityCount = severityCounts.high || 0;
  const mediumSeverityCount = severityCounts.medium || 0;
  const lowSeverityCount = severityCounts.low || 0;
  
  const incidentsByType = _.countBy(incidents, 'type');
  
  // Calculate average response time
  const resolvedWithTime = incidents.filter(i => i.status === 'resolved' && i.resolvedAt);
  const averageResponseTime = resolvedWithTime.length > 0
    ? resolvedWithTime.reduce((sum, i) => 
        sum + (i.resolvedAt!.toMillis() - i.timestamp.toMillis()), 0
      ) / resolvedWithTime.length
    : 0;
  
  // Find hotspots
  const hotspots = findHotspots(incidents);
  
  // Determine threat level
  const threatLevel = calculateThreatLevel(highSeverityCount, mediumSeverityCount, totalIncidents);
  
  const summary: SummaryData = {
    zoneId,
    totalIncidents,
    activeIncidents,
    highSeverityCount,
    mediumSeverityCount,
    lowSeverityCount,
    incidentsByType,
    lastUpdated: now,
    timeframe: '24h',
    averageResponseTime,
    resolvedIncidents,
    threatLevel: threatLevel as any,
    hotspots
  };
  
  // Save summary
  await db.collection('incident_realtime_summary').doc(zoneId).set(summary);
}

async function updateTrends(zoneId: string, timeframe: string): Promise<void> {
  const now = admin.firestore.Timestamp.now();
  let timeWindow: number;
  
  switch (timeframe) {
    case '5m':
      timeWindow = 5 * 60 * 1000;
      break;
    case '15m':
      timeWindow = 15 * 60 * 1000;
      break;
    case '1h':
      timeWindow = 60 * 60 * 1000;
      break;
    case '1d':
      timeWindow = 24 * 60 * 60 * 1000;
      break;
    default:
      timeWindow = 60 * 60 * 1000;
  }
  
  const windowStart = admin.firestore.Timestamp.fromMillis(now.toMillis() - timeWindow);
  
  // Get incidents in time window
  const incidentsSnapshot = await db.collection('security_incidents')
    .where('zoneId', '==', zoneId)
    .where('timestamp', '>=', windowStart)
    .get();
  
  const incidents = incidentsSnapshot.docs.map(doc => doc.data()) as IncidentData[];
  const incidentCount = incidents.length;
  const severityBreakdown = _.countBy(incidents, 'severity');
  
  // Create trend data point
  const dataPoint = {
    timestamp: now,
    incidentCount,
    severityBreakdown
  };
  
  // Calculate trend direction (simplified)
  const trendDirection = incidentCount > 0 ? 0.1 : -0.1; // Placeholder logic
  const trendStrength = 'moderate'; // Placeholder logic
  
  const trendDoc = {
    zoneId,
    timeframe,
    timestamp: now,
    dataPoints: [dataPoint],
    trendDirection,
    trendStrength
  };
  
  const docId = `${zoneId}_${timeframe}_${now.toMillis()}`;
  await db.collection('incident_trends').doc(docId).set(trendDoc);
}

async function sendHighPriorityNotification(incident: IncidentData, incidentId: string): Promise<void> {
  try {
    // Get users in the zone
    const usersSnapshot = await db.collection('users')
      .where('authorizedZones', 'array-contains', incident.zoneId)
      .get();

    const notificationPromises = usersSnapshot.docs.map(async (userDoc) => {
      const userId = userDoc.id;
      const userData = userDoc.data();

      // Check if user has notifications enabled
      if (!userData.notificationsEnabled) return;

      // Calculate distance to incident
      const userLocation = userData.lastKnownLocation;
      if (!userLocation) return;

      const distance = distanceBetween(
        [userLocation.latitude, userLocation.longitude],
        [incident.location.latitude, incident.location.longitude]
      );

      // Only notify if within 3km
      if (distance > 3000) return;

      // Get FCM token
      const tokenDoc = await db.collection('notification_tokens').doc(userId).get();
      if (!tokenDoc.exists) return;

      const fcmToken = tokenDoc.data()?.token;
      if (!fcmToken) return;

      // Send notification
      const message = {
        token: fcmToken,
        notification: {
          title: `${incident.severity.toUpperCase()} Security Alert`,
          body: `${incident.type} reported nearby. Stay alert and avoid the area.`
        },
        data: {
          type: 'security_incident',
          incidentId,
          severity: incident.severity,
          distance: Math.round(distance).toString()
        },
        android: {
          priority: 'high' as const,
          notification: {
            priority: 'high' as const,
            defaultSound: true,
            channelId: 'security_alerts'
          }
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: `${incident.severity.toUpperCase()} Security Alert`,
                body: `${incident.type} reported nearby. Stay alert and avoid the area.`
              },
              sound: 'default',
              badge: 1
            }
          }
        }
      };

      await admin.messaging().send(message);

      // Log notification
      await db.collection('incident_notifications').add({
        userId,
        incidentId,
        type: 'high_priority_alert',
        sentAt: admin.firestore.Timestamp.now(),
        distance: Math.round(distance)
      });
    });

    await Promise.all(notificationPromises);
    console.log(`Sent high-priority notifications for incident: ${incidentId}`);
  } catch (error) {
    console.error('Error sending high-priority notification:', error);
  }
}

async function updateResponseTimeMetrics(zoneId: string, responseTime: number): Promise<void> {
  try {
    const metricsRef = db.collection('analytics').doc(`response_times_${zoneId}`);
    const metricsDoc = await metricsRef.get();

    if (metricsDoc.exists) {
      const data = metricsDoc.data()!;
      const newCount = (data.count || 0) + 1;
      const newTotal = (data.totalResponseTime || 0) + responseTime;
      const newAverage = newTotal / newCount;

      await metricsRef.update({
        count: newCount,
        totalResponseTime: newTotal,
        averageResponseTime: newAverage,
        lastUpdated: admin.firestore.Timestamp.now()
      });
    } else {
      await metricsRef.set({
        zoneId,
        count: 1,
        totalResponseTime: responseTime,
        averageResponseTime: responseTime,
        lastUpdated: admin.firestore.Timestamp.now()
      });
    }
  } catch (error) {
    console.error('Error updating response time metrics:', error);
  }
}

// 5. Cleanup old incidents (run daily)
export const cleanupOldIncidents = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    try {
      const thirtyDaysAgo = admin.firestore.Timestamp.fromMillis(
        Date.now() - 30 * 24 * 60 * 60 * 1000
      );

      // Archive old resolved incidents
      const oldIncidentsSnapshot = await db.collection('security_incidents')
        .where('status', '==', 'resolved')
        .where('resolvedAt', '<', thirtyDaysAgo)
        .limit(100)
        .get();

      const batch = db.batch();
      let archivedCount = 0;

      oldIncidentsSnapshot.docs.forEach(doc => {
        // Move to archive collection
        const archiveRef = db.collection('archived_incidents').doc(doc.id);
        batch.set(archiveRef, {
          ...doc.data(),
          archivedAt: admin.firestore.Timestamp.now()
        });

        // Delete from main collection
        batch.delete(doc.ref);
        archivedCount++;
      });

      if (archivedCount > 0) {
        await batch.commit();
        console.log(`Archived ${archivedCount} old incidents`);
      }
    } catch (error) {
      console.error('Error cleaning up old incidents:', error);
    }
  });

// 6. Emergency Alert Trigger
export const triggerEmergencyAlert = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { zoneId, alertType, message, location } = data;

  try {
    // Create emergency alert
    const alertRef = await db.collection('emergency_alerts').add({
      zoneId,
      alertType,
      message,
      location,
      triggeredBy: context.auth.uid,
      timestamp: admin.firestore.Timestamp.now(),
      status: 'active'
    });

    // Send immediate notifications to all users in zone
    const usersSnapshot = await db.collection('users')
      .where('authorizedZones', 'array-contains', zoneId)
      .get();

    const notificationPromises = usersSnapshot.docs.map(async (userDoc) => {
      const tokenDoc = await db.collection('notification_tokens').doc(userDoc.id).get();
      if (!tokenDoc.exists) return;

      const fcmToken = tokenDoc.data()?.token;
      if (!fcmToken) return;

      const emergencyMessage = {
        token: fcmToken,
        notification: {
          title: '🚨 EMERGENCY ALERT',
          body: message
        },
        data: {
          type: 'emergency_alert',
          alertId: alertRef.id,
          alertType
        },
        android: {
          priority: 'high' as const,
          notification: {
            priority: 'max' as const,
            defaultSound: true,
            channelId: 'emergency_alerts'
          }
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: '🚨 EMERGENCY ALERT',
                body: message
              },
              sound: 'emergency.wav',
              badge: 1,
              'content-available': 1
            }
          }
        }
      };

      await admin.messaging().send(emergencyMessage);
    });

    await Promise.all(notificationPromises);

    return { success: true, alertId: alertRef.id };
  } catch (error) {
    console.error('Error triggering emergency alert:', error);
    throw new functions.https.HttpsError('internal', 'Failed to trigger emergency alert');
  }
});
