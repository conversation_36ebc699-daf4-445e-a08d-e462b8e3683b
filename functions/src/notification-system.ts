import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { onSchedule } from 'firebase-functions/v2/scheduler';
import { onRequest } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';

// Import services
import { NotificationOrchestrator } from './services/notification-orchestrator';
import { ErrorHandlingService } from './services/error-handling-service';
import { AnalyticsService } from './services/analytics-service';
import { GeospatialService } from './services/geospatial-service';
import { FCMNotificationService } from './services/fcm-notification-service';

/**
 * Main Cloud Function: Triggered when a new incident is created
 */
export const processIncidentNotificationsGen2 = onDocumentCreated(
  'incidents/{incidentId}',
  async (event) => {
    const incidentId = event.params?.incidentId;
    const incidentData = event.data?.data();

    if (!incidentId || !incidentData) {
      console.error('Missing incident data or ID');
      return;
    }

    return ErrorHandlingService.executeWithErrorHandling(
      'processIncidentNotifications',
      async () => {
        console.log(`🚨 Processing notifications for incident ${incidentId}`);

        const result = await NotificationOrchestrator.processIncidentNotifications({
          id: incidentId,
          type: incidentData.type,
          title: incidentData.title,
          description: incidentData.description,
          location: {
            latitude: incidentData.location.latitude,
            longitude: incidentData.location.longitude,
            address: incidentData.location.address
          },
          severity: incidentData.severity,
          timestamp: incidentData.timestamp,
          reportedBy: incidentData.reportedBy,
          status: incidentData.status
        });

        console.log(`✅ Notification processing completed:`, result);
        return result;
      },
      { incidentId, userId: incidentData.reportedBy }
    );
  });

/**
 * HTTP Function: Update user location
 */
export const updateUserLocationGen2 = onCall(async (request) => {
  return ErrorHandlingService.executeWithErrorHandling(
    'updateUserLocation',
    async () => {
      if (!request.auth) {
        throw new HttpsError(
          'unauthenticated',
          'Authentication required'
        );
      }

      const { latitude, longitude, accuracy, source } = request.data;
      const userId = request.auth.uid;

      if (!latitude || !longitude) {
        throw new HttpsError(
          'invalid-argument',
          'Latitude and longitude are required'
        );
      }

      await GeospatialService.updateUserLocation(
        userId,
        { latitude, longitude },
        source || 'gps',
        accuracy
      );

      return { success: true, message: 'Location updated successfully' };
    },
    { userId: request.auth?.uid }
  );
});

/**
 * HTTP Function: Get notification analytics
 */
export const getNotificationAnalyticsGen2 = onCall(async (request) => {
  return ErrorHandlingService.executeWithErrorHandling(
    'getNotificationAnalytics',
    async () => {
      if (!request.auth) {
        throw new HttpsError(
          'unauthenticated',
          'Authentication required'
        );
      }

      const { timeRangeHours = 24 } = request.data;
      const dashboardData = await AnalyticsService.generateDashboardData(timeRangeHours);

      return { success: true, data: dashboardData };
    },
    { userId: request.auth?.uid }
  );
});

/**
 * HTTP Function: Test notification
 */
export const sendTestNotificationGen2 = onCall(async (request) => {
  return ErrorHandlingService.executeWithErrorHandling(
    'sendTestNotification',
    async () => {
      if (!request.auth) {
        throw new HttpsError(
          'unauthenticated',
          'Authentication required'
        );
      }

      const { fcmToken } = request.data;
      const userId = request.auth.uid;

      if (!fcmToken) {
        throw new HttpsError(
          'invalid-argument',
          'FCM token is required'
        );
      }

      const result = await FCMNotificationService.sendTestNotification(userId, fcmToken);
      return { success: result.success, messageId: result.messageId, error: result.error };
    },
    { userId: request.auth?.uid }
  );
});

/**
 * Scheduled Function: Clean up old location data
 */
export const cleanupOldLocationDataGen2 = onSchedule(
  { schedule: '0 2 * * *', timeZone: 'UTC' },
  async (event) => {
    await ErrorHandlingService.executeWithErrorHandling(
      'cleanupOldLocationData',
      async () => {
        console.log('🧹 Starting cleanup of old location data');
        const deletedCount = await GeospatialService.cleanupOldLocations(30);
        console.log(`🧹 Cleanup completed: ${deletedCount} records deleted`);
        return { deletedCount };
      }
    );
  });

/**
 * HTTP Function: System health check
 */
export const getSystemHealthGen2 = onRequest(async (req, res) => {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        firestore: 'healthy',
        messaging: 'healthy',
        geospatial: 'healthy'
      },
      version: '1.0.0'
    };

    try {
      await admin.firestore().collection('health_check').doc('test').get();
    } catch (error) {
      healthData.services.firestore = 'unhealthy';
      healthData.status = 'degraded';
    }

    try {
      // Test messaging service availability by checking if we can access the messaging instance
      // This is a safer approach than trying to send actual messages
      const messaging = admin.messaging();

      // Try to validate a dummy token format - this will fail gracefully without sending
      try {
        await messaging.send({
          token: 'invalid-test-token-format-for-health-check',
          notification: { title: 'Health Check', body: 'This should not be sent' }
        }, true); // dry run mode
      } catch (tokenError: any) {
        console.log('Messaging health check token error:', {
          code: tokenError.code,
          message: tokenError.message,
          errorInfo: tokenError.errorInfo
        });

        // Expected to fail with invalid token - this means messaging service is accessible
        if (tokenError.code === 'messaging/invalid-registration-token' ||
          tokenError.code === 'messaging/registration-token-not-registered' ||
          tokenError.message?.includes('invalid-registration-token') ||
          tokenError.message?.includes('registration-token-not-registered')) {
          // This is expected - messaging service is working
          console.log('Messaging service is healthy (expected token error)');
          healthData.services.messaging = 'healthy';
        } else {
          // Unexpected error - messaging service might have issues
          console.warn('Messaging health check unexpected error:', tokenError);
          healthData.services.messaging = 'unhealthy';
          healthData.status = 'degraded';
        }
      }
    } catch (error: any) {
      console.error('Messaging service health check failed:', error);
      healthData.services.messaging = 'unhealthy';
      healthData.status = 'degraded';
    }

    res.status(200).json(healthData);
  } catch (error: any) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * Webhook for notification tracking
 */
export const notificationTrackingWebhookGen2 = AnalyticsService.createTrackingWebhook();

/**
 * HTTP Function: Get error statistics
 */
export const getErrorStatisticsGen2 = onCall(async (request) => {
  return ErrorHandlingService.executeWithErrorHandling(
    'getErrorStatistics',
    async () => {
      if (!request.auth) {
        throw new HttpsError(
          'unauthenticated',
          'Authentication required'
        );
      }

      const { timeRangeHours = 24 } = request.data;
      const errorStats = await ErrorHandlingService.getErrorStatistics(timeRangeHours);

      return { success: true, data: errorStats };
    },
    { userId: request.auth?.uid }
  );
});