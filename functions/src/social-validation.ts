import {onCall, HttpsError} from "firebase-functions/v2/https";
import {getFirestore} from "firebase-admin/firestore";
import * as logger from "firebase-functions/logger";

/**
 * Calculate distance between two coordinates using Haversine formula
 */
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Processes social validation when a validated user scans a QR code
 * The phone sends the validator's location to verify proximity (600m) to the zone
 */
export const processSocialValidation = onCall({
  region: "us-central1",
}, async (request) => {
  try {
    const {zoneId, requestId, validatorLocation} = request.data;

    // Validate input - accept either zoneId or requestId
    if ((!zoneId && !requestId) || !validatorLocation?.latitude || !validatorLocation?.longitude) {
      throw new HttpsError("invalid-argument", "Zone ID (or request ID) and validator location are required");
    }

    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const validatorUserId = request.auth.uid;
    const firestore = getFirestore();
    const now = new Date();

    // Resolve zone ID if request ID was provided
    let actualZoneId = zoneId;
    if (!actualZoneId && requestId) {
      // Look up zone ID from social validation request
      const socialValidationQuery = await firestore
        .collection("social_validation_requests")
        .where("id", "==", requestId)
        .limit(1)
        .get();

      if (socialValidationQuery.empty) {
        throw new HttpsError("not-found", "Social validation request not found or expired");
      }

      const requestDoc = socialValidationQuery.docs[0];
      const requestData = requestDoc.data();
      actualZoneId = requestData.zoneId;

      // Check if request is still valid (not expired)
      const expiresAt = requestData.expiresAt?.toDate();
      if (expiresAt && now > expiresAt) {
        throw new HttpsError("failed-precondition", "Social validation request has expired");
      }
    }

    // Check if validator user is ID-validated
    const validatorDoc = await firestore.collection("users").doc(validatorUserId).get();
    if (!validatorDoc.exists) {
      throw new HttpsError("not-found", "User profile not found");
    }

    const validatorData = validatorDoc.data()!;
    if (validatorData.validationStatus !== 'validated') {
      throw new HttpsError("permission-denied", "Only ID-validated users can validate zones");
    }

    // Get zone data
    const zoneDoc = await firestore.collection("zones").doc(actualZoneId).get();
    if (!zoneDoc.exists) {
      throw new HttpsError("not-found", "Zone not found");
    }

    const zoneData = zoneDoc.data()!;

    // Validate zone eligibility
    if (zoneData.validationMethod !== 'social') {
      throw new HttpsError("invalid-argument", "Zone not configured for social validation");
    }

    if (zoneData.validationStatus !== 'pending') {
      throw new HttpsError("invalid-argument", "Zone is not pending validation");
    }

    if (zoneData.createdBy === validatorUserId) {
      throw new HttpsError("permission-denied", "Cannot validate your own zone");
    }

    const communityValidatedBy = zoneData.communityValidatedBy || [];
    if (communityValidatedBy.includes(validatorUserId)) {
      throw new HttpsError("permission-denied", "You have already validated this zone");
    }

    // Check proximity - validator must be within 600m of zone location
    const zoneLocation = zoneData.centerCoordinates;
    if (!zoneLocation?.latitude || !zoneLocation?.longitude) {
      throw new HttpsError("invalid-argument", "Zone location data is invalid");
    }

    const distance = calculateDistance(
      validatorLocation.latitude,
      validatorLocation.longitude,
      zoneLocation.latitude,
      zoneLocation.longitude
    );

    if (distance > 600) {
      throw new HttpsError("permission-denied", `You must be within 600m of the zone. Current distance: ${Math.round(distance)}m`);
    }

    // Record the validation
    const socialValidationRef = firestore.collection("social_validations").doc();
    await socialValidationRef.set({
      id: socialValidationRef.id,
      zoneId: actualZoneId,
      validatorUserId,
      validatorLocation: {
        latitude: validatorLocation.latitude,
        longitude: validatorLocation.longitude,
        accuracy: validatorLocation.accuracy || null,
      },
      distance: Math.round(distance),
      createdAt: now,
      status: 'completed',
    });

    // Update zone validation count
    const currentValidationCount = zoneData.communityValidationCount || 0;
    const newValidationCount = currentValidationCount + 1;
    const updatedValidatedBy = [...communityValidatedBy, validatorUserId];

    const updateData: any = {
      communityValidationCount: newValidationCount,
      communityValidatedBy: updatedValidatedBy,
      updatedAt: now,
    };

    // Note: Zone validation status is now handled by client-side stream listeners
    // This provides better real-time updates and reduces cloud function complexity

    await firestore.collection("zones").doc(actualZoneId).update(updateData);

    logger.info(`Social validation completed: Zone ${actualZoneId} validated by user ${validatorUserId}. Count: ${newValidationCount}/3`);

    return {
      success: true,
      validationId: socialValidationRef.id,
      newValidationCount,
      isZoneValidated: newValidationCount >= 3,
      distance: Math.round(distance),
      message: newValidationCount >= 3
        ? "Zone successfully validated!"
        : `Validation recorded. ${3 - newValidationCount} more needed.`
    };

  } catch (error) {
    logger.error("Error processing social validation:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Internal server error");
  }
});


