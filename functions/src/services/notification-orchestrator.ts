import * as admin from 'firebase-admin';
import { UserPreferenceService, UserPreferences } from './user-preference-service';
import { GeoPoint } from './geospatial-service';
import { FCMNotificationService, IncidentNotificationData } from './fcm-notification-service';
import moment from 'moment-timezone';

const db = admin.firestore();

export interface IncidentData {
  id: string;
  type: string;
  title: string;
  description: string;
  location: GeoPoint & { address?: string };
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: admin.firestore.Timestamp;
  reportedBy: string;
  status: 'active' | 'resolved' | 'investigating';
}

export interface NotificationProcessingResult {
  incidentId: string;
  totalEligibleUsers: number;
  notificationsSent: number;
  notificationsFailed: number;
  processingTimeMs: number;
  quietHoursFiltered: number;
  priorityFiltered: number;
  radiusFiltered: number;
  consentFiltered: number;
  tokenMissingFiltered: number;
}

/**
 * Main orchestrator for the notification system
 * Integrates all services and handles the complete notification flow
 */
export class NotificationOrchestrator {

  /**
   * Process incident and send notifications to eligible users
   */
  static async processIncidentNotifications(incident: IncidentData): Promise<NotificationProcessingResult> {
    const startTime = Date.now();

    console.log(`🚨 Processing notifications for incident ${incident.id}`, {
      type: incident.type,
      severity: incident.severity,
      location: incident.location
    });

    // Initialize counters
    let totalEligibleUsers = 0;
    let quietHoursFiltered = 0;
    let priorityFiltered = 0;
    let radiusFiltered = 0;
    let consentFiltered = 0;
    let tokenMissingFiltered = 0;

    try {
      // Step 1: Get all users with basic notification preferences
      const allUsers = await UserPreferenceService.getEligibleUsers();
      console.log(`👥 Found ${allUsers.length} users with notifications enabled`);

      // Step 2: Apply all filters and collect eligible users
      const eligibleUsers: Array<{
        user: UserPreferences;
        distanceText: string;
        distance: number;
      }> = [];

      for (const user of allUsers) {
        // Check location consent validity
        if (!UserPreferenceService.isLocationConsentValid(user)) {
          consentFiltered++;
          continue;
        }

        // Check incident type preference
        if (!UserPreferenceService.isIncidentTypeEnabled(user, incident.type)) {
          continue; // Don't count this as it's user preference
        }

        // Check quiet hours
        if (this.isInQuietHours(user)) {
          quietHoursFiltered++;
          continue;
        }

        // Check priority threshold
        if (!UserPreferenceService.meetsPriorityThreshold(user, incident.severity)) {
          priorityFiltered++;
          continue;
        }

        // Check FCM token availability
        if (!user.fcmToken) {
          tokenMissingFiltered++;
          continue;
        }

        // Check distance/radius
        const radiusCheck = await UserPreferenceService.isWithinNotificationRadius(
          user,
          incident.location
        );

        if (!radiusCheck.isWithin) {
          radiusFiltered++;
          continue;
        }

        // User passed all filters
        eligibleUsers.push({
          user,
          distanceText: radiusCheck.distanceText,
          distance: radiusCheck.distance
        });
      }

      totalEligibleUsers = eligibleUsers.length;

      console.log(`✅ Filtering results:`, {
        totalUsers: allUsers.length,
        eligible: totalEligibleUsers,
        filtered: {
          consentExpired: consentFiltered,
          quietHours: quietHoursFiltered,
          priority: priorityFiltered,
          radius: radiusFiltered,
          tokenMissing: tokenMissingFiltered
        }
      });

      // Step 3: Send notifications if there are eligible users
      let notificationsSent = 0;
      let notificationsFailed = 0;

      if (eligibleUsers.length > 0) {
        // Prepare notification data
        const incidentNotificationData: IncidentNotificationData = {
          incidentId: incident.id,
          incidentType: incident.type,
          severity: incident.severity,
          distance: '', // Will be set per user
          latitude: incident.location.latitude.toString(),
          longitude: incident.location.longitude.toString(),
          timestamp: incident.timestamp.toDate().toISOString(),
          address: incident.location.address || 'Unknown location'
        };

        // Prepare recipients for batch sending
        const recipients = eligibleUsers.map(({ user, distanceText }) => ({
          userId: user.userId,
          fcmToken: user.fcmToken!,
          distanceText
        }));

        // Send notifications in batches
        const batchResult = await FCMNotificationService.sendBatchIncidentNotifications(
          recipients,
          incidentNotificationData
        );

        notificationsSent = batchResult.totalSent;
        notificationsFailed = batchResult.totalFailed;

        console.log(`📱 Notification results: ${notificationsSent} sent, ${notificationsFailed} failed`);
      }

      // Step 4: Store analytics
      const processingTimeMs = Date.now() - startTime;
      const result: NotificationProcessingResult = {
        incidentId: incident.id,
        totalEligibleUsers,
        notificationsSent,
        notificationsFailed,
        processingTimeMs,
        quietHoursFiltered,
        priorityFiltered,
        radiusFiltered,
        consentFiltered,
        tokenMissingFiltered
      };

      await this.storeProcessingAnalytics(incident, result);

      console.log(`📊 Processing completed in ${processingTimeMs}ms`);
      return result;

    } catch (error) {
      console.error('❌ Error processing incident notifications:', error);

      // Store error analytics
      const processingTimeMs = Date.now() - startTime;
      const errorResult: NotificationProcessingResult = {
        incidentId: incident.id,
        totalEligibleUsers: 0,
        notificationsSent: 0,
        notificationsFailed: 0,
        processingTimeMs,
        quietHoursFiltered,
        priorityFiltered,
        radiusFiltered,
        consentFiltered,
        tokenMissingFiltered
      };

      await this.storeProcessingAnalytics(incident, errorResult, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Enhanced quiet hours check with timezone support and emergency overrides
   */
  private static isInQuietHours(user: UserPreferences): boolean {
    if (!user.quietHours?.isEnabled) {
      return false;
    }

    const { startTime, endTime, timezone } = user.quietHours;

    try {
      // Get current time in user's timezone
      const userTime = moment().tz(timezone || 'UTC');

      // Parse quiet hours
      const [startHour, startMin] = startTime.split(':').map(Number);
      const [endHour, endMin] = endTime.split(':').map(Number);

      // Convert to minutes for easier comparison
      const currentMinutes = userTime.hour() * 60 + userTime.minute();
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;

      let inQuietHours: boolean;

      // Handle quiet hours that span midnight
      if (startMinutes > endMinutes) {
        inQuietHours = currentMinutes >= startMinutes || currentMinutes <= endMinutes;
      } else {
        inQuietHours = currentMinutes >= startMinutes && currentMinutes <= endMinutes;
      }

      if (inQuietHours) {
        console.log(`🔇 User ${user.userId} in quiet hours (${startTime}-${endTime} ${timezone})`);
      }

      return inQuietHours;

    } catch (error) {
      console.error(`Error checking quiet hours for user ${user.userId}:`, error);
      return false; // Default to not in quiet hours if there's an error
    }
  }



  /**
   * Store comprehensive processing analytics
   */
  private static async storeProcessingAnalytics(
    incident: IncidentData,
    result: NotificationProcessingResult,
    error?: string
  ): Promise<void> {
    try {
      await db.collection('notification_processing_analytics').doc(incident.id).set({
        ...result,
        incidentType: incident.type,
        incidentSeverity: incident.severity,
        incidentLocation: incident.location,
        incidentTimestamp: incident.timestamp,
        processedAt: admin.firestore.FieldValue.serverTimestamp(),
        error: error || null,
        successRate: result.totalEligibleUsers > 0
          ? (result.notificationsSent / result.totalEligibleUsers) * 100
          : 0
      });

      console.log(`📊 Processing analytics stored for incident ${incident.id}`);
    } catch (analyticsError) {
      console.error('Failed to store processing analytics:', analyticsError);
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * Get notification processing statistics for monitoring
   */
  static async getProcessingStats(timeRangeHours: number = 24): Promise<{
    totalIncidents: number;
    totalNotificationsSent: number;
    averageProcessingTime: number;
    successRate: number;
    filteringStats: {
      quietHours: number;
      priority: number;
      radius: number;
      consent: number;
      tokenMissing: number;
    };
  }> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - timeRangeHours);

      const statsSnapshot = await db.collection('notification_processing_analytics')
        .where('processedAt', '>=', admin.firestore.Timestamp.fromDate(cutoffTime))
        .get();

      if (statsSnapshot.empty) {
        return {
          totalIncidents: 0,
          totalNotificationsSent: 0,
          averageProcessingTime: 0,
          successRate: 0,
          filteringStats: {
            quietHours: 0,
            priority: 0,
            radius: 0,
            consent: 0,
            tokenMissing: 0
          }
        };
      }

      const stats = statsSnapshot.docs.map(doc => doc.data() as NotificationProcessingResult);

      const totalIncidents = stats.length;
      const totalNotificationsSent = stats.reduce((sum, s) => sum + s.notificationsSent, 0);
      const totalEligible = stats.reduce((sum, s) => sum + s.totalEligibleUsers, 0);
      const averageProcessingTime = stats.reduce((sum, s) => sum + s.processingTimeMs, 0) / totalIncidents;
      const successRate = totalEligible > 0 ? (totalNotificationsSent / totalEligible) * 100 : 0;

      const filteringStats = {
        quietHours: stats.reduce((sum, s) => sum + s.quietHoursFiltered, 0),
        priority: stats.reduce((sum, s) => sum + s.priorityFiltered, 0),
        radius: stats.reduce((sum, s) => sum + s.radiusFiltered, 0),
        consent: stats.reduce((sum, s) => sum + s.consentFiltered, 0),
        tokenMissing: stats.reduce((sum, s) => sum + s.tokenMissingFiltered, 0)
      };

      return {
        totalIncidents,
        totalNotificationsSent,
        averageProcessingTime,
        successRate,
        filteringStats
      };

    } catch (error) {
      console.error('Error getting processing stats:', error);
      throw error;
    }
  }
}
