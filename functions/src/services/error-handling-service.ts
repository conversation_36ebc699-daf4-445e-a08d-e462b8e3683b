import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';

const db = admin.firestore();

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  EXTERNAL_SERVICE = 'external_service',
  DATABASE = 'database',
  NETWORK = 'network',
  SYSTEM = 'system',
  USER_INPUT = 'user_input',
  BUSINESS_LOGIC = 'business_logic'
}

export interface ErrorContext {
  functionName: string;
  userId?: string;
  incidentId?: string;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorLog {
  id: string;
  timestamp: admin.firestore.Timestamp;
  severity: ErrorSeverity;
  category: ErrorCategory;
  message: string;
  stack?: string;
  context: ErrorContext;
  resolved: boolean;
  resolvedAt?: admin.firestore.Timestamp;
  resolvedBy?: string;
  occurrenceCount: number;
  lastOccurrence: admin.firestore.Timestamp;
}

/**
 * Comprehensive error handling and logging service
 */
export class ErrorHandlingService {

  /**
   * Log error with context and severity
   */
  static async logError(
    error: Error | string | unknown,
    severity: ErrorSeverity,
    category: ErrorCategory,
    context: ErrorContext
  ): Promise<string> {
    try {
      const errorMessage = typeof error === 'string'
        ? error
        : error instanceof Error
          ? error.message
          : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      // Generate error ID based on message and context for deduplication
      const errorId = this.generateErrorId(errorMessage, context.functionName);

      // Check if this error already exists
      const existingErrorDoc = await db.collection('error_logs').doc(errorId).get();

      if (existingErrorDoc.exists) {
        // Update existing error with new occurrence
        await db.collection('error_logs').doc(errorId).update({
          occurrenceCount: admin.firestore.FieldValue.increment(1),
          lastOccurrence: admin.firestore.FieldValue.serverTimestamp(),
          context: context // Update with latest context
        });

        console.log(`🔄 Updated existing error log: ${errorId}`);
      } else {
        // Create new error log
        const errorLog: Omit<ErrorLog, 'id'> = {
          timestamp: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
          severity,
          category,
          message: errorMessage,
          stack: errorStack,
          context,
          resolved: false,
          occurrenceCount: 1,
          lastOccurrence: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp
        };

        await db.collection('error_logs').doc(errorId).set(errorLog);
        console.log(`📝 Created new error log: ${errorId}`);
      }

      // Send alerts for critical errors
      if (severity === ErrorSeverity.CRITICAL) {
        await this.sendCriticalErrorAlert(errorMessage, context);
      }

      // Log to console with structured format
      this.logToConsole(errorMessage, severity, category, context, errorStack);

      return errorId;

    } catch (loggingError) {
      console.error('❌ Failed to log error:', loggingError);
      // Fallback to console logging
      console.error('Original error:', error);
      console.error('Context:', context);
      return 'logging-failed';
    }
  }

  /**
   * Log structured console output
   */
  private static logToConsole(
    message: string,
    severity: ErrorSeverity,
    category: ErrorCategory,
    context: ErrorContext,
    stack?: string
  ): void {
    const logData = {
      severity,
      category,
      message,
      context,
      timestamp: new Date().toISOString()
    };

    switch (severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', logData);
        if (stack) console.error('Stack trace:', stack);
        break;
      case ErrorSeverity.HIGH:
        console.error('❌ HIGH SEVERITY ERROR:', logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ MEDIUM SEVERITY ERROR:', logData);
        break;
      case ErrorSeverity.LOW:
        console.log('ℹ️ LOW SEVERITY ERROR:', logData);
        break;
    }
  }

  /**
   * Generate consistent error ID for deduplication
   */
  private static generateErrorId(message: string, functionName: string): string {
    // Create a hash-like ID from message and function name
    const combined = `${functionName}:${message}`;
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `error_${Math.abs(hash).toString(36)}`;
  }

  /**
   * Send critical error alerts (could integrate with external services)
   */
  private static async sendCriticalErrorAlert(
    message: string,
    context: ErrorContext
  ): Promise<void> {
    try {
      // Store critical error for immediate attention
      await db.collection('critical_alerts').add({
        type: 'critical_error',
        message,
        context,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        acknowledged: false
      });

      console.log('🚨 Critical error alert sent');

      // Here you could integrate with external alerting services like:
      // - Slack webhooks
      // - Email notifications
      // - PagerDuty
      // - Discord webhooks

    } catch (alertError) {
      console.error('Failed to send critical error alert:', alertError);
    }
  }

  /**
   * Wrap function execution with error handling
   */
  static async executeWithErrorHandling<T>(
    functionName: string,
    operation: () => Promise<T>,
    context: Partial<ErrorContext> = {}
  ): Promise<T> {
    const fullContext: ErrorContext = {
      functionName,
      ...context
    };

    try {
      const startTime = Date.now();
      const result = await operation();
      const executionTime = Date.now() - startTime;

      // Log successful execution for monitoring
      console.log(`✅ ${functionName} completed successfully in ${executionTime}ms`);

      return result;

    } catch (error) {
      // Determine error severity and category
      const { severity, category } = this.categorizeError(error);

      await this.logError(error, severity, category, fullContext);

      // Re-throw the error for the caller to handle
      throw error;
    }
  }

  /**
   * Categorize error based on type and message
   */
  private static categorizeError(error: unknown): {
    severity: ErrorSeverity;
    category: ErrorCategory;
  } {
    const message = (error instanceof Error ? error.message : String(error)).toLowerCase();
    const code = (error as any)?.code?.toLowerCase() || '';

    // Critical system errors
    if (message.includes('out of memory') || message.includes('timeout')) {
      return { severity: ErrorSeverity.CRITICAL, category: ErrorCategory.SYSTEM };
    }

    // Database errors
    if (code.includes('firestore') || message.includes('database') || code.includes('permission-denied')) {
      const severity = code.includes('permission-denied')
        ? ErrorSeverity.HIGH
        : ErrorSeverity.MEDIUM;
      return { severity, category: ErrorCategory.DATABASE };
    }

    // Authentication/Authorization errors
    if (code.includes('auth') || code.includes('unauthenticated') || code.includes('unauthorized')) {
      return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.AUTHENTICATION };
    }

    // FCM/External service errors
    if (message.includes('fcm') || message.includes('messaging') || code.includes('invalid-registration-token')) {
      return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.EXTERNAL_SERVICE };
    }

    // Network errors
    if (message.includes('network') || message.includes('connection') || code.includes('unavailable')) {
      return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.NETWORK };
    }

    // Validation errors
    if (message.includes('invalid') || message.includes('validation') || code.includes('invalid-argument')) {
      return { severity: ErrorSeverity.LOW, category: ErrorCategory.VALIDATION };
    }

    // Default categorization
    return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.SYSTEM };
  }

  /**
   * Get error statistics for monitoring dashboard
   */
  static async getErrorStatistics(timeRangeHours: number = 24): Promise<{
    totalErrors: number;
    errorsBySeverity: Record<ErrorSeverity, number>;
    errorsByCategory: Record<ErrorCategory, number>;
    topErrors: Array<{ message: string; count: number; severity: ErrorSeverity }>;
    criticalAlerts: number;
    resolvedErrors: number;
  }> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - timeRangeHours);

      const errorsSnapshot = await db.collection('error_logs')
        .where('lastOccurrence', '>=', admin.firestore.Timestamp.fromDate(cutoffTime))
        .get();

      const errors = errorsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ErrorLog));

      // Calculate statistics
      const totalErrors = errors.reduce((sum, error) => sum + error.occurrenceCount, 0);

      const errorsBySeverity = errors.reduce((acc, error) => {
        acc[error.severity] = (acc[error.severity] || 0) + error.occurrenceCount;
        return acc;
      }, {} as Record<ErrorSeverity, number>);

      const errorsByCategory = errors.reduce((acc, error) => {
        acc[error.category] = (acc[error.category] || 0) + error.occurrenceCount;
        return acc;
      }, {} as Record<ErrorCategory, number>);

      const topErrors = errors
        .sort((a, b) => b.occurrenceCount - a.occurrenceCount)
        .slice(0, 10)
        .map(error => ({
          message: error.message,
          count: error.occurrenceCount,
          severity: error.severity
        }));

      const criticalAlerts = await db.collection('critical_alerts')
        .where('timestamp', '>=', admin.firestore.Timestamp.fromDate(cutoffTime))
        .where('acknowledged', '==', false)
        .get()
        .then(snapshot => snapshot.size);

      const resolvedErrors = errors.filter(error => error.resolved).length;

      return {
        totalErrors,
        errorsBySeverity,
        errorsByCategory,
        topErrors,
        criticalAlerts,
        resolvedErrors
      };

    } catch (error) {
      console.error('Error getting error statistics:', error);
      throw error;
    }
  }

  /**
   * Mark error as resolved
   */
  static async resolveError(errorId: string, resolvedBy: string): Promise<void> {
    try {
      await db.collection('error_logs').doc(errorId).update({
        resolved: true,
        resolvedAt: admin.firestore.FieldValue.serverTimestamp(),
        resolvedBy
      });

      console.log(`✅ Error ${errorId} marked as resolved by ${resolvedBy}`);
    } catch (error) {
      console.error(`Failed to resolve error ${errorId}:`, error);
      throw error;
    }
  }

  /**
   * Create HTTP error response with proper logging
   */
  static createHttpError(
    message: string,
    statusCode: number,
    context: ErrorContext
  ): functions.https.HttpsError {
    // Determine severity based on status code
    let severity: ErrorSeverity;
    if (statusCode >= 500) {
      severity = ErrorSeverity.HIGH;
    } else if (statusCode >= 400) {
      severity = ErrorSeverity.MEDIUM;
    } else {
      severity = ErrorSeverity.LOW;
    }

    // Log the error
    this.logError(message, severity, ErrorCategory.USER_INPUT, context);

    // Map status codes to Firebase error codes
    const errorCodeMap: Record<number, string> = {
      400: 'invalid-argument',
      401: 'unauthenticated',
      403: 'permission-denied',
      404: 'not-found',
      409: 'already-exists',
      429: 'resource-exhausted',
      500: 'internal',
      501: 'unimplemented',
      503: 'unavailable'
    };

    const errorCode = errorCodeMap[statusCode] || 'unknown';

    return new functions.https.HttpsError(errorCode as any, message);
  }
}
