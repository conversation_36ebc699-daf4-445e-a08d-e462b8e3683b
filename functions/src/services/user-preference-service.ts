import * as admin from 'firebase-admin';
import moment from 'moment-timezone';
import { getDistance } from 'geolib';

const db = admin.firestore();

export interface UserPreferences {
  userId: string;
  isLocationNotificationsEnabled: boolean;
  isLocationTrackingConsented: boolean;
  locationConsentGrantedAt: admin.firestore.Timestamp | null;
  locationAccessDurationHours: number;
  notificationRadiusKm: number;
  enabledIncidentTypes: string[];
  quietHours?: {
    isEnabled: boolean;
    startTime: string; // HH:mm format
    endTime: string;   // HH:mm format
    timezone: string;
  };
  priorityLevel: 'low' | 'medium' | 'high' | 'critical';
  fcmToken?: string;
  lastKnownLocation?: {
    latitude: number;
    longitude: number;
    timestamp: admin.firestore.Timestamp;
  };
}

export interface IncidentLocation {
  latitude: number;
  longitude: number;
  address?: string;
}

/**
 * Service for managing and validating user notification preferences
 */
export class UserPreferenceService {

  /**
   * Get all users eligible for notifications
   */
  static async getEligibleUsers(): Promise<UserPreferences[]> {
    try {
      const usersSnapshot = await db.collection('notification_preferences')
        .where('isLocationNotificationsEnabled', '==', true)
        .where('isLocationTrackingConsented', '==', true)
        .get();

      const users: UserPreferences[] = [];

      for (const doc of usersSnapshot.docs) {
        const userData = doc.data() as UserPreferences;
        userData.userId = doc.id;

        // Only include users with valid consent
        if (this.isLocationConsentValid(userData)) {
          users.push(userData);
        }
      }

      return users;
    } catch (error) {
      console.error('Error getting eligible users:', error);
      throw error;
    }
  }

  /**
   * Validate if user's location consent is still active
   */
  static isLocationConsentValid(userPrefs: UserPreferences): boolean {
    if (!userPrefs.locationConsentGrantedAt) {
      return false;
    }

    const consentTime = userPrefs.locationConsentGrantedAt.toDate();
    const expirationTime = moment(consentTime)
      .add(userPrefs.locationAccessDurationHours, 'hours')
      .toDate();

    const isValid = new Date() < expirationTime;

    if (!isValid) {
      console.log(`⏰ Location consent expired for user ${userPrefs.userId}`);
      // Optionally auto-revoke expired consent
      this.revokeExpiredConsent(userPrefs.userId);
    }

    return isValid;
  }

  /**
   * Check if incident type is enabled for user
   */
  static isIncidentTypeEnabled(userPrefs: UserPreferences, incidentType: string): boolean {
    // If no specific types are enabled, assume all types are enabled (default behavior)
    if (!userPrefs.enabledIncidentTypes || userPrefs.enabledIncidentTypes.length === 0) {
      return true;
    }

    return userPrefs.enabledIncidentTypes.includes(incidentType);
  }

  /**
   * Check if current time is within user's quiet hours
   */
  static isInQuietHours(userPrefs: UserPreferences): boolean {
    if (!userPrefs.quietHours?.isEnabled) {
      return false;
    }

    const { startTime, endTime, timezone } = userPrefs.quietHours;
    const now = moment().tz(timezone || 'UTC');
    const currentTime = now.format('HH:mm');

    // Handle quiet hours that span midnight (e.g., 22:00 to 06:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  /**
   * Check if incident meets user's priority threshold
   */
  static meetsPriorityThreshold(
    userPrefs: UserPreferences,
    incidentSeverity: 'low' | 'medium' | 'high' | 'critical'
  ): boolean {
    const priorityLevels = { low: 1, medium: 2, high: 3, critical: 4 };

    const userThreshold = priorityLevels[userPrefs.priorityLevel];
    const incidentPriority = priorityLevels[incidentSeverity];

    return incidentPriority >= userThreshold;
  }

  /**
   * Check if incident is within user's notification radius
   */
  static async isWithinNotificationRadius(
    userPrefs: UserPreferences,
    incidentLocation: IncidentLocation
  ): Promise<{ isWithin: boolean; distance: number; distanceText: string }> {
    try {
      const userLocation = await this.getUserLocation(userPrefs.userId);

      if (!userLocation) {
        console.log(`📍 No location found for user ${userPrefs.userId}`);
        return { isWithin: false, distance: 0, distanceText: 'unknown' };
      }

      // Calculate distance using geolib (returns meters)
      const distanceMeters = getDistance(
        { latitude: userLocation.latitude, longitude: userLocation.longitude },
        { latitude: incidentLocation.latitude, longitude: incidentLocation.longitude }
      );

      const distanceKm = distanceMeters / 1000;
      const isWithin = distanceKm <= userPrefs.notificationRadiusKm;

      // Format distance text
      let distanceText: string;
      if (distanceKm < 1) {
        distanceText = `${Math.round(distanceMeters)}m away`;
      } else {
        distanceText = `${distanceKm.toFixed(1)}km away`;
      }

      console.log(`📍 User ${userPrefs.userId}: ${distanceText} (radius: ${userPrefs.notificationRadiusKm}km) - ${isWithin ? 'WITHIN' : 'OUTSIDE'}`);

      return { isWithin, distance: distanceMeters, distanceText };

    } catch (error) {
      console.error(`Error checking radius for user ${userPrefs.userId}:`, error);
      return { isWithin: false, distance: 0, distanceText: 'error' };
    }
  }

  /**
   * Get user's current location from various sources
   */
  static async getUserLocation(userId: string): Promise<{ latitude: number, longitude: number } | null> {
    try {
      // Priority 1: Real-time location tracking (most recent)
      const locationSnapshot = await db.collection('user_locations')
        .doc(userId)
        .get();

      if (locationSnapshot.exists) {
        const locationData = locationSnapshot.data();
        if (locationData?.latitude && locationData?.longitude) {
          // Check if location is recent (within last 24 hours)
          const locationTime = locationData.timestamp?.toDate();
          if (locationTime && (Date.now() - locationTime.getTime()) < 24 * 60 * 60 * 1000) {
            return {
              latitude: locationData.latitude,
              longitude: locationData.longitude
            };
          }
        }
      }

      // Priority 2: Last known location in preferences
      const prefsSnapshot = await db.collection('notification_preferences')
        .doc(userId)
        .get();

      if (prefsSnapshot.exists) {
        const prefsData = prefsSnapshot.data();
        if (prefsData?.lastKnownLocation) {
          return {
            latitude: prefsData.lastKnownLocation.latitude,
            longitude: prefsData.lastKnownLocation.longitude
          };
        }
      }

      return null;
    } catch (error) {
      console.error(`Error getting location for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Update user's FCM token
   */
  static async updateFCMToken(userId: string, fcmToken: string): Promise<void> {
    try {
      await db.collection('notification_preferences')
        .doc(userId)
        .update({
          fcmToken,
          fcmTokenUpdatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

      console.log(`✅ FCM token updated for user ${userId}`);
    } catch (error) {
      console.error(`Error updating FCM token for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Revoke expired location consent
   */
  static async revokeExpiredConsent(userId: string): Promise<void> {
    try {
      await db.collection('notification_preferences')
        .doc(userId)
        .update({
          isLocationTrackingConsented: false,
          locationConsentGrantedAt: null,
          consentRevokedReason: 'expired',
          consentRevokedAt: admin.firestore.FieldValue.serverTimestamp()
        });

      console.log(`🔒 Expired consent revoked for user ${userId}`);
    } catch (error) {
      console.error(`Error revoking expired consent for user ${userId}:`, error);
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * Get user preference summary for debugging
   */
  static async getUserPreferenceSummary(userId: string): Promise<string> {
    try {
      const userDoc = await db.collection('notification_preferences')
        .doc(userId)
        .get();

      if (!userDoc.exists) {
        return `User ${userId}: No preferences found`;
      }

      const prefs = userDoc.data() as UserPreferences;
      prefs.userId = userId;

      const consentValid = this.isLocationConsentValid(prefs);
      const inQuietHours = this.isInQuietHours(prefs);

      return `User ${userId}: 
        - Notifications: ${prefs.isLocationNotificationsEnabled ? 'ON' : 'OFF'}
        - Consent: ${consentValid ? 'VALID' : 'INVALID/EXPIRED'}
        - Radius: ${prefs.notificationRadiusKm}km
        - Types: ${prefs.enabledIncidentTypes?.length || 0} enabled
        - Priority: ${prefs.priorityLevel}
        - Quiet Hours: ${inQuietHours ? 'ACTIVE' : 'INACTIVE'}
        - FCM Token: ${prefs.fcmToken ? 'PRESENT' : 'MISSING'}`;

    } catch (error) {
      return `User ${userId}: Error getting summary - ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Validate all user eligibility criteria at once
   */
  static async validateUserEligibility(
    userPrefs: UserPreferences,
    incidentType: string,
    incidentSeverity: 'low' | 'medium' | 'high' | 'critical',
    incidentLocation: IncidentLocation
  ): Promise<{
    isEligible: boolean;
    reasons: string[];
    distance?: number;
    distanceText?: string;
  }> {
    const reasons: string[] = [];
    let distance: number | undefined;
    let distanceText: string | undefined;

    // Check location consent
    if (!this.isLocationConsentValid(userPrefs)) {
      reasons.push('Location consent expired or invalid');
    }

    // Check incident type
    if (!this.isIncidentTypeEnabled(userPrefs, incidentType)) {
      reasons.push(`Incident type '${incidentType}' not enabled`);
    }

    // Check quiet hours
    if (this.isInQuietHours(userPrefs)) {
      reasons.push('User is in quiet hours');
    }

    // Check priority threshold
    if (!this.meetsPriorityThreshold(userPrefs, incidentSeverity)) {
      reasons.push(`Incident severity '${incidentSeverity}' below user threshold '${userPrefs.priorityLevel}'`);
    }

    // Check FCM token
    if (!userPrefs.fcmToken) {
      reasons.push('No FCM token available');
    }

    // Check distance (only if other checks pass to avoid unnecessary location queries)
    if (reasons.length === 0) {
      const radiusCheck = await this.isWithinNotificationRadius(userPrefs, incidentLocation);
      distance = radiusCheck.distance;
      distanceText = radiusCheck.distanceText;

      if (!radiusCheck.isWithin) {
        reasons.push(`Outside notification radius (${radiusCheck.distanceText})`);
      }
    }

    const isEligible = reasons.length === 0;

    return {
      isEligible,
      reasons,
      distance,
      distanceText
    };
  }
}
