import * as admin from 'firebase-admin';
import { geohashForLocation, geohashQueryBounds, distanceBetween } from 'geofire-common';
import { getDistance, isPointWithinRadius } from 'geolib';

const db = admin.firestore();

export interface GeoPoint {
  latitude: number;
  longitude: number;
}

export interface UserLocationData {
  userId: string;
  latitude: number;
  longitude: number;
  geohash: string;
  timestamp: admin.firestore.Timestamp;
  accuracy?: number; // GPS accuracy in meters
  source: 'gps' | 'network' | 'passive' | 'manual';
}

export interface GeospatialQuery {
  center: GeoPoint;
  radiusKm: number;
  maxResults?: number;
}

export interface GeospatialResult {
  userId: string;
  location: GeoPoint;
  distance: number; // in meters
  distanceKm: number;
  distanceText: string;
  geohash: string;
}

/**
 * Advanced geospatial service using geofire-common for efficient location queries
 */
export class GeospatialService {

  /**
   * Find all users within a specific radius of an incident using efficient geo-queries
   */
  static async findUsersWithinRadius(query: GeospatialQuery): Promise<GeospatialResult[]> {
    try {
      const { center, radiusKm, maxResults = 1000 } = query;
      const radiusMeters = radiusKm * 1000;

      console.log(`🔍 Searching for users within ${radiusKm}km of [${center.latitude}, ${center.longitude}]`);

      // Generate geohash query bounds for efficient Firestore querying
      const bounds = geohashQueryBounds([center.latitude, center.longitude], radiusMeters);
      
      console.log(`📍 Generated ${bounds.length} geohash bounds for query`);

      // Execute parallel queries for all bounds
      const queryPromises = bounds.map(bound => {
        return db.collection('user_locations')
          .orderBy('geohash')
          .startAt(bound[0])
          .endAt(bound[1])
          .limit(maxResults)
          .get();
      });

      const snapshots = await Promise.all(queryPromises);
      
      // Combine results from all queries
      const allDocs: admin.firestore.QueryDocumentSnapshot[] = [];
      snapshots.forEach(snapshot => {
        snapshot.docs.forEach(doc => allDocs.push(doc));
      });

      console.log(`📊 Found ${allDocs.length} potential users in geohash bounds`);

      // Filter by exact distance and remove duplicates
      const usersWithinRadius: GeospatialResult[] = [];
      const seenUsers = new Set<string>();

      for (const doc of allDocs) {
        const data = doc.data() as UserLocationData;
        
        // Skip duplicates (can happen when geohash bounds overlap)
        if (seenUsers.has(data.userId)) {
          continue;
        }
        seenUsers.add(data.userId);

        // Calculate exact distance using geofire-common
        const distanceKm = distanceBetween(
          [center.latitude, center.longitude],
          [data.latitude, data.longitude]
        );

        // Only include users within the exact radius
        if (distanceKm <= radiusKm) {
          const distanceMeters = distanceKm * 1000;
          
          usersWithinRadius.push({
            userId: data.userId,
            location: {
              latitude: data.latitude,
              longitude: data.longitude
            },
            distance: distanceMeters,
            distanceKm,
            distanceText: this.formatDistance(distanceMeters),
            geohash: data.geohash
          });
        }
      }

      // Sort by distance (closest first)
      usersWithinRadius.sort((a, b) => a.distance - b.distance);

      console.log(`✅ Found ${usersWithinRadius.length} users within ${radiusKm}km radius`);

      return usersWithinRadius.slice(0, maxResults);

    } catch (error) {
      console.error('Error finding users within radius:', error);
      throw error;
    }
  }

  /**
   * Update user location with geohash for efficient querying
   */
  static async updateUserLocation(
    userId: string, 
    location: GeoPoint, 
    source: 'gps' | 'network' | 'passive' | 'manual' = 'gps',
    accuracy?: number
  ): Promise<void> {
    try {
      // Generate geohash for the location
      const geohash = geohashForLocation([location.latitude, location.longitude]);

      const locationData: UserLocationData = {
        userId,
        latitude: location.latitude,
        longitude: location.longitude,
        geohash,
        timestamp: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
        accuracy,
        source
      };

      // Update user location with geohash
      await db.collection('user_locations').doc(userId).set(locationData);

      // Also update last known location in preferences for fallback
      await db.collection('notification_preferences').doc(userId).update({
        lastKnownLocation: {
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        },
        lastLocationUpdate: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`📍 Location updated for user ${userId}: [${location.latitude}, ${location.longitude}] (${geohash})`);

    } catch (error) {
      console.error(`Error updating location for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get users within multiple radius zones (for different notification priorities)
   */
  static async findUsersInZones(
    center: GeoPoint,
    zones: { name: string; radiusKm: number; priority: string }[]
  ): Promise<{ [zoneName: string]: GeospatialResult[] }> {
    try {
      const results: { [zoneName: string]: GeospatialResult[] } = {};

      // Sort zones by radius (largest first) for efficient querying
      const sortedZones = zones.sort((a, b) => b.radiusKm - a.radiusKm);

      // Query the largest radius first
      const largestZone = sortedZones[0];
      const allUsersInLargestRadius = await this.findUsersWithinRadius({
        center,
        radiusKm: largestZone.radiusKm
      });

      // Filter users into appropriate zones
      for (const zone of sortedZones) {
        results[zone.name] = allUsersInLargestRadius.filter(
          user => user.distanceKm <= zone.radiusKm
        );

        console.log(`📍 Zone '${zone.name}' (${zone.radiusKm}km): ${results[zone.name].length} users`);
      }

      return results;

    } catch (error) {
      console.error('Error finding users in zones:', error);
      throw error;
    }
  }

  /**
   * Calculate distance between two points using multiple methods for accuracy
   */
  static calculateDistance(point1: GeoPoint, point2: GeoPoint): {
    geofire: number;    // geofire-common result (km)
    geolib: number;     // geolib result (meters)
    haversine: number;  // custom haversine (meters)
  } {
    // Method 1: geofire-common (returns km)
    const geofireKm = distanceBetween(
      [point1.latitude, point1.longitude],
      [point2.latitude, point2.longitude]
    );

    // Method 2: geolib (returns meters)
    const geolibMeters = getDistance(
      { latitude: point1.latitude, longitude: point1.longitude },
      { latitude: point2.latitude, longitude: point2.longitude }
    );

    // Method 3: Custom Haversine formula (returns meters)
    const haversineMeters = this.haversineDistance(point1, point2);

    return {
      geofire: geofireKm,
      geolib: geolibMeters,
      haversine: haversineMeters
    };
  }

  /**
   * Custom Haversine distance calculation for verification
   */
  private static haversineDistance(point1: GeoPoint, point2: GeoPoint): number {
    const R = 6371000; // Earth's radius in meters
    const lat1Rad = point1.latitude * Math.PI / 180;
    const lat2Rad = point2.latitude * Math.PI / 180;
    const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180;
    const deltaLonRad = (point2.longitude - point1.longitude) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in meters
  }

  /**
   * Format distance for user-friendly display
   */
  static formatDistance(distanceMeters: number): string {
    if (distanceMeters < 1000) {
      return `${Math.round(distanceMeters)}m away`;
    } else {
      const km = distanceMeters / 1000;
      if (km < 10) {
        return `${km.toFixed(1)}km away`;
      } else {
        return `${Math.round(km)}km away`;
      }
    }
  }

  /**
   * Validate if a point is within a circular area using geolib
   */
  static isPointInRadius(center: GeoPoint, point: GeoPoint, radiusMeters: number): boolean {
    return isPointWithinRadius(
      { latitude: point.latitude, longitude: point.longitude },
      { latitude: center.latitude, longitude: center.longitude },
      radiusMeters
    );
  }

  /**
   * Get geohash for a location (useful for debugging)
   */
  static getGeohash(location: GeoPoint, precision: number = 10): string {
    return geohashForLocation([location.latitude, location.longitude], precision);
  }

  /**
   * Batch update multiple user locations efficiently
   */
  static async batchUpdateUserLocations(
    updates: Array<{
      userId: string;
      location: GeoPoint;
      source?: 'gps' | 'network' | 'passive' | 'manual';
      accuracy?: number;
    }>
  ): Promise<void> {
    try {
      const batch = db.batch();
      const timestamp = admin.firestore.FieldValue.serverTimestamp();

      for (const update of updates) {
        const geohash = geohashForLocation([update.location.latitude, update.location.longitude]);
        
        const locationData: UserLocationData = {
          userId: update.userId,
          latitude: update.location.latitude,
          longitude: update.location.longitude,
          geohash,
          timestamp: timestamp as admin.firestore.Timestamp,
          accuracy: update.accuracy,
          source: update.source || 'gps'
        };

        // Update user location
        const locationRef = db.collection('user_locations').doc(update.userId);
        batch.set(locationRef, locationData);

        // Update preferences fallback
        const prefsRef = db.collection('notification_preferences').doc(update.userId);
        batch.update(prefsRef, {
          lastKnownLocation: {
            latitude: update.location.latitude,
            longitude: update.location.longitude,
            timestamp: timestamp
          },
          lastLocationUpdate: timestamp
        });
      }

      await batch.commit();
      console.log(`📍 Batch updated ${updates.length} user locations`);

    } catch (error) {
      console.error('Error batch updating user locations:', error);
      throw error;
    }
  }

  /**
   * Clean up old location data (for maintenance)
   */
  static async cleanupOldLocations(olderThanDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const oldLocationsSnapshot = await db.collection('user_locations')
        .where('timestamp', '<', admin.firestore.Timestamp.fromDate(cutoffDate))
        .limit(500) // Process in batches
        .get();

      if (oldLocationsSnapshot.empty) {
        console.log('🧹 No old locations to clean up');
        return 0;
      }

      const batch = db.batch();
      oldLocationsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      
      const deletedCount = oldLocationsSnapshot.docs.length;
      console.log(`🧹 Cleaned up ${deletedCount} old location records`);
      
      return deletedCount;

    } catch (error) {
      console.error('Error cleaning up old locations:', error);
      throw error;
    }
  }
}
