import * as admin from 'firebase-admin';
import { onRequest } from 'firebase-functions/v2/https';

const db = admin.firestore();

export interface NotificationAnalytics {
  incidentId: string;
  userId: string;
  notificationId: string;
  timestamp: admin.firestore.Timestamp;
  event: 'sent' | 'delivered' | 'opened' | 'clicked' | 'dismissed' | 'failed';
  platform: 'android' | 'ios' | 'web';
  distance: string;
  incidentType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  responseTimeMs?: number; // Time from incident creation to notification sent
  deliveryTimeMs?: number; // Time from sent to delivered
  engagementTimeMs?: number; // Time from delivered to opened
  metadata?: Record<string, any>;
}

export interface UserEngagementMetrics {
  userId: string;
  totalNotificationsReceived: number;
  totalNotificationsOpened: number;
  totalNotificationsClicked: number;
  totalNotificationsDismissed: number;
  openRate: number;
  clickRate: number;
  averageEngagementTimeMs: number;
  preferredIncidentTypes: string[];
  mostActiveTimeOfDay: string;
  lastEngagement: admin.firestore.Timestamp;
}

export interface SystemPerformanceMetrics {
  date: string;
  totalIncidents: number;
  totalNotificationsSent: number;
  totalNotificationsDelivered: number;
  totalNotificationsOpened: number;
  averageResponseTimeMs: number;
  averageDeliveryTimeMs: number;
  deliveryRate: number;
  openRate: number;
  errorRate: number;
  peakHour: string;
  topIncidentTypes: Array<{ type: string; count: number }>;
}

/**
 * Comprehensive analytics and tracking service for notifications
 */
export class AnalyticsService {

  /**
   * Track notification event
   */
  static async trackNotificationEvent(
    event: NotificationAnalytics['event'],
    data: Omit<NotificationAnalytics, 'event' | 'timestamp'>
  ): Promise<void> {
    try {
      const analyticsData: NotificationAnalytics = {
        ...data,
        event,
        timestamp: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp
      };

      // Store individual event
      await db.collection('notification_analytics').add(analyticsData);

      // Update aggregated metrics
      await this.updateAggregatedMetrics(analyticsData);

      console.log(`📊 Tracked notification event: ${event} for user ${data.userId}`);

    } catch (error) {
      console.error('Error tracking notification event:', error);
      // Don't throw to avoid breaking the main flow
    }
  }

  /**
   * Update aggregated metrics for performance
   */
  private static async updateAggregatedMetrics(analytics: NotificationAnalytics): Promise<void> {
    const batch = db.batch();
    const today = new Date().toISOString().split('T')[0];

    try {
      // Update daily system metrics
      const systemMetricsRef = db.collection('system_metrics').doc(today);
      batch.set(systemMetricsRef, {
        date: today,
        [`${analytics.event}Count`]: admin.firestore.FieldValue.increment(1),
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      // Update user engagement metrics
      const userMetricsRef = db.collection('user_engagement_metrics').doc(analytics.userId);
      const updateData: any = {
        userId: analytics.userId,
        lastEngagement: admin.firestore.FieldValue.serverTimestamp()
      };

      switch (analytics.event) {
        case 'sent':
          updateData.totalNotificationsReceived = admin.firestore.FieldValue.increment(1);
          break;
        case 'opened':
          updateData.totalNotificationsOpened = admin.firestore.FieldValue.increment(1);
          break;
        case 'clicked':
          updateData.totalNotificationsClicked = admin.firestore.FieldValue.increment(1);
          break;
        case 'dismissed':
          updateData.totalNotificationsDismissed = admin.firestore.FieldValue.increment(1);
          break;
      }

      batch.set(userMetricsRef, updateData, { merge: true });

      // Update incident type popularity
      const incidentTypeRef = db.collection('incident_type_metrics').doc(analytics.incidentType);
      batch.set(incidentTypeRef, {
        type: analytics.incidentType,
        [`${analytics.event}Count`]: admin.firestore.FieldValue.increment(1),
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      await batch.commit();

    } catch (error) {
      console.error('Error updating aggregated metrics:', error);
    }
  }

  /**
   * Calculate user engagement metrics
   */
  static async calculateUserEngagementMetrics(userId: string): Promise<UserEngagementMetrics> {
    try {
      // Get user's notification events
      const eventsSnapshot = await db.collection('notification_analytics')
        .where('userId', '==', userId)
        .orderBy('timestamp', 'desc')
        .limit(1000) // Last 1000 events
        .get();

      const events = eventsSnapshot.docs.map(doc => doc.data() as NotificationAnalytics);

      // Calculate metrics
      const received = events.filter(e => e.event === 'sent').length;
      const opened = events.filter(e => e.event === 'opened').length;
      const clicked = events.filter(e => e.event === 'clicked').length;
      const dismissed = events.filter(e => e.event === 'dismissed').length;

      const openRate = received > 0 ? (opened / received) * 100 : 0;
      const clickRate = received > 0 ? (clicked / received) * 100 : 0;

      // Calculate average engagement time
      const engagementTimes = events
        .filter(e => e.engagementTimeMs)
        .map(e => e.engagementTimeMs!);
      const averageEngagementTimeMs = engagementTimes.length > 0
        ? engagementTimes.reduce((sum, time) => sum + time, 0) / engagementTimes.length
        : 0;

      // Find preferred incident types
      const incidentTypeCounts = events.reduce((acc, event) => {
        acc[event.incidentType] = (acc[event.incidentType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const preferredIncidentTypes = Object.entries(incidentTypeCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([type]) => type);

      // Find most active time of day
      const hourCounts = events.reduce((acc, event) => {
        const hour = event.timestamp.toDate().getHours();
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);

      const mostActiveHour = Object.entries(hourCounts)
        .sort(([, a], [, b]) => b - a)[0]?.[0] || '12';
      const mostActiveTimeOfDay = `${mostActiveHour}:00`;

      const lastEngagement = events.length > 0
        ? events[0].timestamp
        : admin.firestore.Timestamp.now();

      return {
        userId,
        totalNotificationsReceived: received,
        totalNotificationsOpened: opened,
        totalNotificationsClicked: clicked,
        totalNotificationsDismissed: dismissed,
        openRate,
        clickRate,
        averageEngagementTimeMs,
        preferredIncidentTypes,
        mostActiveTimeOfDay,
        lastEngagement
      };

    } catch (error) {
      console.error(`Error calculating engagement metrics for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get system performance metrics for a date range
   */
  static async getSystemPerformanceMetrics(
    startDate: string,
    endDate: string
  ): Promise<SystemPerformanceMetrics[]> {
    try {
      const metricsSnapshot = await db.collection('system_metrics')
        .where('date', '>=', startDate)
        .where('date', '<=', endDate)
        .orderBy('date')
        .get();

      const metrics: SystemPerformanceMetrics[] = [];

      for (const doc of metricsSnapshot.docs) {
        const data = doc.data();

        // Calculate derived metrics
        const deliveryRate = data.sentCount > 0
          ? (data.deliveredCount || 0) / data.sentCount * 100
          : 0;

        const openRate = data.deliveredCount > 0
          ? (data.openedCount || 0) / data.deliveredCount * 100
          : 0;

        const errorRate = data.sentCount > 0
          ? (data.failedCount || 0) / data.sentCount * 100
          : 0;

        // Get top incident types for this date
        const incidentTypesSnapshot = await db.collection('incident_type_metrics')
          .orderBy('sentCount', 'desc')
          .limit(5)
          .get();

        const topIncidentTypes = incidentTypesSnapshot.docs.map(doc => ({
          type: doc.data().type,
          count: doc.data().sentCount || 0
        }));

        metrics.push({
          date: data.date,
          totalIncidents: data.incidentCount || 0,
          totalNotificationsSent: data.sentCount || 0,
          totalNotificationsDelivered: data.deliveredCount || 0,
          totalNotificationsOpened: data.openedCount || 0,
          averageResponseTimeMs: data.averageResponseTimeMs || 0,
          averageDeliveryTimeMs: data.averageDeliveryTimeMs || 0,
          deliveryRate,
          openRate,
          errorRate,
          peakHour: data.peakHour || '12:00',
          topIncidentTypes
        });
      }

      return metrics;

    } catch (error) {
      console.error('Error getting system performance metrics:', error);
      throw error;
    }
  }

  /**
   * Generate analytics dashboard data
   */
  static async generateDashboardData(timeRangeHours: number = 24): Promise<{
    overview: {
      totalNotificationsSent: number;
      deliveryRate: number;
      openRate: number;
      averageResponseTime: number;
    };
    userEngagement: {
      activeUsers: number;
      topEngagedUsers: Array<{ userId: string; openRate: number }>;
    };
    incidentTypes: Array<{ type: string; count: number; openRate: number }>;
    timeDistribution: Array<{ hour: number; count: number }>;
    geographicDistribution: Array<{ region: string; count: number }>;
  }> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - timeRangeHours);

      // Get recent analytics data
      const analyticsSnapshot = await db.collection('notification_analytics')
        .where('timestamp', '>=', admin.firestore.Timestamp.fromDate(cutoffTime))
        .get();

      const analytics = analyticsSnapshot.docs.map(doc => doc.data() as NotificationAnalytics);

      // Calculate overview metrics
      const totalSent = analytics.filter(a => a.event === 'sent').length;
      const totalDelivered = analytics.filter(a => a.event === 'delivered').length;
      const totalOpened = analytics.filter(a => a.event === 'opened').length;

      const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
      const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0;

      const responseTimes = analytics
        .filter(a => a.responseTimeMs)
        .map(a => a.responseTimeMs!);
      const averageResponseTime = responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

      // Calculate user engagement
      const userEvents = analytics.reduce((acc, event) => {
        if (!acc[event.userId]) acc[event.userId] = [];
        acc[event.userId].push(event);
        return acc;
      }, {} as Record<string, NotificationAnalytics[]>);

      const activeUsers = Object.keys(userEvents).length;

      const topEngagedUsers = Object.entries(userEvents)
        .map(([userId, events]) => {
          const sent = events.filter(e => e.event === 'sent').length;
          const opened = events.filter(e => e.event === 'opened').length;
          const openRate = sent > 0 ? (opened / sent) * 100 : 0;
          return { userId, openRate };
        })
        .sort((a, b) => b.openRate - a.openRate)
        .slice(0, 10);

      // Calculate incident type metrics
      const incidentTypeEvents = analytics.reduce((acc, event) => {
        if (!acc[event.incidentType]) {
          acc[event.incidentType] = { sent: 0, opened: 0 };
        }
        if (event.event === 'sent') acc[event.incidentType].sent++;
        if (event.event === 'opened') acc[event.incidentType].opened++;
        return acc;
      }, {} as Record<string, { sent: number; opened: number }>);

      const incidentTypes = Object.entries(incidentTypeEvents)
        .map(([type, counts]) => ({
          type,
          count: counts.sent,
          openRate: counts.sent > 0 ? (counts.opened / counts.sent) * 100 : 0
        }))
        .sort((a, b) => b.count - a.count);

      // Calculate time distribution
      const hourCounts = analytics.reduce((acc, event) => {
        const hour = event.timestamp.toDate().getHours();
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);

      const timeDistribution = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        count: hourCounts[hour] || 0
      }));

      // Geographic distribution (simplified - would need location data)
      const geographicDistribution = [
        { region: 'North', count: Math.floor(totalSent * 0.3) },
        { region: 'South', count: Math.floor(totalSent * 0.25) },
        { region: 'East', count: Math.floor(totalSent * 0.25) },
        { region: 'West', count: Math.floor(totalSent * 0.2) }
      ];

      return {
        overview: {
          totalNotificationsSent: totalSent,
          deliveryRate,
          openRate,
          averageResponseTime
        },
        userEngagement: {
          activeUsers,
          topEngagedUsers
        },
        incidentTypes,
        timeDistribution,
        geographicDistribution
      };

    } catch (error) {
      console.error('Error generating dashboard data:', error);
      throw error;
    }
  }

  /**
   * Cloud Function to handle notification tracking webhooks
   */
  static createTrackingWebhook() {
    return onRequest(async (req, res) => {
      try {
        if (req.method !== 'POST') {
          res.status(405).send('Method not allowed');
          return;
        }

        const { event, userId, incidentId, notificationId, platform, metadata } = req.body;

        if (!event || !userId || !incidentId || !notificationId) {
          res.status(400).send('Missing required fields');
          return;
        }

        // Track the event
        await this.trackNotificationEvent(event, {
          userId,
          incidentId,
          notificationId,
          platform: platform || 'unknown',
          distance: metadata?.distance || 'unknown',
          incidentType: metadata?.incidentType || 'unknown',
          severity: metadata?.severity || 'medium',
          responseTimeMs: metadata?.responseTimeMs,
          deliveryTimeMs: metadata?.deliveryTimeMs,
          engagementTimeMs: metadata?.engagementTimeMs,
          metadata
        });

        res.status(200).json({ success: true, message: 'Event tracked successfully' });

      } catch (error) {
        console.error('Error in tracking webhook:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    });
  }
}
