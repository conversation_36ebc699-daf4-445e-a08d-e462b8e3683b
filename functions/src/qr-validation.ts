import {onC<PERSON>, HttpsError} from "firebase-functions/v2/https";
import {onValueWritten} from "firebase-functions/v2/database";
import {getDatabase} from "firebase-admin/database";
import {getFirestore} from "firebase-admin/firestore";
import {getAuth} from "firebase-admin/auth";
import * as logger from "firebase-functions/logger";
import * as crypto from "crypto";
import {defineSecret} from "firebase-functions/params";

// Define secrets for production security
const qrValidationSecret = defineSecret("QR_VALIDATION_SECRET");
const encryptionKey = defineSecret("QR_ENCRYPTION_KEY");

// QR Validation Cloud Functions

/**
 * Creates a secure QR token directly for a zone (without session)
 */
export const createZoneQRToken = onCall({
  secrets: [qrValidationSecret, encryptionKey],
  enforceAppCheck: true,
  cors: false,
}, async (request) => {
  const startTime = Date.now();
  let auditContext: any = {};

  try {
    const {zoneId, purpose = "zone_validation"} = request.data;

    if (!zoneId) {
      throw new HttpsError("invalid-argument", "Zone ID is required");
    }

    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;
    const userEmail = request.auth.token.email;

    auditContext = {
      userId,
      userEmail,
      zoneId,
      purpose,
      clientIP: request.rawRequest.ip,
      userAgent: request.rawRequest.get("user-agent"),
    };

    // Validate zone exists and user has permission
    await validateZoneAccess(zoneId, userId);

    // Generate cryptographically secure components
    const tokenId = generateCryptoSecureId(32);
    const nonce = generateCryptoSecureId(16);
    const zoneNonce = generateCryptoSecureId(16);

    const now = Date.now();
    const expiresAt = now + 45000; // 45 seconds

    // Create comprehensive token payload for zone validation
    const payload: any = {
      // Standard JWT claims
      iss: "respublica-seguridad-v2",
      sub: userId,
      aud: "qr-zone-validation",
      exp: Math.floor(expiresAt / 1000),
      iat: Math.floor(now / 1000),
      nbf: Math.floor(now / 1000),
      jti: tokenId,

      // Zone-specific claims
      nonce,
      zoneNonce,
      zoneId,
      purpose,
      tokenType: "zone_validation",
      version: "2.0",
    };

    // Add payload checksum
    payload.checksum = calculatePayloadChecksum(payload);

    // Encrypt sensitive payload data
    const encryptedPayload = encryptPayload(payload, encryptionKey.value());

    // Create HMAC signature
    const signature = createProductionHMAC(encryptedPayload, qrValidationSecret.value());

    // Final token structure
    const secureToken = {
      version: "2.0",
      algorithm: "AES-256-GCM+HMAC-SHA256",
      keyId: getCurrentKeyId(),
      payload: encryptedPayload,
      signature,
      metadata: {
        created: now,
        expires: expiresAt,
        purpose,
        tokenType: "zone_validation",
      },
    };

    // Store token with zone-based tracking
    await storeZoneToken(tokenId, {
      zoneId,
      userId,
      purpose,
      createdAt: now,
      expiresAt,
      status: "active",
      securityLevel: "high",
      tokenType: "zone_validation",
      clientMetadata: auditContext,
    });

    // Log successful token creation
    await logSecurityEvent({
      type: "zone_token_created",
      severity: "info",
      context: auditContext,
      metadata: {
        tokenId,
        expiresAt,
        processingTime: Date.now() - startTime,
      },
    });

    return {
      success: true,
      token: secureToken,
      tokenId,
      expiresAt,
      securityLevel: "high",
      tokenType: "zone_validation",
    };

  } catch (error) {
    await logSecurityEvent({
      type: "zone_token_creation_failed",
      severity: "warning",
      context: auditContext,
      metadata: {
        error: error instanceof Error ? error.message : String(error),
        processingTime: Date.now() - startTime,
      },
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Zone token creation failed");
  }
});

/**
 * Validates zone QR token scan (without session)
 */
export const validateZoneQRTokenScan = onCall(async (request) => {
  try {
    const {tokenId, scannerUserId} = request.data;

    // Validate input
    if (!tokenId || !scannerUserId) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    // Validate user authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const authenticatedUserId = request.auth.uid;

    // Verify scanner user ID matches authenticated user
    if (authenticatedUserId !== scannerUserId) {
      throw new HttpsError("permission-denied", "Scanner user ID mismatch");
    }

    const db = getDatabase();

    // Get token data
    const tokenRef = db.ref(`qr_tokens/${tokenId}`);
    const tokenSnapshot = await tokenRef.get();

    if (!tokenSnapshot.exists()) {
      throw new HttpsError("not-found", "QR token not found");
    }

    const tokenData = tokenSnapshot.val();

    // Check if token is valid
    if (tokenData.status !== "active") {
      throw new HttpsError("failed-precondition", "QR token is not active");
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = new Date(tokenData.expiresAt);
    if (now > expiresAt) {
      // Mark token as expired
      await tokenRef.update({
        status: "expired",
      });
      throw new HttpsError("deadline-exceeded", "QR token has expired");
    }

    // Check if scanner is not the token owner
    if (tokenData.userId === scannerUserId) {
      throw new HttpsError("permission-denied", "Cannot scan your own QR token");
    }

    // Mark token as used
    await tokenRef.update({
      status: "used",
      usedAt: now.toISOString(),
      usedByUserId: scannerUserId,
    });

    // Log event for the zone
    const eventRef = db.ref(`qr_validation_events/${tokenData.sessionId}`).push();
    await eventRef.set({
      eventType: "zone_token_scanned",
      timestamp: now.toISOString(),
      eventData: {
        tokenId,
        scannerUserId,
        tokenOwnerId: tokenData.userId,
        zoneId: tokenData.sessionId, // sessionId contains zoneId for compatibility
      },
    });

    return {
      success: true,
      tokenId,
      zoneId: tokenData.sessionId,
      message: "Zone QR token scanned successfully",
    };
  } catch (error) {
    logger.error("Error validating zone QR token scan:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Internal server error");
  }
});

/**
 * Validates proximity between two users for QR validation
 */
export const validateProximity = onCall(async (request) => {
  try {
    const {sessionId, initiatorLocation, validatorLocation} = request.data;

    // Validate input
    if (!sessionId || !initiatorLocation || !validatorLocation) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    // Validate user authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;

    // Get session data from Realtime Database
    const db = getDatabase();
    const sessionRef = db.ref(`qr_validation_sessions/${sessionId}`);
    const sessionSnapshot = await sessionRef.get();

    if (!sessionSnapshot.exists()) {
      throw new HttpsError("not-found", "Validation session not found");
    }

    const sessionData = sessionSnapshot.val();

    // Verify user is part of the session
    if (sessionData.initiatorUserId !== userId && sessionData.validatorUserId !== userId) {
      throw new HttpsError("permission-denied", "User is not part of this validation session");
    }

    // Check if session is active
    if (sessionData.status !== "active") {
      throw new HttpsError("failed-precondition", "Validation session is not active");
    }

    // Check if session is expired
    const now = new Date();
    const expiresAt = new Date(sessionData.expiresAt);
    if (now > expiresAt) {
      throw new HttpsError("deadline-exceeded", "Validation session has expired");
    }

    // Calculate distance between users using Haversine formula
    const distance = calculateDistance(
      initiatorLocation.latitude,
      initiatorLocation.longitude,
      validatorLocation.latitude,
      validatorLocation.longitude
    );

    // Check proximity (within 100 meters)
    const maxDistance = 100; // meters
    const isWithinProximity = distance <= maxDistance;

    // Verify both users are within the zone
    const firestore = getFirestore();
    const zoneDoc = await firestore.collection("zones").doc(sessionData.zoneId).get();

    if (!zoneDoc.exists) {
      throw new HttpsError("not-found", "Zone not found");
    }

    const zoneData = zoneDoc.data()!;
    const zoneCenter = zoneData.centerCoordinates;
    const zoneRadius = zoneData.radiusInMeters || 600;

    const initiatorInZone = calculateDistance(
      initiatorLocation.latitude,
      initiatorLocation.longitude,
      zoneCenter.latitude,
      zoneCenter.longitude
    ) <= zoneRadius;

    const validatorInZone = calculateDistance(
      validatorLocation.latitude,
      validatorLocation.longitude,
      zoneCenter.latitude,
      zoneCenter.longitude
    ) <= zoneRadius;

    const isProximityVerified = isWithinProximity && initiatorInZone && validatorInZone;

    // Record proximity verification
    const proximityRef = db.ref(`proximity_verifications/${sessionId}`);
    await proximityRef.set({
      sessionId,
      isVerified: isProximityVerified,
      timestamp: now.toISOString(),
      distance,
      initiatorLocation: {
        ...initiatorLocation,
        timestamp: now.toISOString(),
      },
      validatorLocation: {
        ...validatorLocation,
        timestamp: now.toISOString(),
      },
      initiatorInZone,
      validatorInZone,
    });

    // Log event
    const eventRef = db.ref(`qr_validation_events/${sessionId}`).push();
    await eventRef.set({
      eventType: "proximity_verified",
      timestamp: now.toISOString(),
      eventData: {
        userId,
        isVerified: isProximityVerified,
        distance,
        initiatorInZone,
        validatorInZone,
      },
    });

    return {
      success: true,
      isProximityVerified,
      distance,
      initiatorInZone,
      validatorInZone,
    };
  } catch (error) {
    logger.error("Error validating proximity:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Internal server error");
  }
});

/**
 * Validates and processes QR token scan
 */
export const validateQRTokenScan = onCall(async (request) => {
  try {
    const {tokenId, scannerUserId} = request.data;

    // Validate input
    if (!tokenId || !scannerUserId) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    // Validate user authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;

    // Verify scanner is the authenticated user
    if (scannerUserId !== userId) {
      throw new HttpsError("permission-denied", "Scanner user ID mismatch");
    }

    const db = getDatabase();

    // Get token data
    const tokenRef = db.ref(`qr_tokens/${tokenId}`);
    const tokenSnapshot = await tokenRef.get();

    if (!tokenSnapshot.exists()) {
      throw new HttpsError("not-found", "QR token not found");
    }

    const tokenData = tokenSnapshot.val();

    // Check if token is valid
    if (tokenData.status !== "active") {
      throw new HttpsError("failed-precondition", "QR token is not active");
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = new Date(tokenData.expiresAt);
    if (now > expiresAt) {
      // Mark token as expired
      await tokenRef.update({
        status: "expired",
      });
      throw new HttpsError("deadline-exceeded", "QR token has expired");
    }

    // Check if scanner is not the token owner
    if (tokenData.userId === scannerUserId) {
      throw new HttpsError("permission-denied", "Cannot scan your own QR token");
    }

    // Get session data
    const sessionRef = db.ref(`qr_validation_sessions/${tokenData.sessionId}`);
    const sessionSnapshot = await sessionRef.get();

    if (!sessionSnapshot.exists()) {
      throw new HttpsError("not-found", "Validation session not found");
    }

    const sessionData = sessionSnapshot.val();

    // Verify scanner is part of the session
    if (sessionData.initiatorUserId !== scannerUserId && sessionData.validatorUserId !== scannerUserId) {
      throw new HttpsError("permission-denied", "Scanner is not part of this validation session");
    }

    // Mark token as used
    await tokenRef.update({
      status: "used",
      usedAt: now.toISOString(),
      usedByUserId: scannerUserId,
    });

    // Log event
    const eventRef = db.ref(`qr_validation_events/${tokenData.sessionId}`).push();
    await eventRef.set({
      eventType: "token_scanned",
      timestamp: now.toISOString(),
      eventData: {
        tokenId,
        scannerUserId,
        tokenOwnerId: tokenData.userId,
      },
    });

    return {
      success: true,
      tokenId,
      sessionId: tokenData.sessionId,
      message: "QR token scanned successfully",
    };
  } catch (error) {
    logger.error("Error validating QR token scan:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Internal server error");
  }
});

/**
 * Completes QR validation and updates zone status
 */
export const completeQRValidation = onCall(async (request) => {
  try {
    const {sessionId} = request.data;

    // Validate input
    if (!sessionId) {
      throw new HttpsError("invalid-argument", "Missing session ID");
    }

    // Validate user authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;
    const db = getDatabase();
    const firestore = getFirestore();

    // Get session data
    const sessionRef = db.ref(`qr_validation_sessions/${sessionId}`);
    const sessionSnapshot = await sessionRef.get();

    if (!sessionSnapshot.exists()) {
      throw new HttpsError("not-found", "Validation session not found");
    }

    const sessionData = sessionSnapshot.val();

    // Verify user is part of the session
    if (sessionData.initiatorUserId !== userId && sessionData.validatorUserId !== userId) {
      throw new HttpsError("permission-denied", "User is not part of this validation session");
    }

    // Check if session is active
    if (sessionData.status !== "active") {
      throw new HttpsError("failed-precondition", "Validation session is not active");
    }

    // Check proximity verification
    const proximityRef = db.ref(`proximity_verifications/${sessionId}`);
    const proximitySnapshot = await proximityRef.get();

    if (!proximitySnapshot.exists() || !proximitySnapshot.val().isVerified) {
      throw new HttpsError("failed-precondition", "Proximity verification required");
    }

    // Check if both tokens have been scanned
    const tokensRef = db.ref("qr_tokens").orderByChild("sessionId").equalTo(sessionId);
    const tokensSnapshot = await tokensRef.get();

    if (!tokensSnapshot.exists()) {
      throw new HttpsError("failed-precondition", "No tokens found for session");
    }

    const tokens = tokensSnapshot.val();
    const usedTokens = Object.values(tokens).filter((token: any) => token.status === "used");
    const uniqueUsers = new Set(usedTokens.map((token: any) => token.userId));

    if (usedTokens.length < 2 || uniqueUsers.size < 2) {
      throw new HttpsError("failed-precondition", "Both users must scan each other's QR codes");
    }

    // Mark session as completed
    await sessionRef.update({
      status: "completed",
      completedAt: new Date().toISOString(),
    });

    // Add community validation to the zone
    const zoneRef = firestore.collection("zones").doc(sessionData.zoneId);
    const zoneDoc = await zoneRef.get();

    if (zoneDoc.exists) {
      const zoneData = zoneDoc.data()!;
      const currentValidations = zoneData.communityValidationCount || 0;
      const validatedBy = zoneData.communityValidatedBy || [];

      // Add validator if not already validated
      if (!validatedBy.includes(sessionData.validatorUserId)) {
        await zoneRef.update({
          communityValidationCount: currentValidations + 1,
          communityValidatedBy: [...validatedBy, sessionData.validatorUserId],
          updatedAt: new Date(),
        });

        // Check if zone should be auto-validated
        if (currentValidations + 1 >= 3) {
          await zoneRef.update({
            validationStatus: "validated",
            validatedAt: new Date(),
          });
        }
      }
    }

    // Log completion event
    const eventRef = db.ref(`qr_validation_events/${sessionId}`).push();
    await eventRef.set({
      eventType: "validation_completed",
      timestamp: new Date().toISOString(),
      eventData: {
        completedBy: userId,
        zoneId: sessionData.zoneId,
      },
    });

    return {
      success: true,
      sessionId,
      message: "QR validation completed successfully",
    };
  } catch (error) {
    logger.error("Error completing QR validation:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Internal server error");
  }
});

/**
 * Cleanup expired QR validation data
 */
export const cleanupExpiredQRData = onValueWritten(
  {
    ref: "/qr_validation_sessions/{sessionId}",
    region: "us-central1",
  },
  async (event) => {
    try {
      const sessionData = event.data.after.val();

      if (!sessionData) {
        return; // Session was deleted
      }

      const now = new Date();
      const expiresAt = new Date(sessionData.expiresAt);

      // If session is expired, clean it up
      if (now > expiresAt && sessionData.status !== "completed" && sessionData.status !== "expired") {
        const db = getDatabase();
        const sessionRef = db.ref(`qr_validation_sessions/${event.params.sessionId}`);

        // Mark session as expired
        await sessionRef.update({
          status: "expired",
          completedAt: now.toISOString(),
        });

        // Clean up associated tokens
        const tokensRef = db.ref("qr_tokens").orderByChild("sessionId").equalTo(event.params.sessionId);
        const tokensSnapshot = await tokensRef.get();

        if (tokensSnapshot.exists()) {
          const tokens = tokensSnapshot.val();
          const updates: {[key: string]: any} = {};

          Object.keys(tokens).forEach((tokenId) => {
            if (tokens[tokenId].status === "active") {
              updates[`qr_tokens/${tokenId}/status`] = "expired";
            }
          });

          if (Object.keys(updates).length > 0) {
            await db.ref().update(updates);
          }
        }

        logger.info(`Cleaned up expired session: ${event.params.sessionId}`);
      }
    } catch (error) {
      logger.error("Error cleaning up expired QR data:", error);
    }
  }
);

// Production-Level Security Functions

/**
 * Creates a cryptographically secure token with enterprise-grade security
 * Features:
 * - AES-256-GCM encryption for payload
 * - HMAC-SHA256 for integrity
 * - Comprehensive rate limiting
 * - User permission validation
 * - Audit logging with threat detection
 */
export const createSecureToken = onCall({
  secrets: [qrValidationSecret, encryptionKey],
  enforceAppCheck: true, // Require App Check for additional security
  cors: false, // Disable CORS for security
}, async (request) => {
  const startTime = Date.now();
  let auditContext: any = {};

  try {
    // Input validation with strict typing
    const {sessionId, zoneId, purpose, validatorUserId, locationHash} = request.data;

    if (!sessionId || !zoneId || !purpose || !validatorUserId) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    // Validate purpose enum
    const validPurposes = ["initiate", "validate", "scan", "complete"];
    if (!validPurposes.includes(purpose)) {
      throw new HttpsError("invalid-argument", "Invalid token purpose");
    }

    // Enhanced authentication validation
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;
    const userEmail = request.auth.token.email;

    auditContext = {
      userId,
      userEmail,
      sessionId,
      zoneId,
      purpose,
      clientIP: request.rawRequest.ip,
      userAgent: request.rawRequest.get("user-agent"),
    };

    // Verify user identity and permissions
    await validateUserPermissions(userId, sessionId, zoneId, purpose);

    // Advanced rate limiting with multiple tiers
    const clientIP = request.rawRequest.ip || "unknown";
    const rateLimitResult = await checkAdvancedRateLimit(userId, clientIP);
    if (!rateLimitResult.allowed) {
      await logSecurityThreat({
        type: "rate_limit_exceeded",
        severity: "medium",
        context: auditContext,
        metadata: rateLimitResult,
      });
      throw new HttpsError("resource-exhausted", "Rate limit exceeded");
    }

    // Validate session state and constraints
    await validateSessionSecurity(sessionId, userId, validatorUserId);

    // Generate cryptographically secure components
    const tokenId = generateCryptoSecureId(32);
    const nonce = generateCryptoSecureId(16);
    const sessionNonce = generateCryptoSecureId(16);

    const now = Date.now();
    const expiresAt = now + 45000; // 45 seconds

    // Create comprehensive token payload
    const payload = {
      // Standard JWT claims
      iss: "respublica-seguridad-v2",
      sub: userId,
      aud: "qr-validation",
      exp: Math.floor(expiresAt / 1000),
      iat: Math.floor(now / 1000),
      nbf: Math.floor(now / 1000), // Not before
      jti: tokenId,

      // Custom security claims
      nonce,
      sessionNonce,
      sessionId,
      zoneId,
      purpose,
      validatorUserId,
      locationHash: locationHash || null,

      // Security metadata
      keyVersion: getCurrentKeyVersion(),
      securityLevel: "high",
      deviceFingerprint: generateDeviceFingerprint(request.rawRequest),

      // Anti-tampering
      checksum: "", // Will be calculated after payload creation
    };

    // Calculate payload checksum for integrity
    payload.checksum = calculatePayloadChecksum(payload);

    // Encrypt sensitive payload data
    const encryptedPayload = encryptPayload(payload, encryptionKey.value());

    // Create HMAC signature for the entire token
    const signature = createProductionHMAC(encryptedPayload, qrValidationSecret.value());

    // Final token structure with security headers
    const secureToken = {
      version: "2.0",
      algorithm: "AES-256-GCM+HMAC-SHA256",
      keyId: getCurrentKeyId(),
      payload: encryptedPayload,
      signature,
      metadata: {
        created: now,
        expires: expiresAt,
        purpose,
      },
    };

    // Store token with comprehensive tracking
    await storeSecureToken(tokenId, {
      sessionId,
      userId,
      purpose,
      createdAt: now,
      expiresAt,
      status: "active",
      securityLevel: "high",
      clientMetadata: auditContext,
    });

    // Log successful token creation
    await logSecurityEvent({
      type: "secure_token_created",
      severity: "info",
      context: auditContext,
      metadata: {
        tokenId,
        expiresAt,
        processingTime: Date.now() - startTime,
      },
    });

    return {
      success: true,
      token: secureToken,
      tokenId,
      expiresAt,
      securityLevel: "high",
    };

  } catch (error) {
    // Enhanced error logging with security context
    await logSecurityEvent({
      type: "token_creation_failed",
      severity: "warning",
      context: auditContext,
      metadata: {
        error: error instanceof Error ? error.message : String(error),
        processingTime: Date.now() - startTime,
      },
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Secure token creation failed");
  }
});

/**
 * Validates secure tokens with military-grade security checks
 * Features:
 * - AES-256-GCM decryption
 * - Multi-layer signature verification
 * - Advanced replay attack prevention
 * - Real-time threat detection
 * - Comprehensive audit logging
 */
export const validateSecureToken = onCall({
  secrets: [qrValidationSecret, encryptionKey],
  enforceAppCheck: true,
  cors: false,
}, async (request) => {
  const startTime = Date.now();
  let auditContext: any = {};

  try {
    const {token, expectedSessionId, expectedPurpose, locationProof} = request.data;

    if (!token || !expectedSessionId || !expectedPurpose) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const userId = request.auth.uid;

    auditContext = {
      userId,
      userEmail: request.auth.token.email,
      expectedSessionId,
      expectedPurpose,
      clientIP: request.rawRequest.ip,
      userAgent: request.rawRequest.get("user-agent"),
    };

    // Validate token structure and version
    if (!isValidSecureTokenStructure(token)) {
      await logSecurityThreat({
        type: "malformed_token",
        severity: "high",
        context: auditContext,
        metadata: {tokenStructure: typeof token},
      });
      throw new HttpsError("invalid-argument", "Invalid token structure");
    }

    // Verify token version compatibility
    if (token.version !== "2.0") {
      throw new HttpsError("invalid-argument", "Unsupported token version");
    }

    // Verify algorithm and key
    if (token.algorithm !== "AES-256-GCM+HMAC-SHA256") {
      throw new HttpsError("invalid-argument", "Unsupported encryption algorithm");
    }

    if (token.keyId !== getCurrentKeyId()) {
      throw new HttpsError("invalid-argument", "Invalid key identifier");
    }

    // Verify HMAC signature first (fail fast)
    const expectedSignature = createProductionHMAC(token.payload, qrValidationSecret.value());
    if (!constantTimeEquals(token.signature, expectedSignature)) {
      await logSecurityThreat({
        type: "signature_verification_failed",
        severity: "critical",
        context: auditContext,
        metadata: {tokenId: "unknown"},
      });
      throw new HttpsError("permission-denied", "Token signature verification failed");
    }

    // Decrypt and validate payload
    const decryptedPayload = decryptPayload(token.payload, encryptionKey.value());

    // Verify payload integrity
    const expectedChecksum = calculatePayloadChecksum({...decryptedPayload, checksum: null});
    if (decryptedPayload.checksum !== expectedChecksum) {
      await logSecurityThreat({
        type: "payload_integrity_violation",
        severity: "critical",
        context: auditContext,
        metadata: {tokenId: decryptedPayload.jti},
      });
      throw new HttpsError("permission-denied", "Token payload integrity check failed");
    }

    // Comprehensive claims validation
    const claimsValidation = await validateAdvancedTokenClaims(
      decryptedPayload,
      userId,
      expectedSessionId,
      expectedPurpose
    );

    if (!claimsValidation.isValid) {
      await logSecurityEvent({
        type: "token_claims_validation_failed",
        severity: "warning",
        context: auditContext,
        metadata: {
          tokenId: decryptedPayload.jti,
          reason: claimsValidation.reason,
        },
      });
      throw new HttpsError("permission-denied", claimsValidation.reason || "Invalid token claims");
    }

    // Advanced replay attack prevention
    const replayCheck = await checkAdvancedReplayAttack(decryptedPayload.jti, decryptedPayload.nonce);
    if (!replayCheck.isValid) {
      await logSecurityThreat({
        type: "replay_attack_detected",
        severity: "critical",
        context: auditContext,
        metadata: {
          tokenId: decryptedPayload.jti,
          replayDetails: replayCheck,
        },
      });
      throw new HttpsError("permission-denied", "Replay attack detected");
    }

    // Location verification if required
    if (locationProof) {
      const locationValid = await verifyLocationProof(
        decryptedPayload.locationHash,
        locationProof,
        decryptedPayload.zoneId
      );
      if (!locationValid) {
        throw new HttpsError("permission-denied", "Location verification failed");
      }
    }

    // Mark token as consumed with advanced tracking
    await consumeSecureToken(decryptedPayload.jti, {
      consumedBy: userId,
      consumedAt: Date.now(),
      sessionId: expectedSessionId,
      securityContext: auditContext,
    });

    // Log successful validation
    await logSecurityEvent({
      type: "secure_token_validated",
      severity: "info",
      context: auditContext,
      metadata: {
        tokenId: decryptedPayload.jti,
        purpose: expectedPurpose,
        processingTime: Date.now() - startTime,
      },
    });

    return {
      success: true,
      claims: decryptedPayload,
      securityLevel: "high",
      validatedAt: Date.now(),
    };

  } catch (error) {
    await logSecurityEvent({
      type: "token_validation_failed",
      severity: "warning",
      context: auditContext,
      metadata: {
        error: error instanceof Error ? error.message : String(error),
        processingTime: Date.now() - startTime,
      },
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "Token validation failed");
  }
});

// Production-Level Security Helper Functions

/**
 * Generate cryptographically secure ID with specified byte length
 */
function generateCryptoSecureId(byteLength: number): string {
  return crypto.randomBytes(byteLength).toString("base64url");
}

/**
 * Get current key version for key rotation support
 */
function getCurrentKeyVersion(): string {
  return process.env.QR_KEY_VERSION || "v1";
}

/**
 * Get current key ID with version support
 */
function getCurrentKeyId(): string {
  const version = getCurrentKeyVersion();
  const year = new Date().getFullYear();
  return `qr-validation-${version}-${year}`;
}

/**
 * Create production-grade HMAC with key derivation
 */
function createProductionHMAC(data: string, secret: string): string {
  // Use PBKDF2 for key derivation to strengthen the secret
  const salt = "respublica-seguridad-salt-2024";
  const iterations = 100000;
  const keyLength = 32;

  const derivedKey = crypto.pbkdf2Sync(secret, salt, iterations, keyLength, "sha256");
  return crypto.createHmac("sha256", derivedKey).update(data).digest("base64url");
}

/**
 * Encrypt payload using AES-256-GCM
 */
function encryptPayload(payload: any, encryptionKey: string): string {
  const algorithm = "aes-256-gcm";
  const iv = crypto.randomBytes(16);
  const key = crypto.scryptSync(encryptionKey, "salt", 32);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  cipher.setAAD(Buffer.from("respublica-seguridad"));

  const payloadString = JSON.stringify(payload);
  let encrypted = cipher.update(payloadString, "utf8", "base64");
  encrypted += cipher.final("base64");

  const authTag = cipher.getAuthTag();

  // Combine IV, auth tag, and encrypted data
  const combined = Buffer.concat([iv, authTag, Buffer.from(encrypted, "base64")]);
  return combined.toString("base64");
}

/**
 * Decrypt payload using AES-256-GCM
 */
function decryptPayload(encryptedData: string, encryptionKey: string): any {
  const algorithm = "aes-256-gcm";
  const combined = Buffer.from(encryptedData, "base64");

  const iv = combined.subarray(0, 16);
  const authTag = combined.subarray(16, 32);
  const encrypted = combined.subarray(32);

  const key = crypto.scryptSync(encryptionKey, "salt", 32);

  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  decipher.setAAD(Buffer.from("respublica-seguridad"));
  decipher.setAuthTag(authTag);

  let decrypted = decipher.update(encrypted, undefined, "utf8");
  decrypted += decipher.final("utf8");

  return JSON.parse(decrypted);
}

/**
 * Calculate payload checksum for integrity verification
 */
function calculatePayloadChecksum(payload: any): string {
  const payloadCopy = {...payload};
  delete payloadCopy.checksum; // Remove checksum field for calculation

  const payloadString = JSON.stringify(payloadCopy, Object.keys(payloadCopy).sort());
  return crypto.createHash("sha256").update(payloadString).digest("hex");
}

/**
 * Generate device fingerprint for additional security
 */
function generateDeviceFingerprint(request: any): string {
  const userAgent = request.get("user-agent") || "";
  const acceptLanguage = request.get("accept-language") || "";
  const acceptEncoding = request.get("accept-encoding") || "";

  const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}`;
  return crypto.createHash("sha256").update(fingerprint).digest("hex").substring(0, 16);
}

/**
 * Constant-time string comparison to prevent timing attacks
 */
function constantTimeEquals(a: string, b: string): boolean {
  if (a.length !== b.length) return false;

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  return result === 0;
}

/**
 * Validate secure token structure
 */
function isValidSecureTokenStructure(token: any): boolean {
  return token &&
         typeof token === "object" &&
         typeof token.version === "string" &&
         typeof token.algorithm === "string" &&
         typeof token.keyId === "string" &&
         typeof token.payload === "string" &&
         typeof token.signature === "string" &&
         token.metadata &&
         typeof token.metadata === "object";
}

/**
 * Validate user permissions for QR operations
 */
async function validateUserPermissions(
  userId: string,
  sessionId: string,
  zoneId: string,
  purpose: string
): Promise<void> {
  const auth = getAuth();
  const firestore = getFirestore();

  // Verify user exists and is active
  try {
    const userRecord = await auth.getUser(userId);
    if (userRecord.disabled) {
      throw new HttpsError("permission-denied", "User account is disabled");
    }
  } catch (error) {
    throw new HttpsError("permission-denied", "Invalid user");
  }

  // Verify user has completed identity verification
  const userDoc = await firestore.collection("users").doc(userId).get();
  if (!userDoc.exists) {
    throw new HttpsError("permission-denied", "User profile not found");
  }

  const userData = userDoc.data()!;

  // Allow users with validated or pendingReview status to initiate QR validation
  const allowedStatuses = ['validated', 'pendingReview'];
  const userValidationStatus = userData.validationStatus || 'unverified';

  if (!allowedStatuses.includes(userValidationStatus)) {
    throw new HttpsError("permission-denied", "Identity verification required. Please complete identity verification or wait for review completion.");
  }

  // Verify zone exists and user has access
  const zoneDoc = await firestore.collection("zones").doc(zoneId).get();
  if (!zoneDoc.exists) {
    throw new HttpsError("not-found", "Zone not found");
  }

  const zoneData = zoneDoc.data()!;
  if (zoneData.createdBy !== userId && purpose === "initiate") {
    throw new HttpsError("permission-denied", "Only zone creator can initiate validation");
  }
}

/**
 * Advanced rate limiting with multiple tiers and IP tracking
 */
async function checkAdvancedRateLimit(userId: string, clientIP: string): Promise<{
  allowed: boolean;
  reason?: string;
  resetTime?: number;
}> {
  const db = getDatabase();
  const now = Date.now();

  // Check user-level rate limiting
  const userRateLimitRef = db.ref(`rate_limits/users/${userId}`);
  const userSnapshot = await userRateLimitRef.get();

  if (userSnapshot.exists()) {
    const userData = userSnapshot.val();
    const userWindow = 300000; // 5 minutes
    const userLimit = 50; // 50 operations per 5 minutes

    const recentOps = (userData.operations || []).filter((timestamp: number) =>
      now - timestamp < userWindow
    );

    if (recentOps.length >= userLimit) {
      return {
        allowed: false,
        reason: "User rate limit exceeded",
        resetTime: Math.min(...recentOps) + userWindow,
      };
    }
  }

  // Check IP-level rate limiting
  const ipHash = crypto.createHash("sha256").update(clientIP).digest("hex");
  const ipRateLimitRef = db.ref(`rate_limits/ips/${ipHash}`);
  const ipSnapshot = await ipRateLimitRef.get();

  if (ipSnapshot.exists()) {
    const ipData = ipSnapshot.val();
    const ipWindow = 600000; // 10 minutes
    const ipLimit = 100; // 100 operations per 10 minutes

    const recentOps = (ipData.operations || []).filter((timestamp: number) =>
      now - timestamp < ipWindow
    );

    if (recentOps.length >= ipLimit) {
      return {
        allowed: false,
        reason: "IP rate limit exceeded",
        resetTime: Math.min(...recentOps) + ipWindow,
      };
    }
  }

  // Update rate limit counters
  await Promise.all([
    userRateLimitRef.transaction((current) => {
      const operations = (current?.operations || []).filter((timestamp: number) =>
        now - timestamp < 300000
      );
      operations.push(now);
      return {operations, lastUpdated: now};
    }),
    ipRateLimitRef.transaction((current) => {
      const operations = (current?.operations || []).filter((timestamp: number) =>
        now - timestamp < 600000
      );
      operations.push(now);
      return {operations, lastUpdated: now};
    }),
  ]);

  return {allowed: true};
}

/**
 * Validate session security and state
 */
async function validateSessionSecurity(
  sessionId: string,
  userId: string,
  validatorUserId: string
): Promise<void> {
  const db = getDatabase();
  const sessionRef = db.ref(`qr_validation_sessions/${sessionId}`);
  const sessionSnapshot = await sessionRef.get();

  if (!sessionSnapshot.exists()) {
    throw new HttpsError("not-found", "Validation session not found");
  }

  const sessionData = sessionSnapshot.val();

  // Verify user is part of the session
  if (sessionData.initiatorUserId !== userId && sessionData.validatorUserId !== userId) {
    throw new HttpsError("permission-denied", "User not authorized for this session");
  }

  // Check session status
  if (sessionData.status !== "active") {
    throw new HttpsError("failed-precondition", "Session is not active");
  }

  // Check session expiration
  if (Date.now() > new Date(sessionData.expiresAt).getTime()) {
    throw new HttpsError("deadline-exceeded", "Session has expired");
  }

  // Verify validator user exists
  if (validatorUserId !== sessionData.validatorUserId && validatorUserId !== sessionData.initiatorUserId) {
    throw new HttpsError("permission-denied", "Invalid validator user");
  }
}

/**
 * Store secure token with comprehensive metadata
 */
async function storeSecureToken(tokenId: string, tokenData: any): Promise<void> {
  const firestore = getFirestore();
  const db = getDatabase();

  // Store in both Firestore (for queries) and Realtime Database (for real-time updates)
  await Promise.all([
    firestore.collection("secure_tokens").doc(tokenId).set({
      ...tokenData,
      createdAt: new Date(tokenData.createdAt),
      expiresAt: new Date(tokenData.expiresAt),
    }),
    db.ref(`secure_tokens/${tokenId}`).set(tokenData),
  ]);
}

/**
 * Advanced token claims validation
 */
async function validateAdvancedTokenClaims(
  payload: any,
  expectedUserId: string,
  expectedSessionId: string,
  expectedPurpose: string
): Promise<{isValid: boolean; reason?: string}> {
  const now = Date.now();

  // Check expiration with clock skew tolerance
  const exp = payload.exp * 1000;
  if (exp < now - 30000) { // 30 second tolerance
    return {isValid: false, reason: "Token expired"};
  }

  // Check not-before time
  const nbf = payload.nbf * 1000;
  if (nbf > now + 30000) { // 30 second tolerance
    return {isValid: false, reason: "Token not yet valid"};
  }

  // Check issued-at time (prevent future tokens)
  const iat = payload.iat * 1000;
  if (iat > now + 30000) {
    return {isValid: false, reason: "Token issued in future"};
  }

  // Validate standard claims
  if (payload.sub !== expectedUserId) {
    return {isValid: false, reason: "Invalid user ID"};
  }

  if (payload.sessionId !== expectedSessionId) {
    return {isValid: false, reason: "Invalid session ID"};
  }

  if (payload.purpose !== expectedPurpose) {
    return {isValid: false, reason: "Invalid token purpose"};
  }

  if (payload.aud !== "qr-validation") {
    return {isValid: false, reason: "Invalid audience"};
  }

  if (payload.iss !== "respublica-seguridad-v2") {
    return {isValid: false, reason: "Invalid issuer"};
  }

  // Validate security level
  if (payload.securityLevel !== "high") {
    return {isValid: false, reason: "Insufficient security level"};
  }

  return {isValid: true};
}

/**
 * Advanced replay attack detection
 */
async function checkAdvancedReplayAttack(tokenId: string, nonce: string): Promise<{
  isValid: boolean;
  reason?: string;
  previousUse?: any;
}> {
  const firestore = getFirestore();
  const db = getDatabase();

  // Check if token ID has been used
  const tokenDoc = await firestore.collection("consumed_tokens").doc(tokenId).get();
  if (tokenDoc.exists) {
    return {
      isValid: false,
      reason: "Token already consumed",
      previousUse: tokenDoc.data(),
    };
  }

  // Check nonce uniqueness in a time window
  const nonceHash = crypto.createHash("sha256").update(nonce).digest("hex");
  const nonceRef = db.ref(`nonce_registry/${nonceHash}`);
  const nonceSnapshot = await nonceRef.get();

  if (nonceSnapshot.exists()) {
    const nonceData = nonceSnapshot.val();
    const timeDiff = Date.now() - nonceData.timestamp;

    // If nonce was used within the last 5 minutes, it's a replay
    if (timeDiff < 300000) {
      return {
        isValid: false,
        reason: "Nonce replay detected",
        previousUse: nonceData,
      };
    }
  }

  // Register nonce usage
  await nonceRef.set({
    tokenId,
    timestamp: Date.now(),
    expiresAt: Date.now() + 300000, // 5 minutes
  });

  return {isValid: true};
}

/**
 * Verify location proof for additional security
 */
async function verifyLocationProof(
  expectedLocationHash: string | null,
  providedLocationProof: any,
  zoneId: string
): Promise<boolean> {
  if (!expectedLocationHash) {
    return true; // No location verification required
  }

  // Verify the provided location is within the zone
  const firestore = getFirestore();
  const zoneDoc = await firestore.collection("zones").doc(zoneId).get();

  if (!zoneDoc.exists) {
    return false;
  }

  const zoneData = zoneDoc.data()!;
  const distance = calculateDistance(
    providedLocationProof.latitude,
    providedLocationProof.longitude,
    zoneData.centerCoordinates.latitude,
    zoneData.centerCoordinates.longitude
  );

  const maxDistance = zoneData.radiusInMeters || 600;
  return distance <= maxDistance;
}

/**
 * Consume secure token with tracking
 */
async function consumeSecureToken(tokenId: string, consumptionData: any): Promise<void> {
  const firestore = getFirestore();
  const db = getDatabase();

  const consumptionRecord = {
    ...consumptionData,
    consumedAt: new Date(consumptionData.consumedAt),
  };

  // Mark token as consumed in both databases
  await Promise.all([
    firestore.collection("consumed_tokens").doc(tokenId).set(consumptionRecord),
    db.ref(`consumed_tokens/${tokenId}`).set(consumptionData),
    // Remove from active tokens
    db.ref(`secure_tokens/${tokenId}`).remove(),
  ]);
}

/**
 * Log security events with enhanced context
 */
async function logSecurityEvent(event: {
  type: string;
  severity: string;
  context: any;
  metadata: any;
}): Promise<void> {
  try {
    const firestore = getFirestore();
    await firestore.collection("security_audit").add({
      ...event,
      timestamp: new Date(),
      source: "cloud_function_v2",
      environment: process.env.NODE_ENV || "production",
    });
  } catch (error) {
    logger.error("Failed to log security event:", error);
  }
}

/**
 * Log security threats with high priority
 */
async function logSecurityThreat(threat: {
  type: string;
  severity: string;
  context: any;
  metadata: any;
}): Promise<void> {
  try {
    const firestore = getFirestore();

    // Log to security threats collection for immediate attention
    await firestore.collection("security_threats").add({
      ...threat,
      timestamp: new Date(),
      source: "cloud_function_v2",
      environment: process.env.NODE_ENV || "production",
      requiresInvestigation: true,
    });

    // Also log to general audit trail
    await logSecurityEvent({
      type: `threat_${threat.type}`,
      severity: threat.severity,
      context: threat.context,
      metadata: threat.metadata,
    });

    // For critical threats, consider additional alerting
    if (threat.severity === "critical") {
      logger.error("CRITICAL SECURITY THREAT DETECTED", {
        type: threat.type,
        context: threat.context,
        metadata: threat.metadata,
      });
    }
  } catch (error) {
    logger.error("Failed to log security threat:", error);
  }
}

/**
 * Calculate distance between two coordinates using Haversine formula
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Validate zone access for user
 */
async function validateZoneAccess(zoneId: string, userId: string): Promise<void> {
  const firestore = getFirestore();

  // Check if zone exists
  const zoneDoc = await firestore.collection("zones").doc(zoneId).get();
  if (!zoneDoc.exists) {
    throw new HttpsError("not-found", "Zone not found");
  }

  const zoneData = zoneDoc.data();

  // Check if user is the zone creator or has permission
  if (zoneData?.createdBy !== userId) {
    // For now, allow any authenticated user to generate tokens for any zone
    // You can add more specific permission logic here
    logger.info(`User ${userId} generating token for zone ${zoneId} (not owner)`);
  }
}

/**
 * Store zone token with comprehensive metadata
 */
async function storeZoneToken(tokenId: string, tokenData: any): Promise<void> {
  const firestore = getFirestore();
  const db = getDatabase();

  // Store in both Firestore (for queries) and Realtime Database (for real-time updates)
  await Promise.all([
    firestore.collection("zone_tokens").doc(tokenId).set({
      ...tokenData,
      createdAt: new Date(tokenData.createdAt),
      expiresAt: new Date(tokenData.expiresAt),
    }),
    db.ref(`qr_tokens/${tokenId}`).set({
      ...tokenData,
      sessionId: tokenData.zoneId, // Use zoneId as sessionId for compatibility
    }),
  ]);
}
