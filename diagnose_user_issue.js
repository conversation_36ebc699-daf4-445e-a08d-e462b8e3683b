const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'res-p-s-d8is3t'
  });
}

const db = admin.firestore();

async function diagnoseUserIssue() {
  try {
    const userId = 'bwQ9vmyMdXO98LVidys3FYJl2a33';
    
    console.log('🔍 DIAGNOSING NOTIFICATION ISSUE');
    console.log('='.repeat(50));
    
    // 1. Check if user exists in notification_preferences
    console.log('\n📋 Step 1: Checking user preferences...');
    const userDoc = await db.collection('notification_preferences').doc(userId).get();
    
    if (!userDoc.exists) {
      console.log('❌ CRITICAL: User not found in notification_preferences collection!');
      return;
    }
    
    const userData = userDoc.data();
    console.log('✅ User found in notification_preferences');
    
    // 2. Check the exact values that the query is looking for
    console.log('\n🔍 Step 2: Checking query filter values...');
    console.log('Required filters for notification query:');
    console.log('  - isLocationNotificationsEnabled == true');
    console.log('  - isLocationTrackingConsented == true');
    
    console.log('\nActual user values:');
    console.log('  - isLocationNotificationsEnabled:', userData.isLocationNotificationsEnabled, `(${typeof userData.isLocationNotificationsEnabled})`);
    console.log('  - isLocationTrackingConsented:', userData.isLocationTrackingConsented, `(${typeof userData.isLocationTrackingConsented})`);
    
    // 3. Test the exact query that the notification system uses
    console.log('\n🔍 Step 3: Testing the notification system query...');
    const querySnapshot = await db.collection('notification_preferences')
      .where('isLocationNotificationsEnabled', '==', true)
      .where('isLocationTrackingConsented', '==', true)
      .get();
    
    console.log(`Query found ${querySnapshot.size} users total`);
    
    // Check if our user is in the results
    const userInResults = querySnapshot.docs.find(doc => doc.id === userId);
    if (userInResults) {
      console.log('✅ SUCCESS: Your user IS found by the notification query!');
    } else {
      console.log('❌ PROBLEM: Your user is NOT found by the notification query!');
      console.log('This means the field values don\'t match exactly.');
    }
    
    // 4. Show all users found by the query for comparison
    if (querySnapshot.size > 0) {
      console.log('\n📊 Users found by query (for comparison):');
      querySnapshot.docs.slice(0, 3).forEach((doc, index) => {
        const data = doc.data();
        console.log(`  User ${index + 1} (${doc.id}):`);
        console.log(`    - isLocationNotificationsEnabled: ${data.isLocationNotificationsEnabled} (${typeof data.isLocationNotificationsEnabled})`);
        console.log(`    - isLocationTrackingConsented: ${data.isLocationTrackingConsented} (${typeof data.isLocationTrackingConsented})`);
      });
    }
    
    // 5. Check location data
    console.log('\n📍 Step 4: Checking location data...');
    console.log('Location in notification_preferences:');
    if (userData.lastKnownLocation) {
      console.log('  ✅ lastKnownLocation exists');
      console.log('    - latitude:', userData.lastKnownLocation.latitude);
      console.log('    - longitude:', userData.lastKnownLocation.longitude);
    } else {
      console.log('  ❌ No lastKnownLocation in preferences');
    }
    
    // Check user_locations collection
    const locationDoc = await db.collection('user_locations').doc(userId).get();
    if (locationDoc.exists) {
      const locationData = locationDoc.data();
      console.log('  ✅ Location found in user_locations collection');
      console.log('    - latitude:', locationData.latitude);
      console.log('    - longitude:', locationData.longitude);
      console.log('    - timestamp:', locationData.timestamp?.toDate?.()?.toISOString() || 'invalid');
    } else {
      console.log('  ❌ No location in user_locations collection');
    }
    
    // 6. Check FCM token
    console.log('\n🔑 Step 5: Checking FCM token...');
    if (userData.fcmToken) {
      console.log('  ✅ FCM token exists');
      console.log('    - Length:', userData.fcmToken.length);
      console.log('    - Updated:', userData.fcmTokenUpdatedAt?.toDate?.()?.toISOString() || 'unknown');
    } else {
      console.log('  ❌ No FCM token');
    }
    
    // 7. Summary
    console.log('\n📋 DIAGNOSIS SUMMARY:');
    console.log('='.repeat(30));
    
    const issues = [];
    
    if (userData.isLocationNotificationsEnabled !== true) {
      issues.push('❌ isLocationNotificationsEnabled is not true');
    }
    
    if (userData.isLocationTrackingConsented !== true) {
      issues.push('❌ isLocationTrackingConsented is not true');
    }
    
    if (!userData.lastKnownLocation && !locationDoc.exists) {
      issues.push('❌ No location data available');
    }
    
    if (!userData.fcmToken) {
      issues.push('❌ No FCM token');
    }
    
    if (!userInResults) {
      issues.push('❌ User not found by notification query');
    }
    
    if (issues.length === 0) {
      console.log('✅ All checks passed! The notification system should work.');
      console.log('The issue might be in the distance calculation or other filters.');
    } else {
      console.log('Found the following issues:');
      issues.forEach(issue => console.log(`  ${issue}`));
    }
    
  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
  }
}

// Run the diagnosis
diagnoseUserIssue().then(() => {
  console.log('\n✅ Diagnosis completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Diagnosis failed:', error);
  process.exit(1);
});
