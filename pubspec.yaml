name: respublicaseguridad
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# 

# keytool -genkey -v -keystore my-project-key.jks -keyalg RSA -keysize 2048 -validity 10000 -alias my-project-alias

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application. 
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  
  # Very Good Ventures packages
  # very_good_analysis: ^.0.0
  formz: ^0.8.0
  bloc: ^8.1.3
  flutter_bloc: ^8.1.4
  hydrated_bloc: ^9.1.5
  equatable: ^2.0.5

  # UI Components
  shimmer: ^3.0.0
  
  # Routing
  go_router: ^14.0.1
  
  # Persistence
  shared_preferences: ^2.5.3
  
  # UI and Icons
  font_awesome_flutter: ^10.7.0
  flutter_screenutil: ^5.9.2
  fluentui_system_icons: ^1.1.273
  
  # Google Fonts for typography
  google_fonts: ^6.1.0

  # Google Maps
  google_maps_flutter: ^2.5.0

  # Environment configuration
  flutter_dotenv: ^5.1.0

  # Identity Validation
  image_picker: ^1.0.7
  camera: ^0.10.5+9
  path_provider: ^2.1.2
  path: ^1.9.0

  # Media Preview and Gallery
  photo_view: ^0.15.0
  video_player: ^2.10.0
  chewie: ^1.12.1
  cached_network_image: ^3.3.1

  # Firebase
  firebase_core: ^2.27.1
  firebase_auth: ^4.18.1
  cloud_firestore: ^4.15.9
  firebase_database: ^10.4.9
  firebase_analytics: ^10.8.10
  firebase_crashlytics: ^3.4.19
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^6.0.4

 
  
  # DI
  get_it: ^7.6.7
  injectable: ^2.3.4
  dartz: ^0.10.1
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # New dependencies
  http: ^1.1.0
  flutter_svg: ^2.0.7
  firebase_storage: ^11.2.3
  google_mlkit_image_labeling: ^0.14.1
  google_mlkit_commons: ^0.11.0
  google_mlkit_text_recognition: ^0.15.0
  flutter_image_compress: ^2.3.0
  uuid: ^4.5.1
  crypto: ^3.0.3
  app_links: ^3.4.3 # For handling deep links
  qr_flutter: ^4.1.0
  share_plus: ^10.0.0
  url_launcher: ^6.2.5
  gal: ^2.3.0

  # Location and Geofencing
  geolocator: ^12.0.0
  permission_handler: ^11.3.1

  # Background Processing
  workmanager: ^0.8.0

  # Notifications
  firebase_messaging: ^14.7.20
  flutter_local_notifications: ^17.2.2

  # Additional utilities for automatic validation
  timezone: ^0.9.4
  geocoding: ^4.0.0
  cloud_functions: ^4.7.6
  mobile_scanner: ^7.0.1
  lottie: ^3.1.2
  geoflutterfire_plus: ^0.0.21
  flutter_animarker: ^3.2.0
  toastification: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  bloc_test: ^9.1.6
  flutter_launcher_icons: ^0.13.1
  injectable_generator: ^2.7.0
  build_runner: ^2.4.14

# Flutter launcher icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo.png"
  adaptive_icon_background: "#003B8E" # Dark blue background for adaptive icons
  adaptive_icon_foreground: "assets/logo.png"
  web:
    generate: true
    image_path: "assets/logo.png"
    background_color: "#003B8E"
    theme_color: "#003B8E"
  windows:
    generate: true
    image_path: "assets/logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/logo.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable generation of localization files
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo.png
    - assets/images/
    - assets/animations/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
