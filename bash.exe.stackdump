Stack trace:
Frame         Function      Args
0007FFFF98A0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF87A0) msys-2.0.dll+0x1FE8E
0007FFFF98A0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B78) msys-2.0.dll+0x67F9
0007FFFF98A0  000210046832 (000210286019, 0007FFFF9758, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF98A0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF98A0  000210068E24 (0007FFFF98B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B80  00021006A225 (0007FFFF98B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB275A0000 ntdll.dll
7FFB261B0000 KERNEL32.DLL
7FFB24D30000 KERNELBASE.dll
7FFB1DCD0000 apphelp.dll
7FFB263F0000 USER32.dll
7FFB24BC0000 win32u.dll
7FFB26DD0000 GDI32.dll
7FFB24BF0000 gdi32full.dll
7FFB24B10000 msvcp_win.dll
7FFB24780000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB254C0000 advapi32.dll
7FFB26E00000 msvcrt.dll
7FFB25AF0000 sechost.dll
7FFB25D00000 RPCRT4.dll
7FFB23D80000 CRYPTBASE.DLL
7FFB252A0000 bcryptPrimitives.dll
7FFB26D90000 IMM32.DLL
