# Platform Permissions Configuration

This document outlines the platform-specific permissions and configurations required for the automatic zone validation feature in the Respública Seguridad app.

## Android Permissions

### Location Permissions
- `ACCESS_FINE_LOCATION`: Required for precise location access
- `ACCESS_COARSE_LOCATION`: Required for approximate location access
- `ACCESS_BACKGROUND_LOCATION`: Required for location access when app is in background (Android 10+)

### Notification Permissions
- `POST_NOTIFICATIONS`: Required for showing notifications (Android 13+)
- `VIBRATE`: Required for notification vibration
- `WAKE_LOCK`: Required for waking device for notifications

### Background Processing Permissions
- `FOREGROUND_SERVICE`: Required for running foreground services
- `FOREGROUND_SERVICE_LOCATION`: Required for location-based foreground services (Android 14+)
- `RECEIVE_BOOT_COMPLETED`: Required for restarting services after device reboot
- `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS`: Optional, for better background processing

### Services and Receivers
- **WorkmanagerPlugin$BackgroundService**: Handles background location monitoring
- **WorkmanagerPlugin$BootReceiver**: Restarts background services after device reboot

## iOS Permissions

### Location Permissions
- `NSLocationWhenInUseUsageDescription`: Location access when app is active
- `NSLocationAlwaysAndWhenInUseUsageDescription`: Continuous location access
- `NSLocationAlwaysUsageDescription`: Background location access (legacy)

### Background Modes
- `location`: Background location updates
- `background-processing`: Background task processing
- `background-fetch`: Periodic background updates
- `remote-notification`: Push notification handling

### Entitlements
- `com.apple.developer.background-modes`: Enables background processing
- `com.apple.developer.location.background`: Enables background location access
- `aps-environment`: Push notification environment

## Permission Request Flow

### Android
1. Request `ACCESS_FINE_LOCATION` and `ACCESS_COARSE_LOCATION`
2. If granted, request `ACCESS_BACKGROUND_LOCATION` (Android 10+)
3. Request `POST_NOTIFICATIONS` (Android 13+)
4. Optionally request battery optimization exemption

### iOS
1. Request "When In Use" location permission first
2. Request "Always" location permission for background access
3. Request notification permissions
4. Background modes are automatically enabled via Info.plist

## User Experience Considerations

### Permission Rationale
- Clearly explain why each permission is needed
- Provide context about automatic zone validation benefits
- Offer manual validation as an alternative

### Battery Optimization
- Inform users about potential battery impact
- Provide guidance on battery optimization settings
- Implement efficient location monitoring strategies

### Privacy
- Emphasize that location data is used only for zone validation
- Explain data retention and sharing policies
- Provide opt-out mechanisms

## Testing Considerations

### Android Testing
- Test on different Android versions (API 21-35)
- Test with different battery optimization settings
- Test background service persistence across app kills
- Test permission flows on different OEM skins

### iOS Testing
- Test on different iOS versions
- Test background location accuracy and battery usage
- Test app store review compliance
- Test with different location permission states

## Compliance Notes

### Android
- Follow Android's background location policy
- Implement proper foreground service notifications
- Handle permission denials gracefully

### iOS
- Follow Apple's location usage guidelines
- Provide clear usage descriptions
- Implement proper background task management
- Ensure App Store review compliance

## Troubleshooting

### Common Issues
1. **Background location not working**: Check battery optimization settings
2. **Notifications not showing**: Verify notification permissions and channels
3. **Service stops after app kill**: Ensure proper WorkManager configuration
4. **iOS background location stops**: Check background app refresh settings

### Debug Commands
```bash
# Android: Check background services
adb shell dumpsys activity services

# Android: Check location permissions
adb shell dumpsys package com.pego.respublicaseguridad.respublicaseguridad

# iOS: Check location authorization
# Use Xcode's device console and location simulation
```
