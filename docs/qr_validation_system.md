# QR Validation System Documentation

## Overview

The QR Validation System is a secure, proximity-based community validation mechanism that allows users to validate each other's zones through QR code scanning. This system ensures that validators are physically present in the zone being validated, providing an additional layer of security and authenticity to the zone verification process.

## Architecture

The system follows Clean Architecture principles with the following layers:

### Domain Layer
- **Entities**: Core business objects (QRValidationSessionEntity, QRTokenEntity)
- **Enums**: Status definitions and validation types
- **Business Rules**: Validation logic and security constraints
- **Use Cases**: Application-specific business rules

### Data Layer
- **Repositories**: Data access abstractions
- **Data Sources**: Firebase Realtime Database integration
- **Models**: Data transfer objects

### Presentation Layer
- **BLoC**: State management for QR validation flows
- **Screens**: User interface components
- **Widgets**: Reusable UI elements

## Key Features

### 1. Secure QR Token Generation
- **Ephemeral Tokens**: 30-60 second expiration time
- **Cryptographic Security**: HMAC-SHA256 signatures
- **Replay Attack Prevention**: One-time use tokens with nonce
- **Automatic Refresh**: Seamless token regeneration

### 2. Proximity Verification
- **GPS-Based Validation**: Ensures users are within 50-100 meters
- **Location Accuracy Checks**: Minimum 50-meter accuracy requirement
- **Zone Boundary Validation**: Confirms users are within the defined zone
- **Real-time Location Updates**: Continuous proximity monitoring

### 3. Real-time Session Management
- **Firebase Realtime Database**: Live session synchronization
- **Session Lifecycle**: Creation, joining, validation, completion
- **Timeout Handling**: Automatic session expiration
- **State Synchronization**: Real-time updates between participants

### 4. Comprehensive Security
- **Token Validation**: Multi-layer security checks
- **User Authentication**: Firebase Auth integration
- **Permission Validation**: Role-based access control
- **Audit Logging**: Complete activity tracking

## Validation Flow

### 1. Session Initiation
```
Zone Owner → Create QR Validation Session
           → Generate Secure Session ID
           → Set Expiration Time (10 minutes)
           → Wait for Validator
```

### 2. Validator Joining
```
Validator → Scan Zone Owner's QR Code
         → Join Validation Session
         → Verify Proximity to Zone
         → Generate Own QR Token
```

### 3. Mutual Validation
```
Both Users → Generate QR Tokens
          → Scan Each Other's Tokens
          → Verify Proximity Between Users
          → Complete Validation Process
```

### 4. Completion
```
System → Verify All Conditions Met
       → Update Zone Validation Count
       → Log Audit Events
       → Clean Up Session Data
```

## Security Measures

### Token Security
- **HMAC Signatures**: Prevent token tampering
- **Nonce Values**: Prevent replay attacks
- **Timestamp Validation**: Ensure token freshness
- **Expiration Checks**: Automatic token invalidation

### Proximity Security
- **GPS Accuracy**: Minimum 50-meter accuracy
- **Distance Validation**: Maximum 100-meter separation
- **Zone Boundary**: Users must be within zone radius
- **Location Freshness**: Recent GPS readings required

### Session Security
- **User Authorization**: Only session participants allowed
- **Session Timeouts**: Automatic cleanup after 10 minutes
- **Status Validation**: Prevent invalid state transitions
- **Rate Limiting**: Prevent abuse and spam

## Error Handling

### Network Errors
- **Automatic Retry**: Exponential backoff strategy
- **Circuit Breaker**: Prevent cascading failures
- **Offline Support**: Graceful degradation
- **User Feedback**: Clear error messages with recovery suggestions

### Location Errors
- **Permission Handling**: Request location permissions
- **Accuracy Validation**: Reject poor GPS readings
- **Service Availability**: Check location services
- **Fallback Options**: Alternative validation methods

### Security Errors
- **Token Validation**: Comprehensive security checks
- **Replay Detection**: Prevent token reuse
- **Signature Verification**: Ensure data integrity
- **Audit Logging**: Track security events

## API Reference

### Core Services

#### QRValidationCloudSecurityService
```dart
// Create secure token (server-side)
Future<SecureTokenCreationResult> createSecureToken({
  required String sessionId,
  required String zoneId,
  required String purpose,
  required String validatorUserId,
})

// Validate secure token (server-side)
Future<SecureTokenValidationResult> validateSecureToken({
  required Map<String, dynamic> token,
  required String expectedSessionId,
  required String expectedPurpose,
})

// Validate user permissions (client-side check)
Future<SecurityValidationResult> validateUserPermissions({
  required String userId,
  required String zoneId,
  required String sessionId,
  required bool isInitiator,
})
```

#### QRValidationAuditService
```dart
// Log security events
Future<void> logSecurityEvent(SecurityAuditEvent event)

// Get audit logs
Future<List<SecurityAuditEvent>> getUserAuditLogs({
  required String userId,
  DateTime? startDate,
  DateTime? endDate,
})
```

#### ProximityVerificationService
```dart
// Get current location
Future<ProximityDataEntity?> getCurrentLocationForVerification()

// Validate proximity
bool validateProximity({
  required ProximityDataEntity userLocation,
  required ProximityDataEntity targetLocation,
  double maxDistance = 100.0,
})
```

### Use Cases

#### CreateQRValidationSessionUseCase
```dart
Future<Either<Failure, QRValidationSessionResult>> call(
  CreateQRValidationSessionParams params
)
```

#### JoinQRValidationSessionUseCase
```dart
Future<Either<Failure, QRValidationSessionResult>> call(
  JoinQRValidationSessionParams params
)
```

#### GenerateQRTokenUseCase
```dart
Future<Either<Failure, QRTokenEntity>> call(
  GenerateQRTokenParams params
)
```

#### ValidateQRScanUseCase
```dart
Future<Either<Failure, QRScanValidationResult>> call(
  ValidateQRScanParams params
)
```

## Configuration

### Constants
```dart
class ZoneConstants {
  static const Duration qrSessionDuration = Duration(minutes: 10);
  static const Duration qrTokenDuration = Duration(seconds: 45);
  static const double maxProximityDistance = 100.0;
  static const double minLocationAccuracy = 50.0;
  static const int maxActiveSessionsPerUser = 5;
  static const int maxDailyValidationsPerUser = 10;
}
```

### Firebase Security Rules
```javascript
// Realtime Database Rules
{
  "rules": {
    "qr_validation_sessions": {
      "$sessionId": {
        ".read": "auth != null && (data.child('initiatorUserId').val() == auth.uid || data.child('validatorUserId').val() == auth.uid)",
        ".write": "auth != null && (data.child('initiatorUserId').val() == auth.uid || data.child('validatorUserId').val() == auth.uid)"
      }
    },
    "qr_tokens": {
      "$tokenId": {
        ".read": "auth != null && data.child('userId').val() == auth.uid",
        ".write": "auth != null && data.child('userId').val() == auth.uid"
      }
    }
  }
}
```

## Testing

### Unit Tests
- Business rule validation
- Security service functionality
- Token generation and validation
- Proximity calculations
- Error handling scenarios

### Integration Tests
- End-to-end validation flow
- Firebase integration
- Real-time synchronization
- Error recovery mechanisms

### Security Tests
- Token tampering attempts
- Replay attack prevention
- Permission validation
- Audit logging verification

## Deployment

### Prerequisites
- Firebase project with Realtime Database
- Firebase Auth configuration
- Location permissions in app manifest
- Network security configuration

### Environment Setup
1. Configure Firebase project
2. Set up Realtime Database rules
3. Deploy Cloud Functions
4. Configure app permissions
5. Test validation flow

## Monitoring and Analytics

### Key Metrics
- Validation success rate
- Session completion time
- Error frequency by type
- Security incident count
- User engagement metrics

### Audit Logging
- All validation attempts
- Security violations
- Token generation/usage
- Session lifecycle events
- Error occurrences

## Troubleshooting

### Common Issues
1. **Location Permission Denied**: Guide user to app settings
2. **Poor GPS Accuracy**: Suggest moving to open area
3. **Network Connectivity**: Implement retry mechanisms
4. **Token Expiration**: Automatic refresh handling
5. **Session Timeout**: Clear error messages and restart options

### Debug Tools
- Comprehensive logging
- Error tracking integration
- Performance monitoring
- Security audit reports

## Future Enhancements

### Planned Features
- Offline validation support
- Enhanced proximity algorithms
- Multi-factor validation
- Advanced analytics dashboard
- Machine learning fraud detection

### Performance Optimizations
- Token caching strategies
- Database query optimization
- Real-time connection pooling
- Background processing improvements

## Implementation Notes

### Best Practices
1. **Always validate user permissions** before allowing QR validation operations
2. **Use retry mechanisms** for network operations with exponential backoff
3. **Implement proper error handling** with user-friendly messages and recovery suggestions
4. **Log all security events** for audit and monitoring purposes
5. **Validate proximity** before allowing token scanning
6. **Clean up expired sessions** to prevent memory leaks
7. **Use secure token generation** with proper entropy and signatures

### Security Considerations
- Never store sensitive data in local storage
- Always validate tokens on the server side
- Implement rate limiting to prevent abuse
- Use HTTPS for all network communications
- Regularly rotate encryption keys
- Monitor for suspicious activity patterns
- Implement proper session management

### Performance Tips
- Cache frequently accessed data
- Use connection pooling for database operations
- Implement lazy loading for UI components
- Optimize image and QR code generation
- Use background processing for heavy operations
- Implement proper memory management
