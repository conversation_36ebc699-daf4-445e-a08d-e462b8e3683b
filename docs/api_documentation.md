# Notification System API Documentation

## Overview

This document provides comprehensive API documentation for the Location-Based Incident Notification System. The API is designed following clean architecture principles with clear separation of concerns.

## Core Services

### NotificationPreferencesService

Manages user notification preferences and privacy settings.

#### Methods

##### `getUserPreferences(String userId)`
Retrieves user notification preferences.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, NotificationPreferencesEntity>>`

**Example:**
```dart
final result = await notificationPreferencesService.getUserPreferences('user123');
result.fold(
  (failure) => print('Error: ${failure.message}'),
  (preferences) => print('Radius: ${preferences.notificationRadiusKm}km'),
);
```

##### `updateNotificationRadius(String userId, double radiusKm)`
Updates the notification radius for a user.

**Parameters:**
- `userId` (String): Unique user identifier
- `radiusKm` (double): Notification radius in kilometers (0.5-10.0)

**Returns:**
- `Future<Either<Failure, void>>`

**Validation:**
- Radius must be between 0.5km and 10.0km
- User must exist in the system

##### `grantLocationTrackingConsent(String userId)`
Grants location tracking consent for a user.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, void>>`

**Side Effects:**
- Creates consent record with timestamp
- Enables location-based notifications
- Triggers audit log entry

##### `shouldReceiveNotifications(String userId, String incidentType)`
Determines if a user should receive notifications for a specific incident type.

**Parameters:**
- `userId` (String): Unique user identifier
- `incidentType` (String): Type of incident (e.g., 'robbery', 'assault')

**Returns:**
- `Future<Either<Failure, bool>>`

**Logic:**
1. Check if notifications are enabled
2. Verify location tracking consent
3. Check incident type preferences
4. Validate quiet hours

### IncidentDetectionService

Monitors user location and detects nearby incidents.

#### Methods

##### `startMonitoring(String userId)`
Starts location monitoring and incident detection for a user.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, void>>`

**Prerequisites:**
- User must have granted location tracking consent
- Location services must be available
- Network connectivity required

**Configuration:**
- Update interval: 2 minutes
- Distance filter: 100 meters
- Detection interval: 5 minutes

##### `checkIncidentsNearLocation({required String userId, required LocationEntity location, double? customRadiusKm})`
Checks for incidents near a specific location.

**Parameters:**
- `userId` (String): Unique user identifier
- `location` (LocationEntity): Location to check around
- `customRadiusKm` (double?, optional): Custom radius override

**Returns:**
- `Future<Either<Failure, List<IncidentEntity>>>`

**Performance:**
- Uses geospatial indexing for efficient queries
- Filters by user preferences
- Caches results for 5 minutes

##### `triggerIncidentDetection(String userId)`
Manually triggers incident detection for current location.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, List<IncidentEntity>>>`

**Requirements:**
- Monitoring must be active
- Current location must be available

#### Properties

##### `isMonitoring`
Returns whether incident monitoring is currently active.

**Type:** `bool`

##### `monitoringStatus`
Returns the current monitoring status.

**Type:** `IncidentMonitoringStatus`

**Values:**
- `stopped`: Monitoring is not active
- `waitingForLocation`: Monitoring active but no location available
- `staleLocation`: Location data is outdated (>10 minutes)
- `active`: Monitoring active with fresh location data

### PrivacySecurityService

Handles privacy protection and security features.

#### Methods

##### `encryptLocationData(LocationEntity location)`
Encrypts location data for secure storage.

**Parameters:**
- `location` (LocationEntity): Location data to encrypt

**Returns:**
- `Either<Failure, String>`: Encrypted data string

**Security:**
- Uses AES encryption with salted hashing
- Generates unique salt for each encryption
- Base64 encoding for storage

##### `applyLocationFuzzing(LocationEntity location)`
Applies location fuzzing for privacy protection.

**Parameters:**
- `location` (LocationEntity): Original location

**Returns:**
- `LocationEntity`: Fuzzed location with random offset

**Privacy:**
- Random offset within 50-meter radius
- Uses secure random number generator
- Preserves general area while protecting exact location

##### `validateLocationConsent(String userId, Map<String, dynamic> consentData)`
Validates user location tracking consent.

**Parameters:**
- `userId` (String): Unique user identifier
- `consentData` (Map): Consent record data

**Returns:**
- `Either<Failure, bool>`: Validation result

**Validation Rules:**
- Consent must be explicitly granted
- Consent must be recent (within 1 year)
- Consent version must be current

##### `createConsentRecord(String userId, bool hasConsent)`
Creates a consent record for audit purposes.

**Parameters:**
- `userId` (String): Unique user identifier
- `hasConsent` (bool): Consent status

**Returns:**
- `Map<String, dynamic>`: Consent record

**Record Fields:**
- `userId`: User identifier
- `hasConsent`: Consent status
- `timestamp`: ISO 8601 timestamp
- `version`: Consent policy version
- `ipAddress`: Hashed IP address
- `consentMethod`: Method of consent collection

### DataDeletionService

Handles GDPR-compliant data deletion and export.

#### Methods

##### `deleteAllUserData(String userId)`
Deletes all user data for GDPR compliance.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, DataDeletionReport>>`

**Deletion Scope:**
- Notification preferences
- Notification history
- Location tracking data
- Audit logs (anonymized)
- Cached data

##### `exportUserData(String userId)`
Exports user data in machine-readable format.

**Parameters:**
- `userId` (String): Unique user identifier

**Returns:**
- `Future<Either<Failure, UserDataExport>>`

**Export Format:**
- JSON structure with all user data
- Includes metadata and timestamps
- Excludes sensitive system data

### PerformanceOptimizationService

Optimizes system performance and resource usage.

#### Methods

##### `cacheLocation(String userId, LocationEntity location)`
Caches location data for efficient access.

**Parameters:**
- `userId` (String): Unique user identifier
- `location` (LocationEntity): Location to cache

**Cache Policy:**
- LRU eviction when cache is full
- 5-minute expiry time
- Maximum 1000 entries

##### `getOptimalLocationUpdateInterval({required String userId, required double batteryLevel, required bool isMoving, required DateTime lastIncidentTime})`
Calculates optimal location update interval based on context.

**Parameters:**
- `userId` (String): Unique user identifier
- `batteryLevel` (double): Current battery level (0.0-1.0)
- `isMoving` (bool): Whether user is currently moving
- `lastIncidentTime` (DateTime): Time of last incident in area

**Returns:**
- `Duration`: Optimal update interval

**Optimization Logic:**
- Longer intervals for low battery
- Shorter intervals when moving
- Increased frequency after recent incidents
- Bounds: 1-30 minutes

##### `recordMetric(PerformanceMetricType type, double value, {Map<String, dynamic>? metadata})`
Records performance metrics for monitoring.

**Parameters:**
- `type` (PerformanceMetricType): Type of metric
- `value` (double): Metric value
- `metadata` (Map?, optional): Additional metadata

**Metric Types:**
- `responseTime`: API response time in milliseconds
- `batteryUsage`: Battery usage percentage
- `memoryUsage`: Memory usage in MB
- `networkLatency`: Network latency in milliseconds
- `cacheHit`/`cacheMiss`: Cache performance
- `batchSize`: Notification batch size
- `locationAccuracy`: GPS accuracy in meters

## Data Models

### NotificationPreferencesEntity

```dart
class NotificationPreferencesEntity {
  final String userId;                          // Unique user identifier
  final bool isLocationNotificationsEnabled;   // Master notification toggle
  final double notificationRadiusKm;           // Notification radius (0.5-10.0)
  final List<String> enabledIncidentTypes;     // Enabled incident types
  final QuietHoursEntity? quietHours;          // Quiet hours configuration
  final bool isLocationTrackingConsented;      // Location consent status
  final NotificationPriorityLevel priorityLevel; // Notification priority
  final bool vibrationEnabled;                 // Vibration setting
  final bool soundEnabled;                     // Sound setting
  final DateTime createdAt;                    // Creation timestamp
  final DateTime updatedAt;                    // Last update timestamp
}
```

### IncidentNotificationEntity

```dart
class IncidentNotificationEntity {
  final String id;                             // Unique notification ID
  final String userId;                         // Target user ID
  final String incidentId;                     // Related incident ID
  final String incidentType;                   // Type of incident
  final String incidentTitle;                  // Incident title
  final String incidentDescription;            // Incident description
  final LocationEntity incidentLocation;       // Incident location
  final LocationEntity userLocationAtTime;     // User location when notified
  final double distanceFromUserMeters;         // Distance in meters
  final DateTime incidentTimestamp;            // When incident occurred
  final DateTime notificationSentAt;           // When notification was sent
  final IncidentNotificationStatus status;     // Notification status
  final IncidentNotificationPriority priority; // Notification priority
  final Map<String, dynamic>? metadata;        // Additional metadata
}
```

### LocationEntity

```dart
class LocationEntity {
  final double latitude;                       // Latitude coordinate
  final double longitude;                      // Longitude coordinate
  final String address;                        // Human-readable address
}
```

## Error Handling

### Failure Types

#### `ValidationFailure`
Indicates validation errors in input data.

**Common Causes:**
- Invalid notification radius
- Missing required fields
- Invalid incident types
- Expired consent

#### `ServerFailure`
Indicates server-side or network errors.

**Common Causes:**
- Database connection issues
- Network timeouts
- Firebase service errors
- Cloud function failures

#### `NotFoundFailure`
Indicates requested resource was not found.

**Common Causes:**
- User preferences not found
- Incident not found
- Invalid user ID

### Error Response Format

```dart
abstract class Failure {
  final String message;
  const Failure(this.message);
}
```

## Rate Limiting

### Notification Frequency
- Maximum 10 notifications per hour per user
- Burst limit: 3 notifications per minute
- Critical incidents bypass rate limits

### API Rate Limits
- Location updates: 1 per minute per user
- Preference updates: 10 per hour per user
- Data export: 1 per day per user

## Security Considerations

### Authentication
- All API calls require valid user authentication
- JWT tokens with 24-hour expiry
- Refresh token mechanism for long-lived sessions

### Authorization
- Users can only access their own data
- Admin users have elevated permissions
- Audit logging for all data access

### Data Protection
- All location data encrypted at rest
- TLS 1.3 for data in transit
- Regular security audits and penetration testing

## Performance Guidelines

### Best Practices
1. Cache frequently accessed data
2. Use batch operations when possible
3. Implement proper error handling
4. Monitor performance metrics
5. Optimize database queries

### Resource Limits
- Maximum 1000 cached locations per device
- Batch size limit: 50 notifications
- Query timeout: 30 seconds
- Memory limit: 100MB for notification service

## Testing

### Unit Tests
- 95%+ code coverage required
- Mock external dependencies
- Test error conditions
- Validate edge cases

### Integration Tests
- End-to-end notification flows
- Database integration
- Firebase integration
- Performance testing

### Load Testing
- 1000 concurrent users
- 10,000 notifications per minute
- Geographic distribution testing
- Failover scenarios
