<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña - %APP_NAME%</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .header {
            background: linear-gradient(135deg, #132e4f 0%, #2d4f7e 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo-container {
            position: relative;
            z-index: 2;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .header-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 400;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #132e4f;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .email-highlight {
            color: #132e4f;
            font-weight: 600;
            background-color: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #132e4f 0%, #2d4f7e 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(19, 46, 79, 0.3);
            border: none;
            cursor: pointer;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(19, 46, 79, 0.4);
        }
        
        .link-fallback {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #132e4f;
        }
        
        .link-fallback p {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .link-text {
            word-break: break-all;
            color: #132e4f;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            background-color: #ffffff;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }
        
        .security-notice {
            background-color: #fef3cd;
            border: 1px solid #fde68a;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .security-notice h3 {
            color: #92400e;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .security-notice p {
            color: #a16207;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .app-name {
            color: #132e4f;
            font-weight: 600;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 30px 0;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 20px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .cta-button {
                padding: 14px 28px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo-container">
                <div class="logo">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 768 767.999994" preserveAspectRatio="xMidYMid meet">
                        <defs>
                            <clipPath id="9267b66fc9"><path d="M 0 0 L 517.972656 0 L 517.972656 615.875 L 0 615.875 Z M 0 0 "/></clipPath>
                            <clipPath id="fcda4c8f2b"><path d="M 112.859375 222 L 405 222 L 405 505 L 112.859375 505 Z M 112.859375 222 "/></clipPath>
                            <clipPath id="f9d37c89a9"><path d="M 197.871094 165 L 320.074219 165 L 320.074219 332 L 197.871094 332 Z M 197.871094 165 "/></clipPath>
                        </defs>
                        <g id="da630f3fdc">
                            <path style="stroke:none;fill-rule:nonzero;fill:#ffffff;fill-opacity:1;" d="M 258.988281 567.121094 C 258.140625 566.632812 257.226562 566.113281 256.316406 565.558594 C 191.789062 527.382812 138.042969 472.660156 100.910156 407.253906 C 63.808594 341.945312 44.234375 267.648438 44.234375 192.4375 L 44.234375 105.953125 L 258.988281 45.957031 L 473.738281 105.953125 L 473.738281 192.4375 C 473.738281 267.679688 454.128906 341.945312 417.0625 407.253906 C 379.929688 472.660156 326.183594 527.382812 261.65625 565.558594 C 260.714844 566.113281 259.832031 566.632812 258.988281 567.121094 Z M 258.988281 567.121094 "/>
                            <g clip-rule="nonzero" clip-path="url(#9267b66fc9)">
                                <path style="stroke:none;fill-rule:nonzero;fill:#ffffff;fill-opacity:0.9;" d="M 258.988281 0.0625 L 0 72.40625 L 0 192.4375 C 0 361.128906 88.597656 517.742188 233.808594 603.636719 C 246.511719 611.160156 255.535156 615.65625 258.988281 615.65625 C 262.40625 615.65625 271.429688 611.160156 284.164062 603.636719 C 429.375 517.773438 517.972656 361.128906 517.972656 192.4375 L 517.972656 72.40625 Z M 495.855469 192.4375 C 495.855469 271.492188 475.269531 349.535156 436.28125 418.164062 C 397.226562 486.925781 340.746094 544.484375 272.894531 584.613281 C 268.269531 587.347656 264.261719 589.597656 261.007812 591.289062 L 258.988281 592.332031 L 256.96875 591.289062 C 253.710938 589.597656 249.703125 587.347656 245.078125 584.613281 C 177.230469 544.484375 120.714844 486.925781 81.691406 418.164062 C 42.703125 349.535156 22.117188 271.492188 22.117188 192.4375 L 22.117188 89.179688 L 258.988281 23.023438 L 495.855469 89.179688 Z M 495.855469 192.4375 "/>
                            </g>
                            <g clip-rule="nonzero" clip-path="url(#f9d37c89a9)">
                                <path style="stroke:none;fill-rule:nonzero;fill:#ff0404;fill-opacity:1;" d="M 258.964844 257.410156 C 240.882812 257.410156 226.210938 242.742188 226.210938 224.640625 C 226.210938 206.5625 240.882812 191.894531 258.964844 191.894531 C 277.066406 191.894531 291.710938 206.5625 291.710938 224.640625 C 291.710938 242.742188 277.066406 257.410156 258.964844 257.410156 Z M 258.964844 165.34375 C 225.230469 165.34375 197.882812 192.691406 197.882812 226.425781 C 197.882812 260.179688 258.964844 331.167969 258.964844 331.167969 C 258.964844 331.167969 320.066406 260.179688 320.066406 226.425781 C 320.066406 192.691406 292.71875 165.34375 258.964844 165.34375 "/>
                            </g>
                        </g>
                    </svg>
                </div>
            </div>
            <h1 class="header-title">Restablecer Contraseña</h1>
            <p class="header-subtitle">Solicitud de restablecimiento de contraseña</p>
        </div>
        
        <div class="content">
            <div class="greeting">¡Hola!</div>
            
            <p class="message">
                Hemos recibido una solicitud para restablecer la contraseña de tu cuenta de <strong>%APP_NAME%</strong> 
                asociada con el correo electrónico <span class="email-highlight">%EMAIL%</span>.
            </p>
            
            <div class="cta-container">
                <a href="%LINK%" class="cta-button">Restablecer Contraseña</a>
            </div>
            
            <div class="link-fallback">
                <p><strong>¿No puedes hacer clic en el botón?</strong> Copia y pega el siguiente enlace en tu navegador:</p>
                <div class="link-text">%LINK%</div>
            </div>
            
            <div class="security-notice">
                <h3>🔒 Aviso de Seguridad</h3>
                <p>
                    Si no solicitaste restablecer tu contraseña, puedes ignorar este correo de forma segura. 
                    Tu cuenta permanecerá protegida y no se realizarán cambios.
                </p>
            </div>
            
            <div class="divider"></div>
            
            <p class="message">
                Este enlace expirará en <strong>24 horas</strong> por motivos de seguridad. 
                Si necesitas un nuevo enlace, puedes solicitar otro restablecimiento desde la aplicación.
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Gracias por confiar en nosotros,<br>
                <span class="app-name">El equipo de %APP_NAME%</span>
            </p>
            <p class="footer-text" style="font-size: 12px; margin-top: 20px; color: #94a3b8;">
                Este es un correo automático, por favor no respondas a este mensaje.
            </p>
        </div>
    </div>
</body>
</html>
