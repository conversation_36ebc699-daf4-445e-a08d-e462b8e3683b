{"rules": {"qr_validation_sessions": {".read": "auth != null", ".indexOn": ["initiatorUserId", "validatorUserId", "status"], "$sessionId": {".read": "auth != null && (!data.exists() || data.child('initiatorUserId').val() == auth.uid || data.child('validatorUserId').val() == auth.uid)", ".write": "auth != null && (!data.exists() || data.child('initiatorUserId').val() == auth.uid || data.child('validatorUserId').val() == auth.uid)", ".validate": "newData.hasChildren(['id', 'zoneId', 'initiatorUserId', 'status', 'createdAt', 'expiresAt'])", "id": {".validate": "newData.isString() && newData.val() == $sessionId"}, "zoneId": {".validate": "newData.isString() && newData.val().length > 0"}, "initiatorUserId": {".validate": "newData.isString() && newData.val().length > 0"}, "validatorUserId": {".validate": "newData.isString() && newData.val().length > 0"}, "status": {".validate": "newData.isString() && newData.val().matches(/^(pending|active|completed|expired|failed|cancelled)$/)"}, "createdAt": {".validate": "newData.isString()"}, "expiresAt": {".validate": "newData.isString()"}, "completedAt": {".validate": "newData.isString()"}, "failureReason": {".validate": "newData.isString()"}, "metadata": {".validate": "newData.hasChildren()"}}}, "qr_tokens": {".read": "auth != null", ".indexOn": ["userId", "sessionId", "status"], "$tokenId": {".read": "auth != null", ".write": "auth != null", ".validate": "newData.hasChildren(['id', 'sessionId', 'userId', 'encryptedData', 'status', 'createdAt', 'expiresAt'])", "id": {".validate": "newData.isString() && newData.val() == $tokenId"}, "sessionId": {".validate": "newData.isString() && newData.val().length > 0"}, "userId": {".validate": "newData.isString() && newData.val().length > 0"}, "encryptedData": {".validate": "newData.isString() && newData.val().length > 0"}, "status": {".validate": "newData.isString() && newData.val().matches(/^(active|expired|used|invalid)$/)"}, "createdAt": {".validate": "newData.isString()"}, "expiresAt": {".validate": "newData.isString()"}, "usedAt": {".validate": "newData.isString()"}, "usedByUserId": {".validate": "newData.isString() && newData.val().length > 0"}, "metadata": {".validate": "newData.hasChildren()"}}}, "qr_validation_events": {"$zoneId": {"$eventId": {".read": "auth != null", ".write": "auth != null", ".validate": "newData.hasChildren(['eventType', 'timestamp', 'eventData'])", "eventType": {".validate": "newData.isString() && newData.val().length > 0"}, "timestamp": {".validate": "newData.isString()"}, "eventData": {".validate": "newData.hasChildren()"}}}}, "proximity_verifications": {"$zoneId": {".read": "auth != null", ".write": "auth != null", ".validate": "newData.hasChildren(['zoneId', 'isVerified', 'timestamp'])", "zoneId": {".validate": "newData.isString() && newData.val() == $zoneId"}, "isVerified": {".validate": "newData.isBoolean()"}, "timestamp": {".validate": "newData.isString()"}, "initiatorLocation": {".validate": "newData.hasChildren(['latitude', 'longitude', 'accuracy', 'timestamp'])", "latitude": {".validate": "newData.isNumber() && newData.val() >= -90 && newData.val() <= 90"}, "longitude": {".validate": "newData.isNumber() && newData.val() >= -180 && newData.val() <= 180"}, "accuracy": {".validate": "newData.isNumber() && newData.val() >= 0"}, "timestamp": {".validate": "newData.isString()"}}, "validatorLocation": {".validate": "newData.hasChildren(['latitude', 'longitude', 'accuracy', 'timestamp'])", "latitude": {".validate": "newData.isNumber() && newData.val() >= -90 && newData.val() <= 90"}, "longitude": {".validate": "newData.isNumber() && newData.val() >= -180 && newData.val() <= 180"}, "accuracy": {".validate": "newData.isNumber() && newData.val() >= 0"}, "timestamp": {".validate": "newData.isString()"}}}}, "user_validation_stats": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId", "dailyValidationCount": {".validate": "newData.isNumber() && newData.val() >= 0"}, "lastValidationDate": {".validate": "newData.isString()"}, "totalValidations": {".validate": "newData.isNumber() && newData.val() >= 0"}, "activeSessionsCount": {".validate": "newData.isNumber() && newData.val() >= 0"}}}, "rate_limits": {".read": false, ".write": false}, "secure_tokens": {".read": false, ".write": false}, "consumed_tokens": {".read": false, ".write": false}, "nonce_registry": {".read": false, ".write": false}, "session_tokens": {".read": false, ".write": false}, "location_updates": {"$zoneId": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId", ".indexOn": ["timestamp"], "$updateId": {".validate": "newData.hasChildren(['timestamp', 'latitude', 'longitude', 'accuracy', 'isWithinZone'])", "timestamp": {".validate": "newData.isString()"}, "latitude": {".validate": "newData.isNumber() && newData.val() >= -90 && newData.val() <= 90"}, "longitude": {".validate": "newData.isNumber() && newData.val() >= -180 && newData.val() <= 180"}, "accuracy": {".validate": "newData.isNumber() && newData.val() >= 0"}, "isWithinZone": {".validate": "newData.isBoolean()"}, "distance": {".validate": "newData.isNumber() && newData.val() >= 0"}}}}}, "automatic_validation_tracking": {"$trackingId": {".read": false, ".write": false}}, "limited_tracking": {"$userId": {".read": false, ".write": false}}, "incident_markers": {".read": "auth != null", ".indexOn": ["timestamp", "geo<PERSON>h", "categoryKey", "severity", "isAnonymous", "postedAt", "expiresAt"], "$incidentId": {".read": "auth != null", ".write": "auth != null", ".validate": "newData.hasChildren(['incidentId', 'categoryKey', 'categoryTitle', 'severity', 'location', 'postedAt', 'isAnonymous', 'iconName', 'color', 'geohash', 'timestamp', 'expiresAt'])", "incidentId": {".validate": "newData.isString() && newData.val() == $incidentId"}, "categoryKey": {".validate": "newData.isString() && newData.val().length > 0"}, "categoryTitle": {".validate": "newData.isString() && newData.val().length > 0"}, "severity": {".validate": "newData.isString() && (newData.val() == 'Leve' || newData.val() == 'Grave')"}, "location": {".validate": "newData.hasChildren(['latitude', 'longitude', 'address'])", "latitude": {".validate": "newData.isNumber() && newData.val() >= -90 && newData.val() <= 90"}, "longitude": {".validate": "newData.isNumber() && newData.val() >= -180 && newData.val() <= 180"}, "address": {".validate": "newData.isString()"}}, "postedAt": {".validate": "newData.isString()"}, "isAnonymous": {".validate": "newData.isBoolean()"}, "iconName": {".validate": "newData.isString() && newData.val().length > 0"}, "color": {".validate": "newData.isString() && newData.val().length > 0"}, "geohash": {".validate": "newData.isString() && newData.val().length > 0"}, "timestamp": {".validate": "newData.isNumber() && newData.val() > 0"}, "userId": {".validate": "!newData.exists() || (newData.isString() && newData.val().length > 0)"}, "zoneId": {".validate": "!newData.exists() || (newData.isString() && newData.val().length > 0)"}, "subcategoryKey": {".validate": "!newData.exists() || (newData.isString() && newData.val().length > 0)"}, "subcategoryTitle": {".validate": "!newData.exists() || (newData.isString() && newData.val().length > 0)"}, "isHighSeverity": {".validate": "!newData.exists() || newData.isBoolean()"}, "expiresAt": {".validate": "newData.isNumber() && newData.val() > 0"}}}, "zone_incident_markers": {".read": "auth != null", ".indexOn": ["timestamp", "categoryKey", "severity"], "$zoneId": {".read": "auth != null", ".indexOn": ["timestamp"], "$incidentId": {".read": "auth != null", ".write": "auth != null && (!data.exists() || data.child('userId').val() == auth.uid || data.child('isAnonymous').val() == false) && newData.child('timestamp').val() > (now - 259200000)", ".validate": "newData.hasChildren(['incidentId', 'categoryKey', 'categoryTitle', 'severity', 'location', 'postedAt', 'isAnonymous', 'iconName', 'color', 'geohash', 'timestamp']) && newData.child('timestamp').val() <= now"}}}, "incident_marker_rate_limits": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId", "dailyCount": {".validate": "newData.isNumber() && newData.val() >= 0 && newData.val() <= 50"}, "lastUpdate": {".validate": "newData.isString()"}, "hourlyCount": {".validate": "newData.isNumber() && newData.val() >= 0 && newData.val() <= 10"}}}, ".read": false, ".write": false}}