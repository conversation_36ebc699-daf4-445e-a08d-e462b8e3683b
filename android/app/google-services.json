{"project_info": {"project_number": "660923458040", "project_id": "res-p-s-d8is3t", "storage_bucket": "res-p-s-d8is3t.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:660923458040:android:78a5605f200d427256360d", "android_client_info": {"package_name": "com.mycompany.resps"}}, "oauth_client": [{"client_id": "660923458040-gnfno2mq7l2qkcqjurndiaccg917c44r.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycompany.resps", "certificate_hash": "9bbb3186b2189ef16d8f272bdab59362c395cbb4"}}, {"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBl2Ta4vaHjYhOqw9-2gz1XmpNetP2XLwc"}, {"current_key": "AIzaSyB0IUd-FyOMNA6aWUiutABscR_oz6oeic4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}, {"client_id": "660923458040-5ob5o4v0q07flkjhea881913mq1rs113.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycompany.resps"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:660923458040:android:a58781b106f6a3c456360d", "android_client_info": {"package_name": "com.pego.respublicaseg"}}, "oauth_client": [{"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBl2Ta4vaHjYhOqw9-2gz1XmpNetP2XLwc"}, {"current_key": "AIzaSyB0IUd-FyOMNA6aWUiutABscR_oz6oeic4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}, {"client_id": "660923458040-5ob5o4v0q07flkjhea881913mq1rs113.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycompany.resps"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:660923458040:android:18d526ec0640872856360d", "android_client_info": {"package_name": "com.pego.respublicaseguridad.respublicaseguridad"}}, "oauth_client": [{"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBl2Ta4vaHjYhOqw9-2gz1XmpNetP2XLwc"}, {"current_key": "AIzaSyB0IUd-FyOMNA6aWUiutABscR_oz6oeic4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com", "client_type": 3}, {"client_id": "660923458040-5ob5o4v0q07flkjhea881913mq1rs113.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycompany.resps"}}]}}}], "configuration_version": "1"}