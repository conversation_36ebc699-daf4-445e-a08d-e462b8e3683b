{"indexes": [{"collectionGroup": "location_updates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "zoneId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "zones", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "zones", "queryScope": "COLLECTION", "fields": [{"fieldPath": "automaticValidation.isActive", "order": "ASCENDING"}, {"fieldPath": "automaticValidation.expiresAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "incidents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "postedAt", "order": "DESCENDING"}]}, {"collectionGroup": "TiposReporte", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "cat", "order": "ASCENDING"}, {"fieldPath": "Orden", "order": "ASCENDING"}]}, {"collectionGroup": "TiposReporte", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "cat", "order": "ASCENDING"}, {"fieldPath": "Orden", "order": "DESCENDING"}]}, {"collectionGroup": "TiposReporte", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "Nombre", "order": "ASCENDING"}, {"fieldPath": "Orden", "order": "ASCENDING"}]}, {"collectionGroup": "TiposReporte", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "Nombre", "order": "ASCENDING"}, {"fieldPath": "Orden", "order": "DESCENDING"}]}, {"collectionGroup": "incident_markers_geo", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryKey", "order": "ASCENDING"}, {"fieldPath": "postedAt", "order": "DESCENDING"}]}, {"collectionGroup": "incident_markers_geo", "queryScope": "COLLECTION", "fields": [{"fieldPath": "severity", "order": "ASCENDING"}, {"fieldPath": "postedAt", "order": "DESCENDING"}]}, {"collectionGroup": "incident_markers_geo", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryKey", "order": "ASCENDING"}, {"fieldPath": "severity", "order": "ASCENDING"}, {"fieldPath": "postedAt", "order": "DESCENDING"}]}, {"collectionGroup": "user_reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "reportedUserId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "reportedAt", "order": "DESCENDING"}]}, {"collectionGroup": "identity_documents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "uploadTimestamp", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "TiposReporte", "fieldPath": "cat", "ttl": false, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "TiposReporte", "fieldPath": "Orden", "ttl": false, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}]}