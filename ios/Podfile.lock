PODS:
  - abseil/algorithm (1.20240116.2):
    - abseil/algorithm/algorithm (= 1.20240116.2)
    - abseil/algorithm/container (= 1.20240116.2)
  - abseil/algorithm/algorithm (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240116.2):
    - abseil/base/atomic_hook (= 1.20240116.2)
    - abseil/base/base (= 1.20240116.2)
    - abseil/base/base_internal (= 1.20240116.2)
    - abseil/base/config (= 1.20240116.2)
    - abseil/base/core_headers (= 1.20240116.2)
    - abseil/base/cycleclock_internal (= 1.20240116.2)
    - abseil/base/dynamic_annotations (= 1.20240116.2)
    - abseil/base/endian (= 1.20240116.2)
    - abseil/base/errno_saver (= 1.20240116.2)
    - abseil/base/fast_type_id (= 1.20240116.2)
    - abseil/base/log_severity (= 1.20240116.2)
    - abseil/base/malloc_internal (= 1.20240116.2)
    - abseil/base/no_destructor (= 1.20240116.2)
    - abseil/base/nullability (= 1.20240116.2)
    - abseil/base/prefetch (= 1.20240116.2)
    - abseil/base/pretty_function (= 1.20240116.2)
    - abseil/base/raw_logging_internal (= 1.20240116.2)
    - abseil/base/spinlock_wait (= 1.20240116.2)
    - abseil/base/strerror (= 1.20240116.2)
    - abseil/base/throw_delegate (= 1.20240116.2)
  - abseil/base/atomic_hook (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240116.2):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240116.2):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240116.2):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240116.2):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240116.2):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240116.2):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240116.2):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240116.2):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240116.2):
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240116.2):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240116.2):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240116.2):
    - abseil/memory/memory (= 1.20240116.2)
  - abseil/memory/memory (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240116.2):
    - abseil/meta/type_traits (= 1.20240116.2)
  - abseil/meta/type_traits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240116.2):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240116.2):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240116.2):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240116.2):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240116.2):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240116.2):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240116.2):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240116.2):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240116.2):
    - abseil/time/internal (= 1.20240116.2)
    - abseil/time/time (= 1.20240116.2)
  - abseil/time/internal (1.20240116.2):
    - abseil/time/internal/cctz (= 1.20240116.2)
  - abseil/time/internal/cctz (1.20240116.2):
    - abseil/time/internal/cctz/civil_time (= 1.20240116.2)
    - abseil/time/internal/cctz/time_zone (= 1.20240116.2)
  - abseil/time/internal/cctz/civil_time (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240116.2):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240116.2):
    - abseil/types/any (= 1.20240116.2)
    - abseil/types/bad_any_cast (= 1.20240116.2)
    - abseil/types/bad_any_cast_impl (= 1.20240116.2)
    - abseil/types/bad_optional_access (= 1.20240116.2)
    - abseil/types/bad_variant_access (= 1.20240116.2)
    - abseil/types/compare (= 1.20240116.2)
    - abseil/types/optional (= 1.20240116.2)
    - abseil/types/span (= 1.20240116.2)
    - abseil/types/variant (= 1.20240116.2)
  - abseil/types/any (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240116.2):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240116.2)
  - app_links (0.0.1):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - BoringSSL-GRPC (0.0.36):
    - BoringSSL-GRPC/Implementation (= 0.0.36)
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Implementation (0.0.36):
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Interface (0.0.36)
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (4.17.5):
    - Firebase/Firestore (= 11.0.0)
    - firebase_core
    - Flutter
  - cloud_functions (4.7.6):
    - Firebase/Functions (= 11.0.0)
    - firebase_core
    - Flutter
  - FBAEMKit (17.0.3):
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit (17.0.3):
    - FBAEMKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit_Basics (17.0.3)
  - FBSDKLoginKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - Firebase/Analytics (11.0.0):
    - Firebase/Core
  - Firebase/Auth (11.0.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.0.0)
  - Firebase/Core (11.0.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.0.0)
  - Firebase/CoreOnly (11.0.0):
    - FirebaseCore (= 11.0.0)
  - Firebase/Crashlytics (11.0.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.0.0)
  - Firebase/Database (11.0.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.0.0)
  - Firebase/Firestore (11.0.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.0.0)
  - Firebase/Functions (11.0.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 11.0.0)
  - Firebase/Messaging (11.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.0.0)
  - Firebase/Storage (11.0.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.0.0)
  - firebase_analytics (10.10.7):
    - Firebase/Analytics (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_auth (4.20.0):
    - Firebase/Auth (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 11.0.0)
    - Flutter
  - firebase_crashlytics (3.5.7):
    - Firebase/Crashlytics (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_database (10.5.7):
    - Firebase/Database (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.9.4):
    - Firebase/Messaging (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_storage (11.7.7):
    - Firebase/Storage (= 11.0.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.0.0):
    - FirebaseAnalytics/AdIdSupport (= 11.0.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.0.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.0.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (~> 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.0.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.4.1):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.0.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDatabase (11.0.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - leveldb-library (~> 1.22)
  - FirebaseFirestore (11.0.0):
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseFirestoreInternal (= 11.0.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.0.0):
    - abseil/algorithm (~> 1.20240116.1)
    - abseil/base (~> 1.20240116.1)
    - abseil/container/flat_hash_map (~> 1.20240116.1)
    - abseil/memory (~> 1.20240116.1)
    - abseil/meta (~> 1.20240116.1)
    - abseil/strings/strings (~> 1.20240116.1)
    - abseil/time (~> 1.20240116.1)
    - abseil/types (~> 1.20240116.1)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - "gRPC-C++ (~> 1.65.0)"
    - gRPC-Core (~> 1.65.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseFunctions (11.0.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseMessagingInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GTMSessionFetcher/Core (~> 3.4)
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.0.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseMessagingInterop (11.15.0)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.15.0)
  - FirebaseStorage (11.0.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (~> 3.4)
  - Flutter (1.0.0)
  - flutter_facebook_auth (6.2.0):
    - FBSDKLoginKit (~> 17.0.0)
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - google_mlkit_image_labeling (0.14.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/ImageLabeling (~> 7.0.0)
    - GoogleMLKit/ImageLabelingCustom (~> 7.0.0)
  - google_mlkit_text_recognition (0.15.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/TextRecognition (~> 7.0.0)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.0.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.0.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.0.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - GoogleMLKit/ImageLabeling (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitImageLabeling (~> 6.0.0)
  - GoogleMLKit/ImageLabelingCustom (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitImageLabelingCustom (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleMLKit/TextRecognition (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTextRecognition (~> 5.0.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.65.5)":
    - "gRPC-C++/Implementation (= 1.65.5)"
    - "gRPC-C++/Interface (= 1.65.5)"
  - "gRPC-C++/Implementation (1.65.5)":
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/absl_check (~> 1.20240116.2)
    - abseil/log/absl_log (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - "gRPC-C++/Interface (= 1.65.5)"
    - "gRPC-C++/Privacy (= 1.65.5)"
    - gRPC-Core (= 1.65.5)
  - "gRPC-C++/Interface (1.65.5)"
  - "gRPC-C++/Privacy (1.65.5)"
  - gRPC-Core (1.65.5):
    - gRPC-Core/Implementation (= 1.65.5)
    - gRPC-Core/Interface (= 1.65.5)
  - gRPC-Core/Implementation (1.65.5):
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - BoringSSL-GRPC (= 0.0.36)
    - gRPC-Core/Interface (= 1.65.5)
    - gRPC-Core/Privacy (= 1.65.5)
  - gRPC-Core/Interface (1.65.5)
  - gRPC-Core/Privacy (1.65.5)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitImageLabeling (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitImageLabelingCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitImageLabelingCustom (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitObjectDetectionCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitTextRecognition (5.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitTextRecognitionCommon (= 4.0.0)
    - MLKitVision (~> 8.0)
  - MLKitTextRecognitionCommon (4.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - MLKitVisionKit (9.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
  - mobile_scanner (7.0.0):
    - Flutter
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (100.0.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - workmanager_apple (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_image_labeling (from `.symlinks/plugins/google_mlkit_image_labeling/ios`)
  - google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - workmanager_apple (from `.symlinks/plugins/workmanager_apple/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppCheckCore
    - BoringSSL-GRPC
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDatabase
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseFunctions
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FirebaseStorage
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleSignIn
    - GoogleToolboxForMac
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - Mantle
    - MLImage
    - MLKitCommon
    - MLKitImageLabeling
    - MLKitImageLabelingCommon
    - MLKitImageLabelingCustom
    - MLKitObjectDetectionCommon
    - MLKitTextRecognition
    - MLKitTextRecognitionCommon
    - MLKitVision
    - MLKitVisionKit
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_image_labeling:
    :path: ".symlinks/plugins/google_mlkit_image_labeling/ios"
  google_mlkit_text_recognition:
    :path: ".symlinks/plugins/google_mlkit_text_recognition/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  workmanager_apple:
    :path: ".symlinks/plugins/workmanager_apple/ios"

SPEC CHECKSUMS:
  abseil: d121da9ef7e2ff4cab7666e76c5a3e0915ae08c3
  app_links: c5161ac5ab5383ad046884568b4b91cb52df5d91
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  BoringSSL-GRPC: ca6a8e5d04812fce8ffd6437810c2d46f925eaeb
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  cloud_firestore: b70057351594d16c1eedf74f956369ea02f28bfa
  cloud_functions: 5d24a5355a8c278168b34948064497a59a881d1a
  FBAEMKit: 9900b2edd99a2d21629a6277e6166f14c6215799
  FBSDKCoreKit: 0791f8f68a8630931a4c12aa23a56cc021551596
  FBSDKCoreKit_Basics: 46d6b472c0dd0a5a7e972c025033d1c567f54eb4
  FBSDKLoginKit: b4a4eba1d62eb452544411824f41689adabd5bd2
  Firebase: 9f574c08c2396885b5e7e100ed4293d956218af9
  firebase_analytics: 94e6b8cb96b92465cd1e4343ddd6d3d5b60eaf75
  firebase_auth: 26184ce7c77328f35dea0e47ed690352302127e7
  firebase_core: 990ffcb1ddbed497954cc560b64a3d6a6bbef3ea
  firebase_crashlytics: 1d30b66d602fcf3e0bd59404da580d01212fb228
  firebase_database: 4701ab3c3c40cdc796c1b8459eee512fd909ec16
  firebase_messaging: 8383573338081b56ca315f056e30591962f29924
  firebase_storage: a8b2fa097e855c62d1eb89e204808a7a8f01410e
  FirebaseAnalytics: 27eb78b97880ea4a004839b9bac0b58880f5a92a
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: d5cf28be74d7e82257f6a3f717509eff70d3cf4a
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: 3cf438f431f18c12cdf2aaf64434648b63f7e383
  FirebaseCoreExtension: f1bc67a4702931a7caa097d8e4ac0a1b0d16720e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseCrashlytics: 745d8f0221fe49c62865391d1bf56f5a12eeec0b
  FirebaseDatabase: 47b021386877846d30a7f3527c4f4fad8fd04b19
  FirebaseFirestore: a1758850668dbb503537b7780a2a1fdc5e37c6ce
  FirebaseFirestoreInternal: 9fcc0ccb987ab73163f2249444e4bfd9eac63748
  FirebaseFunctions: 49653511d8c966ad16c5c02acc2bfd642ef1cec1
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: d2d1d9c62c46dd2db49a952f7deb5b16ad2c9742
  FirebaseMessagingInterop: 63e504b147a7206cfe64cbe2f40c2ddd009945bd
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: 655ff17f3cc1a635cbdc2d69b953878001f9e25b
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  FirebaseStorage: f9e2bf027d549db18b6195a37b31c85f56e40200
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 2f28c889d5727b4538ed8d25be3367092b2cbef7
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  gal: baecd024ebfd13c441269ca7404792a7152fde89
  geocoding_ios: 33776c9ebb98d037b5e025bb0e7537f6dd19646e
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_mlkit_commons: 2abe6a70e1824e431d16a51085cb475b672c8aab
  google_mlkit_image_labeling: 2e3181a45117018ec7e541ab5b5401a15d6a1d07
  google_mlkit_text_recognition: ec2122ec89bfe0d7200763336a6e4ef44810674c
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleAppMeasurement: 6e49ffac7d3f2c3ded9cc663f912a13b67bbd0de
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": 2fa52b3141e7789a28a737f251e0c45b4cb20a87
  gRPC-Core: a27c294d6149e1c39a7d173527119cfbc3375ce4
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitImageLabeling: 7f052589de9ffde213bc2c26854d212ea4dde17a
  MLKitImageLabelingCommon: 23f0c9037c2c6433784db594bd1bb87173172fe9
  MLKitImageLabelingCustom: 594c49fa1bb809ec05215bb4ac1c15729c7a601a
  MLKitObjectDetectionCommon: 0198709a728984e3b6fac98a5fa53a8042880336
  MLKitTextRecognition: 3b41f3ff084a79afb214408d25d2068d77ab322c
  MLKitTextRecognitionCommon: cd44577a8c506fc6bba065096de03bec0d01a213
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  MLKitVisionKit: 8a7abd5f11aeb1add2942a694c2685eca422a849
  mobile_scanner: 9157936403f5a0644ca3779a38ff8404c5434a93
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share_plus: 011d6fb4f9d2576b83179a3a5c5e323202cdabcf
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  workmanager_apple: f540d652595dfe5c8b8200c4c85ba622d6fb5c5b

PODFILE CHECKSUM: de64fc2e5aeddd73032d3c22e9ed46552f64edad

COCOAPODS: 1.16.2
