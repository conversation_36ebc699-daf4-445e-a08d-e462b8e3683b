<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Respública Seguridad</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Respública Seguridad</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- Facebook configuration -->
	<key>FacebookAppID</key>
	<string>1873262320124106</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Respública Seguridad</string>
	
	<!-- URL schemes for Facebook and Custom URI Scheme -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1873262320124106</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.respublica.seguridad.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>respublicaseguridad</string>
			</array>
		</dict>
	</array>
	
	<!-- LSApplicationQueriesSchemes for Facebook -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>

	<!-- Google Maps API Key -->
	<key>GMSApiKey</key>
	<string>AIzaSyBkeVJSAOi2jdEDPxH6b2afVsRH5FEubGk</string>

	<!-- Location permissions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to validate your presence in security zones and provide accurate incident reporting.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs continuous location access to automatically validate your presence in security zones, even when the app is in the background. This enables automatic zone validation and enhances the credibility of your incident reports.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs continuous location access to automatically validate your presence in security zones, even when the app is in the background.</string>

	<!-- Background modes for location and notifications -->
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>remote-notification</string>
	</array>

	<!-- Notification permissions -->
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>

	<!-- Camera and Photo Library permissions -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to capture photos and videos for incident reports.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to select images and videos for incident reports.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access to record videos with audio for incident reports.</string>
</dict>
</plist>
