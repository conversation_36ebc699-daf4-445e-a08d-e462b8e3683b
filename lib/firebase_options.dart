import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCtjLsF02ApUnQFZowWrajqWsp0ytTOoTg',
    appId: '1:660923458040:web:60dff6163a9dd61156360d',
    messagingSenderId: '660923458040',
    projectId: 'res-p-s-d8is3t',
    authDomain: 'res-p-s-d8is3t.firebaseapp.com',
    storageBucket: 'res-p-s-d8is3t.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBl2Ta4vaHjYhOqw9-2gz1XmpNetP2XLwc',
    appId: '1:660923458040:android:18d526ec0640872856360d',
    messagingSenderId: '660923458040',
    projectId: 'res-p-s-d8is3t',
    storageBucket: 'res-p-s-d8is3t.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBgnZf6hMykgMnS9RVjaN5Ta61ao8e3ORA',
    appId: '1:660923458040:ios:44185508e50cdad456360d',
    messagingSenderId: '660923458040',
    projectId: 'res-p-s-d8is3t',
    storageBucket: 'res-p-s-d8is3t.firebasestorage.app',
    androidClientId: '660923458040-gnfno2mq7l2qkcqjurndiaccg917c44r.apps.googleusercontent.com',
    iosClientId: '660923458040-0v0eqom6s029beo9ugo9li7e6js2v73m.apps.googleusercontent.com',
    iosBundleId: 'com.pego.respublicaseguridad.respublicaseguridad',
  );

 
} 