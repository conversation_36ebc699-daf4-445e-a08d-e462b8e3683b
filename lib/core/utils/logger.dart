import 'dart:developer' as developer;

/// Simple logger utility for the application
class Logger {
  static const String _defaultTag = 'RespublicaSeguridad';

  /// Log debug message
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(message, level: 500, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(message, level: 800, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(message, level: 900, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(message, level: 1000, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log critical error message
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(message, level: 1200, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Internal log method
  static void _log(
    String message, {
    required int level,
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final logTag = tag ?? _defaultTag;
    final timestamp = DateTime.now().toIso8601String();
    
    String logMessage = '[$timestamp] [$logTag] $message';
    
    if (error != null) {
      logMessage += '\nError: $error';
    }
    
    if (stackTrace != null) {
      logMessage += '\nStackTrace: $stackTrace';
    }

    developer.log(
      logMessage,
      name: logTag,
      level: level,
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log method call for debugging
  static void methodCall(String className, String methodName, {Map<String, dynamic>? params}) {
    String message = '$className.$methodName()';
    if (params != null && params.isNotEmpty) {
      message += ' with params: $params';
    }
    debug(message, tag: 'MethodCall');
  }

  /// Log performance timing
  static void performance(String operation, Duration duration, {String? tag}) {
    info('$operation completed in ${duration.inMilliseconds}ms', tag: tag ?? 'Performance');
  }

  /// Log network request
  static void networkRequest(String method, String url, {int? statusCode, Duration? duration}) {
    String message = '$method $url';
    if (statusCode != null) {
      message += ' -> $statusCode';
    }
    if (duration != null) {
      message += ' (${duration.inMilliseconds}ms)';
    }
    info(message, tag: 'Network');
  }

  /// Log user action
  static void userAction(String action, {Map<String, dynamic>? context}) {
    String message = 'User action: $action';
    if (context != null && context.isNotEmpty) {
      message += ' with context: $context';
    }
    info(message, tag: 'UserAction');
  }

  /// Log business logic event
  static void businessEvent(String event, {Map<String, dynamic>? data}) {
    String message = 'Business event: $event';
    if (data != null && data.isNotEmpty) {
      message += ' with data: $data';
    }
    info(message, tag: 'Business');
  }

  /// Log security event
  static void security(String event, {String? userId, Map<String, dynamic>? context}) {
    String message = 'Security event: $event';
    if (userId != null) {
      message += ' for user: $userId';
    }
    if (context != null && context.isNotEmpty) {
      message += ' with context: $context';
    }
    warning(message, tag: 'Security');
  }
}

/// Global logger instance for convenience
final logger = Logger();

/// Extension for easy logging from any class
extension LoggerExtension on Object {
  void logDebug(String message, {Object? error, StackTrace? stackTrace}) {
    Logger.debug(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    Logger.info(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    Logger.warning(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    Logger.error(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logMethodCall(String methodName, {Map<String, dynamic>? params}) {
    Logger.methodCall(runtimeType.toString(), methodName, params: params);
  }
}
