import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// NavigationService provides convenient methods for navigation.
class NavigationService {
  /// Navigate to a named route.
  static void navigateTo(
    BuildContext context, 
    String routeName, {
    Map<String, String>? params,
    Map<String, dynamic>? queryParams,
    Object? extra,
  }) {
    context.goNamed(
      routeName,
      pathParameters: params ?? {},
      queryParameters: queryParams ?? {},
      extra: extra,
    );
  }

  /// Navigate to a route by path.
  static void navigateToPath(
    BuildContext context, 
    String path, {
    Map<String, dynamic>? queryParams,
    Object? extra,
  }) {
    context.go(
      path,
      extra: extra,
    );
  }

  /// Replace the current route with a named route.
  static void replaceTo(
    BuildContext context, 
    String routeName, {
    Map<String, String>? params,
    Map<String, dynamic>? queryParams,
    Object? extra,
  }) {
    context.goNamed(
      routeName,
      pathParameters: params ?? {},
      queryParameters: queryParams ?? {},
      extra: extra,
    );
  }

  /// Go back to the previous route.
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    }
  }
} 