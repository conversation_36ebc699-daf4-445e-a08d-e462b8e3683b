import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/my_incidents_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/incident_category_selection_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/incident_report_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/incidents_list_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/incident_details_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/add_incident_update_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/incident_update_details_screen.dart';
import 'package:respublicaseguridad/features/incidents/presentation/screens/media_viewer_screen.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/add_incident_update_cubit.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/screens/forgot_password_screen.dart';
import 'package:respublicaseguridad/features/auth/presentation/screens/login_screen.dart';
import 'package:respublicaseguridad/features/auth/presentation/screens/password_reset_confirmation_screen.dart';
import 'package:respublicaseguridad/features/auth/presentation/screens/signup_screen.dart';
import 'package:respublicaseguridad/features/debug/crash_reports_screen.dart';
import 'package:respublicaseguridad/features/home/<USER>/screens/home_screen.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/screens/id_validation_screen.dart';
import 'package:respublicaseguridad/features/main_layout.dart';
import 'package:respublicaseguridad/features/more/more_screen.dart';
import 'package:respublicaseguridad/features/notifications/presentation/screens/notification_preferences_screen.dart';
import 'package:respublicaseguridad/features/notifications/presentation/screens/notification_history_screen.dart';
import 'package:respublicaseguridad/features/security/security_screen.dart';
import 'package:respublicaseguridad/features/settings/settings_screen.dart';
import 'package:respublicaseguridad/features/settings/account_settings_screen.dart';
import 'package:respublicaseguridad/features/splash/splash_screen.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/screens/zone_verification_screens.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_bloc_exports.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/screens/qr_validation_screen.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/screens/standalone_qr_scanner_screen.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/qr_validation_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/standalone_qr_scanner_cubit.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/zone_validation_watcher_cubit.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:respublicaseguridad/main.dart';

/// AppRouter is responsible for handling all the navigation in the app.
class AppRouter {
  static final _authBloc = getIt<AuthBloc>();

  /// Creates a [GoRouter] instance with all the routes configured.
  static final router = GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    navigatorKey: navigatorKey,
    redirect: (BuildContext context, GoRouterState state) {
      final isLoggedIn = _authBloc.state.isAuthenticated;
      final isInitializing = _authBloc.state.isInitial;
      final isAuthRoute =
          state.matchedLocation == '/login' ||
          state.matchedLocation == '/signup' ||
          state.matchedLocation == '/forgot-password' ||
          state.matchedLocation == '/password-reset-confirmation';

      // Check if coming from forgot password flow (forgot PIN refers to forgot password)
      final isFromForgotPassword =
          state.uri.queryParameters.containsKey('from_forgot_password') ||
          state.matchedLocation == '/password-reset-confirmation';

      // Debug print to track router redirects
      print(
        'Router - Auth State: ${_authBloc.state.status}, Path: ${state.matchedLocation}',
      );

      // During initialization, stay on splash screen
      if (isInitializing && state.matchedLocation != '/') {
        print('Router - Redirecting to / (initializing)');
        return '/';
      }

      // If we're on the splash screen and authentication check is complete, redirect accordingly
      if (state.matchedLocation == '/' && !isInitializing) {
        if (isLoggedIn) {
          print('Router - Redirecting to /home from splash (authenticated)');
          return '/home';
        } else {
          print('Router - Redirecting to /login from splash (unauthenticated)');
          return '/login';
        }
      }

      // If authenticated and trying to access auth routes, redirect to home
      // Exception: allow password reset confirmation even if authenticated
      if (isLoggedIn && isAuthRoute && !isFromForgotPassword) {
        print('Router - Redirecting to /home (authenticated)');
        return '/home';
      }

      // If not authenticated and trying to access protected routes, redirect to login
      if (!isLoggedIn &&
          !isAuthRoute &&
          !isInitializing &&
          state.matchedLocation != '/') {
        print('Router - Redirecting to /login (unauthenticated)');
        return '/login';
      }

      print('Router - No redirect needed');
      return null;
    },
    refreshListenable: GoRouterRefreshStream(_authBloc.stream),
    routes: [
      GoRoute(
        path: '/',
        name: 'splash',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const SplashScreen(),
            ),
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const LoginScreen(),
            ),
      ),
      GoRoute(
        path: '/signup',
        name: 'signup',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const SignupScreen(),
            ),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const ForgotPasswordScreen(),
            ),
      ),
      GoRoute(
        path: '/password-reset-confirmation',
        name: 'password-reset-confirmation',
        builder: (context, state) {
          final email = state.extra as String;
          return BlocProvider.value(
            value: _authBloc,
            child: PasswordResetConfirmationScreen(email: email),
          );
        },
      ),
      ShellRoute(
        builder: (context, state, child) {
          return BlocProvider.value(
            value: _authBloc,
            child: MainShell(location: state.matchedLocation, child: child),
          );
        },
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: '/alerts',
            name: 'alerts',
            builder:
                (context, state) => BlocProvider.value(
                  value: _authBloc,
                  child: const MyIncidentsScreen(),
                ),
          ),
          GoRoute(
            path: '/security',
            name: 'security',
            builder: (context, state) => const SecurityScreen(),
          ),
          GoRoute(
            path: '/more',
            name: 'more',
            builder: (context, state) => const MoreScreen(),
          ),
        ],
      ),
      // Incident Routes
      GoRoute(
        path: '/incidents',
        name: 'incidents',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const IncidentsListScreen(),
            ),
      ),
      GoRoute(
        path: '/incidents/report',
        name: 'incident-report',
        builder: (context, state) {
          final selectedCategory = state.extra as IncidentCategoryEntity?;
          return BlocProvider.value(
            value: _authBloc,
            child:
                selectedCategory != null
                    ? IncidentReportScreen(
                      preselectedCategory: selectedCategory,
                    )
                    : const IncidentCategorySelectionScreen(),
          );
        },
      ),
      GoRoute(
        path: '/incidents/details',
        name: 'incident-details',
        builder: (context, state) {
          final incident = state.extra as IncidentEntity;
          return BlocProvider.value(
            value: _authBloc,
            child: IncidentDetailsScreen(incident: incident),
          );
        },
      ),
      GoRoute(
        path: '/incidents/add-update',
        name: 'add-incident-update',
        builder: (context, state) {
          final incident = state.extra as IncidentEntity;
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(value: _authBloc),
              BlocProvider(
                create: (context) => getIt<AddIncidentUpdateCubit>(),
              ),
            ],
            child: AddIncidentUpdateScreen(incident: incident),
          );
        },
      ),
      GoRoute(
        path: '/incidents/update-details/:incidentId/:updateId',
        name: 'incident-update-details',
        builder: (context, state) {
          final incidentId = state.pathParameters['incidentId']!;
          final updateId = state.pathParameters['updateId']!;
          return BlocProvider.value(
            value: _authBloc,
            child: IncidentUpdateDetailsScreen(
              incidentId: incidentId,
              updateId: updateId,
            ),
          );
        },
      ),
      GoRoute(
        path: '/media/preview',
        name: 'media-preview',
        builder: (context, state) {
          final params = state.extra as Map<String, dynamic>;
          final mediaList = params['mediaList'] as List<MediaEntity>;
          final initialIndex = params['initialIndex'] as int? ?? 0;
          return MediaViewerScreen(
            mediaList: mediaList,
            initialIndex: initialIndex,
          );
        },
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: '/account-settings',
        name: 'account-settings',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const AccountSettingsScreen(),
            ),
      ),
      GoRoute(
        path: '/crash-reports',
        name: 'crash-reports',
        builder: (context, state) => const CrashReportsScreen(),
      ),
      GoRoute(
        path: '/identity-validation',
        name: 'identity-validation',
        builder: (context, state) => const IdValidationScreen(),
      ),
      // Notification Routes
      GoRoute(
        path: '/notification-preferences',
        name: 'notification-preferences',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const NotificationPreferencesScreen(),
            ),
      ),
      GoRoute(
        path: '/notification-history',
        name: 'notification-history',
        builder:
            (context, state) => BlocProvider.value(
              value: _authBloc,
              child: const NotificationHistoryScreen(),
            ),
      ),
      // Zone Validation Routes
      GoRoute(
        path: '/my-zones',
        name: 'my-zones',
        builder:
            (context, state) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: _authBloc),
                BlocProvider(create: (context) => getIt<ZoneValidationBloc>()),
              ],
              child: const MyZonesScreen(),
            ),
      ),
      GoRoute(
        path: '/define-zone',
        name: 'define-zone',
        builder: (context, state) {
          final zoneToEdit = state.extra as ZoneEntity?;
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(value: _authBloc),
              BlocProvider(create: (context) => getIt<ZoneValidationBloc>()),
            ],
            child: DefineZoneScreen(zoneToEdit: zoneToEdit),
          );
        },
      ),
      GoRoute(
        path: '/zone-details/:zoneId',
        name: 'zone-details',
        builder: (context, state) {
          final zoneId = state.pathParameters['zoneId']!;
          final zone = state.extra as ZoneEntity?;
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(value: _authBloc),
              BlocProvider(create: (context) => getIt<ZoneValidationBloc>()),
            ],
            child: ZoneDetailsScreen(zoneId: zoneId, zone: zone),
          );
        },
      ),

      GoRoute(
        path: '/automatic-validation/:zoneId',
        name: 'automatic-validation',
        builder: (context, state) {
          final zone = state.extra as ZoneEntity;
          return BlocProvider(
            create: (context) => getIt<ZoneValidationBloc>(),
            child: AutomaticValidationScreen(zone: zone),
          );
        },
      ),
      GoRoute(
        path: '/qr-validation/:zoneId',
        name: 'qr-validation',
        builder: (context, state) {
          final zone = state.extra as ZoneEntity;
          return MultiBlocProvider(
            providers: [
              BlocProvider(create: (context) => getIt<QRValidationBloc>()),
              BlocProvider(
                create: (context) => getIt<ZoneValidationWatcherCubit>(),
              ),
            ],
            child: QRValidationScreen(
              zone: zone,
              currentUserId: getIt<FirebaseAuth>().currentUser?.uid ?? '',
            ),
          );
        },
      ),
      GoRoute(
        path: '/standalone-qr-scanner',
        name: 'standalone-qr-scanner',
        builder: (context, state) {
          return BlocProvider(
            create: (context) => getIt<StandaloneQRScannerCubit>(),
            child: StandaloneQRScannerScreen(
              currentUserId: getIt<FirebaseAuth>().currentUser?.uid ?? '',
            ),
          );
        },
      ),
    ],
    errorBuilder:
        (context, state) =>
            Scaffold(body: Center(child: Text('Error: ${state.error}'))),
  );
}

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    _subscription = stream.listen((_) {
      print("GoRouterRefreshStream - Auth state changed, notifying router");
      notifyListeners();
    });
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
