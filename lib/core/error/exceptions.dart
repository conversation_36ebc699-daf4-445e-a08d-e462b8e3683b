/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when server operations fail
class ServerException extends AppException {
  const ServerException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'ServerException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when validation fails
class ValidationException extends AppException {
  const ValidationException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'ValidationException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when cache operations fail
class CacheException extends AppException {
  const CacheException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'CacheException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when network operations fail
class NetworkException extends AppException {
  const NetworkException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'NetworkException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when authentication fails
class AuthException extends AppException {
  const AuthException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'AuthException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when location operations fail
class LocationException extends AppException {
  const LocationException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'LocationException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when permission operations fail
class PermissionException extends AppException {
  const PermissionException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'PermissionException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when storage operations fail
class StorageException extends AppException {
  const StorageException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'StorageException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when parsing operations fail
class ParseException extends AppException {
  const ParseException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'ParseException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when timeout occurs
class TimeoutException extends AppException {
  const TimeoutException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'TimeoutException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when resource is not found
class NotFoundException extends AppException {
  const NotFoundException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'NotFoundException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when operation is forbidden
class ForbiddenException extends AppException {
  const ForbiddenException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'ForbiddenException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when rate limit is exceeded
class RateLimitException extends AppException {
  const RateLimitException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'RateLimitException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when configuration is invalid
class ConfigurationException extends AppException {
  const ConfigurationException(String message, {String? code}) : super(message, code: code);

  @override
  String toString() => 'ConfigurationException: $message${code != null ? ' (Code: $code)' : ''}';
}
