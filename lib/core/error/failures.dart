import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

// Authentication related failures
class AuthFailure extends Failure {
  const AuthFailure({required String message, int? code}) 
      : super(message: message, code: code);
}

class ServerFailure extends Failure {
  const ServerFailure({required String message, int? code})
      : super(message: message, code: code);
}

class CacheFailure extends Failure {
  const CacheFailure({required String message, int? code}) 
      : super(message: message, code: code);
}

class NetworkFailure extends Failure {
  const NetworkFailure({required String message, int? code}) 
      : super(message: message, code: code);
}

class UnknownFailure extends Failure {
  const UnknownFailure({required String message, int? code})
      : super(message: message, code: code);
}

// Auth specific failures
class EmailAlreadyInUseFailure extends AuthFailure {
  const EmailAlreadyInUseFailure() 
      : super(message: 'Email already in use. Try another email or sign in.');
}

class InvalidEmailFailure extends AuthFailure {
  const InvalidEmailFailure() 
      : super(message: 'The email address is invalid.');
}

class WeakPasswordFailure extends AuthFailure {
  const WeakPasswordFailure() 
      : super(message: 'The password is too weak.');
}

class UserNotFoundFailure extends AuthFailure {
  const UserNotFoundFailure() 
      : super(message: 'No user found with this email.');
}

class WrongPasswordFailure extends AuthFailure {
  const WrongPasswordFailure() 
      : super(message: 'Wrong password. Please try again.');
}

class UserDisabledFailure extends AuthFailure {
  const UserDisabledFailure() 
      : super(message: 'This user has been disabled.');
}

class GoogleSignInCanceledFailure extends AuthFailure {
  const GoogleSignInCanceledFailure() 
      : super(message: 'Google sign in was canceled.');
}

class FacebookSignInCanceledFailure extends AuthFailure {
  const FacebookSignInCanceledFailure()
      : super(message: 'Facebook sign in was canceled.');
}

// Identity Verification specific failures
class ValidationFailure extends Failure {
  const ValidationFailure(String message)
      : super(message: message);
}

class NotFoundFailure extends Failure {
  const NotFoundFailure(String message)
      : super(message: message);
}

class AuthorizationFailure extends Failure {
  const AuthorizationFailure(String message)
      : super(message: message);
}

class StorageFailure extends Failure {
  const StorageFailure({required String message, int? code})
      : super(message: message, code: code);
}

class FirestoreFailure extends Failure {
  const FirestoreFailure({required String message, int? code})
      : super(message: message, code: code);
}

class LocationFailure extends Failure {
  const LocationFailure(String message)
      : super(message: message);
}

/// Failure that indicates identity validation is required before proceeding
class IdentityValidationRequiredFailure extends Failure {
  final String userId;
  final String validationStatus;
  final bool requiresRedirect;

  const IdentityValidationRequiredFailure({
    required String message,
    required this.userId,
    required this.validationStatus,
    this.requiresRedirect = true,
  }) : super(message: message);
}
