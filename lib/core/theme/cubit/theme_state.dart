import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents the state of the theme
class ThemeState extends Equatable {
  /// Creates a new theme state with the given theme mode
  const ThemeState({required this.themeMode});

  /// The current theme mode (light, dark, or system)
  final ThemeMode themeMode;

  @override
  List<Object> get props => [themeMode];

  /// Creates a copy of this theme state with the given fields replaced with new values
  ThemeState copyWith({
    ThemeMode? themeMode,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
    );
  }

  /// Creates a theme state with the light theme mode
  factory ThemeState.light() => const ThemeState(themeMode: ThemeMode.light);

  /// Creates a theme state with the dark theme mode
  factory ThemeState.dark() => const ThemeState(themeMode: ThemeMode.dark);

  /// Creates a theme state with the system theme mode
  factory ThemeState.system() => const ThemeState(themeMode: ThemeMode.system);
} 