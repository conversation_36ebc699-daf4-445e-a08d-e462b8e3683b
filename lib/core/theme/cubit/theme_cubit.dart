import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_state.dart';
import 'package:respublicaseguridad/core/theme/theme_repository.dart';

/// Manages the theme state and persistence
@injectable
class ThemeCubit extends Cubit<ThemeState> {
  /// Creates a new [ThemeCubit] with the given theme repository
  final IThemeRepository _themeRepository;

  ThemeCubit(this._themeRepository) : super(const ThemeState(themeMode: ThemeMode.light)) {
    _loadThemeMode();
  }

  /// Initializes the cubit by loading the saved theme preference
  Future<void> _loadThemeMode() async {
    final themeMode = await _themeRepository.getThemeMode();
    
    // If theme is system, change it to light mode for first run
    if (themeMode == ThemeMode.system) {
      await setLightTheme();
    } else {
      emit(ThemeState(themeMode: themeMode));
    }
  }

  /// Sets the theme to light mode
  Future<void> setLightTheme() async {
    await _themeRepository.setThemeMode(ThemeMode.light);
    emit(ThemeState.light());
  }

  /// Sets the theme to dark mode
  Future<void> setDarkTheme() async {
    await _themeRepository.setThemeMode(ThemeMode.dark);
    emit(ThemeState.dark());
  }

  /// Sets the theme to system mode
  Future<void> setSystemTheme() async {
    await _themeRepository.setThemeMode(ThemeMode.system);
    emit(ThemeState.system());
  }

  /// Toggles between light and dark theme
  Future<void> toggleTheme() async {
    final newMode = state.themeMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
    
    await setTheme(newMode);
  }

  Future<void> setTheme(ThemeMode mode) async {
    await _themeRepository.setThemeMode(mode);
    emit(ThemeState(themeMode: mode));
  }
} 