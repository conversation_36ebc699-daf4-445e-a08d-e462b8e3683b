import 'package:flutter/material.dart';
import 'package:respublicaseguridad/core/app_theme.dart';

/// Centralized color definitions for the application
/// Uses colors from AppTheme for consistency
class AppColors {
  // Primary Colors
  static const Color primary = AppTheme.primaryBlue;
  static const Color secondary = AppTheme.secondaryBlue;
  static const Color accent = AppTheme.accentBlue;
  static const Color brightBlue = AppTheme.brightBlue;
  
  // Surface and Background Colors
  static const Color surface = AppTheme.lightSurfaceColor;
  static const Color background = AppTheme.lightBackgroundColor;
  static const Color fieldPrimary = AppTheme.lightFieldPrimary;
  
  // Dark Theme Colors
  static const Color darkSurface = AppTheme.darkSurfaceColor;
  static const Color darkBackground = AppTheme.darkBackgroundColor;
  static const Color darkFieldPrimary = AppTheme.darkFieldPrimary;
  
  // Text Colors
  static const Color textPrimary = Colors.black87;
  static const Color textSecondary = Colors.black54;
  static const Color textTertiary = Colors.black38;
  static const Color textOnPrimary = Colors.white;
  
  // Dark Text Colors
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Colors.white70;
  static const Color darkTextTertiary = Colors.white54;
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = AppTheme.warningOrange;
  static const Color error = Color(0xFFD80032);
  static const Color info = AppTheme.accentBlue;
  
  // Border and Divider Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFEEEEEE);
  static const Color darkBorder = Color(0xFF424242);
  static const Color darkDivider = Color(0xFF303030);
  
  // Overlay Colors
  static Color overlay = Colors.black.withValues(alpha: 0.5);
  static Color lightOverlay = Colors.black.withValues(alpha: 0.1);
  static Color darkOverlay = Colors.white.withValues(alpha: 0.1);
  
  // Gradient Colors
  static const List<Color> primaryGradient = AppTheme.lightPrimaryGradient;
  static const List<Color> darkPrimaryGradient = AppTheme.darkPrimaryGradient;
  
  // Card Colors
  static Color cardShadow = Colors.black.withValues(alpha: 0.05);
  static Color cardBorder = AppTheme.secondaryBlue.withValues(alpha: 0.1);
  
  // Helper method to get colors based on theme brightness
  static Color getTextPrimary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? darkTextPrimary 
        : textPrimary;
  }
  
  static Color getTextSecondary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? darkTextSecondary 
        : textSecondary;
  }
  
  static Color getSurface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? darkSurface 
        : surface;
  }
  
  static Color getBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? darkBackground 
        : background;
  }
  
  static Color getBorder(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? darkBorder 
        : border;
  }
}
