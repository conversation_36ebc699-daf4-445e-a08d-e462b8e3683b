import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class IThemeRepository {
  Future<ThemeMode> getThemeMode();
  Future<void> setThemeMode(ThemeMode mode);
}

@Lazy<PERSON>ingleton(as: IThemeRepository)
class ThemeRepository implements IThemeRepository {
  /// Creates a new [ThemeRepository]
  ThemeRepository();

  static const String _themeKey = 'app_theme';

  @override
  Future<ThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeValue = prefs.getInt(_themeKey);
    
    if (themeValue == null) {
      return ThemeMode.light;
    }
    
    return ThemeMode.values[themeValue];
  }

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, mode.index);
  }
} 
