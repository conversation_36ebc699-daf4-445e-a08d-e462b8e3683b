import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_cubit.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_state.dart';
import 'package:respublicaseguridad/core/theme/theme_repository.dart';

/// A widget that provides theme management to the application
class ThemeProvider extends StatelessWidget {
  /// Creates a new [ThemeProvider]
  const ThemeProvider({
    required this.child,
    required this.themeRepository,
    super.key,
  });

  /// The child widget
  final Widget child;

  /// The theme repository
  final ThemeRepository themeRepository;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ThemeCubit(themeRepository),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return child;
        },
      ),
    );
  }
}

/// Extension on BuildContext to access theme functionality
extension ThemeExtension on BuildContext {
  /// Gets the current theme cubit
  ThemeCubit get themeCubit => BlocProvider.of<ThemeCubit>(this);

  /// Gets the current theme mode
  ThemeMode get themeMode => BlocProvider.of<ThemeCubit>(this).state.themeMode;

  /// Sets the theme to light mode
  Future<void> setLightTheme() => themeCubit.setLightTheme();

  /// Sets the theme to dark mode
  Future<void> setDarkTheme() => themeCubit.setDarkTheme();

  /// Sets the theme to system mode
  Future<void> setSystemTheme() => themeCubit.setSystemTheme();

  /// Toggles between light and dark theme
  Future<void> toggleTheme() => themeCubit.toggleTheme();
} 