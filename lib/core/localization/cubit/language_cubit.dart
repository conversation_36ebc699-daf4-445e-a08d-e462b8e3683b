import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';

/// Cubit for managing app language state
class LanguageCubit extends Cubit<Locale> {
  LanguageCubit() : super(LocalizationService.getCurrentLocale());

  /// Change the app language
  Future<void> changeLanguage(String languageCode) async {
    await LocalizationService.setLanguage(languageCode);
    emit(Locale(languageCode));
  }

  /// Get current language code
  String get currentLanguageCode => state.languageCode;

  /// Check if current language is Spanish
  bool get isSpanish => state.languageCode == 'es';

  /// Check if current language is English
  bool get isEnglish => state.languageCode == 'en';
}
