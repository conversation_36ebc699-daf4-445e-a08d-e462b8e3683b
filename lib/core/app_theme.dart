import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Brand Colors
  static const Color primaryBlue = Color.fromARGB(255, 11, 3, 83);    // Deep blue for primary actions
  static const Color secondaryBlue = Color(0xFF02234d);  // Lighter blue for secondary elements
  static const Color accentBlue = Color(0xFF3282B8);
  static const Color brightBlue = Color(0xFF4BA3F3);  // Bright blue for dark mode
  static const Color warningOrange = Color.fromRGBO(252, 166, 82, 1);
  
  // Light Theme Colors
  static const Color lightSurfaceColor = Color(0xFFFAFAFC);  // Subtle cool white
  static const Color lightBackgroundColor = Color(0xFFFFFFFF);  // Pure white
  static const Color lightFieldPrimary =  Color(0xFFEFF0F0);  // Slightly darker input field
  
  // Dark Theme Colors
  static const Color darkSurfaceColor = Color(0xFF1A1D2E);  // Rich navy-black
  static const Color darkBackgroundColor = Color(0xFF12141F);  // Deep space black
  static const Color darkInputFillColor = Color(0xFF242842);  // Elevated dark blue
  static const Color darkFieldPrimary = Color(0xFF2A2E4A);  // Lighter input field

  // Gradients
  static const List<Color> lightPrimaryGradient = [
    Color(0xFF074799),  // Top: Sage green
    Color(0xFF074799),  // Bottom: Forest green
  ];

  static const List<Color> darkPrimaryGradient = [
    Color(0xFF2A4365),  // Top: Deep blue
    Color(0xFF1A365D),  // Bottom: Navy blue
  ];

  // Card Styling
  static final cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      secondaryBlue.withOpacity(0.05),
      secondaryBlue.withOpacity(0.02),
    ],
  );

  static final cardBorder = BorderSide(
    color: secondaryBlue.withOpacity(0.1),
    width: 1,
  );

  static final _baseTextTheme = GoogleFonts.outfitTextTheme();
  static final _outfitFont = GoogleFonts.outfit().fontFamily;

  static final TextTheme _lightTextTheme = _baseTextTheme.copyWith(
    displayLarge: _baseTextTheme.displayLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.black,
    ),
    displayMedium: _baseTextTheme.displayMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.black,
    ),
    displaySmall: _baseTextTheme.displaySmall?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.black,
    ),
    headlineLarge: _baseTextTheme.headlineLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.black,
    ),
    headlineMedium: _baseTextTheme.headlineMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.black,
    ),
    titleLarge: _baseTextTheme.titleLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.w500,
      color: Colors.black87,
    ),
    titleMedium: _baseTextTheme.titleMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.w500,
      color: Colors.black87,
    ),
    bodyLarge: _baseTextTheme.bodyLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.normal,
      color: Colors.black87,
    ),
    bodyMedium: _baseTextTheme.bodyMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.normal,
      color: Colors.black87,
    ),
  );

  static final TextTheme _darkTextTheme = _baseTextTheme.copyWith(
    displayLarge: _baseTextTheme.displayLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    displayMedium: _baseTextTheme.displayMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    displaySmall: _baseTextTheme.displaySmall?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    headlineLarge: _baseTextTheme.headlineLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    headlineMedium: _baseTextTheme.headlineMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    titleLarge: _baseTextTheme.titleLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.w500,
      color: Colors.white70,
    ),
    titleMedium: _baseTextTheme.titleMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.w500,
      color: Colors.white70,
    ),
    bodyLarge: _baseTextTheme.bodyLarge?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.normal,
      color: Colors.white70,
    ),
    bodyMedium: _baseTextTheme.bodyMedium?.copyWith(
      fontFamily: _outfitFont,
      fontWeight: FontWeight.normal,
      color: Colors.white70,
    ),
  );

  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    fontFamily: _outfitFont,
    colorScheme: ColorScheme.fromSeed(
      seedColor: lightSurfaceColor,
      primary: secondaryBlue,
      secondary: secondaryBlue,
      surface: lightSurfaceColor,
      tertiaryContainer: lightFieldPrimary,
      error: const Color(0xFFD80032),
      
      tertiary: accentBlue,
    ),
    appBarTheme: AppBarTheme(
      centerTitle: false,
      elevation: 20,
      backgroundColor: lightBackgroundColor,
      surfaceTintColor: lightBackgroundColor,
      shadowColor: Colors.black.withOpacity(0.21),
      titleTextStyle: GoogleFonts.outfit(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: Colors.black,
      ),
    ),
  
    
    textTheme: _lightTextTheme,
    primaryTextTheme: _lightTextTheme,
  );

  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    fontFamily: _outfitFont,
    colorScheme: ColorScheme.fromSeed(
      seedColor: brightBlue,
      primary: brightBlue,
      secondary: accentBlue,
      surface: darkSurfaceColor,
      background: darkBackgroundColor,
      tertiaryContainer: darkFieldPrimary,
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: darkBackgroundColor,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 0.4,
      shadowColor: Colors.white.withOpacity(0.05),
    ),
    
    textTheme: _darkTextTheme,
    primaryTextTheme: _darkTextTheme,
  );

  // Add theme mode methods
  static ThemeMode get defaultTheme => ThemeMode.system;
  
  static String themeModeToString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }
  
  static ThemeMode stringToThemeMode(String modeStr) {
    switch (modeStr) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      default:
        return ThemeMode.system;
    }
  }
}