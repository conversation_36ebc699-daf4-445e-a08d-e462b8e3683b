import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/core/services/media_upload_service.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_cubit.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_cubit.dart';
import 'package:respublicaseguridad/core/theme/theme_repository.dart';
import 'package:respublicaseguridad/features/auth/data/repositories/firebase_auth_repository.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/get_user_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/reset_password_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_email_password_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_facebook_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_google_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_out_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_up_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/link_google_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/link_facebook_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/unlink_provider_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/get_linked_providers_use_case.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/settings/bloc/account_settings_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/data/datasources/firebase_identity_data_source.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/data/repositories/identity_verification_repository_impl.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/get_identity_verification_status_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/upload_id_document_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/watch_user_validation_status_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/process_dual_verification_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/dual_verification_workflow_service.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/automated_id_verification_service.dart';
import 'package:respublicaseguridad/features/identity_validation/data/repositories/user_report_repository_impl.dart';

import 'package:respublicaseguridad/features/home/<USER>/home_injection.dart';
import 'package:respublicaseguridad/features/incidents/di/incident_injection.dart';
import 'package:respublicaseguridad/features/zone_verification/data/datasources/zone_firebase_datasource.dart';
import 'package:respublicaseguridad/features/zone_verification/data/repositories/zone_repository_impl.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/create_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/check_zone_creation_prerequisites_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/get_user_zones_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/delete_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/request_social_validation_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/enable_automatic_validation_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/validate_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/perform_limited_validation_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/trigger_automatic_validation_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/watch_zone_validation_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/update_zone_validation_status_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/zone_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/presence_hours_parser_service.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/zone_validation_watcher_cubit.dart';
import 'package:respublicaseguridad/features/zone_verification/data/datasources/qr_validation_firebase_datasource.dart';
import 'package:respublicaseguridad/features/zone_verification/data/repositories/qr_validation_repository_impl.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/create_qr_validation_session_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/generate_qr_token_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/validate_qr_scan_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/complete_qr_validation_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/qr_validation_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/standalone_qr_scanner_cubit.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/services/permission_service.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';
import 'package:respublicaseguridad/core/services/limited_tracking_service.dart';
import 'package:respublicaseguridad/core/services/qr_token_service.dart';
import 'package:respublicaseguridad/core/services/proximity_verification_service.dart';
import 'package:respublicaseguridad/core/services/qr_validation_cloud_security_service.dart';
import 'package:respublicaseguridad/core/services/qr_validation_audit_service.dart';
import 'package:respublicaseguridad/core/services/share_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/push_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/location_consent_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_manager.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';
import 'package:respublicaseguridad/features/notifications/data/repositories/notification_preferences_repository_impl.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_preferences_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/services/notification_integration_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/real_time_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/location_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/notification_analytics_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/notification_preferences_sync_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';
import 'package:respublicaseguridad/features/notifications/data/repositories/incident_notification_repository_impl.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/incident_notification_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_cloud_functions_datasource.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

// This is a manual dependency injection configuration
final GetIt getIt = GetIt.instance;

// Manual dependency registration instead of using injectable
Future<void> configureDependencies() async {
  // Core dependencies
  final sharedPreferences = await SharedPreferences.getInstance();

  // Firebase instances
  getIt
    ..registerLazySingleton(() => FirebaseAuth.instance)
    ..registerLazySingleton(() => FirebaseFirestore.instance)
    ..registerLazySingleton(() => FirebaseStorage.instance)
    ..registerLazySingleton(() => FirebaseDatabase.instance)
    ..registerLazySingleton(() => const Uuid())
    ..registerLazySingleton(GoogleSignIn.standard)
    ..registerLazySingleton(() => FacebookAuth.instance)
    ..registerLazySingleton(() => sharedPreferences)
    // Repositories
    ..registerLazySingleton<IThemeRepository>(ThemeRepository.new)
    ..registerLazySingleton<AuthRepository>(
      () => FirebaseAuthRepository(
        getIt<FirebaseAuth>(),
        getIt<GoogleSignIn>(),
        getIt<FacebookAuth>(),
      ),
    )
    // Identity Validation Data Sources
    ..registerLazySingleton<FirebaseIdentityDataSource>(
      () => FirebaseIdentityDataSourceImpl(
        firestore: getIt<FirebaseFirestore>(),
        storage: getIt<FirebaseStorage>(),
        uuid: getIt<Uuid>(),
      ),
    )
    // Identity Validation Repository
    ..registerLazySingleton<IdentityVerificationRepository>(
      () => IdentityVerificationRepositoryImpl(
        getIt<FirebaseIdentityDataSource>(),
      ),
    )
    ..registerLazySingleton(() => GetUserUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(
      () => SignInEmailPasswordUseCase(getIt<AuthRepository>()),
    )
    ..registerLazySingleton(() => SignUpUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(() => SignInGoogleUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(
      () => SignInFacebookUseCase(getIt<AuthRepository>()),
    )
    ..registerLazySingleton(() => SignOutUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(() => ResetPasswordUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(() => LinkGoogleUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(() => LinkFacebookUseCase(getIt<AuthRepository>()))
    ..registerLazySingleton(
      () => UnlinkProviderUseCase(getIt<AuthRepository>()),
    )
    ..registerLazySingleton(
      () => GetLinkedProvidersUseCase(getIt<AuthRepository>()),
    )
    ..registerLazySingleton(
      () => UploadIdDocumentUseCase(getIt<IdentityVerificationRepository>()),
    )
    ..registerLazySingleton(
      () => GetIdentityVerificationStatusUseCase(
        getIt<IdentityVerificationRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => WatchUserValidationStatusUseCase(
        getIt<IdentityVerificationRepository>(),
      ),
    )
    // Dual Verification System Dependencies
    ..registerLazySingleton<UserReportRepository>(
      () => UserReportRepositoryImpl(
        firestore: getIt<FirebaseFirestore>(),
        uuid: getIt<Uuid>(),
      ),
    )
    ..registerLazySingleton<AdminNotificationService>(
      () => SimpleAdminNotificationService(),
    )
    // Simple auto-approval service - automatically validates uploaded documents
    ..registerLazySingleton<AutomatedIdVerificationService>(
      () => SimpleAutoApprovalService(),
    )
    ..registerLazySingleton<DualVerificationWorkflowService>(
      () => DualVerificationWorkflowService(
        identityRepository: getIt<IdentityVerificationRepository>(),
        reportRepository: getIt<UserReportRepository>(),
        automatedService: getIt<AutomatedIdVerificationService>(),
        adminNotificationService: getIt<AdminNotificationService>(),
      ),
    )
    ..registerLazySingleton(
      () => ProcessDualVerificationUseCase(
        getIt<DualVerificationWorkflowService>(),
      ),
    )
    ..registerFactory(() => ThemeCubit(getIt<IThemeRepository>()))
    ..registerFactory(
      () => IdentityVerificationCubit(
        getIdentityVerificationStatusUseCase:
            getIt<GetIdentityVerificationStatusUseCase>(),
      ),
    )
    ..registerLazySingleton(
      () => AuthBloc(
        getUserUseCase: getIt<GetUserUseCase>(),
        signInEmailPasswordUseCase: getIt<SignInEmailPasswordUseCase>(),
        signUpUseCase: getIt<SignUpUseCase>(),
        signInGoogleUseCase: getIt<SignInGoogleUseCase>(),
        signInFacebookUseCase: getIt<SignInFacebookUseCase>(),
        signOutUseCase: getIt<SignOutUseCase>(),
        resetPasswordUseCase: getIt<ResetPasswordUseCase>(),
      ),
    )
    // Identity Validation BLoC
    ..registerFactory(
      () => IdValidationBloc(
        uploadIdDocumentUseCase: getIt<UploadIdDocumentUseCase>(),
        getIdentityVerificationStatusUseCase:
            getIt<GetIdentityVerificationStatusUseCase>(),
        processDualVerificationUseCase: getIt<ProcessDualVerificationUseCase>(),
        firebaseAuth: getIt<FirebaseAuth>(),
      ),
    )
    // Account Settings BLoC
    ..registerFactory(
      () => AccountSettingsBloc(
        getIt<GetLinkedProvidersUseCase>(),
        getIt<LinkGoogleUseCase>(),
        getIt<LinkFacebookUseCase>(),
        getIt<UnlinkProviderUseCase>(),
      ),
    )
    // Zone Validation Data Sources
    ..registerLazySingleton<ZoneFirebaseDataSource>(
      () => ZoneFirebaseDataSourceImpl(firestore: getIt<FirebaseFirestore>()),
    )
    // QR Validation Data Sources
    ..registerLazySingleton<QRValidationFirebaseDataSource>(
      () => QRValidationFirebaseDataSourceImpl(getIt<FirebaseDatabase>()),
    )
    // Zone Validation Repository
    ..registerLazySingleton<ZoneRepository>(
      () => ZoneRepositoryImpl(
        firebaseDataSource: getIt<ZoneFirebaseDataSource>(),
      ),
    )
    // QR Validation Repository
    ..registerLazySingleton<QRValidationRepository>(
      () => QRValidationRepositoryImpl(
        getIt<QRValidationFirebaseDataSource>(),
        getIt<Uuid>(),
      ),
    )
    // Core Services
    ..registerLazySingleton(() => LocationService.instance)
    ..registerLazySingleton(() => PermissionService.instance)
    ..registerLazySingleton(() => NotificationService.instance)
    ..registerLazySingleton(() => FirebaseMessaging.instance)
    // Notification Preferences Dependencies
    ..registerLazySingleton<NotificationPreferencesDataSource>(
      () => NotificationPreferencesFirebaseDataSource(
        firestore: getIt<FirebaseFirestore>(),
      ),
    )
    ..registerLazySingleton<NotificationPreferencesRepository>(
      () => NotificationPreferencesRepositoryImpl(
        dataSource: getIt<NotificationPreferencesDataSource>(),
      ),
    )
    ..registerLazySingleton<NotificationPreferencesService>(
      () => NotificationPreferencesService(
        repository: getIt<NotificationPreferencesRepository>(),
      ),
    )
    // Notification Services
    ..registerLazySingleton<PushNotificationService>(
      () => PushNotificationService(
        notificationService: getIt<NotificationService>(),
        preferencesService: getIt<NotificationPreferencesService>(),
        firebaseMessaging: getIt<FirebaseMessaging>(),
      ),
    )
    ..registerLazySingleton<LocationConsentService>(
      () => LocationConsentService(
        preferencesService: getIt<NotificationPreferencesService>(),
        pushNotificationService: getIt<PushNotificationService>(),
      ),
    )
    ..registerLazySingleton<NotificationManager>(
      () => NotificationManager(
        pushNotificationService: getIt<PushNotificationService>(),
        locationConsentService: getIt<LocationConsentService>(),
        preferencesService: getIt<NotificationPreferencesService>(),
      ),
    )
    // Notification Integration Services
    ..registerLazySingleton(() => FlutterLocalNotificationsPlugin())
    ..registerLazySingleton(() => FirebaseFunctions.instance)
    ..registerLazySingleton<NotificationCloudFunctionsDataSource>(
      () => NotificationCloudFunctionsDataSource(
        functions: getIt<FirebaseFunctions>(),
      ),
    )
    ..registerLazySingleton<IncidentNotificationDataSource>(
      () => IncidentNotificationFirebaseDataSource(
        firestore: getIt<FirebaseFirestore>(),
      ),
    )
    ..registerLazySingleton<IncidentNotificationRepository>(
      () => IncidentNotificationRepositoryImpl(
        dataSource: getIt<IncidentNotificationDataSource>(),
      ),
    )
    ..registerLazySingleton<RealTimeNotificationService>(
      () => RealTimeNotificationService(
        firebaseMessaging: getIt<FirebaseMessaging>(),
        localNotifications: getIt<FlutterLocalNotificationsPlugin>(),
      ),
    )
    ..registerLazySingleton<LocationNotificationService>(
      () => LocationNotificationService(
        cloudFunctionsDataSource: getIt<NotificationCloudFunctionsDataSource>(),
      ),
    )
    ..registerLazySingleton<NotificationAnalyticsService>(
      () => NotificationAnalyticsService(
        cloudFunctionsDataSource: getIt<NotificationCloudFunctionsDataSource>(),
      ),
    )
    ..registerLazySingleton<NotificationPreferencesSyncService>(
      () => NotificationPreferencesSyncService(
        cloudFunctionsDataSource: getIt<NotificationCloudFunctionsDataSource>(),
        locationService: getIt<LocationNotificationService>(),
      ),
    )
    ..registerLazySingleton<NotificationIntegrationService>(
      () => NotificationIntegrationService(
        realTimeService: getIt<RealTimeNotificationService>(),
        locationService: getIt<LocationNotificationService>(),
        analyticsService: getIt<NotificationAnalyticsService>(),
        preferencesSync: getIt<NotificationPreferencesSyncService>(),
        notificationRepository: getIt<IncidentNotificationRepository>(),
      ),
    )
    ..registerLazySingleton(() => GeofencingService.instance)
    ..registerLazySingleton(() => LimitedTrackingService.instance)
    ..registerLazySingleton(() => QRTokenService.instance)
    ..registerLazySingleton(() => ProximityVerificationService.instance)
    ..registerLazySingleton(() => QRValidationCloudSecurityService.instance)
    ..registerLazySingleton(() => QRValidationAuditService.instance)
    ..registerLazySingleton(() => MediaUploadService.instance)
    ..registerLazySingleton(() => ShareService())
    // Data Sync Services
    // Automatic Validation Services
    ..registerLazySingleton(() => AutomaticValidationService())
    // Zone Verification Domain Services
    ..registerLazySingleton(
      () => ZoneValidationService(
        getIt<ZoneRepository>(),
        automaticValidationService: getIt<AutomaticValidationService>(),
      ),
    )
    // Zone Validation Services
    ..registerLazySingleton<PresenceHoursParserService>(
      () => PresenceHoursParserServiceImpl(),
    )
    // Zone Validation Use Cases
    ..registerLazySingleton(
      () => CheckZoneCreationPrerequisitesUseCase(
        getIt<IdentityVerificationRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => CreateZoneUseCase(
        getIt<ZoneRepository>(),
        getIt<PresenceHoursParserService>(),
        getIt<CheckZoneCreationPrerequisitesUseCase>(),
        getIt<AutomaticValidationService>(),
      ),
    )
    ..registerLazySingleton(() => GetUserZonesUseCase(getIt<ZoneRepository>()))
    ..registerLazySingleton(() => DeleteZoneUseCase(getIt<ZoneRepository>()))
    ..registerLazySingleton(
      () => RequestSocialValidationUseCase(getIt<ZoneValidationService>()),
    )
    ..registerLazySingleton(
      () => EnableAutomaticValidationUseCase(getIt<ZoneValidationService>()),
    )
    ..registerLazySingleton(
      () => ValidateZoneUseCase(getIt<ZoneValidationService>()),
    )
    ..registerLazySingleton(
      () => PerformLimitedValidationUseCase(getIt<LimitedTrackingService>()),
    )
    // Automatic Validation Use Cases
    ..registerLazySingleton(
      () => TriggerAutomaticValidationUseCase(
        automaticValidationService: getIt<AutomaticValidationService>(),
        zoneRepository: getIt<ZoneRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => ProcessLocationUpdateUseCase(
        automaticValidationService: getIt<AutomaticValidationService>(),
        zoneRepository: getIt<ZoneRepository>(),
      ),
    )
    // QR Validation Use Cases
    ..registerLazySingleton(
      () => CreateQRValidationSessionUseCase(
        getIt<QRValidationRepository>(),
        getIt<ZoneRepository>(),
        getIt<IdentityVerificationRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => JoinQRValidationSessionUseCase(
        getIt<QRValidationRepository>(),
        getIt<ZoneRepository>(),
        getIt<IdentityVerificationRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => GenerateQRTokenUseCase(getIt<QRValidationRepository>()),
    )
    ..registerLazySingleton(
      () => ValidateQRScanUseCase(
        getIt<QRValidationRepository>(),
        getIt<ZoneRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => CompleteQRValidationUseCase(
        getIt<QRValidationRepository>(),
        getIt<ZoneRepository>(),
      ),
    )
    ..registerLazySingleton(
      () => CancelQRValidationUseCase(getIt<QRValidationRepository>()),
    )
    // Zone Validation Watcher Use Cases
    ..registerLazySingleton(
      () => WatchZoneValidationUseCase(getIt<ZoneRepository>()),
    )
    ..registerLazySingleton(
      () => UpdateZoneValidationStatusUseCase(getIt<ZoneRepository>()),
    )
    // Zone Validation Watcher Cubit
    ..registerFactory(
      () => ZoneValidationWatcherCubit(
        watchZoneUseCase: getIt<WatchZoneValidationUseCase>(),
        updateZoneStatusUseCase: getIt<UpdateZoneValidationStatusUseCase>(),
      ),
    )
    // Zone Validation BLoC
    ..registerFactory(
      () => ZoneValidationBloc(
        createZoneUseCase: getIt<CreateZoneUseCase>(),
        getUserZonesUseCase: getIt<GetUserZonesUseCase>(),
        deleteZoneUseCase: getIt<DeleteZoneUseCase>(),
        requestSocialValidationUseCase: getIt<RequestSocialValidationUseCase>(),
        enableAutomaticValidationUseCase:
            getIt<EnableAutomaticValidationUseCase>(),
        validateZoneUseCase: getIt<ValidateZoneUseCase>(),
        limitedTrackingService: getIt<LimitedTrackingService>(),
      ),
    )
    // QR Validation BLoC
    ..registerFactory(
      () => QRValidationBloc(
        createSessionUseCase: getIt<CreateQRValidationSessionUseCase>(),
        joinSessionUseCase: getIt<JoinQRValidationSessionUseCase>(),
        generateTokenUseCase: getIt<GenerateQRTokenUseCase>(),
        validateScanUseCase: getIt<ValidateQRScanUseCase>(),
        completeValidationUseCase: getIt<CompleteQRValidationUseCase>(),
        cancelValidationUseCase: getIt<CancelQRValidationUseCase>(),
      ),
    )
    // Standalone QR Scanner Cubit
    ..registerFactory(
      () => StandaloneQRScannerCubit(
        validateScanUseCase: getIt<ValidateQRScanUseCase>(),
      ),
    );

  // Initialize Home feature dependencies
  HomeInjection.init();

  // Initialize Incident feature dependencies
  IncidentInjection.init();

  // Notification Preferences Cubit (after incident injection to ensure dependencies are available)
  // Note: This is registered as a factory since it needs userId parameter
  // The actual cubit is created in the screen with the userId parameter
}
