import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';

@module
abstract class FirebaseModule {
  @lazySingleton
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;
  
  @lazySingleton
  GoogleSignIn get googleSignIn => GoogleSignIn(
    // Server client ID from Firebase console OAuth 2.0 client
    serverClientId: '660923458040-0dtbco53hgi282rpetjojelce902udmu.apps.googleusercontent.com',
    scopes: ['email', 'profile'],
  );
  
  @lazySingleton
  FacebookAuth get facebookAuth => FacebookAuth.instance;
} 
