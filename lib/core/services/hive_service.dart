import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../../features/security/data/models/incident_model.dart';
import '../../features/security/data/models/incident_summary_model.dart';

class HiveService {
  static bool _isInitialized = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize Hive
    await Hive.initFlutter();

    // Register adapters
    _registerAdapters();

    _isInitialized = true;
  }

  static void _registerAdapters() {
    // Register model adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(IncidentModelAdapter());
    }
    
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(IncidentSummaryModelAdapter());
    }
    
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(HotspotModelAdapter());
    }
    
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(IncidentTrendModelAdapter());
    }
    
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(TrendDataPointAdapter());
    }
  }

  static Future<Box<T>> openBox<T>(String boxName) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    return await Hive.openBox<T>(boxName);
  }

  static Future<void> closeAllBoxes() async {
    await Hive.close();
  }

  static Future<void> deleteBox(String boxName) async {
    await Hive.deleteBoxFromDisk(boxName);
  }

  static Future<void> clearAllData() async {
    final boxes = [
      'security_incidents',
      'incident_summaries',
      'incident_trends',
      'security_preferences',
      'offline_queue',
    ];

    for (final boxName in boxes) {
      try {
        final box = await Hive.openBox(boxName);
        await box.clear();
        await box.close();
      } catch (e) {
        print('Error clearing box $boxName: $e');
      }
    }
  }

  static Future<Map<String, dynamic>> getStorageInfo() async {
    final boxes = [
      'security_incidents',
      'incident_summaries', 
      'incident_trends',
      'security_preferences',
      'offline_queue',
    ];

    final info = <String, dynamic>{};
    int totalSize = 0;

    for (final boxName in boxes) {
      try {
        final box = await Hive.openBox(boxName);
        final size = box.length;
        info[boxName] = size;
        totalSize += size;
      } catch (e) {
        info[boxName] = 'Error: $e';
      }
    }

    info['total_items'] = totalSize;
    return info;
  }
}
