import 'package:workmanager/workmanager.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:respublicaseguridad/core/utils/logger.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:convert';

/// Clean and simplified background service
///
/// Handles only essential background tasks since automatic validation
/// is now handled efficiently by cloud functions with minimal resource usage
class BackgroundService {
  static const String _logContext = 'BackgroundService';

  // Task identifiers - simplified for clean architecture
  static const String _locationUpdateTask = 'location_update_task';
  static const String _automaticValidationTask = 'automatic_validation_task';

  // Configuration - Location updates more frequent than cloud function freshness requirement (10 min)
  static const Duration _locationUpdateInterval = Duration(minutes: 8);
  static const Duration _automaticValidationInterval = Duration(minutes: 10);

  /// Initializes the background service with clean task management
  static Future<void> initialize() async {
    const methodContext = 'initialize';

    try {
      Logger.info(
        '$_logContext.$methodContext: Initializing clean background service',
      );

      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to false for production
      );

      Logger.info(
        '$_logContext.$methodContext: Background service initialized successfully',
      );
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Failed to initialize - $e');
      rethrow;
    }
  }

  /// Starts lightweight automatic validation background monitoring
  ///
  /// Note: The actual validation logic is handled by cloud functions
  /// This just ensures minimal location updates are available when needed
  static Future<void> startAutomaticValidation(String zoneId) async {
    const methodContext = 'startAutomaticValidation';

    try {
      Logger.info('$_logContext.$methodContext: Starting for zone $zoneId');

      await Workmanager().registerPeriodicTask(
        _automaticValidationTask,
        _automaticValidationTask,
        frequency: _automaticValidationInterval,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: true,
        ),
        inputData: {
          'zoneId': zoneId,
          'startTime': DateTime.now().millisecondsSinceEpoch,
        },
      );

      Logger.info(
        '$_logContext.$methodContext: Lightweight monitoring started for zone $zoneId',
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Failed to start monitoring - $e',
      );
    }
  }

  /// Stops automatic validation background tasks
  static Future<void> stopAutomaticValidation() async {
    const methodContext = 'stopAutomaticValidation';

    try {
      Logger.info(
        '$_logContext.$methodContext: Stopping automatic validation monitoring',
      );

      await Workmanager().cancelByUniqueName(_automaticValidationTask);

      Logger.info(
        '$_logContext.$methodContext: Monitoring stopped successfully',
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error stopping monitoring - $e',
      );
    }
  }

  /// Starts periodic location updates for user_locations collection
  static Future<void> startLocationUpdates() async {
    const methodContext = 'startLocationUpdates';

    try {
      Logger.info(
        '$_logContext.$methodContext: Starting periodic location updates',
      );

      await Workmanager().registerPeriodicTask(
        _locationUpdateTask,
        _locationUpdateTask,
        frequency: _locationUpdateInterval,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: true,
        ),
      );

      Logger.info('$_logContext.$methodContext: Location updates started');
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Failed to start location updates - $e',
      );
    }
  }

  /// Stops location update tasks
  static Future<void> stopLocationUpdates() async {
    const methodContext = 'stopLocationUpdates';

    try {
      Logger.info('$_logContext.$methodContext: Stopping location updates');

      await Workmanager().cancelByUniqueName(_locationUpdateTask);

      Logger.info('$_logContext.$methodContext: Location updates stopped');
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error stopping location updates - $e',
      );
    }
  }

  /// Stops all background tasks
  static Future<void> stopAllTasks() async {
    const methodContext = 'stopAllTasks';

    try {
      Logger.info('$_logContext.$methodContext: Stopping all background tasks');

      await Workmanager().cancelAll();

      Logger.info('$_logContext.$methodContext: All background tasks stopped');
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Error stopping tasks - $e');
    }
  }

  /// Gets current location if permissions allow
  static Future<Position?> _getCurrentLocationSafely() async {
    const methodContext = '_getCurrentLocationSafely';

    try {
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        Logger.warning(
          '$_logContext.$methodContext: Location permission denied',
        );
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Logger.warning(
          '$_logContext.$methodContext: Location services disabled',
        );
        return null;
      }

      // Get current location with timeout and accuracy settings
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 30),
      );
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Error getting location - $e');
      return null;
    }
  }

  /// Stores location data locally and syncs to Firestore for cloud function access
  static Future<void> _storeLocationForCloudAccess(Position position) async {
    const methodContext = '_storeLocationForCloudAccess';

    try {
      final prefs = await SharedPreferences.getInstance();

      final locationData = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Store locally for backup
      await prefs.setString('last_location_update', jsonEncode(locationData));

      // Sync to Firestore for cloud function access
      await _syncLocationToFirestore(position);

      Logger.info(
        '$_logContext.$methodContext: Location stored locally and synced to cloud',
      );
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Error storing location - $e');
    }
  }

  /// Syncs location data to Firestore user_locations collection
  static Future<void> _syncLocationToFirestore(Position position) async {
    const methodContext = '_syncLocationToFirestore';

    try {
      // Check if user is authenticated
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        Logger.warning(
          '$_logContext.$methodContext: User not authenticated, skipping sync',
        );
        return;
      }

      // Call cloud function to update user location
      final functions = FirebaseFunctions.instance;
      final callable = functions.httpsCallable('updateUserLocationGen2');

      await callable.call({
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'source': 'background',
      });

      Logger.info(
        '$_logContext.$methodContext: Successfully synced to Firestore',
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error syncing to Firestore - $e',
      );
    }
  }

  /// Logs background task execution for monitoring
  static Future<void> _logBackgroundExecution(
    String taskName,
    Map<String, dynamic>? inputData,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final logData = {
        'taskName': taskName,
        'executionTime': DateTime.now().toIso8601String(),
        'inputData': inputData,
      };

      final existingLogs =
          prefs.getStringList('background_execution_logs') ?? [];
      existingLogs.add(jsonEncode(logData));

      // Keep only last 10 logs to prevent storage bloat
      if (existingLogs.length > 10) {
        existingLogs.removeRange(0, existingLogs.length - 10);
      }

      await prefs.setStringList('background_execution_logs', existingLogs);
    } catch (e) {
      Logger.error(
        '$_logContext._logBackgroundExecution: Error logging execution - $e',
      );
    }
  }
}

/// Clean callback dispatcher for background tasks
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((taskName, inputData) async {
    const logContext = 'BackgroundService.callbackDispatcher';

    try {
      Logger.info('$logContext: Executing task $taskName');

      // Log task execution
      await BackgroundService._logBackgroundExecution(taskName, inputData);

      switch (taskName) {
        case 'automatic_validation_task':
          await _handleAutomaticValidationTask(inputData);
          break;

        case 'location_update_task':
          await _handleLocationUpdateTask(inputData);
          break;

        default:
          Logger.warning('$logContext: Unknown task $taskName');
      }

      return Future.value(true);
    } catch (e) {
      Logger.error('$logContext: Task execution failed - $e');
      return Future.value(false);
    }
  });
}

/// Handles automatic validation background task (lightweight)
Future<void> _handleAutomaticValidationTask(
  Map<String, dynamic>? inputData,
) async {
  const methodContext = '_handleAutomaticValidationTask';

  try {
    Logger.info(
      'BackgroundService.$methodContext: Running lightweight validation task',
    );

    // Get current location and store it for cloud function access
    final position = await BackgroundService._getCurrentLocationSafely();

    if (position != null) {
      await BackgroundService._storeLocationForCloudAccess(position);
      Logger.info(
        'BackgroundService.$methodContext: Location updated for cloud validation',
      );
    } else {
      Logger.warning('BackgroundService.$methodContext: No location available');
    }

    // The actual validation logic is handled by cloud functions
    // This task just ensures fresh location data is available
  } catch (e) {
    Logger.error(
      'BackgroundService.$methodContext: Error in validation task - $e',
    );
  }
}

/// Handles location update background task
Future<void> _handleLocationUpdateTask(Map<String, dynamic>? inputData) async {
  const methodContext = '_handleLocationUpdateTask';

  try {
    Logger.info(
      'BackgroundService.$methodContext: Running location update task',
    );

    final position = await BackgroundService._getCurrentLocationSafely();

    if (position != null) {
      await BackgroundService._storeLocationForCloudAccess(position);
      Logger.info(
        'BackgroundService.$methodContext: Location updated successfully',
      );
    } else {
      Logger.warning(
        'BackgroundService.$methodContext: Failed to get location',
      );
    }
  } catch (e) {
    Logger.error(
      'BackgroundService.$methodContext: Error in location update - $e',
    );
  }
}
