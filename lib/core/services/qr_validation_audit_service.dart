import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Security audit event types for QR validation system
enum SecurityAuditEventType {
  tokenGenerated,
  tokenValidated,
  tokenExpired,
  replayAttackDetected,
  invalidSignature,
  unauthorizedAccess,
  sessionCreated,
  sessionCompleted,
  sessionCancelled,
  proximityVerified,
  proximityFailed,
}

/// Security audit event
class SecurityAuditEvent {
  final SecurityAuditEventType type;
  final String userId;
  final String? sessionId;
  final String? zoneId;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final String? ipAddress;
  final String? userAgent;

  const SecurityAuditEvent({
    required this.type,
    required this.userId,
    this.sessionId,
    this.zoneId,
    required this.timestamp,
    this.metadata = const {},
    this.ipAddress,
    this.userAgent,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'userId': userId,
      'sessionId': sessionId,
      'zoneId': zoneId,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
    };
  }
}

/// Audit logging service for QR validation system
class QRValidationAuditService {
  static QRValidationAuditService? _instance;
  static QRValidationAuditService get instance => _instance ??= QRValidationAuditService._();
  QRValidationAuditService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Log a security audit event
  Future<void> logSecurityEvent(SecurityAuditEvent event) async {
    try {
      await _firestore
          .collection('security_audit_logs')
          .add(event.toJson());
    } catch (e) {
      // Log to local storage or crash reporting service
      print('Failed to log security event: $e');
    }
  }

  /// Log QR token generation
  Future<void> logTokenGeneration({
    required String sessionId,
    required String userId,
    required String zoneId,
    required DateTime expiresAt,
  }) async {
    final event = SecurityAuditEvent(
      type: SecurityAuditEventType.tokenGenerated,
      userId: userId,
      sessionId: sessionId,
      zoneId: zoneId,
      timestamp: DateTime.now(),
      metadata: {
        'expiresAt': expiresAt.toIso8601String(),
        'tokenLifetime': expiresAt.difference(DateTime.now()).inSeconds,
      },
    );

    await logSecurityEvent(event);
  }

  /// Log QR token validation
  Future<void> logTokenValidation({
    required String sessionId,
    required String userId,
    required String zoneId,
    required bool isValid,
    String? errorReason,
    Map<String, dynamic>? proximityData,
  }) async {
    final event = SecurityAuditEvent(
      type: SecurityAuditEventType.tokenValidated,
      userId: userId,
      sessionId: sessionId,
      zoneId: zoneId,
      timestamp: DateTime.now(),
      metadata: {
        'isValid': isValid,
        'errorReason': errorReason,
        'proximityData': proximityData,
      },
    );

    await logSecurityEvent(event);
  }

  /// Log replay attack detection
  Future<void> logReplayAttackDetection({
    required String userId,
    required String sessionId,
    required String tokenId,
    required String attackDetails,
  }) async {
    final event = SecurityAuditEvent(
      type: SecurityAuditEventType.replayAttackDetected,
      userId: userId,
      sessionId: sessionId,
      timestamp: DateTime.now(),
      metadata: {
        'tokenId': tokenId,
        'attackDetails': attackDetails,
        'severity': 'HIGH',
      },
    );

    await logSecurityEvent(event);
  }

  /// Log unauthorized access attempt
  Future<void> logUnauthorizedAccess({
    required String userId,
    required String attemptedAction,
    required String reason,
    String? sessionId,
    String? zoneId,
  }) async {
    final event = SecurityAuditEvent(
      type: SecurityAuditEventType.unauthorizedAccess,
      userId: userId,
      sessionId: sessionId,
      zoneId: zoneId,
      timestamp: DateTime.now(),
      metadata: {
        'attemptedAction': attemptedAction,
        'reason': reason,
        'severity': 'MEDIUM',
      },
    );

    await logSecurityEvent(event);
  }

  /// Log session lifecycle events
  Future<void> logSessionEvent({
    required SecurityAuditEventType eventType,
    required String sessionId,
    required String userId,
    required String zoneId,
    Map<String, dynamic>? additionalData,
  }) async {
    final event = SecurityAuditEvent(
      type: eventType,
      userId: userId,
      sessionId: sessionId,
      zoneId: zoneId,
      timestamp: DateTime.now(),
      metadata: additionalData ?? {},
    );

    await logSecurityEvent(event);
  }

  /// Log proximity verification events
  Future<void> logProximityVerification({
    required String sessionId,
    required String userId,
    required String zoneId,
    required bool isValid,
    required double distance,
    required double accuracy,
    String? errorReason,
  }) async {
    final eventType = isValid 
        ? SecurityAuditEventType.proximityVerified 
        : SecurityAuditEventType.proximityFailed;

    final event = SecurityAuditEvent(
      type: eventType,
      userId: userId,
      sessionId: sessionId,
      zoneId: zoneId,
      timestamp: DateTime.now(),
      metadata: {
        'distance': distance,
        'accuracy': accuracy,
        'isValid': isValid,
        'errorReason': errorReason,
        'maxAllowedDistance': 100, // meters
      },
    );

    await logSecurityEvent(event);
  }

  /// Get audit logs for a user
  Future<List<SecurityAuditEvent>> getUserAuditLogs({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      Query query = _firestore
          .collection('security_audit_logs')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: endDate.toIso8601String());
      }

      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return SecurityAuditEvent(
          type: SecurityAuditEventType.values.firstWhere(
            (e) => e.name == data['type'],
            orElse: () => SecurityAuditEventType.unauthorizedAccess,
          ),
          userId: data['userId'],
          sessionId: data['sessionId'],
          zoneId: data['zoneId'],
          timestamp: DateTime.parse(data['timestamp']),
          metadata: data['metadata'] ?? {},
          ipAddress: data['ipAddress'],
          userAgent: data['userAgent'],
        );
      }).toList();
    } catch (e) {
      print('Failed to get user audit logs: $e');
      return [];
    }
  }

  /// Get audit logs for a session
  Future<List<SecurityAuditEvent>> getSessionAuditLogs({
    required String sessionId,
    int limit = 50,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('security_audit_logs')
          .where('sessionId', isEqualTo: sessionId)
          .orderBy('timestamp', descending: false)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return SecurityAuditEvent(
          type: SecurityAuditEventType.values.firstWhere(
            (e) => e.name == data['type'],
            orElse: () => SecurityAuditEventType.unauthorizedAccess,
          ),
          userId: data['userId'],
          sessionId: data['sessionId'],
          zoneId: data['zoneId'],
          timestamp: DateTime.parse(data['timestamp']),
          metadata: data['metadata'] ?? {},
          ipAddress: data['ipAddress'],
          userAgent: data['userAgent'],
        );
      }).toList();
    } catch (e) {
      print('Failed to get session audit logs: $e');
      return [];
    }
  }

  /// Get security alerts (high-severity events)
  Future<List<SecurityAuditEvent>> getSecurityAlerts({
    DateTime? since,
    int limit = 20,
  }) async {
    try {
      final alertTypes = [
        SecurityAuditEventType.replayAttackDetected,
        SecurityAuditEventType.invalidSignature,
        SecurityAuditEventType.unauthorizedAccess,
      ];

      Query query = _firestore
          .collection('security_audit_logs')
          .where('type', whereIn: alertTypes.map((e) => e.name).toList())
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (since != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: since.toIso8601String());
      }

      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return SecurityAuditEvent(
          type: SecurityAuditEventType.values.firstWhere(
            (e) => e.name == data['type'],
            orElse: () => SecurityAuditEventType.unauthorizedAccess,
          ),
          userId: data['userId'],
          sessionId: data['sessionId'],
          zoneId: data['zoneId'],
          timestamp: DateTime.parse(data['timestamp']),
          metadata: data['metadata'] ?? {},
          ipAddress: data['ipAddress'],
          userAgent: data['userAgent'],
        );
      }).toList();
    } catch (e) {
      print('Failed to get security alerts: $e');
      return [];
    }
  }

  /// Generate audit report for a time period
  Future<AuditReport> generateAuditReport({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
    String? zoneId,
  }) async {
    try {
      Query query = _firestore
          .collection('security_audit_logs')
          .where('timestamp', isGreaterThanOrEqualTo: startDate.toIso8601String())
          .where('timestamp', isLessThanOrEqualTo: endDate.toIso8601String());

      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      if (zoneId != null) {
        query = query.where('zoneId', isEqualTo: zoneId);
      }

      final snapshot = await query.get();
      final events = snapshot.docs.map((doc) => doc.data() as Map<String, dynamic>).toList();

      return AuditReport(
        startDate: startDate,
        endDate: endDate,
        totalEvents: events.length,
        eventsByType: _groupEventsByType(events),
        securityAlerts: _countSecurityAlerts(events),
        topUsers: _getTopUsers(events),
        topZones: _getTopZones(events),
      );
    } catch (e) {
      print('Failed to generate audit report: $e');
      return AuditReport.empty(startDate, endDate);
    }
  }

  Map<String, int> _groupEventsByType(List<Map<String, dynamic>> events) {
    final Map<String, int> counts = {};
    for (final event in events) {
      final type = event['type'] as String;
      counts[type] = (counts[type] ?? 0) + 1;
    }
    return counts;
  }

  int _countSecurityAlerts(List<Map<String, dynamic>> events) {
    final alertTypes = [
      'replayAttackDetected',
      'invalidSignature',
      'unauthorizedAccess',
    ];
    
    return events.where((event) => alertTypes.contains(event['type'])).length;
  }

  List<String> _getTopUsers(List<Map<String, dynamic>> events) {
    final Map<String, int> userCounts = {};
    for (final event in events) {
      final userId = event['userId'] as String;
      userCounts[userId] = (userCounts[userId] ?? 0) + 1;
    }
    
    final sortedUsers = userCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedUsers.take(10).map((e) => e.key).toList();
  }

  List<String> _getTopZones(List<Map<String, dynamic>> events) {
    final Map<String, int> zoneCounts = {};
    for (final event in events) {
      final zoneId = event['zoneId'] as String?;
      if (zoneId != null) {
        zoneCounts[zoneId] = (zoneCounts[zoneId] ?? 0) + 1;
      }
    }
    
    final sortedZones = zoneCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedZones.take(10).map((e) => e.key).toList();
  }
}

/// Audit report data structure
class AuditReport {
  final DateTime startDate;
  final DateTime endDate;
  final int totalEvents;
  final Map<String, int> eventsByType;
  final int securityAlerts;
  final List<String> topUsers;
  final List<String> topZones;

  const AuditReport({
    required this.startDate,
    required this.endDate,
    required this.totalEvents,
    required this.eventsByType,
    required this.securityAlerts,
    required this.topUsers,
    required this.topZones,
  });

  factory AuditReport.empty(DateTime startDate, DateTime endDate) {
    return AuditReport(
      startDate: startDate,
      endDate: endDate,
      totalEvents: 0,
      eventsByType: {},
      securityAlerts: 0,
      topUsers: [],
      topZones: [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'totalEvents': totalEvents,
      'eventsByType': eventsByType,
      'securityAlerts': securityAlerts,
      'topUsers': topUsers,
      'topZones': topZones,
    };
  }
}
