import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/coordinates.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';

/// Service for managing geofences and zone entry/exit detection
class GeofencingService {
  static GeofencingService? _instance;
  static GeofencingService get instance => _instance ??= GeofencingService._();
  GeofencingService._();

  final LocationService _locationService = LocationService.instance;
  final Map<String, GeofenceRegion> _activeGeofences = {};
  final Map<String, GeofenceState> _geofenceStates = {};
  
  StreamController<GeofenceEvent>? _geofenceEventController;
  StreamSubscription<Position>? _locationSubscription;
  bool _isMonitoring = false;

  /// Stream of geofence events (enter/exit)
  Stream<GeofenceEvent>? get geofenceEvents => _geofenceEventController?.stream;

  /// Check if geofencing is currently active
  bool get isMonitoring => _isMonitoring;

  /// Get all active geofences
  Map<String, GeofenceRegion> get activeGeofences => Map.unmodifiable(_activeGeofences);

  /// Start geofencing monitoring
  Future<bool> startGeofencing() async {
    if (_isMonitoring) return true;

    try {
      // Start location monitoring first
      final locationStarted = await _locationService.startLocationMonitoring(
        interval: const Duration(seconds: 30), // More frequent for geofencing
        distanceFilter: 5.0, // Smaller distance filter for better accuracy
      );

      if (!locationStarted) return false;

      _geofenceEventController = StreamController<GeofenceEvent>.broadcast();

      // Listen to location updates
      _locationSubscription = _locationService.locationStream?.listen(
        _handleLocationUpdate,
        onError: (error) {
          _geofenceEventController?.addError(error);
        },
      );

      _isMonitoring = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Stop geofencing monitoring
  Future<void> stopGeofencing() async {
    if (!_isMonitoring) return;

    await _locationSubscription?.cancel();
    _locationSubscription = null;

    await _geofenceEventController?.close();
    _geofenceEventController = null;

    _activeGeofences.clear();
    _geofenceStates.clear();
    _isMonitoring = false;
  }

  /// Add a geofence for a zone
  Future<bool> addGeofence(ZoneEntity zone) async {
    try {
      final geofence = GeofenceRegion(
        id: zone.id,
        center: zone.centerCoordinates,
        radius: zone.radiusInMeters,
        zoneEntity: zone,
      );

      _activeGeofences[zone.id] = geofence;
      
      // Initialize state as unknown
      _geofenceStates[zone.id] = GeofenceState.unknown;

      // Check current position against this geofence
      final currentPosition = _locationService.lastKnownPosition;
      if (currentPosition != null) {
        _checkGeofenceForPosition(geofence, currentPosition);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove a geofence
  Future<bool> removeGeofence(String zoneId) async {
    try {
      _activeGeofences.remove(zoneId);
      _geofenceStates.remove(zoneId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Add multiple geofences for zones
  Future<void> addGeofencesForZones(List<ZoneEntity> zones) async {
    for (final zone in zones) {
      await addGeofence(zone);
    }
  }

  /// Remove all geofences
  Future<void> clearAllGeofences() async {
    _activeGeofences.clear();
    _geofenceStates.clear();
  }

  /// Get current state of a geofence
  GeofenceState? getGeofenceState(String zoneId) {
    return _geofenceStates[zoneId];
  }

  /// Check if user is currently inside a specific zone
  bool isInsideZone(String zoneId) {
    return _geofenceStates[zoneId] == GeofenceState.inside;
  }

  /// Get all zones user is currently inside
  List<String> getZonesUserIsInside() {
    return _geofenceStates.entries
        .where((entry) => entry.value == GeofenceState.inside)
        .map((entry) => entry.key)
        .toList();
  }

  /// Handle location updates and check geofences
  void _handleLocationUpdate(Position position) {
    for (final geofence in _activeGeofences.values) {
      _checkGeofenceForPosition(geofence, position);
    }
  }

  /// Check a specific geofence against a position
  void _checkGeofenceForPosition(GeofenceRegion geofence, Position position) {
    final isInside = _locationService.isPositionInZone(
      position,
      geofence.center,
      geofence.radius,
    );

    final currentState = _geofenceStates[geofence.id] ?? GeofenceState.unknown;
    final newState = isInside ? GeofenceState.inside : GeofenceState.outside;

    // Only trigger events on state changes
    if (currentState != newState) {
      _geofenceStates[geofence.id] = newState;

      final eventType = isInside ? GeofenceEventType.enter : GeofenceEventType.exit;
      final event = GeofenceEvent(
        type: eventType,
        geofence: geofence,
        position: position,
        timestamp: DateTime.now(),
        accuracy: position.accuracy,
      );

      _geofenceEventController?.add(event);
    }
  }

  /// Calculate dwell time in a zone
  Duration? getDwellTime(String zoneId) {
    // This would require tracking entry times
    // Implementation depends on requirements
    return null;
  }

  /// Get distance to zone center
  double? getDistanceToZone(String zoneId) {
    final geofence = _activeGeofences[zoneId];
    final currentPosition = _locationService.lastKnownPosition;
    
    if (geofence == null || currentPosition == null) return null;

    return _locationService.calculateDistance(
      _locationService.positionToCoordinates(currentPosition),
      geofence.center,
    );
  }

  /// Check if position accuracy is sufficient for reliable geofencing
  bool isAccuracySufficient(Position position, {double maxAccuracy = 15.0}) {
    return position.accuracy <= maxAccuracy;
  }

  /// Dispose resources
  Future<void> dispose() async {
    await stopGeofencing();
    _instance = null;
  }
}

/// Represents a geofence region
class GeofenceRegion {
  final String id;
  final Coordinates center;
  final double radius;
  final ZoneEntity zoneEntity;
  final DateTime createdAt;

  GeofenceRegion({
    required this.id,
    required this.center,
    required this.radius,
    required this.zoneEntity,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Check if a position is inside this geofence
  bool containsPosition(Position position) {
    final distance = Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      center.latitude,
      center.longitude,
    );
    return distance <= radius;
  }

  /// Get the area of this geofence in square meters
  double get areaInSquareMeters => pi * radius * radius;

  @override
  String toString() {
    return 'GeofenceRegion(id: $id, center: $center, radius: ${radius}m)';
  }
}

/// Geofence event representing entry or exit
class GeofenceEvent {
  final GeofenceEventType type;
  final GeofenceRegion geofence;
  final Position position;
  final DateTime timestamp;
  final double accuracy;

  const GeofenceEvent({
    required this.type,
    required this.geofence,
    required this.position,
    required this.timestamp,
    required this.accuracy,
  });

  /// Get zone ID
  String get zoneId => geofence.id;

  /// Get zone entity
  ZoneEntity get zone => geofence.zoneEntity;

  /// Check if this event has sufficient accuracy
  bool get hasGoodAccuracy => accuracy <= 20.0;

  @override
  String toString() {
    return 'GeofenceEvent(type: $type, zone: ${geofence.id}, accuracy: ${accuracy}m)';
  }
}

/// Types of geofence events
enum GeofenceEventType {
  enter,
  exit,
}

/// States of a geofence
enum GeofenceState {
  unknown,
  inside,
  outside,
}

/// Extension methods for GeofenceEventType
extension GeofenceEventTypeExtension on GeofenceEventType {
  String get displayName {
    switch (this) {
      case GeofenceEventType.enter:
        return 'Entered';
      case GeofenceEventType.exit:
        return 'Exited';
    }
  }

  bool get isEntry => this == GeofenceEventType.enter;
  bool get isExit => this == GeofenceEventType.exit;
}

/// Extension methods for GeofenceState
extension GeofenceStateExtension on GeofenceState {
  String get displayName {
    switch (this) {
      case GeofenceState.unknown:
        return 'Unknown';
      case GeofenceState.inside:
        return 'Inside';
      case GeofenceState.outside:
        return 'Outside';
    }
  }

  bool get isInside => this == GeofenceState.inside;
  bool get isOutside => this == GeofenceState.outside;
  bool get isUnknown => this == GeofenceState.unknown;
}
