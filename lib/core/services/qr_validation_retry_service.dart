import 'dart:async';
import 'dart:math';
import 'package:respublicaseguridad/core/services/qr_validation_error_handler.dart';

/// Service for handling retries in QR validation operations
class QRValidationRetryService {
  static QRValidationRetryService? _instance;
  static QRValidationRetryService get instance => _instance ??= QRValidationRetryService._();
  QRValidationRetryService._();

  /// Execute a function with retry logic
  Future<T> executeWithRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
    int maxRetries = 3,
    Duration? initialDelay,
    bool useExponentialBackoff = true,
  }) async {
    int attempts = 0;
    Duration delay = initialDelay ?? const Duration(seconds: 1);

    while (attempts < maxRetries) {
      try {
        attempts++;
        return await operation();
      } catch (error) {
        final qrError = QRValidationErrorHandler.instance.handleError(
          error,
          context: '$operationName (attempt $attempts/$maxRetries)',
        );

        // If this is the last attempt or error is not recoverable, throw
        if (attempts >= maxRetries || !QRValidationErrorHandler.instance.isRecoverable(qrError)) {
          throw qrError;
        }

        // Calculate delay for next attempt
        if (useExponentialBackoff) {
          delay = _calculateExponentialBackoff(attempts, delay);
        } else {
          delay = QRValidationErrorHandler.instance.getRetryDelay(qrError);
        }

        // Wait before next attempt
        await Future.delayed(delay);
      }
    }

    throw QRValidationError.unknown(
      message: 'Máximo número de intentos alcanzado para $operationName',
      context: operationName,
    );
  }

  /// Execute with timeout and retry
  Future<T> executeWithTimeoutAndRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
    Duration timeout = const Duration(seconds: 30),
    int maxRetries = 3,
  }) async {
    return executeWithRetry(
      operation: () => operation().timeout(timeout),
      operationName: operationName,
      maxRetries: maxRetries,
    );
  }

  /// Execute with circuit breaker pattern
  Future<T> executeWithCircuitBreaker<T>({
    required Future<T> Function() operation,
    required String operationName,
    Duration circuitBreakerTimeout = const Duration(minutes: 5),
  }) async {
    final circuitBreaker = _getCircuitBreaker(operationName);
    
    if (circuitBreaker.isOpen) {
      if (DateTime.now().difference(circuitBreaker.lastFailureTime) < circuitBreakerTimeout) {
        throw QRValidationError.server(
          message: 'Servicio temporalmente no disponible. Intenta en ${circuitBreakerTimeout.inMinutes} minutos.',
          context: operationName,
        );
      } else {
        // Reset circuit breaker
        circuitBreaker.reset();
      }
    }

    try {
      final result = await operation();
      circuitBreaker.recordSuccess();
      return result;
    } catch (error) {
      circuitBreaker.recordFailure();
      rethrow;
    }
  }

  /// Retry with progressive delays
  Future<T> retryWithProgressiveDelay<T>({
    required Future<T> Function() operation,
    required String operationName,
    List<Duration> delays = const [
      Duration(seconds: 1),
      Duration(seconds: 3),
      Duration(seconds: 5),
      Duration(seconds: 10),
    ],
  }) async {
    for (int i = 0; i < delays.length; i++) {
      try {
        return await operation();
      } catch (error) {
        final qrError = QRValidationErrorHandler.instance.handleError(
          error,
          context: '$operationName (attempt ${i + 1}/${delays.length})',
        );

        // If this is the last attempt or error is not recoverable, throw
        if (i >= delays.length - 1 || !QRValidationErrorHandler.instance.isRecoverable(qrError)) {
          throw qrError;
        }

        // Wait before next attempt
        await Future.delayed(delays[i]);
      }
    }

    throw QRValidationError.unknown(
      message: 'Todos los intentos fallaron para $operationName',
      context: operationName,
    );
  }

  /// Retry with jitter to avoid thundering herd
  Future<T> retryWithJitter<T>({
    required Future<T> Function() operation,
    required String operationName,
    int maxRetries = 3,
    Duration baseDelay = const Duration(seconds: 1),
    double jitterFactor = 0.1,
  }) async {
    final random = Random();
    
    return executeWithRetry(
      operation: operation,
      operationName: operationName,
      maxRetries: maxRetries,
      initialDelay: _addJitter(baseDelay, jitterFactor, random),
      useExponentialBackoff: false,
    );
  }

  /// Calculate exponential backoff delay
  Duration _calculateExponentialBackoff(int attempt, Duration baseDelay) {
    final multiplier = pow(2, attempt - 1);
    final delayMs = (baseDelay.inMilliseconds * multiplier).toInt();
    
    // Cap at 30 seconds
    return Duration(milliseconds: min(delayMs, 30000));
  }

  /// Add jitter to delay
  Duration _addJitter(Duration baseDelay, double jitterFactor, Random random) {
    final jitterMs = (baseDelay.inMilliseconds * jitterFactor * random.nextDouble()).toInt();
    return Duration(milliseconds: baseDelay.inMilliseconds + jitterMs);
  }

  /// Circuit breaker storage
  final Map<String, _CircuitBreaker> _circuitBreakers = {};

  /// Get or create circuit breaker for operation
  _CircuitBreaker _getCircuitBreaker(String operationName) {
    return _circuitBreakers.putIfAbsent(
      operationName,
      () => _CircuitBreaker(),
    );
  }
}

/// Simple circuit breaker implementation
class _CircuitBreaker {
  int _failureCount = 0;
  DateTime _lastFailureTime = DateTime.now();
  bool _isOpen = false;

  static const int failureThreshold = 5;
  static const Duration resetTimeout = Duration(minutes: 5);

  bool get isOpen => _isOpen;
  DateTime get lastFailureTime => _lastFailureTime;

  void recordSuccess() {
    _failureCount = 0;
    _isOpen = false;
  }

  void recordFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();
    
    if (_failureCount >= failureThreshold) {
      _isOpen = true;
    }
  }

  void reset() {
    _failureCount = 0;
    _isOpen = false;
  }
}

/// Retry configuration for different operations
class QRValidationRetryConfig {
  static const Map<String, RetrySettings> operationConfigs = {
    'generateToken': RetrySettings(
      maxRetries: 3,
      baseDelay: Duration(seconds: 1),
      useExponentialBackoff: true,
    ),
    'validateToken': RetrySettings(
      maxRetries: 2,
      baseDelay: Duration(seconds: 2),
      useExponentialBackoff: false,
    ),
    'proximityVerification': RetrySettings(
      maxRetries: 3,
      baseDelay: Duration(seconds: 1),
      useExponentialBackoff: true,
    ),
    'sessionCreation': RetrySettings(
      maxRetries: 3,
      baseDelay: Duration(seconds: 2),
      useExponentialBackoff: true,
    ),
    'locationUpdate': RetrySettings(
      maxRetries: 5,
      baseDelay: Duration(milliseconds: 500),
      useExponentialBackoff: false,
    ),
  };

  static RetrySettings getConfig(String operation) {
    return operationConfigs[operation] ?? const RetrySettings();
  }
}

/// Retry settings configuration
class RetrySettings {
  final int maxRetries;
  final Duration baseDelay;
  final bool useExponentialBackoff;
  final Duration timeout;

  const RetrySettings({
    this.maxRetries = 3,
    this.baseDelay = const Duration(seconds: 1),
    this.useExponentialBackoff = true,
    this.timeout = const Duration(seconds: 30),
  });
}

/// Mixin for adding retry capabilities to classes
mixin QRValidationRetryMixin {
  Future<T> retryOperation<T>({
    required Future<T> Function() operation,
    required String operationName,
  }) async {
    final config = QRValidationRetryConfig.getConfig(operationName);
    
    return QRValidationRetryService.instance.executeWithTimeoutAndRetry(
      operation: operation,
      operationName: operationName,
      timeout: config.timeout,
      maxRetries: config.maxRetries,
    );
  }

  Future<T> retryWithCircuitBreaker<T>({
    required Future<T> Function() operation,
    required String operationName,
  }) async {
    return QRValidationRetryService.instance.executeWithCircuitBreaker(
      operation: operation,
      operationName: operationName,
    );
  }
}
