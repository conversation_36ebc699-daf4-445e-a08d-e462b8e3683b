import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/core/services/permission_service.dart';

/// Service for handling proximity verification between users
class ProximityVerificationService {
  static ProximityVerificationService? _instance;
  static ProximityVerificationService get instance => _instance ??= ProximityVerificationService._();
  ProximityVerificationService._();

  static const double maxProximityDistance = 100.0;
  
  /// Minimum required GPS accuracy (in meters)
  static const double minRequiredAccuracy = 50.0;
  
  /// Maximum age of location data (in seconds)
  static const int maxLocationAge = 30;

  /// Verify proximity between two users within a zone
  Future<ProximityVerificationResult> verifyProximity({
    required ProximityDataEntity initiatorLocation,
    required ProximityDataEntity validatorLocation,
    required ZoneEntity zone,
  }) async {
    try {
      // Check location data freshness
      final now = DateTime.now();
      final initiatorAge = now.difference(initiatorLocation.timestamp).inSeconds;
      final validatorAge = now.difference(validatorLocation.timestamp).inSeconds;

      if (initiatorAge > maxLocationAge) {
        return ProximityVerificationResult.failure(
          reason: 'Initiator location data is too old (${initiatorAge}s)',
          errorCode: 'STALE_LOCATION_DATA',
        );
      }

      if (validatorAge > maxLocationAge) {
        return ProximityVerificationResult.failure(
          reason: 'Validator location data is too old (${validatorAge}s)',
          errorCode: 'STALE_LOCATION_DATA',
        );
      }

      // Check GPS accuracy
      if (initiatorLocation.accuracy > minRequiredAccuracy) {
        return ProximityVerificationResult.failure(
          reason: 'Initiator GPS accuracy insufficient (${initiatorLocation.accuracy.toStringAsFixed(1)}m)',
          errorCode: 'POOR_GPS_ACCURACY',
        );
      }

      if (validatorLocation.accuracy > minRequiredAccuracy) {
        return ProximityVerificationResult.failure(
          reason: 'Validator GPS accuracy insufficient (${validatorLocation.accuracy.toStringAsFixed(1)}m)',
          errorCode: 'POOR_GPS_ACCURACY',
        );
      }

      // Calculate distance between users
      final userDistance = Geolocator.distanceBetween(
        initiatorLocation.latitude,
        initiatorLocation.longitude,
        validatorLocation.latitude,
        validatorLocation.longitude,
      );

      // Check if users are within proximity range
      if (userDistance > maxProximityDistance) {
        return ProximityVerificationResult.failure(
          reason: 'Users are too far apart (${userDistance.toStringAsFixed(1)}m). Must be within ${maxProximityDistance.toStringAsFixed(0)}m',
          errorCode: 'USERS_TOO_FAR',
          metadata: {'distance': userDistance},
        );
      }

      // Check if both users are within the zone
      final initiatorInZone = _isLocationInZone(initiatorLocation, zone);
      final validatorInZone = _isLocationInZone(validatorLocation, zone);

      if (!initiatorInZone) {
        final distanceFromZone = Geolocator.distanceBetween(
          initiatorLocation.latitude,
          initiatorLocation.longitude,
          zone.centerCoordinates.latitude,
          zone.centerCoordinates.longitude,
        );
        
        return ProximityVerificationResult.failure(
          reason: 'Initiator is outside the zone (${distanceFromZone.toStringAsFixed(1)}m from center)',
          errorCode: 'OUTSIDE_ZONE',
          metadata: {'distanceFromZone': distanceFromZone},
        );
      }

      if (!validatorInZone) {
        final distanceFromZone = Geolocator.distanceBetween(
          validatorLocation.latitude,
          validatorLocation.longitude,
          zone.centerCoordinates.latitude,
          zone.centerCoordinates.longitude,
        );
        
        return ProximityVerificationResult.failure(
          reason: 'Validator is outside the zone (${distanceFromZone.toStringAsFixed(1)}m from center)',
          errorCode: 'OUTSIDE_ZONE',
          metadata: {'distanceFromZone': distanceFromZone},
        );
      }

      // All checks passed
      return ProximityVerificationResult.success(
        userDistance: userDistance,
        initiatorInZone: initiatorInZone,
        validatorInZone: validatorInZone,
        metadata: {
          'userDistance': userDistance,
          'initiatorAccuracy': initiatorLocation.accuracy,
          'validatorAccuracy': validatorLocation.accuracy,
          'locationAge': {
            'initiator': initiatorAge,
            'validator': validatorAge,
          },
        },
      );

    } catch (e) {
      return ProximityVerificationResult.failure(
        reason: 'Proximity verification failed: ${e.toString()}',
        errorCode: 'VERIFICATION_ERROR',
      );
    }
  }

  /// Get current user location with high accuracy
  Future<ProximityDataEntity?> getCurrentLocationForVerification() async {
    try {
      // Check permissions first
      final hasPermission = await PermissionService.instance.requestPermission( PermissionType.location);
      if (!hasPermission.isGranted) {
        return null;
      }

      // Get high-accuracy location
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      return ProximityDataEntity(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: DateTime.now(),
      );

    } catch (e) {
      return null;
    }
  }

  /// Continuously monitor location for proximity verification
  Stream<ProximityDataEntity> watchLocationForVerification() async* {
    try {
      // Check permissions
      final hasPermission = await PermissionService.instance.requestPermission(PermissionType.location);
      if (!hasPermission.isGranted) {
        return;
      }

      // Create location stream with high accuracy
      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5, // Update every 5 meters
        timeLimit: Duration(seconds: 10),
      );

      await for (final position in Geolocator.getPositionStream(locationSettings: locationSettings)) {
        yield ProximityDataEntity(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          timestamp: DateTime.now(),
        );
      }

    } catch (e) {
      // Stream ends on error
      return;
    }
  }



  /// Check if a location is within a zone's boundaries
  bool _isLocationInZone(ProximityDataEntity location, ZoneEntity zone) {
    final distance = Geolocator.distanceBetween(
      location.latitude,
      location.longitude,
      zone.centerCoordinates.latitude,
      zone.centerCoordinates.longitude,
    );

    return distance <= zone.radiusInMeters;
  }

  /// Validate location data quality
  bool isLocationDataValid(ProximityDataEntity location) {
    final now = DateTime.now();
    final age = now.difference(location.timestamp).inSeconds;
    
    return age <= maxLocationAge && location.accuracy <= minRequiredAccuracy;
  }

  /// Get location quality score (0-100)
  int getLocationQualityScore(ProximityDataEntity location) {
    final now = DateTime.now();
    final age = now.difference(location.timestamp).inSeconds;
    
    // Age score (0-50 points)
    final ageScore = (50 * (1 - (age / maxLocationAge))).clamp(0, 50).toInt();
    
    // Accuracy score (0-50 points)
    final accuracyScore = (50 * (1 - (location.accuracy / minRequiredAccuracy))).clamp(0, 50).toInt();
    
    return ageScore + accuracyScore;
  }

  /// Check if device supports high-accuracy location
  Future<bool> supportsHighAccuracyLocation() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return false;

      final permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
    } catch (e) {
      return false;
    }
  }
}

/// Result of proximity verification
class ProximityVerificationResult {
  final bool isSuccess;
  final String? reason;
  final String? errorCode;
  final double? userDistance;
  final bool? initiatorInZone;
  final bool? validatorInZone;
  final Map<String, dynamic>? metadata;

  const ProximityVerificationResult({
    required this.isSuccess,
    this.reason,
    this.errorCode,
    this.userDistance,
    this.initiatorInZone,
    this.validatorInZone,
    this.metadata,
  });

  /// Create a successful verification result
  factory ProximityVerificationResult.success({
    required double userDistance,
    required bool initiatorInZone,
    required bool validatorInZone,
    Map<String, dynamic>? metadata,
  }) {
    return ProximityVerificationResult(
      isSuccess: true,
      userDistance: userDistance,
      initiatorInZone: initiatorInZone,
      validatorInZone: validatorInZone,
      metadata: metadata,
    );
  }

  /// Create a failed verification result
  factory ProximityVerificationResult.failure({
    required String reason,
    required String errorCode,
    Map<String, dynamic>? metadata,
  }) {
    return ProximityVerificationResult(
      isSuccess: false,
      reason: reason,
      errorCode: errorCode,
      metadata: metadata,
    );
  }
}
