import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:respublicaseguridad/core/utils/logger.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Service for monitoring location permissions and services during automatic validation periods
class ValidationMonitoringService {
  static ValidationMonitoringService? _instance;
  static ValidationMonitoringService get instance =>
      _instance ??= ValidationMonitoringService._();
  ValidationMonitoringService._();

  static const String _logContext = 'ValidationMonitoringService';

  Timer? _monitoringTimer;
  bool _isMonitoring = false;
  String? _currentZoneId;
  DateTime? _validationStartTime;
  DateTime? _validationEndTime;

  /// Start monitoring location permissions and services for a zone validation
  Future<void> startValidationMonitoring({
    required String zoneId,
    required Duration validationPeriod,
  }) async {
    const methodContext = 'startValidationMonitoring';

    try {
      Logger.info(
        '$_logContext.$methodContext: Starting monitoring for zone $zoneId',
      );

      // Stop any existing monitoring
      await stopValidationMonitoring();

      _currentZoneId = zoneId;
      _validationStartTime = DateTime.now();
      _validationEndTime = _validationStartTime!.add(validationPeriod);
      _isMonitoring = true;

      // Store monitoring state
      await _storeMonitoringState();

      // Start periodic monitoring (every 5 minutes)
      _monitoringTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _performMonitoringCheck(),
      );

      // Perform initial check
      await _performMonitoringCheck();

      Logger.info(
        '$_logContext.$methodContext: Monitoring started for zone $zoneId',
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error starting monitoring - $e',
      );
    }
  }

  /// Stop validation monitoring
  Future<void> stopValidationMonitoring() async {
    const methodContext = 'stopValidationMonitoring';

    try {
      Logger.info(
        '$_logContext.$methodContext: Stopping validation monitoring',
      );

      _monitoringTimer?.cancel();
      _monitoringTimer = null;
      _isMonitoring = false;
      _currentZoneId = null;
      _validationStartTime = null;
      _validationEndTime = null;

      // Clear stored state
      await _clearMonitoringState();

      Logger.info('$_logContext.$methodContext: Monitoring stopped');
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error stopping monitoring - $e',
      );
    }
  }

  /// Check if currently monitoring a validation
  bool get isMonitoring => _isMonitoring;

  /// Get current zone being monitored
  String? get currentZoneId => _currentZoneId;

  /// Restore monitoring state from storage (for app restarts)
  Future<void> restoreMonitoringState() async {
    const methodContext = 'restoreMonitoringState';

    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString('validation_monitoring_state');

      if (stateJson != null) {
        final state = jsonDecode(stateJson) as Map<String, dynamic>;

        _currentZoneId = state['zoneId'] as String?;
        _validationStartTime =
            state['startTime'] != null
                ? DateTime.parse(state['startTime'] as String)
                : null;
        _validationEndTime =
            state['endTime'] != null
                ? DateTime.parse(state['endTime'] as String)
                : null;

        // Check if monitoring period is still valid
        if (_validationEndTime != null &&
            DateTime.now().isBefore(_validationEndTime!)) {
          _isMonitoring = true;

          // Restart monitoring timer
          _monitoringTimer = Timer.periodic(
            const Duration(minutes: 5),
            (_) => _performMonitoringCheck(),
          );

          Logger.info(
            '$_logContext.$methodContext: Monitoring state restored for zone $_currentZoneId',
          );
        } else {
          // Monitoring period expired, clear state
          await _clearMonitoringState();
          Logger.info(
            '$_logContext.$methodContext: Monitoring period expired, cleared state',
          );
        }
      }
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error restoring monitoring state - $e',
      );
    }
  }

  /// Perform monitoring check for location permissions and services
  Future<void> _performMonitoringCheck() async {
    const methodContext = '_performMonitoringCheck';

    if (!_isMonitoring || _currentZoneId == null) {
      return;
    }

    try {
      Logger.info(
        '$_logContext.$methodContext: Performing monitoring check for zone $_currentZoneId',
      );

      // Check if validation period has expired
      if (_validationEndTime != null &&
          DateTime.now().isAfter(_validationEndTime!)) {
        Logger.info(
          '$_logContext.$methodContext: Validation period expired, stopping monitoring',
        );
        await stopValidationMonitoring();
        return;
      }

      // Check location services and permissions
      final isLocationServiceEnabled =
          await Geolocator.isLocationServiceEnabled();
      if (!isLocationServiceEnabled) {
        await _handleLocationServiceDisabled();
        return;
      }

      final locationPermission = await Geolocator.checkPermission();
      if (locationPermission == LocationPermission.denied ||
          locationPermission == LocationPermission.deniedForever) {
        await _handleLocationPermissionDenied(locationPermission);
        return;
      }

      Logger.info('$_logContext.$methodContext: All location requirements met');
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error during monitoring check - $e',
      );
    }
  }

  /// Handle location service disabled
  Future<void> _handleLocationServiceDisabled() async {
    const methodContext = '_handleLocationServiceDisabled';

    try {
      Logger.warning(
        '$_logContext.$methodContext: Location services disabled during validation',
      );

      await NotificationService.instance.showGeneralNotification(
        title: '⚠️ Location Services Required',
        body:
            'Location services are disabled. Please enable them to continue automatic validation for your zone.',
        payload: jsonEncode({
          'type': 'location_service_disabled',
          'zoneId': _currentZoneId,
        }),
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error handling location service disabled - $e',
      );
    }
  }

  /// Handle location permission denied
  Future<void> _handleLocationPermissionDenied(
    LocationPermission permission,
  ) async {
    const methodContext = '_handleLocationPermissionDenied';

    try {
      Logger.warning(
        '$_logContext.$methodContext: Location permission denied during validation: $permission',
      );

      final isPermanent = permission == LocationPermission.deniedForever;

      await NotificationService.instance.showGeneralNotification(
        title: '🚫 Location Permission Required',
        body:
            isPermanent
                ? 'Location permission permanently denied. Please enable in Settings to continue automatic validation.'
                : 'Location permission denied. Please grant permission to continue automatic validation for your zone.',
        payload: jsonEncode({
          'type': 'location_permission_denied',
          'zoneId': _currentZoneId,
          'isPermanent': isPermanent,
        }),
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error handling location permission denied - $e',
      );
    }
  }

  /// Handle background location permission denied
  Future<void> _handleBackgroundLocationDenied() async {
    const methodContext = '_handleBackgroundLocationDenied';

    try {
      Logger.warning(
        '$_logContext.$methodContext: Background location permission denied during validation',
      );

      await NotificationService.instance.showGeneralNotification(
        title: '📍 Background Location Recommended',
        body:
            'Background location access helps ensure reliable automatic validation. Consider enabling "Allow all the time" in Settings.',
        payload: jsonEncode({
          'type': 'background_location_denied',
          'zoneId': _currentZoneId,
        }),
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error handling background location denied - $e',
      );
    }
  }

  /// Store monitoring state to persistent storage
  Future<void> _storeMonitoringState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final state = {
        'zoneId': _currentZoneId,
        'startTime': _validationStartTime?.toIso8601String(),
        'endTime': _validationEndTime?.toIso8601String(),
      };

      await prefs.setString('validation_monitoring_state', jsonEncode(state));
    } catch (e) {
      Logger.error(
        '$_logContext._storeMonitoringState: Error storing state - $e',
      );
    }
  }

  /// Clear monitoring state from persistent storage
  Future<void> _clearMonitoringState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('validation_monitoring_state');
    } catch (e) {
      Logger.error(
        '$_logContext._clearMonitoringState: Error clearing state - $e',
      );
    }
  }
}
