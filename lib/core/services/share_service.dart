import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

/// Service for handling sharing functionality across the app
class ShareService {
  /// Share text content
  static Future<void> shareText({
    required String text,
    String? subject,
  }) async {
    try {
      await Share.share(
        text,
        subject: subject,
      );
    } catch (e) {
      throw ShareException('Failed to share text: ${e.toString()}');
    }
  }

  /// Share text with files
  static Future<void> shareTextWithFiles({
    required String text,
    required List<String> filePaths,
    String? subject,
  }) async {
    try {
      final files = filePaths.map((path) => XFile(path)).toList();
      await Share.shareXFiles(
        files,
        text: text,
        subject: subject,
      );
    } catch (e) {
      throw ShareException('Failed to share text with files: ${e.toString()}');
    }
  }

  /// Copy text to clipboard
  static Future<void> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      // Provide haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      throw ShareException('Failed to copy to clipboard: ${e.toString()}');
    }
  }

  /// Share with files
  static Future<void> shareFiles({
    required List<String> filePaths,
    String? text,
    String? subject,
  }) async {
    try {
      final files = filePaths.map((path) => XFile(path)).toList();
      await Share.shareXFiles(
        files,
        text: text,
        subject: subject,
      );
    } catch (e) {
      throw ShareException('Failed to share files: ${e.toString()}');
    }
  }
}

/// Exception thrown when sharing operations fail
class ShareException implements Exception {
  final String message;
  
  const ShareException(this.message);
  
  @override
  String toString() => 'ShareException: $message';
}
