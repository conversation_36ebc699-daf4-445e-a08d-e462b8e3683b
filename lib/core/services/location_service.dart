import 'dart:async';
import 'dart:io';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/coordinates.dart';

/// Service for handling location operations, permissions, and limited tracking
class LocationService {
  static LocationService? _instance;
  static LocationService get instance => _instance ??= LocationService._();
  LocationService._();

  StreamController<Position>? _locationStreamController;
  StreamSubscription<Position>? _locationSubscription;
  Position? _lastKnownPosition;
  bool _isMonitoring = false;

  // Limited tracking configuration
  static const int maxValidationAttempts = 5;
  static const int validationWindowDays = 3;
  static const int locationSampleDurationSeconds = 5;

  /// Stream of location updates
  Stream<Position>? get locationStream => _locationStreamController?.stream;

  /// Get current location status
  bool get isMonitoring => _isMonitoring;
  Position? get lastKnownPosition => _lastKnownPosition;

  /// Check if location services are enabled on the device
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Get current location permission status
  Future<LocationPermission> getLocationPermission() async {
    return await Geolocator.checkPermission();
  }

  /// Request location permissions with proper escalation
  Future<LocationPermissionResult> requestLocationPermissions({
    bool requestAlwaysPermission = false,
  }) async {
    try {
      // Check if location services are enabled
      if (!await isLocationServiceEnabled()) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.serviceDisabled,
          message: 'Location services are disabled. Please enable them in device settings.',
        );
      }

      // Check current permission
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.deniedForever,
          message: 'Location permissions are permanently denied. Please enable them in app settings.',
        );
      }

      if (permission == LocationPermission.denied) {
        return LocationPermissionResult(
          status: LocationPermissionStatus.denied,
          message: 'Location permission is required for automatic zone validation.',
        );
      }

      // For automatic validation, we need "always" permission for background location
      if (requestAlwaysPermission && permission != LocationPermission.always) {
        if (Platform.isAndroid) {
          // On Android, request background location permission
          final backgroundPermission = await Permission.locationAlways.request();
          if (backgroundPermission != PermissionStatus.granted) {
            return LocationPermissionResult(
              status: LocationPermissionStatus.backgroundDenied,
              message: 'Background location permission is required for automatic validation.',
            );
          }
        } else if (Platform.isIOS) {
          // On iOS, the system will automatically prompt for "always" permission
          // when the app requests location in the background
          permission = await Geolocator.requestPermission();
          if (permission != LocationPermission.always) {
            return LocationPermissionResult(
              status: LocationPermissionStatus.backgroundDenied,
              message: 'Always location permission is required for automatic validation.',
            );
          }
        }
      }

      return LocationPermissionResult(
        status: LocationPermissionStatus.granted,
        message: 'Location permissions granted successfully.',
        hasBackgroundPermission: permission == LocationPermission.always,
      );
    } catch (e) {
      return LocationPermissionResult(
        status: LocationPermissionStatus.error,
        message: 'Error requesting location permissions: $e',
      );
    }
  }

  /// Get current position with high accuracy
  Future<Position?> getCurrentPosition({
    Duration timeout = const Duration(seconds: 15),
  }) async {
    try {
      final permission = await getLocationPermission();
      if (permission == LocationPermission.denied || 
          permission == LocationPermission.deniedForever) {
        return null;
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: timeout,
      );

      _lastKnownPosition = position;
      return position;
    } catch (e) {
      // Return last known position if available
      return _lastKnownPosition;
    }
  }

  /// Start monitoring location changes
  Future<bool> startLocationMonitoring({
    Duration interval = const Duration(minutes: 1),
    double distanceFilter = 10.0, // meters
  }) async {
    if (_isMonitoring) {
      return true;
    }

    try {
      final permissionResult = await requestLocationPermissions(
        requestAlwaysPermission: true,
      );

      if (permissionResult.status != LocationPermissionStatus.granted) {
        return false;
      }

      _locationStreamController = StreamController<Position>.broadcast();

      final locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: distanceFilter.toInt(),
      );

      _locationSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _lastKnownPosition = position;
          _locationStreamController?.add(position);
        },
        onError: (error) {
          // Handle location stream errors
          _locationStreamController?.addError(error);
        },
      );

      _isMonitoring = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Stop location monitoring
  Future<void> stopLocationMonitoring() async {
    if (!_isMonitoring) return;

    await _locationSubscription?.cancel();
    _locationSubscription = null;

    await _locationStreamController?.close();
    _locationStreamController = null;

    _isMonitoring = false;
  }

  /// Calculate distance between two coordinates in meters
  double calculateDistance(Coordinates from, Coordinates to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    );
  }

  /// Check if a position is within a zone (circular geofence)
  bool isPositionInZone(Position position, Coordinates zoneCenter, double radiusInMeters) {
    final distance = Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      zoneCenter.latitude,
      zoneCenter.longitude,
    );
    return distance <= radiusInMeters;
  }

  /// Convert Position to Coordinates
  Coordinates positionToCoordinates(Position position) {
    return Coordinates(
      latitude: position.latitude,
      longitude: position.longitude,
    );
  }

  /// Get location accuracy description
  String getAccuracyDescription(Position position) {
    final accuracy = position.accuracy;
    if (accuracy <= 5) return 'Excellent';
    if (accuracy <= 10) return 'Good';
    if (accuracy <= 20) return 'Fair';
    return 'Poor';
  }

  /// Check if location is accurate enough for zone validation
  bool isLocationAccurateEnough(Position position, {double maxAccuracy = 20.0}) {
    return position.accuracy <= maxAccuracy;
  }

  /// Get address from coordinates using reverse geocoding
  Future<String> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _formatAddress(placemark);
      }

      // Fallback to coordinates if no address found
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    } catch (e) {
      // Return coordinates as fallback
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    }
  }

  /// Get a better location name from coordinates
  Future<String> getBetterLocationName(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _generateLocationName(placemark);
      }

      // Fallback to coordinates
      return 'Location (${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)})';
    } catch (e) {
      // Return coordinates as fallback
      return 'Location (${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)})';
    }
  }

  /// Format address from placemark
  String _formatAddress(Placemark placemark) {
    List<String> addressParts = [];

    if (placemark.street?.isNotEmpty == true) {
      addressParts.add(placemark.street!);
    }
    if (placemark.subLocality?.isNotEmpty == true) {
      addressParts.add(placemark.subLocality!);
    }
    if (placemark.locality?.isNotEmpty == true) {
      addressParts.add(placemark.locality!);
    }
    if (placemark.administrativeArea?.isNotEmpty == true) {
      addressParts.add(placemark.administrativeArea!);
    }
    if (placemark.country?.isNotEmpty == true) {
      addressParts.add(placemark.country!);
    }

    return addressParts.isNotEmpty ? addressParts.join(', ') : 'Unknown Location';
  }

  /// Generate a better location name from placemark
  String _generateLocationName(Placemark placemark) {
    // Priority order for location naming
    if (placemark.name?.isNotEmpty == true && placemark.name != placemark.street) {
      return placemark.name!;
    }

    if (placemark.thoroughfare?.isNotEmpty == true) {
      return placemark.thoroughfare!;
    }

    if (placemark.subLocality?.isNotEmpty == true) {
      return placemark.subLocality!;
    }

    if (placemark.locality?.isNotEmpty == true) {
      return placemark.locality!;
    }

    if (placemark.subAdministrativeArea?.isNotEmpty == true) {
      return placemark.subAdministrativeArea!;
    }

    if (placemark.administrativeArea?.isNotEmpty == true) {
      return placemark.administrativeArea!;
    }

    return 'Selected Location';
  }

  /// Dispose resources
  Future<void> dispose() async {
    await stopLocationMonitoring();
    _instance = null;
  }
}

/// Result of location permission request
class LocationPermissionResult {
  final LocationPermissionStatus status;
  final String message;
  final bool hasBackgroundPermission;

  const LocationPermissionResult({
    required this.status,
    required this.message,
    this.hasBackgroundPermission = false,
  });

  bool get isGranted => status == LocationPermissionStatus.granted;
  bool get canUseLocation => isGranted;
  bool get canUseBackgroundLocation => isGranted && hasBackgroundPermission;
}

/// Location permission status enumeration
enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  backgroundDenied,
  serviceDisabled,
  error,
}

/// Extension methods for LocationPermissionStatus
extension LocationPermissionStatusExtension on LocationPermissionStatus {
  String get description {
    switch (this) {
      case LocationPermissionStatus.granted:
        return 'Location permission granted';
      case LocationPermissionStatus.denied:
        return 'Location permission denied';
      case LocationPermissionStatus.deniedForever:
        return 'Location permission permanently denied';
      case LocationPermissionStatus.backgroundDenied:
        return 'Background location permission denied';
      case LocationPermissionStatus.serviceDisabled:
        return 'Location services disabled';
      case LocationPermissionStatus.error:
        return 'Error requesting location permission';
    }
  }

  bool get requiresSettings {
    return this == LocationPermissionStatus.deniedForever ||
           this == LocationPermissionStatus.serviceDisabled;
  }
}
