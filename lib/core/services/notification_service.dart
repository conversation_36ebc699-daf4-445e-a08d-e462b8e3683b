import 'dart:async';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/core/utils/logger.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:geolocator/geolocator.dart';

/// Service for handling all types of notifications
class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance =>
      _instance ??= NotificationService._();
  NotificationService._();

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  bool _isInitialized = false;
  String? _fcmToken;
  StreamController<NotificationPayload>? _notificationStreamController;

  /// Stream of notification interactions
  Stream<NotificationPayload>? get notificationStream =>
      _notificationStreamController?.stream;

  /// Get FCM token
  String? get fcmToken => _fcmToken;

  /// Generate safe 32-bit notification ID
  int _generateSafeNotificationId() {
    return DateTime.now().millisecondsSinceEpoch % 2147483647;
  }

  /// Initialize notification service
  Future<bool> initialize() async {
    Logger.info(
      'NotificationService.initialize: ===== INITIALIZING NOTIFICATION SERVICE =====',
    );
    Logger.info(
      'NotificationService.initialize: Already initialized: $_isInitialized',
    );

    if (_isInitialized) {
      Logger.info(
        'NotificationService.initialize: Service already initialized, returning true',
      );
      return true;
    }

    try {
      Logger.info(
        'NotificationService.initialize: Starting local notifications initialization...',
      );
      // Initialize local notifications
      await _initializeLocalNotifications();
      Logger.info(
        'NotificationService.initialize: Local notifications initialized successfully',
      );

      Logger.info(
        'NotificationService.initialize: Starting Firebase messaging initialization...',
      );
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      Logger.info(
        'NotificationService.initialize: Firebase messaging initialized successfully',
      );

      _notificationStreamController =
          StreamController<NotificationPayload>.broadcast();
      _isInitialized = true;

      Logger.info(
        'NotificationService.initialize: ===== NOTIFICATION SERVICE INITIALIZED SUCCESSFULLY =====',
      );
      return true;
    } catch (e, stackTrace) {
      Logger.error(
        'NotificationService.initialize: Failed to initialize notification service: $e',
      );
      Logger.error('NotificationService.initialize: Stack trace: $stackTrace');
      return false;
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _handleNotificationResponse,
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  /// Initialize Firebase messaging
  Future<void> _initializeFirebaseMessaging() async {
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Starting Firebase messaging setup',
    );

    // Request permission for iOS
    if (Platform.isIOS) {
      Logger.info(
        'NotificationService._initializeFirebaseMessaging: Requesting iOS permissions',
      );
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      Logger.info(
        'NotificationService._initializeFirebaseMessaging: iOS permission result: ${settings.authorizationStatus}',
      );
    }

    // Get FCM token
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Getting FCM token',
    );
    _fcmToken = await _firebaseMessaging.getToken();
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: FCM token obtained: ${_fcmToken?.substring(0, 20)}...',
    );

    // Handle foreground messages
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Setting up foreground message handler',
    );
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Setting up background message tap handler',
    );
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessageTap);

    // Handle app launch from terminated state
    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Checking for initial message',
    );
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      Logger.info(
        'NotificationService._initializeFirebaseMessaging: Found initial message, handling it',
      );
      _handleBackgroundMessageTap(initialMessage);
    } else {
      Logger.info(
        'NotificationService._initializeFirebaseMessaging: No initial message found',
      );
    }

    Logger.info(
      'NotificationService._initializeFirebaseMessaging: Firebase messaging setup complete',
    );
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    final List<AndroidNotificationChannel> channels = [
      // General notifications
      const AndroidNotificationChannel(
        'general',
        'General Notifications',
        description: 'General app notifications',
        importance: Importance.defaultImportance,
      ),

      // Zone validation channels
      const AndroidNotificationChannel(
        'zone_validation_success',
        'Zone Validation Success',
        description: 'Notifications for successful zone validations',
        importance: Importance.high,
      ),

      const AndroidNotificationChannel(
        'zone_validation_failed',
        'Zone Validation Failed',
        description: 'Notifications for failed zone validations',
        importance: Importance.defaultImportance,
      ),

      // Zone events
      const AndroidNotificationChannel(
        'zone_events',
        'Zone Events',
        description: 'Zone entry/exit notifications',
        importance: Importance.defaultImportance,
      ),

      // Incident alerts with different priorities
      const AndroidNotificationChannel(
        'incident_alerts',
        'Security Incidents',
        description: 'Security incident notifications',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      ),

      const AndroidNotificationChannel(
        'incident_alerts_critical',
        'Critical Security Alerts',
        description: 'Critical security incident notifications',
        importance: Importance.max,
        enableVibration: true,
        playSound: true,
      ),

      const AndroidNotificationChannel(
        'incident_alerts_low',
        'Security Updates',
        description: 'Low priority security notifications',
        importance: Importance.low,
      ),

      // Permission requests
      const AndroidNotificationChannel(
        'permission_requests',
        'Permission Requests',
        description: 'App permission request notifications',
        importance: Importance.defaultImportance,
      ),

      // Location access requests
      const AndroidNotificationChannel(
        'location_requests',
        'Location Requests',
        description:
            'Location access request notifications for zone validation',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      ),
    ];

    // Create all channels
    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);
    }
  }

  /// Show zone validation notification
  Future<void> showZoneValidationNotification({
    required ZoneEntity zone,
    required ZoneValidationNotificationType type,
    String? customMessage,
  }) async {
    if (!_isInitialized) return;

    final notification = _createZoneValidationNotification(
      zone,
      type,
      customMessage,
    );
    await _showLocalNotification(notification);
  }

  /// Show permission request notification
  Future<void> showPermissionNotification({
    required PermissionNotificationType type,
    String? customMessage,
  }) async {
    if (!_isInitialized) return;

    final notification = _createPermissionNotification(type, customMessage);
    await _showLocalNotification(notification);
  }

  /// Show geofence event notification
  Future<void> showGeofenceNotification({
    required String zoneName,
    required GeofenceNotificationType type,
    DateTime? timestamp,
  }) async {
    if (!_isInitialized) return;

    final notification = _createGeofenceNotification(zoneName, type, timestamp);
    await _showLocalNotification(notification);
  }

  /// Show general notification
  Future<void> showGeneralNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) return;

    final notification = LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: title,
      body: body,
      channelId: 'general',
      payload: payload,
    );

    await _showLocalNotification(notification);
  }

  /// Show incident alert notification
  Future<void> showIncidentNotification({
    required String incidentId,
    required String incidentType,
    required String title,
    required String description,
    required double distanceMeters,
    required IncidentNotificationPriority priority,
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isInitialized) return;

    final notification = _createIncidentNotification(
      incidentId: incidentId,
      incidentType: incidentType,
      title: title,
      description: description,
      distanceMeters: distanceMeters,
      priority: priority,
      metadata: metadata,
    );

    await _showLocalNotification(notification);
  }

  /// Show critical incident alert notification
  Future<void> showCriticalIncidentNotification({
    required String incidentId,
    required String incidentType,
    required String title,
    required String description,
    required double distanceMeters,
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isInitialized) return;

    final notification = _createCriticalIncidentNotification(
      incidentId: incidentId,
      incidentType: incidentType,
      title: title,
      description: description,
      distanceMeters: distanceMeters,
      metadata: metadata,
    );

    await _showLocalNotification(notification);
  }

  /// Show incident notification with user preferences check
  Future<void> showIncidentNotificationWithPreferences({
    required String userId,
    required String incidentId,
    required String incidentType,
    required String title,
    required String description,
    required double distanceMeters,
    required IncidentNotificationPriority priority,
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isInitialized) return;

    // This method would integrate with the notification preferences service
    // For now, we'll show the notification directly
    // In a complete implementation, this would check user preferences first

    await showIncidentNotification(
      incidentId: incidentId,
      incidentType: incidentType,
      title: title,
      description: description,
      distanceMeters: distanceMeters,
      priority: priority,
      metadata: metadata,
    );
  }

  /// Schedule a notification for later
  Future<void> scheduleNotification({
    required LocalNotificationData notification,
    required DateTime scheduledTime,
  }) async {
    if (!_isInitialized) return;

    await _localNotifications.zonedSchedule(
      notification.id,
      notification.title,
      notification.body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      _createNotificationDetails(notification.channelId),
      payload: notification.payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  /// Cancel a scheduled notification
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Show local notification
  Future<void> _showLocalNotification(
    LocalNotificationData notification,
  ) async {
    await _localNotifications.show(
      notification.id,
      notification.title,
      notification.body,
      _createNotificationDetails(notification.channelId),
      payload: notification.payload,
    );
  }

  /// Create notification details
  NotificationDetails _createNotificationDetails(String channelId) {
    return NotificationDetails(
      android: AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getChannelImportance(channelId),
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
      ),
    );
  }

  /// Create zone validation notification
  LocalNotificationData _createZoneValidationNotification(
    ZoneEntity zone,
    ZoneValidationNotificationType type,
    String? customMessage,
  ) {
    String title;
    String body;

    switch (type) {
      case ZoneValidationNotificationType.validated:
        title = 'Zone Validated ✅';
        body =
            customMessage ??
            'Your zone "${zone.name}" has been successfully validated!';
        break;
      case ZoneValidationNotificationType.rejected:
        title = 'Zone Rejected ❌';
        body =
            customMessage ??
            'Your zone "${zone.name}" validation was rejected.';
        break;
      case ZoneValidationNotificationType.automaticValidationStarted:
        title = 'Automatic Validation Started 🔄';
        body =
            customMessage ??
            'Automatic validation has started for "${zone.name}".';
        break;
      case ZoneValidationNotificationType.automaticValidationCompleted:
        title = 'Automatic Validation Complete ✅';
        body =
            customMessage ??
            'Automatic validation completed for "${zone.name}".';
        break;
      case ZoneValidationNotificationType.presenceConfirmed:
        title = 'Presence Confirmed 📍';
        body =
            customMessage ??
            'Your presence in "${zone.name}" has been confirmed.';
        break;
    }

    return LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: title,
      body: body,
      channelId: 'zone_validation',
      payload: 'zone_${zone.id}',
    );
  }

  /// Create permission notification
  LocalNotificationData _createPermissionNotification(
    PermissionNotificationType type,
    String? customMessage,
  ) {
    String title;
    String body;

    switch (type) {
      case PermissionNotificationType.locationRequired:
        title = 'Location Permission Required 📍';
        body =
            customMessage ??
            'Please enable location access for automatic zone validation.';
        break;
      case PermissionNotificationType.backgroundLocationRequired:
        title = 'Background Location Required 🔄';
        body =
            customMessage ??
            'Enable "Always" location access for automatic validation.';
        break;
      case PermissionNotificationType.notificationPermissionRequired:
        title = 'Notification Permission Required 🔔';
        body =
            customMessage ??
            'Enable notifications to receive validation updates.';
        break;
    }

    return LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: title,
      body: body,
      channelId: 'permissions',
      payload: 'permission_${type.name}',
    );
  }

  /// Create geofence notification
  LocalNotificationData _createGeofenceNotification(
    String zoneName,
    GeofenceNotificationType type,
    DateTime? timestamp,
  ) {
    String title;
    String body;

    switch (type) {
      case GeofenceNotificationType.entered:
        title = 'Zone Entered 📍';
        body = 'You have entered the zone "$zoneName"';
        break;
      case GeofenceNotificationType.exited:
        title = 'Zone Exited 🚶';
        body = 'You have exited the zone "$zoneName"';
        break;
    }

    return LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: title,
      body: body,
      channelId: 'geofence',
      payload: 'geofence_$zoneName',
    );
  }

  /// Create incident notification
  LocalNotificationData _createIncidentNotification({
    required String incidentId,
    required String incidentType,
    required String title,
    required String description,
    required double distanceMeters,
    required IncidentNotificationPriority priority,
    Map<String, dynamic>? metadata,
  }) {
    final distanceText = _formatDistance(distanceMeters);
    final emoji = _getIncidentEmoji(incidentType);

    final notificationTitle = '$emoji ${incidentType.toUpperCase()} Alert';
    final notificationBody = '$title - $distanceText';

    final channelId = _getChannelIdForPriority(priority);

    return LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: notificationTitle,
      body: notificationBody,
      channelId: channelId,
      payload: 'incident_$incidentId',
    );
  }

  /// Create critical incident notification
  LocalNotificationData _createCriticalIncidentNotification({
    required String incidentId,
    required String incidentType,
    required String title,
    required String description,
    required double distanceMeters,
    Map<String, dynamic>? metadata,
  }) {
    final distanceText = _formatDistance(distanceMeters);

    final notificationTitle = '🚨 CRITICAL: ${incidentType.toUpperCase()}';
    final notificationBody =
        '$title - $distanceText - IMMEDIATE ATTENTION REQUIRED';

    return LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: notificationTitle,
      body: notificationBody,
      channelId: 'incident_alerts_critical',
      payload: 'critical_incident_$incidentId',
    );
  }

  /// Format distance for display
  String _formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      final km = distanceMeters / 1000;
      return '${km.toStringAsFixed(1)}km away';
    }
  }

  /// Get emoji for incident type
  String _getIncidentEmoji(String incidentType) {
    switch (incidentType.toLowerCase()) {
      case 'robbery':
        return '💰';
      case 'assault':
        return '⚠️';
      case 'vandalism':
        return '🔨';
      case 'theft':
        return '🎒';
      case 'suspicious_activity':
        return '👀';
      case 'emergency':
        return '🚨';
      case 'accident':
        return '🚗';
      case 'fire':
        return '🔥';
      case 'medical_emergency':
        return '🏥';
      default:
        return '⚠️';
    }
  }

  /// Get notification channel ID based on priority
  String _getChannelIdForPriority(IncidentNotificationPriority priority) {
    switch (priority) {
      case IncidentNotificationPriority.low:
        return 'incident_alerts_low';
      case IncidentNotificationPriority.medium:
        return 'incident_alerts';
      case IncidentNotificationPriority.high:
        return 'incident_alerts';
      case IncidentNotificationPriority.critical:
        return 'incident_alerts_critical';
    }
  }

  /// Handle notification response
  void _handleNotificationResponse(NotificationResponse response) {
    Logger.info(
      'NotificationService._handleNotificationResponse: ===== LOCAL NOTIFICATION TAPPED =====',
    );
    Logger.info(
      'NotificationService._handleNotificationResponse: Notification ID: ${response.id}',
    );
    Logger.info(
      'NotificationService._handleNotificationResponse: Action ID: ${response.actionId}',
    );
    Logger.info(
      'NotificationService._handleNotificationResponse: Input: ${response.input}',
    );
    Logger.info(
      'NotificationService._handleNotificationResponse: Raw Payload: ${response.payload}',
    );

    if (response.payload != null && response.payload!.isNotEmpty) {
      try {
        Logger.info(
          'NotificationService._handleNotificationResponse: Starting payload parsing...',
        );

        // Parse the payload to determine notification type
        final payloadMap = _parsePayloadString(response.payload!);
        final messageType = payloadMap['type'];

        Logger.info(
          'NotificationService._handleNotificationResponse: Parsed payload map: $payloadMap',
        );
        Logger.info(
          'NotificationService._handleNotificationResponse: Message type: $messageType',
        );

        if (messageType == 'zone_location_request') {
          Logger.info(
            'NotificationService._handleNotificationResponse: ===== HANDLING LOCATION REQUEST =====',
          );

          // Extract zone info from payload
          final zoneId = payloadMap['zoneId'];
          final zoneName = payloadMap['zoneName'] ?? 'Zone';

          Logger.info(
            'NotificationService._handleNotificationResponse: Zone ID: $zoneId',
          );
          Logger.info(
            'NotificationService._handleNotificationResponse: Zone Name: $zoneName',
          );

          if (zoneId != null && zoneId.isNotEmpty) {
            Logger.info(
              'NotificationService._handleNotificationResponse: Zone ID valid, triggering instant validation flow',
            );
            // Trigger the same location request flow as background taps
            _requestLocationForInstantValidation(zoneId, zoneName);
          } else {
            Logger.error(
              'NotificationService._handleNotificationResponse: Zone ID is null or empty in payload',
            );
            Logger.error(
              'NotificationService._handleNotificationResponse: Full payload map: $payloadMap',
            );
          }
        } else {
          Logger.info(
            'NotificationService._handleNotificationResponse: Using standard stream approach for type: $messageType',
          );
          // For other notification types, use the standard stream approach
          final payload = NotificationPayload.fromString(response.payload!);
          _notificationStreamController?.add(payload);
        }
      } catch (e, stackTrace) {
        Logger.error(
          'NotificationService._handleNotificationResponse: Error parsing payload - $e',
        );
        Logger.error(
          'NotificationService._handleNotificationResponse: Stack trace: $stackTrace',
        );

        // Fallback to standard handling
        try {
          final payload = NotificationPayload.fromString(response.payload!);
          _notificationStreamController?.add(payload);
          Logger.info(
            'NotificationService._handleNotificationResponse: Fallback handling successful',
          );
        } catch (fallbackError) {
          Logger.error(
            'NotificationService._handleNotificationResponse: Fallback handling also failed: $fallbackError',
          );
        }
      }
    } else {
      Logger.warning(
        'NotificationService._handleNotificationResponse: No payload received or payload is empty',
      );
      Logger.warning(
        'NotificationService._handleNotificationResponse: Response object: $response',
      );
    }

    Logger.info(
      'NotificationService._handleNotificationResponse: ===== NOTIFICATION RESPONSE HANDLING COMPLETE =====',
    );
  }

  /// Create a reliable payload string that can be parsed consistently
  String _createReliablePayload(Map<String, dynamic> data) {
    // Create a simple key=value&key=value format that's easy to parse
    final pairs =
        data.entries.map((entry) => '${entry.key}=${entry.value}').toList();
    return pairs.join('&');
  }

  /// Parse payload string to extract data map
  Map<String, dynamic> _parsePayloadString(String payload) {
    Logger.info(
      'NotificationService._parsePayloadString: ===== PARSING PAYLOAD =====',
    );
    Logger.info(
      'NotificationService._parsePayloadString: Input payload: "$payload"',
    );
    Logger.info(
      'NotificationService._parsePayloadString: Payload length: ${payload.length}',
    );
    Logger.info(
      'NotificationService._parsePayloadString: Payload type: ${payload.runtimeType}',
    );

    try {
      // First try to parse as key=value&key=value format (our new reliable format)
      if (payload.contains('=') && payload.contains('&')) {
        Logger.info(
          'NotificationService._parsePayloadString: Detected key=value&key=value format',
        );

        final Map<String, dynamic> result = {};
        final pairs = payload.split('&');

        for (final pair in pairs) {
          final equalIndex = pair.indexOf('=');
          if (equalIndex != -1) {
            final key = pair.substring(0, equalIndex).trim();
            final value = pair.substring(equalIndex + 1).trim();
            result[key] = value;
            Logger.info(
              'NotificationService._parsePayloadString: Parsed key: "$key", value: "$value"',
            );
          }
        }

        Logger.info(
          'NotificationService._parsePayloadString: Key=value parsing result: $result',
        );
        return result;
      }

      // Handle legacy map-like format from message.data.toString()
      if (payload.startsWith('{') && payload.endsWith('}')) {
        Logger.info(
          'NotificationService._parsePayloadString: Detected map-like format',
        );

        // It's already a JSON-like string from message.data.toString()
        // Remove curly braces and parse key-value pairs
        final cleanPayload = payload.substring(1, payload.length - 1);
        Logger.info(
          'NotificationService._parsePayloadString: Clean payload: "$cleanPayload"',
        );

        final pairs = cleanPayload.split(', ');
        Logger.info(
          'NotificationService._parsePayloadString: Split into ${pairs.length} pairs: $pairs',
        );

        final Map<String, dynamic> result = {};

        for (int i = 0; i < pairs.length; i++) {
          final pair = pairs[i];
          Logger.info(
            'NotificationService._parsePayloadString: Processing pair $i: "$pair"',
          );

          final colonIndex = pair.indexOf(': ');
          if (colonIndex != -1) {
            final key = pair.substring(0, colonIndex).trim();
            final value = pair.substring(colonIndex + 2).trim();

            Logger.info(
              'NotificationService._parsePayloadString: Extracted key: "$key", value: "$value"',
            );
            result[key] = value;
          } else {
            Logger.warning(
              'NotificationService._parsePayloadString: No colon found in pair: "$pair"',
            );
          }
        }

        Logger.info(
          'NotificationService._parsePayloadString: Final result map: $result',
        );
        return result;
      } else {
        Logger.warning(
          'NotificationService._parsePayloadString: Payload does not match expected format',
        );
        Logger.warning(
          'NotificationService._parsePayloadString: Starts with {: ${payload.startsWith('{')}',
        );
        Logger.warning(
          'NotificationService._parsePayloadString: Ends with }: ${payload.endsWith('}')}',
        );

        // Try to handle as direct JSON string
        if (payload.contains(':')) {
          Logger.info(
            'NotificationService._parsePayloadString: Attempting direct key-value parsing',
          );
          final Map<String, dynamic> result = {};
          final pairs = payload.split(',');

          for (final pair in pairs) {
            final colonIndex = pair.indexOf(':');
            if (colonIndex != -1) {
              final key = pair
                  .substring(0, colonIndex)
                  .trim()
                  .replaceAll('"', '');
              final value = pair
                  .substring(colonIndex + 1)
                  .trim()
                  .replaceAll('"', '');
              result[key] = value;
            }
          }

          Logger.info(
            'NotificationService._parsePayloadString: Direct parsing result: $result',
          );
          return result;
        }

        // Handle other formats or return empty map
        Logger.warning(
          'NotificationService._parsePayloadString: Returning empty map for unrecognized format',
        );
        return {};
      }
    } catch (e, stackTrace) {
      Logger.error(
        'NotificationService._parsePayloadString: Error parsing payload - $e',
      );
      Logger.error(
        'NotificationService._parsePayloadString: Stack trace: $stackTrace',
      );
      Logger.error(
        'NotificationService._parsePayloadString: Problematic payload: "$payload"',
      );
      return {};
    }
  }

  /// Handle foreground Firebase messages
  void _handleForegroundMessage(RemoteMessage message) {
    // Check message type and handle accordingly
    final messageType = message.data['type'];

    if (messageType == 'zone_validation_result') {
      _handleValidationResultMessage(message);
    } else if (messageType == 'zone_location_request') {
      _handleLocationRequestMessage(message);
    } else if (message.notification != null) {
      // Show local notification for other foreground messages
      // Create reliable payload for general notifications
      String? notificationPayload;
      if (message.data.isNotEmpty) {
        final payloadData = Map<String, dynamic>.from(message.data);
        if (!payloadData.containsKey('type')) {
          payloadData['type'] = 'general';
        }
        notificationPayload = _createReliablePayload(payloadData);
      }

      final notification = LocalNotificationData(
        id: _generateSafeNotificationId(),
        title: message.notification!.title ?? 'Notification',
        body: message.notification!.body ?? '',
        channelId: 'general',
        payload: notificationPayload,
      );

      _showLocalNotification(notification);
    }
  }

  /// Handle background message tap
  void _handleBackgroundMessageTap(RemoteMessage message) {
    final messageType = message.data['type'];

    Logger.info(
      'NotificationService._handleBackgroundMessageTap: Notification tapped with type: $messageType',
    );
    Logger.info(
      'NotificationService._handleBackgroundMessageTap: Message data: ${message.data}',
    );

    if (messageType == 'zone_validation_result') {
      Logger.info(
        'NotificationService._handleBackgroundMessageTap: Handling validation result tap',
      );
      _handleValidationResultTap(message);
    } else if (messageType == 'zone_location_request') {
      Logger.info(
        'NotificationService._handleBackgroundMessageTap: Handling location request tap',
      );
      _handleLocationRequestTap(message);
    } else if (message.data.isNotEmpty) {
      Logger.info(
        'NotificationService._handleBackgroundMessageTap: Handling generic message tap',
      );
      final payload = NotificationPayload.fromMap(message.data);
      _notificationStreamController?.add(payload);
    } else {
      Logger.warning(
        'NotificationService._handleBackgroundMessageTap: Unknown message type or empty data',
      );
    }
  }

  /// Handle validation result message when app is in foreground
  void _handleValidationResultMessage(RemoteMessage message) {
    Logger.info(
      'NotificationService._handleValidationResultMessage: Processing validation result',
    );
    final data = message.data;
    final validationResult = data['validationResult'] ?? 'unknown';
    final zoneName = data['zoneName'] ?? 'Zone';
    final isValidated = validationResult == 'validated';

    // Create a reliable payload for validation results
    final payloadData = {
      'type': 'zone_validation_result',
      'zoneId': data['zoneId'] ?? '',
      'zoneName': zoneName,
      'validationResult': validationResult,
      'sampleType': data['sampleType'] ?? '',
      'attempt': data['attempt'] ?? '',
      'totalAttempts': data['totalAttempts'] ?? '',
    };

    final notification = LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: isValidated ? '✅ Validation Successful' : '❌ Validation Failed',
      body: '${zoneName} - ${_formatValidationMessage(data)}',
      channelId:
          isValidated ? 'zone_validation_success' : 'zone_validation_failed',
      payload: _createReliablePayload(payloadData),
    );

    _showLocalNotification(notification);

    // Add to notification stream for app-level handling
    final payload = NotificationPayload.fromMap(message.data);
    _notificationStreamController?.add(payload);
  }

  /// Handle validation result notification tap
  void _handleValidationResultTap(RemoteMessage message) {
    Logger.info(
      'NotificationService._handleValidationResultTap: Validation result tapped',
    );
    final payload = NotificationPayload.fromMap(message.data);
    _notificationStreamController?.add(payload);
  }

  /// Handle location request message when app is in foreground
  void _handleLocationRequestMessage(RemoteMessage message) {
    Logger.info(
      'NotificationService._handleLocationRequestMessage: Processing location request',
    );
    final data = message.data;
    final zoneName = data['zoneName'] ?? 'Zone';
    final attempt = data['attempt'] ?? '1';
    final totalAttempts = data['totalAttempts'] ?? '7';

    // Create a reliable JSON payload instead of using toString()
    final payloadData = {
      'type': 'zone_location_request',
      'zoneId': message.data['zoneId'] ?? '',
      'zoneName': zoneName,
      'attempt': attempt,
      'totalAttempts': totalAttempts,
    };

    final notification = LocalNotificationData(
      id: _generateSafeNotificationId(),
      title: '📍 Location Access Needed',
      body:
          'Tap to enable location for zone validation in "${zoneName}" • Sample $attempt/$totalAttempts',
      channelId: 'location_requests',
      payload: _createReliablePayload(payloadData),
    );

    _showLocalNotification(notification);

    // Add to notification stream for app-level handling
    final payload = NotificationPayload.fromMap(message.data);
    _notificationStreamController?.add(payload);
  }

  /// Handle location request notification tap
  void _handleLocationRequestTap(RemoteMessage message) {
    Logger.info(
      'NotificationService._handleLocationRequestTap: ===== LOCATION REQUEST TAP STARTED =====',
    );

    final zoneId = message.data['zoneId'];
    final zoneName = message.data['zoneName'] ?? 'Zone';

    Logger.info(
      'NotificationService._handleLocationRequestTap: Zone ID: $zoneId, Zone Name: $zoneName',
    );

    if (zoneId != null) {
      Logger.info(
        'NotificationService._handleLocationRequestTap: Zone ID valid, starting instant validation flow',
      );

      // Trigger immediate location permission and validation
      _requestLocationForInstantValidation(zoneId, zoneName);
    } else {
      Logger.error(
        'NotificationService._handleLocationRequestTap: Zone ID is null! Cannot proceed',
      );
    }
  }

  /// Request location permission and trigger instant validation
  Future<void> _requestLocationForInstantValidation(
    String zoneId,
    String zoneName,
  ) async {
    Logger.info(
      'NotificationService._requestLocationForInstantValidation: ===== STARTING INSTANT VALIDATION =====',
    );
    Logger.info(
      'NotificationService._requestLocationForInstantValidation: Zone: $zoneId ($zoneName)',
    );

    try {
      // Show processing notification
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 1 - Showing processing notification',
      );
      _showLocalNotification(
        LocalNotificationData(
          id: _generateSafeNotificationId(),
          title: '⚡ Requesting Location',
          body: 'Please grant location access for $zoneName...',
          channelId: 'location_requests',
          payload: '',
        ),
      );

      // Request location permission
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 2 - Checking location permission',
      );
      LocationPermission permission = await Geolocator.checkPermission();
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Current permission: $permission',
      );

      if (permission == LocationPermission.denied) {
        Logger.info(
          'NotificationService._requestLocationForInstantValidation: Permission denied, requesting...',
        );
        Logger.info(
          'NotificationService._requestLocationForInstantValidation: About to show permission dialog',
        );

        // Show notification about permission dialog
        _showLocalNotification(
          LocalNotificationData(
            id: _generateSafeNotificationId(),
            title: '📍 Permission Dialog',
            body:
                'A location permission dialog should appear. Please allow location access.',
            channelId: 'location_requests',
            payload: '',
          ),
        );

        permission = await Geolocator.requestPermission();
        Logger.info(
          'NotificationService._requestLocationForInstantValidation: Permission after request: $permission',
        );

        // Show result notification
        _showLocalNotification(
          LocalNotificationData(
            id: _generateSafeNotificationId(),
            title: '📍 Permission Result',
            body: 'Location permission result: $permission',
            channelId: 'location_requests',
            payload: '',
          ),
        );
      }

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        Logger.error(
          'NotificationService._requestLocationForInstantValidation: Permission denied - showing error',
        );
        // Permission denied - show error
        _showLocalNotification(
          LocalNotificationData(
            id: _generateSafeNotificationId(),
            title: '❌ Location Permission Denied',
            body:
                'Please enable location access in settings for $zoneName validation',
            channelId: 'zone_validation_failed',
            payload: '',
          ),
        );
        return;
      }

      // Check if location services are enabled
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 3 - Checking location services',
      );
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Location services enabled: $serviceEnabled',
      );

      if (!serviceEnabled) {
        Logger.error(
          'NotificationService._requestLocationForInstantValidation: Location services disabled',
        );
        _showLocalNotification(
          LocalNotificationData(
            id: _generateSafeNotificationId(),
            title: '📍 Location Services Disabled',
            body: 'Please enable location services for $zoneName validation',
            channelId: 'zone_validation_failed',
            payload: '',
          ),
        );
        return;
      }

      // Get current location
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 4 - Getting current location',
      );
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 30),
      );

      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 5 - Location obtained: (${position.latitude}, ${position.longitude}) accuracy: ${position.accuracy}m',
      );

      // Store location for cloud function access
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 6 - Storing location for cloud access',
      );
      final automaticValidationService = getIt<AutomaticValidationService>();
      await automaticValidationService.processLocationUpdate(
        zoneId: zoneId,
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
      );

      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 7 - Location stored successfully',
      );

      // Show success and trigger immediate validation
      _showLocalNotification(
        LocalNotificationData(
          id: _generateSafeNotificationId(),
          title: '✅ Location Captured',
          body: 'Processing validation for $zoneName...',
          channelId: 'location_requests',
          payload: '',
        ),
      );

      // Now trigger immediate validation with fresh location data
      Logger.info(
        'NotificationService._requestLocationForInstantValidation: Step 8 - Triggering immediate validation',
      );
      await automaticValidationService.performImmediateValidation(zoneId);

      Logger.info(
        'NotificationService._requestLocationForInstantValidation: ===== INSTANT VALIDATION COMPLETED SUCCESSFULLY =====',
      );
    } catch (e) {
      Logger.error(
        'NotificationService._requestLocationForInstantValidation: ===== ERROR DURING INSTANT VALIDATION: $e =====',
      );

      // Show error notification
      _showLocalNotification(
        LocalNotificationData(
          id: _generateSafeNotificationId(),
          title: '❌ Validation Error',
          body:
              'Unable to get location for $zoneName validation: ${e.toString()}',
          channelId: 'zone_validation_failed',
          payload: '',
        ),
      );
    }
  }

  /// Show instant validation progress dialog
  void _showInstantValidationDialog(String zoneName) {
    // This would typically be handled by the UI layer
    // For now, we'll show a notification indicating processing
    _showLocalNotification(
      LocalNotificationData(
        id: _generateSafeNotificationId(),
        title: '⚡ Processing Validation',
        body: 'Checking your location for $zoneName...',
        channelId: 'location_requests',
        payload: '',
      ),
    );
  }

  /// TEST METHOD: Create a simple test notification to verify tap handling works
  void showTestNotification() {
    Logger.info(
      'NotificationService.showTestNotification: ===== CREATING TEST NOTIFICATION =====',
    );

    final testPayload = {
      'type': 'zone_location_request',
      'zoneId': 'TEST_ZONE_123',
      'zoneName': 'Test Zone',
      'attempt': '1',
      'totalAttempts': '7',
    };

    final notificationId = _generateSafeNotificationId();
    Logger.info(
      'NotificationService.showTestNotification: Generated notification ID: $notificationId',
    );
    Logger.info(
      'NotificationService.showTestNotification: Test payload: $testPayload',
    );

    _showLocalNotification(
      LocalNotificationData(
        id: notificationId,
        title: '🧪 TEST: Tap Me!',
        body: 'This is a test notification - tap to verify tap handling works',
        channelId: 'location_requests',
        payload: _createReliablePayload(testPayload),
      ),
    );

    Logger.info(
      'NotificationService.showTestNotification: Test notification created successfully',
    );
    Logger.info(
      'NotificationService.showTestNotification: Notification should appear in system tray',
    );
    Logger.info(
      'NotificationService.showTestNotification: ===== TEST NOTIFICATION CREATION COMPLETE =====',
    );
  }

  /// TEST METHOD: Create a simple local notification without Firebase payload
  void showSimpleTestNotification() {
    Logger.info(
      'NotificationService.showSimpleTestNotification: ===== CREATING SIMPLE TEST NOTIFICATION =====',
    );

    final notificationId = _generateSafeNotificationId();
    Logger.info(
      'NotificationService.showSimpleTestNotification: Generated notification ID: $notificationId',
    );

    _showLocalNotification(
      LocalNotificationData(
        id: notificationId,
        title: '🔔 Simple Test',
        body: 'Tap this notification to test local notification handling',
        channelId: 'general',
        payload: 'simple_test_payload',
      ),
    );

    Logger.info(
      'NotificationService.showSimpleTestNotification: Simple test notification created',
    );
    Logger.info(
      'NotificationService.showSimpleTestNotification: ===== SIMPLE TEST NOTIFICATION COMPLETE =====',
    );
  }

  /// Format validation message for display
  String _formatValidationMessage(Map<String, dynamic> data) {
    final validationResult = data['validationResult'] ?? 'unknown';
    // final sampleType = data['sampleType'] ?? 'sample';
    final attempt = data['attempt'] ?? '1';
    final totalAttempts = data['totalAttempts'] ?? '7';
    final distance = data['distance'];

    String message = '';

    if (validationResult == 'validated') {
      message = 'You were detected within the zone';
    } else {
      if (distance != null) {
        final distanceMeters = double.tryParse(distance) ?? 0;
        final distanceText =
            distanceMeters < 1000
                ? '${distanceMeters.round()}m away'
                : '${(distanceMeters / 1000).toStringAsFixed(1)}km away';
        message = 'You were $distanceText from the zone';
      } else {
        message = 'You were outside the zone';
      }
    }

    return '$message • Sample $attempt/$totalAttempts';
  }

  /// Get channel name by ID
  String _getChannelName(String channelId) {
    switch (channelId) {
      case 'general':
        return 'General Notifications';
      case 'zone_validation_success':
        return 'Zone Validation Success';
      case 'zone_validation_failed':
        return 'Zone Validation Failed';
      case 'zone_events':
        return 'Zone Events';
      case 'incident_alerts':
        return 'Security Incidents';
      case 'incident_alerts_critical':
        return 'Critical Security Alerts';
      case 'incident_alerts_low':
        return 'Security Updates';
      case 'permission_requests':
        return 'Permission Requests';
      case 'location_requests':
        return 'Location Requests';
      default:
        return 'Notifications';
    }
  }

  /// Get channel description by ID
  String _getChannelDescription(String channelId) {
    switch (channelId) {
      case 'general':
        return 'General app notifications';
      case 'zone_validation_success':
        return 'Notifications for successful zone validations';
      case 'zone_validation_failed':
        return 'Notifications for failed zone validations';
      case 'zone_events':
        return 'Zone entry/exit notifications';
      case 'incident_alerts':
        return 'Security incident notifications';
      case 'incident_alerts_critical':
        return 'Critical security incident notifications';
      case 'incident_alerts_low':
        return 'Low priority security notifications';
      case 'permission_requests':
        return 'App permission request notifications';
      case 'location_requests':
        return 'Location access request notifications';
      default:
        return 'App notifications';
    }
  }

  /// Get channel importance by ID
  Importance _getChannelImportance(String channelId) {
    switch (channelId) {
      case 'zone_validation_success':
        return Importance.high;
      case 'incident_alerts':
        return Importance.high;
      case 'incident_alerts_critical':
        return Importance.max;
      case 'incident_alerts_low':
        return Importance.low;
      case 'zone_validation_failed':
      case 'zone_events':
      case 'permission_requests':
      case 'general':
      case 'location_requests':
      default:
        return Importance.defaultImportance;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _notificationStreamController?.close();
    _notificationStreamController = null;
    _instance = null;
  }
}

/// Local notification data
class LocalNotificationData {
  final int id;
  final String title;
  final String body;
  final String channelId;
  final String? payload;

  const LocalNotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.channelId,
    this.payload,
  });
}

/// Notification payload data class
class NotificationPayload {
  final String type;
  final Map<String, dynamic> data;

  const NotificationPayload({required this.type, required this.data});

  /// Create from string payload
  factory NotificationPayload.fromString(String payload) {
    try {
      final Map<String, dynamic> data = {};
      // Simple parsing - assumes key=value pairs separated by &
      for (final pair in payload.split('&')) {
        final parts = pair.split('=');
        if (parts.length == 2) {
          data[parts[0]] = parts[1];
        }
      }
      return NotificationPayload(type: data['type'] ?? 'unknown', data: data);
    } catch (e) {
      return NotificationPayload(type: 'unknown', data: {'payload': payload});
    }
  }

  /// Create from map data
  factory NotificationPayload.fromMap(Map<String, dynamic> map) {
    return NotificationPayload(type: map['type'] ?? 'unknown', data: map);
  }

  /// Check if this is a validation result notification
  bool get isValidationResult => type == 'zone_validation_result';

  /// Check if this is an incident notification
  bool get isIncident => type == 'incident' || type == 'incident_alert';

  /// Check if this is a location request notification
  bool get isLocationRequest => type == 'zone_location_request';

  /// Get validation result (validated/not_in_zone/no_location)
  String? get validationResult => data['validationResult'];

  /// Get zone ID
  String? get zoneId => data['zoneId'];

  /// Get zone name
  String? get zoneName => data['zoneName'];

  /// Check if validation was successful
  bool get isValidationSuccessful => validationResult == 'validated';

  @override
  String toString() => 'NotificationPayload(type: $type, data: $data)';
}

/// Types of zone validation notifications
enum ZoneValidationNotificationType {
  validated,
  rejected,
  automaticValidationStarted,
  automaticValidationCompleted,
  presenceConfirmed,
}

/// Types of permission notifications
enum PermissionNotificationType {
  locationRequired,
  backgroundLocationRequired,
  notificationPermissionRequired,
}

/// Types of geofence notifications
enum GeofenceNotificationType { entered, exited }
