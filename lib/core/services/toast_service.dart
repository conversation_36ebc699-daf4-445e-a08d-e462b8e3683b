import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';

enum ToastType { success, error, warning, info }

class ToastService {
  static final ToastService _instance = ToastService._internal();
  factory ToastService() => _instance;
  ToastService._internal();

  /// Show a toast message with customizable type and styling
  static void show({
    required String message,
    ToastType type = ToastType.info,
    Duration? duration,
    Alignment alignment = Alignment.topCenter,
  }) {
    final toastType = _mapToToastificationType(type);

    toastification.show(
      title: Text(message),
      type: toastType,
      style: ToastificationStyle.fillColored,
      autoCloseDuration: duration ?? const Duration(seconds: 3),
      alignment: alignment,
      direction: TextDirection.ltr,
      animationDuration: const Duration(milliseconds: 300),
      animationBuilder: (context, animation, alignment, child) {
        return FadeTransition(opacity: animation, child: child);
      },
      showProgressBar: true,
      closeButtonShowType: CloseButtonShowType.onHover,
      closeOnClick: true,
      pauseOnHover: true,
      dragToClose: true,
    );
  }

  /// Show a success toast
  static void showSuccess(String message, {Duration? duration}) {
    show(message: message, type: ToastType.success, duration: duration);
  }

  /// Show an error toast
  static void showError(String message, {Duration? duration}) {
    show(message: message, type: ToastType.error, duration: duration);
  }

  /// Show a warning toast
  static void showWarning(String message, {Duration? duration}) {
    show(message: message, type: ToastType.warning, duration: duration);
  }

  /// Show an info toast
  static void showInfo(String message, {Duration? duration}) {
    show(message: message, type: ToastType.info, duration: duration);
  }

  /// Map ToastType to ToastificationType
  static ToastificationType _mapToToastificationType(ToastType type) {
    switch (type) {
      case ToastType.success:
        return ToastificationType.success;
      case ToastType.error:
        return ToastificationType.error;
      case ToastType.warning:
        return ToastificationType.warning;
      case ToastType.info:
        return ToastificationType.info;
    }
  }

  /// Dismiss all toasts
  static void dismissAll() {
    toastification.dismissAll();
  }
}
