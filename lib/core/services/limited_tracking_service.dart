import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:geolocator/geolocator.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/services/permission_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/limited_tracking_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/coordinates.dart';

/// Service for managing limited location tracking (5 attempts in 3 days, 5-second samples)
class LimitedTrackingService {
  static LimitedTrackingService? _instance;
  static LimitedTrackingService get instance => _instance ??= LimitedTrackingService._();
  LimitedTrackingService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final LocationService _locationService = LocationService.instance;
  final PermissionService _permissionService = PermissionService.instance;
  final Uuid _uuid = const Uuid();

  static const String _collectionName = 'limited_tracking';
  static const int _sampleDurationSeconds = 5;

  /// Get current user's limited tracking status
  Future<LimitedTrackingEntity?> getUserTrackingStatus() async {
    final user = _auth.currentUser;
    if (user == null) return null;

    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(user.uid)
          .get();

      if (!doc.exists) {
        // Create initial tracking record for new user
        final initialTracking = LimitedTrackingEntity.initial(user.uid);
        await _saveTrackingStatus(initialTracking);
        return initialTracking;
      }

      return LimitedTrackingEntity.fromMap(doc.data()!);
    } catch (e) {
      print('Error getting tracking status: $e');
      return null;
    }
  }

  /// Save tracking status to Firestore
  Future<void> _saveTrackingStatus(LimitedTrackingEntity tracking) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(tracking.userId)
          .set(tracking.toMap());
    } catch (e) {
      print('Error saving tracking status: $e');
    }
  }

  /// Check if user can perform a validation attempt
  Future<bool> canPerformValidation() async {
    final tracking = await getUserTrackingStatus();
    return tracking?.canAttemptValidation ?? false;
  }

  /// Get remaining validation attempts for user
  Future<int> getRemainingAttempts() async {
    final tracking = await getUserTrackingStatus();
    return tracking?.remainingAttempts ?? 0;
  }

  /// Get days remaining in validation window
  Future<int> getDaysRemainingInWindow() async {
    final tracking = await getUserTrackingStatus();
    return tracking?.daysRemainingInWindow ?? 0;
  }

  /// Call this when user adds their first zone (starts the 3-day validation window)
  Future<void> onFirstZoneAdded() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final tracking = await getUserTrackingStatus();
      if (tracking == null) return;

      // If this is the first zone being added, start the validation window
      if (tracking.firstZoneAdded == null) {
        final updatedTracking = tracking.copyWithFirstZone();
        await _saveTrackingStatus(updatedTracking);
        print('Started 3-day validation window for user ${user.uid}');
      }
    } catch (e) {
      print('Error starting validation window: $e');
    }
  }

  /// Perform a limited validation attempt (5-second location sample)
  Future<ValidationAttemptResult> performValidationAttempt(ZoneEntity zone) async {
    final tracking = await getUserTrackingStatus();
    if (tracking == null || !tracking.canAttemptValidation) {
      return ValidationAttemptResult.failed;
    }

    final attemptId = _uuid.v4();
    final startTime = DateTime.now();

    try {
      // Check permissions first
      final permissionStatus = await _permissionService.checkAutomaticValidationPermissions();
      if (!permissionStatus.allGranted) {
        await _recordAttempt(tracking, attemptId, zone.id, startTime, ValidationAttemptResult.permissionDenied);
        return ValidationAttemptResult.permissionDenied;
      }

      // Perform 5-second location sampling
      final result = await _performLocationSampling(zone);
      await _recordAttempt(tracking, attemptId, zone.id, startTime, result);
      
      return result;
    } catch (e) {
      await _recordAttempt(tracking, attemptId, zone.id, startTime, ValidationAttemptResult.failed, e.toString());
      return ValidationAttemptResult.failed;
    }
  }

  /// Perform 5-second location sampling to validate zone presence
  Future<ValidationAttemptResult> _performLocationSampling(ZoneEntity zone) async {
    final completer = Completer<ValidationAttemptResult>();
    Timer? timeoutTimer;
    StreamSubscription<Position>? locationSubscription;
    
    final samples = <Position>[];
    final sampleStartTime = DateTime.now();

    try {
      // Set up timeout timer (5 seconds)
      timeoutTimer = Timer(Duration(seconds: _sampleDurationSeconds), () {
        locationSubscription?.cancel();
        if (!completer.isCompleted) {
          completer.complete(ValidationAttemptResult.timeout);
        }
      });

      // Start location sampling
      locationSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 0, // Get all location updates
        ),
      ).listen(
        (position) {
          samples.add(position);
          
          // Check if we have enough samples or time has elapsed
          final elapsed = DateTime.now().difference(sampleStartTime);
          if (elapsed.inSeconds >= _sampleDurationSeconds) {
            locationSubscription?.cancel();
            timeoutTimer?.cancel();
            
            if (!completer.isCompleted) {
              final result = _analyzeLocationSamples(samples, zone);
              completer.complete(result);
            }
          }
        },
        onError: (error) {
          locationSubscription?.cancel();
          timeoutTimer?.cancel();
          if (!completer.isCompleted) {
            completer.complete(ValidationAttemptResult.locationUnavailable);
          }
        },
      );

      return await completer.future;
    } catch (e) {
      timeoutTimer?.cancel();
      locationSubscription?.cancel();
      return ValidationAttemptResult.failed;
    }
  }

  /// Analyze location samples to determine if user was in zone
  ValidationAttemptResult _analyzeLocationSamples(List<Position> samples, ZoneEntity zone) {
    if (samples.isEmpty) {
      return ValidationAttemptResult.locationUnavailable;
    }

    // Check if majority of samples are within the zone
    int samplesInZone = 0;
    for (final sample in samples) {
      final distance = Geolocator.distanceBetween(
        zone.centerCoordinates.latitude,
        zone.centerCoordinates.longitude,
        sample.latitude,
        sample.longitude,
      );

      if (distance <= zone.radiusInMeters) {
        samplesInZone++;
      }
    }

    // Require at least 60% of samples to be in zone for success
    final successThreshold = (samples.length * 0.6).ceil();
    return samplesInZone >= successThreshold 
        ? ValidationAttemptResult.success 
        : ValidationAttemptResult.failed;
  }

  /// Record a validation attempt
  Future<void> _recordAttempt(
    LimitedTrackingEntity tracking,
    String attemptId,
    String zoneId,
    DateTime startTime,
    ValidationAttemptResult result,
    [String? errorMessage]
  ) async {
    final attempt = ValidationAttempt(
      id: attemptId,
      zoneId: zoneId,
      timestamp: startTime,
      sampleDuration: Duration(seconds: _sampleDurationSeconds),
      result: result,
      errorMessage: errorMessage,
    );

    final updatedTracking = tracking.copyWithNewAttempt(attempt);
    await _saveTrackingStatus(updatedTracking);
  }

  /// Get user's validation attempt history
  Future<List<ValidationAttempt>> getValidationHistory() async {
    final tracking = await getUserTrackingStatus();
    return tracking?.attempts ?? [];
  }

  /// Reset tracking for testing purposes (development only)
  Future<void> resetTrackingForUser() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      await _firestore
          .collection(_collectionName)
          .doc(user.uid)
          .delete();
    } catch (e) {
      print('Error resetting tracking: $e');
    }
  }
}
