import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';
import 'package:respublicaseguridad/core/di/injection.dart';

class ProfilePhotoService {
  static final ProfilePhotoService _instance = ProfilePhotoService._internal();
  factory ProfilePhotoService() => _instance;
  ProfilePhotoService._internal();

  static ProfilePhotoService get instance => _instance;

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = const Uuid();
  final AuthRepository _authRepository = getIt<AuthRepository>();

  /// Upload and update user profile photo
  Future<ProfilePhotoResult> uploadAndUpdateProfilePhoto({
    required File imageFile,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      print('🚀 ProfilePhotoService: Starting upload for user $userId');
      print('📁 Original file path: ${imageFile.path}');
      print('📏 Original file size: ${await imageFile.length()} bytes');

      // Step 1: Compress the image
      print('🗜️ Starting image compression...');
      final compressedFile = await _compressProfileImage(imageFile);
      print('✅ Image compressed successfully');
      print('📏 Compressed file size: ${await compressedFile.length()} bytes');

      // Step 2: Upload to Firebase Storage
      final photoId = _uuid.v4();
      final fileExtension = path.extension(imageFile.path).toLowerCase();
      final fileName = 'profile_$photoId$fileExtension';
      final storagePath = 'users/$userId/profile/$fileName';

      print('☁️ Uploading to Firebase Storage: $storagePath');
      final ref = _storage.ref().child(storagePath);
      final uploadTask = ref.putFile(compressedFile);

      // Listen to upload progress
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          print('📤 Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
          onProgress(progress);
        });
      }

      print('⏳ Waiting for upload to complete...');
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      print('✅ Upload completed! Download URL: $downloadUrl');

      // Step 3: Update user profile in Firebase Auth
      print('👤 Updating Firebase Auth profile...');
      final updateResult = await _authRepository.updateProfilePhoto(downloadUrl);

      return updateResult.fold(
        (failure) {
          print('❌ Failed to update Firebase Auth profile: ${failure.message}');
          throw ProfilePhotoException('Failed to update profile: ${failure.message}');
        },
        (user) {
          print('✅ Firebase Auth profile updated successfully');
          return ProfilePhotoResult(
            photoId: photoId,
            url: downloadUrl,
            fileName: fileName,
            user: user,
          );
        },
      );
    } catch (e) {
      print('💥 ProfilePhotoService error: $e');
      throw ProfilePhotoException('Failed to upload profile photo: $e');
    }
  }

  /// Compress image specifically for profile photos
  Future<File> _compressProfileImage(File imageFile) async {
    try {
      print('🗜️ Starting compression for: ${imageFile.path}');
      final outputPath = '${imageFile.parent.path}/profile_compressed_${path.basename(imageFile.path)}';
      print('📁 Output path: $outputPath');

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        outputPath,
        quality: 90,
        minWidth: 400,
        minHeight: 400,
        format: CompressFormat.jpeg,
      );

      // Convert XFile to File if compression succeeded, otherwise return original
      if (compressedFile != null) {
        print('✅ Compression successful: ${compressedFile.path}');
        return File(compressedFile.path);
      }
      print('⚠️ Compression returned null, using original file');
      return imageFile;
    } catch (e) {
      // If compression fails, return original file
      print('❌ Compression failed: $e, using original file');
      return imageFile;
    }
  }

  /// Delete old profile photo from storage
  Future<void> deleteProfilePhoto(String photoUrl) async {
    try {
      final ref = _storage.refFromURL(photoUrl);
      await ref.delete();
    } catch (e) {
      // Ignore deletion errors for now
      print('Failed to delete old profile photo: $e');
    }
  }
}

class ProfilePhotoResult {
  final String photoId;
  final String url;
  final String fileName;
  final dynamic user; // User entity

  const ProfilePhotoResult({
    required this.photoId,
    required this.url,
    required this.fileName,
    required this.user,
  });
}

class ProfilePhotoException implements Exception {
  final String message;
  const ProfilePhotoException(this.message);

  @override
  String toString() => 'ProfilePhotoException: $message';
}
