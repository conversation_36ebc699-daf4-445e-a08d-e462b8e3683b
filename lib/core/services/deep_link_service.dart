import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Service to handle deep links in the application
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  final _appLinks = AppLinks();
  StreamSubscription? _appLinksSubscription;
  
  /// Factory constructor to return the singleton instance
  factory DeepLinkService() {
    return _instance;
  }

  DeepLinkService._internal();

  /// Initialize the deep link service and set up listeners
  Future<void> initialize(GoRouter router) async {
    // Handle initial link if app was opened from a link
    try {
      final appLink = await _appLinks.getInitialAppLink();
      if (appLink != null) {
        _handleDeepLink(appLink, router);
      }
    } catch (e) {
      debugPrint('Error getting initial app link: $e');
    }

    // Listen for incoming links while app is running
    _appLinksSubscription = _appLinks.uriLinkStream.listen((uri) {
      _handleDeepLink(uri, router);
    });
  }

  /// Handle the deep link and navigate accordingly
  void _handleDeepLink(Uri uri, GoRouter router) {
    debugPrint('Deep link received: $uri');

    // Handle respublicaseguridad://validatezone?zoneId=123
    if (uri.scheme == 'respublicaseguridad') {
      if (uri.host == 'validatezone') {
        final zoneId = uri.queryParameters['zoneId'];
        if (zoneId != null) {
          // Navigate to zone details screen
          router.goNamed('zone-details', pathParameters: {'zoneId': zoneId});
        }
      }
      // Add more deep link handlers as needed
    }
  }

  /// Dispose of resources
  void dispose() {
    _appLinksSubscription?.cancel();
  }
}