import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

/// Service for generating secure QR tokens with cryptographic security
class QRTokenService {
  static QRTokenService? _instance;
  static QRTokenService get instance => _instance ??= QRTokenService._();
  QRTokenService._();

  final Uuid _uuid = const Uuid();
  final Random _random = Random.secure();

  /// Generate a secure QR token with encrypted data
  String generateSecureToken({
    required String sessionId,
    required String userId,
    required String tokenId,
    Duration? customExpiration,
  }) {
    final now = DateTime.now();
    final expiration = customExpiration ?? const Duration(seconds: 45);
    final expiresAt = now.add(expiration);

    // Create token payload
    final payload = {
      'tokenId': tokenId,
      'sessionId': sessionId,
      'userId': userId,
      'timestamp': now.millisecondsSinceEpoch,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'nonce': _generateNonce(),
      'version': '1.0',
    };

    // Convert to JSON and encrypt
    final jsonPayload = jsonEncode(payload);
    final encryptedData = _encryptData(jsonPayload, _generateEncryptionKey(sessionId, userId));

    // Create final token structure
    final tokenData = {
      'data': encryptedData,
      'signature': _generateSignature(encryptedData, sessionId),
      'checksum': _generateChecksum(encryptedData),
    };

    // Encode as base64 for QR code
    final tokenJson = jsonEncode(tokenData);
    return base64Url.encode(utf8.encode(tokenJson));
  }

  /// Generate a secure QR token for zone validation (without session)
  String generateSecureTokenForZone({
    required String zoneId,
    required String userId,
    required String tokenId,
    Duration? customExpiration,
  }) {
    final now = DateTime.now();
    final expiration = customExpiration ?? const Duration(seconds: 45);
    final expiresAt = now.add(expiration);

    // Create token payload for zone validation
    final payload = {
      'tokenId': tokenId,
      'zoneId': zoneId,
      'userId': userId,
      'timestamp': now.millisecondsSinceEpoch,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'nonce': _generateNonce(),
      'version': '2.0', // Version 2.0 for zone-based tokens
      'type': 'zone_validation',
    };

    // Convert to JSON and encrypt
    final jsonPayload = jsonEncode(payload);
    final encryptedData = _encryptData(jsonPayload, _generateEncryptionKey(zoneId, userId));

    // Create final token structure
    final tokenData = {
      'data': encryptedData,
      'signature': _generateSignature(encryptedData, zoneId),
      'checksum': _generateChecksum(encryptedData),
    };

    // Encode as base64 for QR code
    final tokenJson = jsonEncode(tokenData);
    return base64Url.encode(utf8.encode(tokenJson));
  }

  /// Validate and decrypt a QR token
  Map<String, dynamic>? validateAndDecryptToken(String token) {
    try {
      // Decode from base64
      final tokenBytes = base64Url.decode(token);
      final tokenJson = utf8.decode(tokenBytes);
      final tokenData = jsonDecode(tokenJson) as Map<String, dynamic>;

      final encryptedData = tokenData['data'] as String;
      final signature = tokenData['signature'] as String;
      final checksum = tokenData['checksum'] as String;

      // Verify checksum
      if (!_verifyChecksum(encryptedData, checksum)) {
        return null;
      }

      // For signature verification, we need session info
      // This is a simplified version - in production, you'd need to verify the signature
      // against the session data from the database

      // Decrypt the data (simplified - using consistent demo key)
      final decryptedJson = _decryptData(encryptedData, 'demo-key-respublica-seguridad-2024');
      final payload = jsonDecode(decryptedJson) as Map<String, dynamic>;

      // Check expiration
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(payload['expiresAt'] as int);
      if (DateTime.now().isAfter(expiresAt)) {
        return null; // Token expired
      }

      return payload;
    } catch (e) {
      return null; // Invalid token
    }
  }

  /// Generate a cryptographically secure nonce
  String _generateNonce() {
    final bytes = Uint8List(16);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = _random.nextInt(256);
    }
    return base64.encode(bytes);
  }

  /// Generate encryption key based on session and user data
  String _generateEncryptionKey(String sessionId, String userId) {
    // For demo purposes, use a simplified but consistent key
    // In production, use proper key derivation with secure storage
    return 'demo-key-respublica-seguridad-2024';
  }

  /// Simple encryption (in production, use proper AES encryption)
  String _encryptData(String data, String key) {
    // This is a simplified encryption for demo purposes
    // In production, use proper AES-256-GCM encryption
    final dataBytes = utf8.encode(data);
    final keyBytes = utf8.encode(key); // Use consistent key encoding

    final encrypted = <int>[];
    for (int i = 0; i < dataBytes.length; i++) {
      encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64.encode(encrypted);
  }

  /// Simple decryption (in production, use proper AES decryption)
  String _decryptData(String encryptedData, String key) {
    // This is a simplified decryption for demo purposes
    // In production, use proper AES-256-GCM decryption
    try {
      final encryptedBytes = base64.decode(encryptedData);
      final keyBytes = utf8.encode(key); // Simplified key for demo
      
      final decrypted = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      throw Exception('Decryption failed');
    }
  }

  /// Generate signature for token integrity
  String _generateSignature(String data, String sessionId) {
    final combined = '$data:$sessionId:${DateTime.now().day}';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return base64.encode(digest.bytes);
  }

  /// Generate checksum for data integrity
  String _generateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = md5.convert(bytes);
    return base64.encode(digest.bytes);
  }

  /// Verify checksum
  bool _verifyChecksum(String data, String expectedChecksum) {
    final actualChecksum = _generateChecksum(data);
    return actualChecksum == expectedChecksum;
  }

  /// Generate a time-based one-time password (TOTP) for additional security
  String generateTOTP(String secret, {int timeStep = 30}) {
    final now = DateTime.now();
    final timeCounter = (now.millisecondsSinceEpoch ~/ 1000) ~/ timeStep;
    
    final secretBytes = utf8.encode(secret);
    final timeBytes = _intToBytes(timeCounter);
    
    final hmac = Hmac(sha1, secretBytes);
    final digest = hmac.convert(timeBytes);
    
    final offset = digest.bytes.last & 0x0f;
    final truncated = _bytesToInt(digest.bytes.sublist(offset, offset + 4)) & 0x7fffffff;
    
    return (truncated % 1000000).toString().padLeft(6, '0');
  }

  /// Verify TOTP
  bool verifyTOTP(String token, String secret, {int timeStep = 30, int window = 1}) {
    final now = DateTime.now();
    final timeCounter = (now.millisecondsSinceEpoch ~/ 1000) ~/ timeStep;
    
    // Check current time and adjacent time windows
    for (int i = -window; i <= window; i++) {
      final testCounter = timeCounter + i;
      final testToken = generateTOTP(secret, timeStep: timeStep);
      if (testToken == token) {
        return true;
      }
    }
    
    return false;
  }

  /// Convert integer to bytes
  List<int> _intToBytes(int value) {
    return [
      (value >> 56) & 0xff,
      (value >> 48) & 0xff,
      (value >> 40) & 0xff,
      (value >> 32) & 0xff,
      (value >> 24) & 0xff,
      (value >> 16) & 0xff,
      (value >> 8) & 0xff,
      value & 0xff,
    ];
  }

  /// Convert bytes to integer
  int _bytesToInt(List<int> bytes) {
    int result = 0;
    for (int i = 0; i < bytes.length; i++) {
      result = (result << 8) | (bytes[i] & 0xff);
    }
    return result;
  }

  /// Generate a secure random token ID
  String generateTokenId() {
    return _uuid.v4();
  }

  /// Generate a secure session secret
  String generateSessionSecret() {
    final bytes = Uint8List(32);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = _random.nextInt(256);
    }
    return base64.encode(bytes);
  }

  /// Validate token format
  bool isValidTokenFormat(String token) {
    try {
      final tokenBytes = base64Url.decode(token);
      final tokenJson = utf8.decode(tokenBytes);
      final tokenData = jsonDecode(tokenJson) as Map<String, dynamic>;
      
      return tokenData.containsKey('data') &&
             tokenData.containsKey('signature') &&
             tokenData.containsKey('checksum');
    } catch (e) {
      return false;
    }
  }

  /// Extract token metadata without full decryption
  Map<String, dynamic>? getTokenMetadata(String token) {
    try {
      final tokenBytes = base64Url.decode(token);
      final tokenJson = utf8.decode(tokenBytes);
      final tokenData = jsonDecode(tokenJson) as Map<String, dynamic>;
      
      return {
        'hasData': tokenData.containsKey('data'),
        'hasSignature': tokenData.containsKey('signature'),
        'hasChecksum': tokenData.containsKey('checksum'),
        'dataLength': (tokenData['data'] as String?)?.length ?? 0,
      };
    } catch (e) {
      return null;
    }
  }
}
