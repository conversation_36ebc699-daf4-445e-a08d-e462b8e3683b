import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/error/failures.dart';

/// Comprehensive error handler for QR validation system
class QRValidationErrorHandler {
  static QRValidationErrorHandler? _instance;
  static QRValidationErrorHandler get instance => _instance ??= QRValidationErrorHandler._();
  QRValidationErrorHandler._();

  /// Handle and categorize QR validation errors
  QRValidationError handleError(dynamic error, {String? context}) {
    if (error is QRValidationError) {
      return error;
    }

    // Network-related errors
    if (error is SocketException) {
      return QRValidationError.network(
        message: 'No hay conexión a internet. Verifica tu conexión y vuelve a intentar.',
        originalError: error,
        context: context,
      );
    }

    if (error is HttpException) {
      return QRValidationError.network(
        message: 'Error de conexión con el servidor. Intenta nuevamente.',
        originalError: error,
        context: context,
      );
    }

    

    // Location-related errors
    if (error is LocationServiceDisabledException) {
      return QRValidationError.location(
        message: 'Los servicios de ubicación están deshabilitados. Actívalos en la configuración.',
        originalError: error,
        context: context,
      );
    }

    if (error is PermissionDeniedException) {
      return QRValidationError.permission(
        message: 'Se requieren permisos de ubicación para la validación QR.',
        originalError: error,
        context: context,
      );
    }

    // Timeout errors
    if (error.toString().contains('timeout') || error.toString().contains('TimeoutException')) {
      return QRValidationError.timeout(
        message: 'La operación tardó demasiado tiempo. Verifica tu conexión e intenta nuevamente.',
        originalError: error,
        context: context,
      );
    }

    // Generic server errors
    if (error.toString().contains('server') || error.toString().contains('500')) {
      return QRValidationError.server(
        message: 'Error interno del servidor. Intenta nuevamente en unos momentos.',
        originalError: error,
        context: context,
      );
    }

    // Default error
    return QRValidationError.unknown(
      message: 'Ocurrió un error inesperado. Intenta nuevamente.',
      originalError: error,
      context: context,
    );
  }

  QRValidationError _handleFirebaseError(FirebaseException error, String? context) {
    switch (error.code) {
      case 'permission-denied':
        return QRValidationError.permission(
          message: 'No tienes permisos para realizar esta acción.',
          originalError: error,
          context: context,
        );

      case 'network-request-failed':
        return QRValidationError.network(
          message: 'Error de red. Verifica tu conexión a internet.',
          originalError: error,
          context: context,
        );

      case 'unavailable':
        return QRValidationError.server(
          message: 'El servicio no está disponible temporalmente.',
          originalError: error,
          context: context,
        );

      case 'deadline-exceeded':
        return QRValidationError.timeout(
          message: 'La operación tardó demasiado tiempo.',
          originalError: error,
          context: context,
        );

      case 'not-found':
        return QRValidationError.validation(
          message: 'Los datos solicitados no fueron encontrados.',
          originalError: error,
          context: context,
        );

      case 'already-exists':
        return QRValidationError.validation(
          message: 'Ya existe una sesión de validación activa.',
          originalError: error,
          context: context,
        );

      default:
        return QRValidationError.server(
          message: 'Error del servidor: ${error.message ?? 'Error desconocido'}',
          originalError: error,
          context: context,
        );
    }
  }

  /// Get user-friendly error message with recovery suggestions
  String getErrorMessageWithSuggestions(QRValidationError error) {
    final baseMessage = error.message;
    final suggestions = getRecoverySuggestions(error);

    if (suggestions.isNotEmpty) {
      return '$baseMessage\n\nSugerencias:\n${suggestions.join('\n')}';
    }

    return baseMessage;
  }

  /// Get recovery suggestions based on error type
  List<String> getRecoverySuggestions(QRValidationError error) {
    switch (error.type) {
      case QRValidationErrorType.network:
        return [
          '• Verifica tu conexión a internet',
          '• Intenta conectarte a una red WiFi',
          '• Verifica que tengas datos móviles disponibles',
        ];

      case QRValidationErrorType.location:
        return [
          '• Activa los servicios de ubicación en configuración',
          '• Asegúrate de estar en un área con buena señal GPS',
          '• Intenta salir al aire libre para mejor precisión',
        ];

      case QRValidationErrorType.permission:
        return [
          '• Ve a configuración de la app y otorga los permisos necesarios',
          '• Reinicia la app después de otorgar permisos',
          '• Verifica que los permisos no estén bloqueados por el sistema',
        ];

      case QRValidationErrorType.timeout:
        return [
          '• Verifica tu conexión a internet',
          '• Intenta nuevamente en unos segundos',
          '• Asegúrate de tener buena señal',
        ];

      case QRValidationErrorType.validation:
        return [
          '• Verifica que estés dentro de la zona',
          '• Asegúrate de estar cerca del otro usuario',
          '• Intenta generar un nuevo código QR',
        ];

      case QRValidationErrorType.server:
        return [
          '• Intenta nuevamente en unos minutos',
          '• Verifica tu conexión a internet',
          '• Contacta soporte si el problema persiste',
        ];

      case QRValidationErrorType.security:
        return [
          '• Genera un nuevo código QR',
          '• Asegúrate de estar validando con la persona correcta',
          '• Verifica que ambos estén en la zona correcta',
        ];

      case QRValidationErrorType.unknown:
        return [
          '• Reinicia la aplicación',
          '• Verifica tu conexión a internet',
          '• Contacta soporte si el problema persiste',
        ];
    }
  }

  /// Check if error is recoverable
  bool isRecoverable(QRValidationError error) {
    switch (error.type) {
      case QRValidationErrorType.network:
      case QRValidationErrorType.timeout:
      case QRValidationErrorType.validation:
        return true;
      
      case QRValidationErrorType.location:
      case QRValidationErrorType.permission:
        return true; // User can fix these
      
      case QRValidationErrorType.server:
        return true; // Might be temporary
      
      case QRValidationErrorType.security:
        return true; // Can retry with new tokens
      
      case QRValidationErrorType.unknown:
        return false; // Unknown errors are not safely recoverable
    }
  }

  /// Get retry delay based on error type
  Duration getRetryDelay(QRValidationError error) {
    switch (error.type) {
      case QRValidationErrorType.network:
        return const Duration(seconds: 3);
      
      case QRValidationErrorType.timeout:
        return const Duration(seconds: 5);
      
      case QRValidationErrorType.server:
        return const Duration(seconds: 10);
      
      case QRValidationErrorType.validation:
        return const Duration(seconds: 2);
      
      case QRValidationErrorType.security:
        return const Duration(seconds: 1);
      
      default:
        return const Duration(seconds: 5);
    }
  }
}

/// QR validation error types
enum QRValidationErrorType {
  network,
  location,
  permission,
  timeout,
  validation,
  server,
  security,
  unknown,
}

/// Specialized error class for QR validation
class QRValidationError extends Failure {
  final QRValidationErrorType type;
  final dynamic originalError;
  final String? context;

  const QRValidationError({
    required this.type,
    required String message,
    this.originalError,
    this.context,
  }) : super(message: message);

  factory QRValidationError.network({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.network,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.location({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.location,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.permission({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.permission,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.timeout({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.timeout,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.validation({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.validation,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.server({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.server,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.security({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.security,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  factory QRValidationError.unknown({
    required String message,
    dynamic originalError,
    String? context,
  }) {
    return QRValidationError(
      type: QRValidationErrorType.unknown,
      message: message,
      originalError: originalError,
      context: context,
    );
  }

  @override
  List<Object?> get props => [type, message, originalError, context];
}
