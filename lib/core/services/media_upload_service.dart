import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

class MediaUploadService {
  static final MediaUploadService _instance = MediaUploadService._internal();
  factory MediaUploadService() => _instance;
  MediaUploadService._internal();

  static MediaUploadService get instance => _instance;

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = const Uuid();

  /// Upload a media file to Firebase Storage
  Future<MediaUploadResult> uploadMedia({
    required File file,
    required String userId,
    required MediaUploadType type,
    Function(double)? onProgress,
  }) async {
    try {
      final fileExtension = path.extension(file.path).toLowerCase();
      final mediaId = _uuid.v4();
      final fileName = '${type.name}_$mediaId$fileExtension';
      final storagePath = 'incidents/$userId/media/$fileName';

      File fileToUpload = file;
      
      // Compress images for better performance
      if (type == MediaUploadType.photo) {
        fileToUpload = await _compressImage(file);
      }

      final ref = _storage.ref().child(storagePath);
      final uploadTask = ref.putFile(fileToUpload);

      // Listen to upload progress
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      // Get file size
      final fileSize = await file.length();

      return MediaUploadResult(
        mediaId: mediaId,
        url: downloadUrl,
        fileName: fileName,
        fileSizeBytes: fileSize,
        type: type,
      );
    } catch (e) {
      throw MediaUploadException('Failed to upload media: $e');
    }
  }

  /// Compress image for better performance and storage efficiency
  Future<File> _compressImage(File imageFile) async {
    try {
      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        '${imageFile.parent.path}/compressed_${path.basename(imageFile.path)}',
        quality: 85,
        minWidth: 800,
        minHeight: 600,
        format: CompressFormat.jpeg,
      );

      // Convert XFile to File if compression succeeded, otherwise return original
      if (compressedFile != null) {
        return File(compressedFile.path);
      }
      return imageFile;
    } catch (e) {
      // If compression fails, return original file
      return imageFile;
    }
  }

  /// Delete a media file from Firebase Storage
  Future<void> deleteMedia(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
    } catch (e) {
      // Ignore deletion errors for now
      print('Failed to delete media: $e');
    }
  }

  /// Generate a thumbnail for video files (placeholder implementation)
  Future<String?> generateVideoThumbnail(String videoUrl) async {
    // TODO: Implement video thumbnail generation
    // For now, return null - the UI will show a video icon instead
    return null;
  }
}

enum MediaUploadType {
  photo,
  video,
  audio,
}

class MediaUploadResult {
  final String mediaId;
  final String url;
  final String fileName;
  final int fileSizeBytes;
  final MediaUploadType type;
  final String? thumbnailUrl;

  const MediaUploadResult({
    required this.mediaId,
    required this.url,
    required this.fileName,
    required this.fileSizeBytes,
    required this.type,
    this.thumbnailUrl,
  });
}

class MediaUploadException implements Exception {
  final String message;
  const MediaUploadException(this.message);

  @override
  String toString() => 'MediaUploadException: $message';
}
