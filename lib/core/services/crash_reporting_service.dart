import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling crash reporting and debugging in development mode
class CrashReportingService {
  static const String _crashReportsKey = 'crash_reports';
  static const String _maxCrashReports = 'max_crash_reports';
  static const int _maxReports = 50; // Keep last 50 crash reports

  /// Initialize crash reporting (only in debug mode)
  static void initialize() {
    if (kDebugMode) {
      // Set up global error handlers
      FlutterError.onError = (FlutterErrorDetails details) {
        // Log to console
        FlutterError.presentError(details);
        
        // Save crash report
        _saveCrashReport(
          type: 'Flutter Error',
          error: details.exception.toString(),
          stackTrace: details.stack.toString(),
          context: details.context?.toString(),
          library: details.library,
        );
      };

      // Handle platform errors
      PlatformDispatcher.instance.onError = (error, stack) {
        _saveCrashReport(
          type: 'Platform Error',
          error: error.toString(),
          stackTrace: stack.toString(),
        );
        return true;
      };

      if (kDebugMode) {
        print('🐛 Debug Crash Reporting initialized');
      }
    }
  }

  /// Save a crash report to local storage
  static Future<void> _saveCrashReport({
    required String type,
    required String error,
    required String stackTrace,
    String? context,
    String? library,
  }) async {
    if (!kDebugMode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final existingReports = await getCrashReports();

      final crashReport = CrashReport(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp: DateTime.now(),
        type: type,
        error: error,
        stackTrace: stackTrace,
        context: context,
        library: library,
        deviceInfo: await _getDeviceInfo(),
      );

      // Add new report to the beginning
      existingReports.insert(0, crashReport);

      // Keep only the last N reports
      if (existingReports.length > _maxReports) {
        existingReports.removeRange(_maxReports, existingReports.length);
      }

      // Save to SharedPreferences
      final reportsJson = existingReports.map((r) => r.toJson()).toList();
      await prefs.setString(_crashReportsKey, jsonEncode(reportsJson));

      if (kDebugMode) {
        print('💥 Crash report saved: $type - ${error.substring(0, error.length > 100 ? 100 : error.length)}...');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to save crash report: $e');
      }
    }
  }

  /// Get all stored crash reports
  static Future<List<CrashReport>> getCrashReports() async {
    if (!kDebugMode) return [];

    try {
      final prefs = await SharedPreferences.getInstance();
      final reportsJson = prefs.getString(_crashReportsKey);
      
      if (reportsJson == null) return [];

      final List<dynamic> reportsList = jsonDecode(reportsJson);
      return reportsList.map((json) => CrashReport.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load crash reports: $e');
      }
      return [];
    }
  }

  /// Clear all crash reports
  static Future<void> clearCrashReports() async {
    if (!kDebugMode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_crashReportsKey);
      
      if (kDebugMode) {
        print('🗑️ All crash reports cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear crash reports: $e');
      }
    }
  }

  /// Get device information for crash reports
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'isDebug': kDebugMode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Manually log a custom error (for testing)
  static Future<void> logCustomError(String error, [StackTrace? stackTrace]) async {
    if (!kDebugMode) return;

    await _saveCrashReport(
      type: 'Manual Error',
      error: error,
      stackTrace: stackTrace?.toString() ?? 'No stack trace available',
    );
  }
}

/// Data class for crash reports
class CrashReport {
  final String id;
  final DateTime timestamp;
  final String type;
  final String error;
  final String stackTrace;
  final String? context;
  final String? library;
  final Map<String, dynamic> deviceInfo;

  CrashReport({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.error,
    required this.stackTrace,
    this.context,
    this.library,
    required this.deviceInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'error': error,
      'stackTrace': stackTrace,
      'context': context,
      'library': library,
      'deviceInfo': deviceInfo,
    };
  }

  factory CrashReport.fromJson(Map<String, dynamic> json) {
    return CrashReport(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      type: json['type'],
      error: json['error'],
      stackTrace: json['stackTrace'],
      context: json['context'],
      library: json['library'],
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] ?? {}),
    );
  }

  String get shortError {
    if (error.length <= 100) return error;
    return '${error.substring(0, 100)}...';
  }

  String get formattedTimestamp {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
