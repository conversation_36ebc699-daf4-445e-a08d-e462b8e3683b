import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Service for managing environment variables and configuration
class EnvironmentService {
  static bool _isInitialized = false;

  /// Initialize the environment service by loading the .env file
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await dotenv.load(fileName: '.env');
      _isInitialized = true;
      
      if (kDebugMode) {
        print('Environment service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load environment file: $e');
      }
      // Continue without env file - use fallback values
      _isInitialized = true;
    }
  }

  /// Get Google Maps API key based on platform
  static String getGoogleMapsApiKey() {
    if (!_isInitialized) {
      throw Exception('EnvironmentService not initialized. Call initialize() first.');
    }

    if (Platform.isAndroid) {
      return dotenv.env['GOOGLE_MAPS_API_KEY_ANDROID'] ?? '';
    } else if (Platform.isIOS) {
      return dotenv.env['GOOGLE_MAPS_API_KEY_IOS'] ?? '';
    } else {
      // For web or other platforms, use Android key as fallback
      return dotenv.env['GOOGLE_MAPS_API_KEY_ANDROID'] ?? '';
    }
  }

  /// Get environment type (development, staging, production)
  static String getEnvironment() {
    if (!_isInitialized) {
      return 'development';
    }
    return dotenv.env['ENVIRONMENT'] ?? 'development';
  }

  /// Check if running in development mode
  static bool isDevelopment() {
    return getEnvironment() == 'development';
  }

  /// Check if running in production mode
  static bool isProduction() {
    return getEnvironment() == 'production';
  }

  /// Get a specific environment variable
  static String? getEnvVar(String key) {
    if (!_isInitialized) {
      return null;
    }
    return dotenv.env[key];
  }

  /// Check if environment service is initialized
  static bool get isInitialized => _isInitialized;
}
