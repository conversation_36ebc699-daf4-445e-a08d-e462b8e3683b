import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:respublicaseguridad/l10n/generated/app_localizations.dart';

/// Service for managing app localization and language preferences
class LocalizationService {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'es'; // Spanish as primary
  
  static SharedPreferences? _prefs;
  
  /// Initialize the localization service
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  /// Get the currently selected language code
  static String getCurrentLanguage() {
    return _prefs?.getString(_languageKey) ?? _defaultLanguage;
  }
  
  /// Set the language preference
  static Future<void> setLanguage(String languageCode) async {
    await _prefs?.setString(_languageKey, languageCode);
  }
  
  /// Get the locale for the current language
  static Locale getCurrentLocale() {
    final languageCode = getCurrentLanguage();
    return Locale(languageCode);
  }
  
  /// Get all supported locales
  static List<Locale> getSupportedLocales() {
    return const [
      Locale('es'), // Spanish (primary)
      Locale('en'), // English (secondary)
    ];
  }
  
  /// Check if a locale is supported
  static bool isLocaleSupported(Locale locale) {
    return getSupportedLocales().any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }
  
  /// Get language name for display
  static String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'es':
        return 'Español';
      case 'en':
        return 'English';
      default:
        return languageCode.toUpperCase();
    }
  }
  
  /// Get all available languages with their codes and names
  static Map<String, String> getAvailableLanguages() {
    return {
      'es': 'Español',
      'en': 'English',
    };
  }
}

/// Extension on BuildContext for easy access to localizations
extension LocalizationExtension on BuildContext {
  /// Get the current app localizations
  AppLocalizations get l10n => AppLocalizations.of(this)!;
  
  /// Get the current locale
  Locale get locale => Localizations.localeOf(this);
  
  /// Check if current language is Spanish
  bool get isSpanish => locale.languageCode == 'es';
  
  /// Check if current language is English
  bool get isEnglish => locale.languageCode == 'en';
  
  /// Get localized plural form for crash reports
  String getCrashReportsText(int count) {
    final plural = count == 1 ? '' : 's';
    return l10n.crashReportsFound(count, plural);
  }
}

/// Extension for convenient localization access in widgets
extension LocalizedText on String {
  /// Get localized version of common strings
  String localized(BuildContext context) {
    final l10n = context.l10n;
    
    // Map common strings to their localized versions
    switch (toLowerCase()) {
      case 'home':
        return l10n.home;
      case 'alerts':
        return l10n.alerts;
      case 'security':
        return l10n.security;
      case 'more':
        return l10n.more;
      case 'settings':
        return l10n.settings;
      case 'loading':
        return l10n.loading;
      case 'error':
        return l10n.error;
      case 'success':
        return l10n.success;
      case 'cancel':
        return l10n.cancel;
      case 'ok':
        return l10n.ok;
      case 'confirm':
        return l10n.confirm;
      default:
        return this; // Return original string if no translation found
    }
  }
}
