import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';

/// Abstract base class for all use cases in the application.
/// 
/// This class defines the contract that all use cases must follow.
/// Use cases represent the business logic of the application and should
/// be independent of any external frameworks or UI concerns.
/// 
/// Type parameters:
/// - [Type]: The return type of the use case
/// - [Params]: The parameters required by the use case
abstract class UseCase<Type, Params> {
  /// Executes the use case with the given parameters.
  /// 
  /// Returns an [Either] containing:
  /// - [Left]: A [Failure] if the operation failed
  /// - [Right]: The expected result of type [Type] if successful
  Future<Either<Failure, Type>> call(Params params);
}

/// A special use case that doesn't require any parameters.
/// 
/// This is useful for use cases that don't need input parameters,
/// such as getting current user information or fetching app settings.
abstract class NoParamsUseCase<Type> {
  /// Executes the use case without parameters.
  /// 
  /// Returns an [Either] containing:
  /// - [Left]: A [Failure] if the operation failed
  /// - [Right]: The expected result of type [Type] if successful
  Future<Either<Failure, Type>> call();
}
