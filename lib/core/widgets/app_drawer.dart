import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_state.dart';

class AppDrawer extends StatelessWidget {
  final AuthState authState;
  final HomeState homeState;

  const AppDrawer({
    Key? key,
    required this.authState,
    required this.homeState,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Drawer(
      width: 280.w,
      backgroundColor: isDark ? theme.cardColor : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
      child: Column(
        children: [
          _buildHeader(context, theme, isDark),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                SizedBox(height: 16.h),
                _buildNavigationSection(context, theme),
                const Divider(height: 32),
                _buildAccountSection(context, theme),
                SizedBox(height: 16.h),
              ],
            ),
          ),
          _buildFooter(context, theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme, bool isDark) {
    return Container(
      height: 160.h,
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
      ),
      child: Stack(
        children: [
          // Subtle pattern
          Positioned.fill(
            child: CustomPaint(
              painter: PatternPainter(),
            ),
          ),
          SafeArea(
            bottom: false,
            child: Padding(
              padding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // App name
                  Text(
                    'Respública Seguridad',
                    style: GoogleFonts.outfit(
                      color: Colors.white,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // User info
                  if (authState.isAuthenticated && authState.user.isNotEmpty)
                    _buildUserInfo(authState)
                  else
                    _buildGuestInfo(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo(AuthState authState) {
    final userName = authState.user.displayName?.isNotEmpty == true 
        ? authState.user.displayName! 
        : authState.user.email?.split('@').first ?? 'User';
    
    // Get zone information from homeState
    String zoneInfo = 'No Zone Assigned';
    // TODO: Add zone information when available in the user entity
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          userName,
          style: GoogleFonts.outfit(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        Text(
          zoneInfo,
          style: GoogleFonts.outfit(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12.sp,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildGuestInfo() {
    return Text(
      'Guest User',
      style: GoogleFonts.outfit(
        color: Colors.white,
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildNavigationSection(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        
        _buildDrawerItem(
          context: context,
          icon: FluentIcons.home_24_regular,
          activeIcon: FluentIcons.home_24_filled,
          title: context.l10n.home ?? 'Home',
          isActive: true,
          onTap: () => Navigator.pop(context),
        ),
        if (authState.isAuthenticated) ...[
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.person_24_regular,
            activeIcon: FluentIcons.person_24_filled,
            title: 'Profile',
            onTap: () {
              Navigator.pop(context);
              // Navigate to profile when implemented
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.location_24_regular,
            activeIcon: FluentIcons.location_24_filled,
            title: 'My Zones',
            onTap: () {
              Navigator.pop(context);
              context.push('/my-zones');
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.alert_24_regular,
            activeIcon: FluentIcons.alert_24_filled,
            title: 'My Incidents',
            onTap: () {
              Navigator.pop(context);
              context.go('/alerts');
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.alert_24_regular,
            activeIcon: FluentIcons.alert_24_filled,
            title: 'Notifications',
            onTap: () {
              Navigator.pop(context);
              context.push('/notification-history');
            },
          ),
        ],
      ],
    );
  }

  Widget _buildAccountSection(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Text(
            'Account',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ),
        SizedBox(height: 8.h),
        if (authState.isAuthenticated) ...[
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.settings_24_regular,
            activeIcon: FluentIcons.settings_24_filled,
            title: context.l10n.settings ?? 'Settings',
            onTap: () {
              Navigator.pop(context);
              context.push('/settings');
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.sign_out_24_regular,
            activeIcon: FluentIcons.sign_out_24_regular,
            title: 'Sign Out',
            onTap: () {
              Navigator.pop(context);
              _showSignOutDialog(context);
            },
            isDestructive: true,
          ),
        ] else ...[
          _buildDrawerItem(
            context: context,
            icon: FluentIcons.person_add_24_regular,
            activeIcon: FluentIcons.person_add_24_filled,
            title: 'Sign In',
            onTap: () {
              Navigator.pop(context);
              context.push('/auth/login');
            },
          ),
        ],
      ],
    );
  }

  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required IconData activeIcon,
    required String title,
    required VoidCallback onTap,
    bool isActive = false,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    
    return ListTile(
      leading: Icon(
        isActive ? activeIcon : icon,
        color: isDestructive 
            ? Colors.red
            : isActive 
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withOpacity(0.7),
        size: 24.sp,
      ),
      title: Text(
        title,
        style: GoogleFonts.outfit(
          fontSize: 16.sp,
          fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
          color: isDestructive 
              ? Colors.red
              : isActive 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
      selected: isActive,
      selectedTileColor: theme.colorScheme.primary.withOpacity(0.1),
    );
  }

  Widget _buildFooter(BuildContext context, ThemeData theme, bool isDark) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Text(
        'Version 1.0.0',
        style: GoogleFonts.outfit(
          fontSize: 12.sp,
          color: theme.colorScheme.onSurface.withOpacity(0.5),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Sign Out',
      message: 'Are you sure you want to sign out?',
      cancelText: context.l10n.cancel ?? 'Cancel',
      confirmText: 'Sign Out',
      onCancel: () {},
      onConfirm: () {
        context.read<AuthBloc>().add(AuthSignOutRequested());
      },
    );
  }
}

// Custom painter for subtle pattern in header
class PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    // Create a subtle dot pattern
    const double spacing = 40.0;
    const double dotRadius = 1.5;

    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), dotRadius, paint);
      }
    }

    // Add some diagonal lines for texture
    final linePaint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    const double lineSpacing = 60.0;
    for (double i = -size.height; i < size.width + size.height; i += lineSpacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        linePaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 