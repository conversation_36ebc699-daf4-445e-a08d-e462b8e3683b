import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_fonts/google_fonts.dart';

/// A reusable Google Maps widget with customizable features
class GoogleMapsWidget extends StatefulWidget {
  final LatLng? initialLocation;
  final double initialZoom;
  final Set<Marker> markers;
  final bool showMyLocationButton;
  final bool showZoomControls;
  final Function(GoogleMapController)? onMapCreated;
  final Function(LatLng)? onTap;
  final Function(CameraPosition)? onCameraMove;

  const GoogleMapsWidget({
    Key? key,
    this.initialLocation,
    this.initialZoom = 14.0,
    this.markers = const {},
    this.showMyLocationButton = true,
    this.showZoomControls = true,
    this.onMapCreated,
    this.onTap,
    this.onCameraMove,
  }) : super(key: key);

  @override
  State<GoogleMapsWidget> createState() => _GoogleMapsWidgetState();
}

class _GoogleMapsWidgetState extends State<GoogleMapsWidget> {
  Completer<GoogleMapController> _controller = Completer();
  LatLng _currentLocation = const LatLng(-34.397, 150.644); // Default Sydney location
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() {
    try {
      // Use provided initial location or default
      _currentLocation = widget.initialLocation ?? _currentLocation;
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize location: $e';
        _isLoading = false;
      });
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller.complete(controller);
    widget.onMapCreated?.call(controller);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_errorMessage != null) {
      return _buildErrorWidget();
    }

    return GoogleMap(
      onMapCreated: _onMapCreated,
      initialCameraPosition: CameraPosition(
        target: _currentLocation,
        zoom: widget.initialZoom,
      ),
      markers: widget.markers,
      myLocationEnabled: true,
      myLocationButtonEnabled: widget.showMyLocationButton,
      zoomControlsEnabled: widget.showZoomControls,
      mapToolbarEnabled: false,
      onTap: widget.onTap,
      onCameraMove: widget.onCameraMove,
      style: _getMapStyle(),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Loading Map...',
              style: GoogleFonts.outfit(
                fontSize: 16,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Map Error',
              style: GoogleFonts.outfit(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage ?? 'Unknown error occurred',
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _initializeLocation();
              },
              child: Text(
                'Retry',
                style: GoogleFonts.outfit(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _getMapStyle() {
    // You can customize map style here based on theme
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (isDark) {
      // Return dark mode map style JSON string if needed
      return null;
    }
    return null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}

/// Helper class for creating common map markers
class MapMarkerHelper {
  static Marker createMarker({
    required String markerId,
    required LatLng position,
    String? title,
    String? snippet,
    BitmapDescriptor? icon,
    Function()? onTap,
  }) {
    return Marker(
      markerId: MarkerId(markerId),
      position: position,
      infoWindow: InfoWindow(
        title: title,
        snippet: snippet,
      ),
      icon: icon ?? BitmapDescriptor.defaultMarker,
      onTap: onTap,
    );
  }

  static Set<Marker> createMarkersFromLocations(List<MapLocation> locations) {
    return locations.map((location) {
      return createMarker(
        markerId: location.id,
        position: location.position,
        title: location.title,
        snippet: location.description,
      );
    }).toSet();
  }
}

/// Data class for map locations
class MapLocation {
  final String id;
  final LatLng position;
  final String title;
  final String? description;

  const MapLocation({
    required this.id,
    required this.position,
    required this.title,
    this.description,
  });
}
