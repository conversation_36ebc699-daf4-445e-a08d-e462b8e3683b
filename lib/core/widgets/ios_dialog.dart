import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';

/// A utility class that provides iOS-style dialogs for the application
class IosDialog {
  /// Shows an iOS-style alert dialog with title, message, and actions
  static Future<T?> showAlertDialog<T>({
    required BuildContext context,
    required String title,
    required String message,
    String? cancelText,
    String? confirmText,
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
  }) {
    return showCupertinoDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) => CupertinoAlertDialog(
        title: Text(
          title,
          style: GoogleFonts.outfit(),
        ),
        content: Text(
          message,
          style: GoogleFonts.outfit(),
        ),
        actions: [
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
            child: Text(
              cancelText ?? context.l10n.cancel,
              style: GoogleFonts.outfit(),
            ),
          ),
          if (confirmText != null)
            CupertinoDialogAction(
              isDefaultAction: true,
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm?.call();
              },
              child: Text(
                confirmText,
                style: GoogleFonts.outfit(),
              ),
            ),
        ],
      ),
    );
  }

  /// Shows an iOS-style action sheet with title, message, and actions
  static Future<T?> showActionSheet<T>({
    required BuildContext context,
    String? title,
    String? message,
    required List<CupertinoActionSheetAction> actions,
    CupertinoActionSheetAction? cancelAction,
  }) {
    return showCupertinoModalPopup<T>(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: title != null ? Text(
          title,
          style: GoogleFonts.outfit(),
        ) : null,
        message: message != null ? Text(
          message,
          style: GoogleFonts.outfit(),
        ) : null,
        actions: actions,
        cancelButton: cancelAction ??
            CupertinoActionSheetAction(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                context.l10n.cancel,
                style: GoogleFonts.outfit(),
              ),
            ),
      ),
    );
  }

  /// Shows an iOS-style loading indicator dialog
  static Future<void> showLoadingDialog({
    required BuildContext context,
    String? message,
  }) {
    return showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            color: CupertinoTheme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CupertinoActivityIndicator(),
              const SizedBox(height: 12),
              Text(
                message ?? context.l10n.loading,
                style: GoogleFonts.outfit(fontSize: 14),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Shows an iOS-style date picker
  static Future<DateTime?> showDatePicker({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
  }) {
    DateTime? selectedDate = initialDate;
    
    return showCupertinoModalPopup<DateTime>(
      context: context,
      builder: (context) => Container(
        height: 216,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        color: CupertinoTheme.of(context).scaffoldBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    child: Text(
                      context.l10n.cancel,
                      style: GoogleFonts.outfit(),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  CupertinoButton(
                    child: Text(
                      context.l10n.done,
                      style: GoogleFonts.outfit(),
                    ),
                    onPressed: () => Navigator.of(context).pop(selectedDate),
                  ),
                ],
              ),
              const Divider(height: 0),
              Expanded(
                child: CupertinoDatePicker(
                  initialDateTime: initialDate,
                  minimumDate: firstDate,
                  maximumDate: lastDate,
                  mode: CupertinoDatePickerMode.date,
                  onDateTimeChanged: (DateTime newDate) {
                    selectedDate = newDate;
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}