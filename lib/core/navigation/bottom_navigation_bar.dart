import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';

class AppBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const AppBottomNavigationBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
      child: Container(
        height: 68.h,
        decoration: BoxDecoration(
          color: isDark ? theme.cardColor : Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow:
              isDark
                  ? []
                  : [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 0),
                    ),
                  ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: BottomNavigationBar(
            backgroundColor: Colors.transparent,
            currentIndex: currentIndex,
            onTap: onTap,
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: isDark ? Colors.grey[400] : Colors.grey,
            elevation: 0,
            showSelectedLabels: true,
            showUnselectedLabels: true,
            selectedFontSize: 12.sp,
            unselectedFontSize: 12.sp,
            iconSize: 24.sp,
            items: [
              BottomNavigationBarItem(
                icon: const Icon(FluentIcons.compass_northwest_16_filled),
                activeIcon: const Icon(FluentIcons.compass_northwest_16_filled),
                label: context.l10n.home,
              ),
              BottomNavigationBarItem(
                icon: const Icon(FluentIcons.add_circle_24_filled),
                activeIcon: const Icon(FluentIcons.add_circle_24_filled),
                label: context.l10n.alerts,
              ),
              BottomNavigationBarItem(
                icon: const Icon(FluentIcons.shield_24_regular),
                activeIcon: const Icon(FluentIcons.shield_24_filled),
                label: context.l10n.security,
              ),
              BottomNavigationBarItem(
                icon: const Icon(FluentIcons.more_horizontal_24_regular),
                activeIcon: const Icon(FluentIcons.more_horizontal_24_filled),
                label: context.l10n.more,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Scaffold wrapper for handling bottom navigation logic
class ScaffoldWithBottomNavBar extends StatefulWidget {
  final Widget body;
  final String currentRoute;

  const ScaffoldWithBottomNavBar({
    Key? key,
    required this.body,
    required this.currentRoute,
  }) : super(key: key);

  @override
  State<ScaffoldWithBottomNavBar> createState() =>
      _ScaffoldWithBottomNavBarState();
}

// InheritedWidget to share drawer state
class DrawerStateProvider extends InheritedWidget {
  final bool isDrawerOpen;
  final Function(bool) setDrawerState;

  const DrawerStateProvider({
    Key? key,
    required this.isDrawerOpen,
    required this.setDrawerState,
    required Widget child,
  }) : super(key: key, child: child);

  static DrawerStateProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<DrawerStateProvider>();
  }

  @override
  bool updateShouldNotify(DrawerStateProvider oldWidget) {
    return isDrawerOpen != oldWidget.isDrawerOpen;
  }
}

class _ScaffoldWithBottomNavBarState extends State<ScaffoldWithBottomNavBar> {
  bool _isDrawerOpen = false;

  void _setDrawerState(bool isOpen) {
    if (_isDrawerOpen != isOpen) {
      setState(() {
        _isDrawerOpen = isOpen;
      });
    }
  }

  int _getCurrentIndex() {
    switch (widget.currentRoute) {
      case '/home':
        return 0;
      case '/alerts':
        return 1;
      case '/security':
        return 2;
      case '/more':
        return 3;
      default:
        return 0;
    }
  }

  void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case 0:
        if (widget.currentRoute != '/home') {
          context.go('/home');
        }
        break;
      case 1:
        if (widget.currentRoute != '/alerts') {
          context.go('/alerts');
        }
        break;
      case 2:
        if (widget.currentRoute != '/security') {
          context.go('/security');
        }
        break;
      case 3:
        if (widget.currentRoute != '/more') {
          context.go('/more');
        }
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        extendBody: true,
        body: DrawerStateProvider(
          isDrawerOpen: _isDrawerOpen,
          setDrawerState: _setDrawerState,
          child: widget.body,
        ),
        bottomNavigationBar: _isDrawerOpen 
            ? null 
            : AppBottomNavigationBar(
                currentIndex: _getCurrentIndex(),
                onTap: (index) => _onItemTapped(index, context),
              ),
      ),
    );
  }
}
