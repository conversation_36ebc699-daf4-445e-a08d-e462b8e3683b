// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'República Seguridad';

  @override
  String get home => 'Inicio';

  @override
  String get alerts => 'Alertas';

  @override
  String get security => 'Seguridad';

  @override
  String get more => 'Más';

  @override
  String get settings => 'Configuración';

  @override
  String get appearance => 'Apariencia';

  @override
  String get lightTheme => 'Tema claro';

  @override
  String get lightThemeDescription => 'Interfaz con fondo claro';

  @override
  String get darkTheme => 'Tema oscuro';

  @override
  String get darkThemeDescription => 'Interfaz con fondo oscuro';

  @override
  String get language => 'Idioma';

  @override
  String get spanish => 'Español';

  @override
  String get english => 'English';

  @override
  String get debug => '🐛 Debug';

  @override
  String get crashReports => 'Reportes de Errores';

  @override
  String get crashReportsDescription =>
      'Ver reportes de errores y fallos de la aplicación';

  @override
  String get generateTestReport => 'Generar Reporte de Prueba';

  @override
  String get generateTestReportDescription =>
      'Crear un reporte de error de prueba para depuración';

  @override
  String get testReportGenerated =>
      '¡Reporte de prueba generado! Revisa los reportes de errores para verlo.';

  @override
  String get email => 'Correo electrónico';

  @override
  String get password => 'Contraseña';

  @override
  String get fullName => 'Nombre completo';

  @override
  String get login => 'Iniciar Sesión';

  @override
  String get signup => 'Registrarse';

  @override
  String get forgotPassword => '¿Olvidaste tu contraseña?';

  @override
  String get resetPassword => 'Restablecer Contraseña';

  @override
  String get sendLink => 'Enviar Enlace';

  @override
  String get backToLogin => 'Volver a Iniciar Sesión';

  @override
  String get pleaseEnterEmail => 'Por favor ingresa tu correo';

  @override
  String get pleaseEnterValidEmail => 'Ingresa un correo electrónico válido';

  @override
  String get pleaseEnterName => 'Por favor ingresa tu nombre';

  @override
  String get searchForLocations => 'Buscar ubicaciones...';

  @override
  String locationNotFound(String location) {
    return 'Ubicación no encontrada: $location';
  }

  @override
  String get noCrashReports => 'Sin Reportes de Errores';

  @override
  String get appRunningSmooth =>
      '¡Tu aplicación funciona sin problemas!\nToca el ícono de error para generar un error de prueba.';

  @override
  String crashReportsFound(int count, String plural) {
    return '$count reporte$plural de error$plural encontrado$plural';
  }

  @override
  String get debugReport => 'Reporte de Depuración';

  @override
  String get widgetError => 'Error de Widget';

  @override
  String get systemError => 'Error del Sistema';

  @override
  String get loading => 'Cargando...';

  @override
  String get cancel => 'Cancelar';

  @override
  String get ok => 'Aceptar';

  @override
  String get confirm => 'Confirmar';

  @override
  String get error => 'Error';

  @override
  String get success => 'Éxito';

  @override
  String get warning => 'Advertencia';

  @override
  String get info => 'Información';

  @override
  String get enterCredentials => 'Ingresa tus credenciales para continuar';

  @override
  String get dontHaveAccount => '¿No tienes una cuenta?';

  @override
  String get alreadyHaveAccount => '¿Ya tienes una cuenta?';

  @override
  String get createAccount => 'Crear Cuenta';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get orContinueWith => 'O continúa con';

  @override
  String get google => 'Google';

  @override
  String get facebook => 'Facebook';

  @override
  String get signUpPrompt => 'Regístrate';

  @override
  String get mustAcceptTerms => 'Debes aceptar los términos y condiciones';

  @override
  String get signUpToStart =>
      'Regístrate para comenzar a utilizar la plataforma';

  @override
  String get orSignUpWithEmail => 'O regístrate con email';

  @override
  String get confirmPassword => 'Confirmar contraseña';

  @override
  String get passwordStrength => 'Fortaleza de la contraseña: ';

  @override
  String get veryWeak => 'Muy débil';

  @override
  String get weak => 'Débil';

  @override
  String get moderate => 'Moderada';

  @override
  String get strong => 'Fuerte';

  @override
  String get veryStrong => 'Muy fuerte';

  @override
  String get acceptTerms => 'Acepto los ';

  @override
  String get termsAndConditions => 'términos y condiciones';

  @override
  String get andThe => ' y la ';

  @override
  String get privacyPolicy => 'política de privacidad';

  @override
  String get done => 'Listo';

  @override
  String get homeLocation => 'Ubicación de Inicio';

  @override
  String get yourCurrentArea => 'Tu área actual';

  @override
  String securityPoint(String number) {
    return 'Punto de Seguridad $number';
  }

  @override
  String get activeSecurityMonitoring => 'Monitoreo de seguridad activo';

  @override
  String get emergencyResponseAvailable => 'Respuesta de emergencia disponible';

  @override
  String get searchResult => 'Resultado de búsqueda';

  @override
  String found(String location) {
    return 'Encontrado: $location';
  }

  @override
  String get identityVerification => '🔐 Verificación de Identidad';

  @override
  String get identityVerificationMessage =>
      '¡Bienvenido a República Seguridad! Para garantizar tu seguridad y acceso a todas las funciones de protección, por favor verifica tu identidad. Esto nos ayuda a proteger tu cuenta y brindar servicios de seguridad personalizados en tu área.';

  @override
  String get skipForNow => 'Omitir por Ahora';

  @override
  String get verifyIdentity => 'Verificar Identidad';

  @override
  String get moreOptions => 'Más Opciones';

  @override
  String get logout => 'Cerrar sesión';

  @override
  String get logoutConfirmation => '¿Estás seguro que deseas cerrar sesión?';

  @override
  String get accountSettings => 'Configuración de Cuenta';

  @override
  String get manageAccountPreferences =>
      'Administra las preferencias de tu cuenta';

  @override
  String get notifications => 'Notificaciones';

  @override
  String get controlNotificationSettings =>
      'Controla la configuración de notificaciones';

  @override
  String get themeMode => 'Modo de Tema';

  @override
  String get light => 'Claro';

  @override
  String get dark => 'Oscuro';

  @override
  String get privacy => 'Privacidad';

  @override
  String get helpSupport => 'Ayuda y Soporte';

  @override
  String get idFrontSide => 'Frente del DNI';

  @override
  String get idBackSide => 'Reverso del DNI';

  @override
  String get profilePhoto => 'Foto de Perfil';

  @override
  String get review => 'Revisión';

  @override
  String get captureIdFront => 'Capturar Frente del DNI';

  @override
  String get captureIdBack => 'Capturar Reverso del DNI';

  @override
  String get captureProfilePhoto => 'Capturar Foto de Perfil';

  @override
  String get retake => 'Volver a tomar';

  @override
  String get next => 'Siguiente';

  @override
  String get back => 'Atrás';

  @override
  String get submit => 'Enviar';

  @override
  String get identityVerificationComplete =>
      'Verificación de identidad completada';

  @override
  String get submittingDocuments => 'Enviando tus documentos...';

  @override
  String get idFrontInstructions => 'Coloca tu DNI dentro del marco';

  @override
  String get idBackInstructions => 'Ahora captura el reverso de tu DNI';

  @override
  String get profilePhotoInstructions =>
      'Toma una selfie posicionando tu rostro dentro del marco ovalado';

  @override
  String get reviewInstructions =>
      'Por favor revisa tus documentos antes de enviar';

  @override
  String get selectFromGallery => 'Seleccionar de la galería';

  @override
  String get edit => 'Editar';

  @override
  String get identityVerificationTitle => 'Verificación de Identidad';

  @override
  String get identityVerificationSubtitle =>
      'Verifica tu identidad para mayor seguridad';

  @override
  String get linkedAccounts => 'Cuentas Vinculadas';

  @override
  String get linkAdditionalAccounts => 'Vincular Cuentas Adicionales';

  @override
  String get addMoreLoginOptions => 'Agrega más opciones de inicio de sesión';

  @override
  String get noLinkedAccounts => 'No tienes cuentas adicionales vinculadas';

  @override
  String get linkGoogle => 'Vincular Google';

  @override
  String get linkFacebook => 'Vincular Facebook';

  @override
  String get googleAccountLinked => 'Cuenta de Google vinculada';

  @override
  String get facebookAccountLinked => 'Cuenta de Facebook vinculada';

  @override
  String get emailPasswordMethod => 'Correo y Contraseña';

  @override
  String get primaryLoginMethod => 'Método principal de inicio de sesión';

  @override
  String get quickLoginWithGoogle =>
      'Inicia sesión rápidamente con tu cuenta de Google';

  @override
  String get quickLoginWithFacebook =>
      'Inicia sesión rápidamente con tu cuenta de Facebook';

  @override
  String get unlink => 'Desvincular';

  @override
  String get primary => 'Principal';

  @override
  String get verified => 'Verificado';

  @override
  String get inProcess => 'En Proceso';

  @override
  String get underReview => 'En Revisión';

  @override
  String get rejected => 'Rechazado';

  @override
  String get unverified => 'Sin Verificar';

  @override
  String unlinkConfirmationMessage(String provider) {
    return '¿Estás seguro de que quieres desvincular tu cuenta de $provider? Podrás volver a vincularla más tarde.';
  }

  @override
  String get myZones => 'Mis Zonas';

  @override
  String get addZone => 'Agregar Zona';

  @override
  String get defineZone => 'Definir Zona';

  @override
  String get zoneDetails => 'Detalles de Zona';

  @override
  String get zoneName => 'Nombre de la Zona';

  @override
  String get zoneNameHint => 'Ej: Mi Casa, Oficina, Universidad';

  @override
  String get zoneType => 'Tipo de Zona';

  @override
  String get zoneAddress => 'Dirección';

  @override
  String get searchAddress => 'Buscar dirección...';

  @override
  String get presenceHours => 'Horas de Presencia';

  @override
  String get presenceHoursHint => 'Ej: 8:00 AM - 6:00 PM';

  @override
  String get confirmZone => 'Confirmar Zona';

  @override
  String get zoneTypeHome => 'Hogar';

  @override
  String get zoneTypeWork => 'Trabajo';

  @override
  String get zoneTypeSchool => 'Escuela';

  @override
  String get zoneTypeUniversity => 'Universidad';

  @override
  String get zoneTypeOther => 'Otro';

  @override
  String get zoneStatusPending => 'Pendiente';

  @override
  String get zoneStatusValidated => 'Validada';

  @override
  String get zoneStatusRejected => 'Rechazada';

  @override
  String get socialValidation => 'Validación Social';

  @override
  String get automaticValidation => 'Validación Automática';

  @override
  String get requestSocialValidation => 'Solicitar Validación Social';

  @override
  String get enableAutomaticValidation => 'Habilitar Validación Automática';

  @override
  String get validationProgress => 'Progreso de Validación';

  @override
  String get shareValidationLink => 'Compartir Enlace';

  @override
  String get qrCodeInstructions =>
      'Comparte este código QR con vecinos validados para que puedan validar tu zona';

  @override
  String get automaticValidationExplanation =>
      'La validación automática monitorea tu presencia en la zona de forma consistente para validarla automáticamente';

  @override
  String get locationPermissionRequired => 'Permiso de Ubicación';

  @override
  String get enableLocationAccess => 'Habilitar Acceso a Ubicación';

  @override
  String get zoneValidated => 'Zona Validada';

  @override
  String get validationMethod => 'Método de Validación';

  @override
  String get noZonesYet => 'Aún no tienes zonas definidas';

  @override
  String get createFirstZone => 'Crea tu primera zona para comenzar';

  @override
  String maxZonesReached(int max) {
    return 'Has alcanzado el límite máximo de $max zonas';
  }

  @override
  String get zoneCreatedSuccessfully => 'Zona creada exitosamente';

  @override
  String get errorCreatingZone => 'Error al crear la zona';

  @override
  String get selectLocationOnMap => 'Selecciona la ubicación en el mapa';

  @override
  String zoneRadius(int radius) {
    return 'Radio de la zona: ${radius}m';
  }

  @override
  String get deleteZoneConfirmation =>
      '¿Estás seguro de que quieres eliminar esta zona?';

  @override
  String get zoneDeletedSuccessfully => 'Zona eliminada exitosamente';

  @override
  String get validationInProgress => 'Validación en Progreso';

  @override
  String get needsMoreValidations => 'Necesita más validaciones';

  @override
  String get socialValidationDescription =>
      'Solicita a vecinos validados que confirmen tu presencia en esta zona';

  @override
  String get automaticValidationDescription =>
      'Habilita la validación automática para verificar tu presencia en zonas de seguridad usando monitoreo de ubicación en segundo plano.';

  @override
  String get validationRequired => 'Validación requerida';

  @override
  String get chooseValidationMethod =>
      'Elige un método de validación para tu zona';

  @override
  String validationsNeeded(int count) {
    return 'Se necesitan $count validaciones más';
  }

  @override
  String get shareZoneValidation => 'Compartir Validación de Zona';

  @override
  String get copyLink => 'Copiar Enlace';

  @override
  String get linkCopied => 'Enlace copiado al portapapeles';

  @override
  String get enableAutomaticValidationTitle =>
      'Habilitar Validación Automática';

  @override
  String get automaticValidationEnabled => 'Validación automática habilitada';

  @override
  String get automaticValidationDisabled =>
      'Validación automática deshabilitada';

  @override
  String get locationAccessDenied => 'Acceso a ubicación denegado';

  @override
  String get openSettings => 'Abrir Configuración';

  @override
  String get automaticValidationTitle => 'Validación Automática';

  @override
  String get disableAutomaticValidation => 'Deshabilitar Validación Automática';

  @override
  String get permissionsRequired => 'Permisos Requeridos';

  @override
  String get locationPermissionDescription =>
      'Requerido para detectar cuando entras o sales de zonas de seguridad.';

  @override
  String get backgroundLocationPermissionRequired =>
      'Permiso de Ubicación en Segundo Plano';

  @override
  String get backgroundLocationPermissionDescription =>
      'Requerido para monitorear tu ubicación incluso cuando la aplicación está cerrada.';

  @override
  String get requestLocationPermission => 'Solicitar Permiso de Ubicación';

  @override
  String get requestBackgroundPermission =>
      'Solicitar Permiso de Segundo Plano';

  @override
  String get permissionGranted => 'Permiso Concedido';

  @override
  String get permissionDenied => 'Permiso Denegado';

  @override
  String get presenceHoursConfiguration =>
      'Configuración de Horas de Presencia';

  @override
  String get activeDays => 'Días Activos';

  @override
  String get activeTimeBlocks => 'Bloques de Tiempo Activos';

  @override
  String get timeBlocks => 'Bloques de Tiempo';

  @override
  String get timeInZone => 'Tiempo en Zona';

  @override
  String get zoneEntries => 'Entradas a Zona';

  @override
  String get currentStatus => 'Estado Actual';

  @override
  String get inZone => 'En Zona';

  @override
  String get outsideZone => 'Fuera de Zona';

  @override
  String get recentActivity => 'Actividad Reciente';

  @override
  String get enteredZone => 'Entró a Zona';

  @override
  String get exitedZone => 'Salió de Zona';

  @override
  String get noRecentActivity =>
      'Sin actividad reciente. Los eventos aparecerán aquí una vez que comience la validación automática.';

  @override
  String get privacyAndSecurity => 'Privacidad y Seguridad';

  @override
  String get privacyDescription =>
      'Tus datos de ubicación se usan únicamente para validación de zonas y no se comparten con terceros. El seguimiento de ubicación está activo solo cuando la validación automática está habilitada.';

  @override
  String get validationCriteria => 'Criterios de Validación';

  @override
  String get minimumTimeRequired => 'Tiempo Mínimo Requerido';

  @override
  String get minimumVisitsRequired => 'Visitas Mínimas Requeridas';

  @override
  String get validationPeriod => 'Período de Validación';

  @override
  String get hoursAbbreviation => 'h';

  @override
  String get minutesAbbreviation => 'm';

  @override
  String get validationComplete => 'Validación Completa';

  @override
  String get validationFailed => 'Validación Fallida';

  @override
  String get locationServiceDisabled =>
      'El servicio de ubicación está deshabilitado. Por favor habilítalo en la configuración de tu dispositivo.';

  @override
  String get backgroundLocationDenied =>
      'El acceso a ubicación en segundo plano es requerido para la validación automática. Por favor habilítalo en la configuración de tu dispositivo.';

  @override
  String get batteryOptimizationWarning =>
      'La optimización de batería puede afectar el monitoreo de ubicación en segundo plano. Considera deshabilitarla para esta aplicación.';

  @override
  String get automaticValidationStarted =>
      'Validación automática iniciada para esta zona';

  @override
  String get automaticValidationStopped =>
      'Validación automática detenida para esta zona';

  @override
  String get zoneEntryDetected => 'Entrada a zona detectada';

  @override
  String get zoneExitDetected => 'Salida de zona detectada';

  @override
  String get validationProgressUpdate => 'Progreso de validación actualizado';

  @override
  String get resetPasswordTitle => 'Restablece Tu Contraseña';

  @override
  String get resetPasswordEmailSent =>
      '¡Correo enviado! Hemos enviado un email a tu cuenta con un enlace para que puedas establecer una nueva contraseña.';

  @override
  String get resetPasswordInstructions =>
      'Por favor, revisa tu bandeja de entrada y, si no lo ves, no olvides mirar en tu carpeta de correo no deseado o spam.';

  @override
  String get resetPasswordSignature =>
      'Gracias por tu paciencia,\nEl equipo de ResPública Seguridad';

  @override
  String get resendEmail => 'Reenviar correo';

  @override
  String get close => 'Cerrar';

  @override
  String emailSentTo(String email) {
    return 'Correo enviado a $email';
  }

  @override
  String get securityPageSubtitle => 'Tu seguridad es nuestra prioridad';

  @override
  String get emergencyAlert => 'Alerta de Emergencia';

  @override
  String get emergencyAlertDescription =>
      'Mantén presionado el botón por 3 segundos para enviar una alerta de emergencia a las autoridades y tus contactos de emergencia.';

  @override
  String get holdToActivate =>
      'Mantén presionado para activar alerta de emergencia...';

  @override
  String get emergencyAlertActivated => 'Alerta de Emergencia Activada';

  @override
  String get emergencyAlertConfirmation =>
      'Los servicios de emergencia han sido notificados. La ayuda está en camino. Mantén la calma y sigue los protocolos de seguridad.';

  @override
  String get cancelAlert => 'Cancelar Alerta';

  @override
  String get confirmed => 'Confirmado';

  @override
  String get emergencyAlertActive =>
      'Alerta de emergencia activa. La ayuda está en camino.';

  @override
  String get securityFeatures => 'Funciones de Seguridad';

  @override
  String get locationTracking => 'Seguimiento de Ubicación';

  @override
  String get locationTrackingDescription =>
      'Monitoreo de ubicación en tiempo real para mayor seguridad';

  @override
  String get emergencyContacts => 'Contactos de Emergencia';

  @override
  String get emergencyContactsDescription =>
      'Administra tu lista de contactos de emergencia';

  @override
  String get safeZones => 'Zonas Seguras';

  @override
  String get safeZonesDescription => 'Configura y monitorea tus zonas seguras';

  @override
  String get incidentReports => 'Reportes de Incidentes';

  @override
  String get incidentReportsDescription =>
      'Ver y reportar incidentes de seguridad';

  @override
  String get active => 'Activo';

  @override
  String get safetyTips => 'Consejos de Seguridad';

  @override
  String get stayAlert => 'Mantente Alerta';

  @override
  String get stayAlertDescription =>
      'Siempre mantente consciente de tu entorno y confía en tus instintos.';

  @override
  String get keepContactsUpdated => 'Mantén Contactos Actualizados';

  @override
  String get keepContactsUpdatedDescription =>
      'Asegúrate de que tus contactos de emergencia estén actualizados y accesibles.';

  @override
  String get shareYourLocation => 'Comparte tu Ubicación';

  @override
  String get shareYourLocationDescription =>
      'Permite que contactos de confianza sepan tu paradero cuando viajes.';

  @override
  String get keepDeviceCharged => 'Mantén el Dispositivo Cargado';

  @override
  String get keepDeviceChargedDescription =>
      'Mantén la batería de tu teléfono para situaciones de emergencia.';

  @override
  String get emergencyNumbers => 'Números de Emergencia';

  @override
  String get emergencyNumbersList =>
      'Policía: 911 • Bomberos: 911 • Médico: 911';
}
