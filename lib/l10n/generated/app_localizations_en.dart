// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Republic Security';

  @override
  String get home => 'Home';

  @override
  String get alerts => 'Alerts';

  @override
  String get security => 'Security';

  @override
  String get more => 'More';

  @override
  String get settings => 'Settings';

  @override
  String get appearance => 'Appearance';

  @override
  String get lightTheme => 'Light Theme';

  @override
  String get lightThemeDescription => 'Interface with light background';

  @override
  String get darkTheme => 'Dark Theme';

  @override
  String get darkThemeDescription => 'Interface with dark background';

  @override
  String get language => 'Language';

  @override
  String get spanish => 'Español';

  @override
  String get english => 'English';

  @override
  String get debug => '🐛 Debug';

  @override
  String get crashReports => 'Crash Reports';

  @override
  String get crashReportsDescription => 'View app crash reports and errors';

  @override
  String get generateTestReport => 'Generate Test Report';

  @override
  String get generateTestReportDescription =>
      'Create a test error report for debugging';

  @override
  String get testReportGenerated =>
      'Test report generated! Check crash reports to view it.';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get fullName => 'Full Name';

  @override
  String get login => 'Login';

  @override
  String get signup => 'Sign Up';

  @override
  String get forgotPassword => 'Forgot your password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get sendLink => 'Send Link';

  @override
  String get backToLogin => 'Back to Login';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Enter a valid email address';

  @override
  String get pleaseEnterName => 'Please enter your name';

  @override
  String get searchForLocations => 'Search for locations...';

  @override
  String locationNotFound(String location) {
    return 'Location not found: $location';
  }

  @override
  String get noCrashReports => 'No Crash Reports';

  @override
  String get appRunningSmooth =>
      'Your app is running smoothly!\nTap the bug icon to generate a test crash.';

  @override
  String crashReportsFound(int count, String plural) {
    return '$count crash report$plural found';
  }

  @override
  String get debugReport => 'Debug Report';

  @override
  String get widgetError => 'Widget Error';

  @override
  String get systemError => 'System Error';

  @override
  String get loading => 'Loading...';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get confirm => 'Confirm';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get enterCredentials => 'Enter your credentials to continue';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get welcome => 'Welcome';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get google => 'Google';

  @override
  String get facebook => 'Facebook';

  @override
  String get signUpPrompt => 'Sign Up';

  @override
  String get mustAcceptTerms => 'You must accept the terms and conditions';

  @override
  String get signUpToStart => 'Sign up to start using the platform';

  @override
  String get orSignUpWithEmail => 'Or sign up with email';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get passwordStrength => 'Password strength: ';

  @override
  String get veryWeak => 'Very Weak';

  @override
  String get weak => 'Weak';

  @override
  String get moderate => 'Moderate';

  @override
  String get strong => 'Strong';

  @override
  String get veryStrong => 'Very Strong';

  @override
  String get acceptTerms => 'I accept the ';

  @override
  String get termsAndConditions => 'terms and conditions';

  @override
  String get andThe => ' and the ';

  @override
  String get privacyPolicy => 'privacy policy';

  @override
  String get done => 'Done';

  @override
  String get homeLocation => 'Home Location';

  @override
  String get yourCurrentArea => 'Your current area';

  @override
  String securityPoint(String number) {
    return 'Security Point $number';
  }

  @override
  String get activeSecurityMonitoring => 'Active security monitoring';

  @override
  String get emergencyResponseAvailable => 'Emergency response available';

  @override
  String get searchResult => 'Search result';

  @override
  String found(String location) {
    return 'Found: $location';
  }

  @override
  String get identityVerification => 'Identity Verification';

  @override
  String get identityVerificationMessage =>
      'Welcome to Republic Security! To ensure your safety and access to all protection features, please verify your identity. This helps us protect your account and provide personalized security services in your area.';

  @override
  String get skipForNow => 'Skip for Now';

  @override
  String get verifyIdentity => 'Verify Identity';

  @override
  String get moreOptions => 'More Options';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get manageAccountPreferences => 'Manage your account preferences';

  @override
  String get notifications => 'Notifications';

  @override
  String get controlNotificationSettings =>
      'Control your notification settings';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get light => 'Light';

  @override
  String get dark => 'Dark';

  @override
  String get privacy => 'Privacy';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get idFrontSide => 'ID Front Side';

  @override
  String get idBackSide => 'ID Back Side';

  @override
  String get profilePhoto => 'Profile Photo';

  @override
  String get review => 'Review';

  @override
  String get captureIdFront => 'Capture ID Front';

  @override
  String get captureIdBack => 'Capture ID Back';

  @override
  String get captureProfilePhoto => 'Capture Profile Photo';

  @override
  String get retake => 'Retake';

  @override
  String get next => 'Next';

  @override
  String get back => 'Back';

  @override
  String get submit => 'Submit';

  @override
  String get identityVerificationComplete => 'Identity verification complete';

  @override
  String get submittingDocuments => 'Submitting your documents...';

  @override
  String get idFrontInstructions => 'Position your ID card within the frame';

  @override
  String get idBackInstructions => 'Now capture the back side of your ID card';

  @override
  String get profilePhotoInstructions =>
      'Take a selfie by positioning your face within the oval frame';

  @override
  String get reviewInstructions =>
      'Please review your documents before submitting';

  @override
  String get selectFromGallery => 'Select from gallery';

  @override
  String get edit => 'Edit';

  @override
  String get identityVerificationTitle => 'Identity Verification';

  @override
  String get identityVerificationSubtitle =>
      'Verify your identity for enhanced security';

  @override
  String get linkedAccounts => 'Linked Accounts';

  @override
  String get linkAdditionalAccounts => 'Link Additional Accounts';

  @override
  String get addMoreLoginOptions => 'Add more login options';

  @override
  String get noLinkedAccounts =>
      'You don\'t have any additional linked accounts';

  @override
  String get linkGoogle => 'Link Google';

  @override
  String get linkFacebook => 'Link Facebook';

  @override
  String get googleAccountLinked => 'Google account linked';

  @override
  String get facebookAccountLinked => 'Facebook account linked';

  @override
  String get emailPasswordMethod => 'Email & Password';

  @override
  String get primaryLoginMethod => 'Primary login method';

  @override
  String get quickLoginWithGoogle => 'Sign in quickly with your Google account';

  @override
  String get quickLoginWithFacebook =>
      'Sign in quickly with your Facebook account';

  @override
  String get unlink => 'Unlink';

  @override
  String get primary => 'Primary';

  @override
  String get verified => 'Verified';

  @override
  String get inProcess => 'In Process';

  @override
  String get underReview => 'Under Review';

  @override
  String get rejected => 'Rejected';

  @override
  String get unverified => 'Unverified';

  @override
  String unlinkConfirmationMessage(String provider) {
    return 'Are you sure you want to unlink your $provider account? You can link it again later.';
  }

  @override
  String get myZones => 'My Zones';

  @override
  String get addZone => 'Add Zone';

  @override
  String get defineZone => 'Define Zone';

  @override
  String get zoneDetails => 'Zone Details';

  @override
  String get zoneName => 'Zone Name';

  @override
  String get zoneNameHint => 'e.g., My Home, Office, University';

  @override
  String get zoneType => 'Zone Type';

  @override
  String get zoneAddress => 'Address';

  @override
  String get searchAddress => 'Search address...';

  @override
  String get presenceHours => 'Presence Hours';

  @override
  String get presenceHoursHint => 'e.g., 8:00 AM - 6:00 PM';

  @override
  String get confirmZone => 'Confirm Zone';

  @override
  String get zoneTypeHome => 'Home';

  @override
  String get zoneTypeWork => 'Work';

  @override
  String get zoneTypeSchool => 'School';

  @override
  String get zoneTypeUniversity => 'University';

  @override
  String get zoneTypeOther => 'Other';

  @override
  String get zoneStatusPending => 'Pending';

  @override
  String get zoneStatusValidated => 'Validated';

  @override
  String get zoneStatusRejected => 'Rejected';

  @override
  String get socialValidation => 'Social Validation';

  @override
  String get automaticValidation => 'Automatic Validation';

  @override
  String get requestSocialValidation => 'Request Social Validation';

  @override
  String get enableAutomaticValidation => 'Enable Automatic Validation';

  @override
  String get validationProgress => 'Validation Progress';

  @override
  String get shareValidationLink => 'Share Link';

  @override
  String get qrCodeInstructions =>
      'Share this QR code with validated neighbors so they can validate your zone';

  @override
  String get automaticValidationExplanation =>
      'Automatic validation monitors your consistent presence in the zone to validate it automatically';

  @override
  String get locationPermissionRequired => 'Location Permission';

  @override
  String get enableLocationAccess => 'Enable Location Access';

  @override
  String get zoneValidated => 'Zone Validated';

  @override
  String get validationMethod => 'Validation Method';

  @override
  String get noZonesYet => 'You don\'t have any zones defined yet';

  @override
  String get createFirstZone => 'Create your first zone to get started';

  @override
  String maxZonesReached(int max) {
    return 'You have reached the maximum limit of $max zones';
  }

  @override
  String get zoneCreatedSuccessfully => 'Zone created successfully';

  @override
  String get errorCreatingZone => 'Error creating zone';

  @override
  String get selectLocationOnMap => 'Select location on the map';

  @override
  String zoneRadius(int radius) {
    return 'Zone radius: ${radius}m';
  }

  @override
  String get deleteZoneConfirmation =>
      'Are you sure you want to delete this zone?';

  @override
  String get zoneDeletedSuccessfully => 'Zone deleted successfully';

  @override
  String get validationInProgress => 'Validation in Progress';

  @override
  String get needsMoreValidations => 'Needs more validations';

  @override
  String get socialValidationDescription =>
      'Ask validated neighbors to confirm your presence in this zone';

  @override
  String get automaticValidationDescription =>
      'Enable automatic validation to verify your presence in security zones using background location monitoring.';

  @override
  String get validationRequired => 'Validation required';

  @override
  String get chooseValidationMethod =>
      'Choose a validation method for your zone';

  @override
  String validationsNeeded(int count) {
    return '$count more validations needed';
  }

  @override
  String get shareZoneValidation => 'Share Zone Validation';

  @override
  String get copyLink => 'Copy Link';

  @override
  String get linkCopied => 'Link copied to clipboard';

  @override
  String get enableAutomaticValidationTitle => 'Enable Automatic Validation';

  @override
  String get automaticValidationEnabled => 'Automatic validation enabled';

  @override
  String get automaticValidationDisabled => 'Automatic validation disabled';

  @override
  String get locationAccessDenied => 'Location access denied';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get automaticValidationTitle => 'Automatic Validation';

  @override
  String get disableAutomaticValidation => 'Disable Automatic Validation';

  @override
  String get permissionsRequired => 'Permissions Required';

  @override
  String get locationPermissionDescription =>
      'Required to detect when you enter or exit security zones.';

  @override
  String get backgroundLocationPermissionRequired =>
      'Background Location Permission';

  @override
  String get backgroundLocationPermissionDescription =>
      'Required to monitor your location even when the app is closed.';

  @override
  String get requestLocationPermission => 'Request Location Permission';

  @override
  String get requestBackgroundPermission => 'Request Background Permission';

  @override
  String get permissionGranted => 'Permission Granted';

  @override
  String get permissionDenied => 'Permission Denied';

  @override
  String get presenceHoursConfiguration => 'Presence Hours Configuration';

  @override
  String get activeDays => 'Active Days';

  @override
  String get activeTimeBlocks => 'Active Time Blocks';

  @override
  String get timeBlocks => 'Time Blocks';

  @override
  String get timeInZone => 'Time in Zone';

  @override
  String get zoneEntries => 'Zone Entries';

  @override
  String get currentStatus => 'Current Status';

  @override
  String get inZone => 'In Zone';

  @override
  String get outsideZone => 'Outside Zone';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get enteredZone => 'Entered Zone';

  @override
  String get exitedZone => 'Exited Zone';

  @override
  String get noRecentActivity =>
      'No recent activity. Events will appear here once automatic validation starts.';

  @override
  String get privacyAndSecurity => 'Privacy & Security';

  @override
  String get privacyDescription =>
      'Your location data is used only for zone validation and is not shared with third parties. Location tracking is active only when automatic validation is enabled.';

  @override
  String get validationCriteria => 'Validation Criteria';

  @override
  String get minimumTimeRequired => 'Minimum Time Required';

  @override
  String get minimumVisitsRequired => 'Minimum Visits Required';

  @override
  String get validationPeriod => 'Validation Period';

  @override
  String get hoursAbbreviation => 'h';

  @override
  String get minutesAbbreviation => 'm';

  @override
  String get validationComplete => 'Validation Complete';

  @override
  String get validationFailed => 'Validation Failed';

  @override
  String get locationServiceDisabled =>
      'Location service is disabled. Please enable it in your device settings.';

  @override
  String get backgroundLocationDenied =>
      'Background location access is required for automatic validation. Please enable it in your device settings.';

  @override
  String get batteryOptimizationWarning =>
      'Battery optimization may affect background location monitoring. Consider disabling it for this app.';

  @override
  String get automaticValidationStarted =>
      'Automatic validation started for this zone';

  @override
  String get automaticValidationStopped =>
      'Automatic validation stopped for this zone';

  @override
  String get zoneEntryDetected => 'Zone entry detected';

  @override
  String get zoneExitDetected => 'Zone exit detected';

  @override
  String get validationProgressUpdate => 'Validation progress updated';

  @override
  String get resetPasswordTitle => 'Reset Your Password';

  @override
  String get resetPasswordEmailSent =>
      'Email sent! We have sent an email to your account with a link so you can set a new password.';

  @override
  String get resetPasswordInstructions =>
      'Please check your inbox and, if you don\'t see it, don\'t forget to check your spam or junk mail folder.';

  @override
  String get resetPasswordSignature =>
      'Thank you for your patience,\nThe ResPública Security team';

  @override
  String get resendEmail => 'Resend email';

  @override
  String get close => 'Close';

  @override
  String emailSentTo(String email) {
    return 'Email sent to $email';
  }

  @override
  String get securityPageSubtitle => 'Your safety is our priority';

  @override
  String get emergencyAlert => 'Emergency Alert';

  @override
  String get emergencyAlertDescription =>
      'Hold the button below for 3 seconds to send an emergency alert to authorities and your emergency contacts.';

  @override
  String get holdToActivate => 'Hold to activate emergency alert...';

  @override
  String get emergencyAlertActivated => 'Emergency Alert Activated';

  @override
  String get emergencyAlertConfirmation =>
      'Emergency services have been notified. Help is on the way. Stay calm and follow safety protocols.';

  @override
  String get cancelAlert => 'Cancel Alert';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get emergencyAlertActive =>
      'Emergency alert active. Help is on the way.';

  @override
  String get securityFeatures => 'Security Features';

  @override
  String get locationTracking => 'Location Tracking';

  @override
  String get locationTrackingDescription =>
      'Real-time location monitoring for enhanced safety';

  @override
  String get emergencyContacts => 'Emergency Contacts';

  @override
  String get emergencyContactsDescription =>
      'Manage your emergency contact list';

  @override
  String get safeZones => 'Safe Zones';

  @override
  String get safeZonesDescription => 'Set up and monitor your safe zones';

  @override
  String get incidentReports => 'Incident Reports';

  @override
  String get incidentReportsDescription => 'View and report security incidents';

  @override
  String get active => 'Active';

  @override
  String get safetyTips => 'Safety Tips';

  @override
  String get stayAlert => 'Stay Alert';

  @override
  String get stayAlertDescription =>
      'Always be aware of your surroundings and trust your instincts.';

  @override
  String get keepContactsUpdated => 'Keep Contacts Updated';

  @override
  String get keepContactsUpdatedDescription =>
      'Ensure your emergency contacts are current and accessible.';

  @override
  String get shareYourLocation => 'Share Your Location';

  @override
  String get shareYourLocationDescription =>
      'Let trusted contacts know your whereabouts when traveling.';

  @override
  String get keepDeviceCharged => 'Keep Device Charged';

  @override
  String get keepDeviceChargedDescription =>
      'Maintain your phone battery for emergency situations.';

  @override
  String get emergencyNumbers => 'Emergency Numbers';

  @override
  String get emergencyNumbersList => 'Police: 911 • Fire: 911 • Medical: 911';
}
