import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
  ];

  /// The title of the application
  ///
  /// In es, this message translates to:
  /// **'República Seguridad'**
  String get appTitle;

  /// Home navigation label
  ///
  /// In es, this message translates to:
  /// **'Inicio'**
  String get home;

  /// Alerts navigation label
  ///
  /// In es, this message translates to:
  /// **'Alertas'**
  String get alerts;

  /// Security navigation label
  ///
  /// In es, this message translates to:
  /// **'Seguridad'**
  String get security;

  /// More navigation label
  ///
  /// In es, this message translates to:
  /// **'Más'**
  String get more;

  /// Settings screen title
  ///
  /// In es, this message translates to:
  /// **'Configuración'**
  String get settings;

  /// Appearance section title in settings
  ///
  /// In es, this message translates to:
  /// **'Apariencia'**
  String get appearance;

  /// Light theme option
  ///
  /// In es, this message translates to:
  /// **'Tema claro'**
  String get lightTheme;

  /// Light theme description
  ///
  /// In es, this message translates to:
  /// **'Interfaz con fondo claro'**
  String get lightThemeDescription;

  /// Dark theme option
  ///
  /// In es, this message translates to:
  /// **'Tema oscuro'**
  String get darkTheme;

  /// Dark theme description
  ///
  /// In es, this message translates to:
  /// **'Interfaz con fondo oscuro'**
  String get darkThemeDescription;

  /// Language section title
  ///
  /// In es, this message translates to:
  /// **'Idioma'**
  String get language;

  /// Spanish language option
  ///
  /// In es, this message translates to:
  /// **'Español'**
  String get spanish;

  /// English language option
  ///
  /// In es, this message translates to:
  /// **'English'**
  String get english;

  /// Debug section title
  ///
  /// In es, this message translates to:
  /// **'🐛 Debug'**
  String get debug;

  /// Crash reports option
  ///
  /// In es, this message translates to:
  /// **'Reportes de Errores'**
  String get crashReports;

  /// Crash reports description
  ///
  /// In es, this message translates to:
  /// **'Ver reportes de errores y fallos de la aplicación'**
  String get crashReportsDescription;

  /// Generate test report option
  ///
  /// In es, this message translates to:
  /// **'Generar Reporte de Prueba'**
  String get generateTestReport;

  /// Generate test report description
  ///
  /// In es, this message translates to:
  /// **'Crear un reporte de error de prueba para depuración'**
  String get generateTestReportDescription;

  /// Test report generated success message
  ///
  /// In es, this message translates to:
  /// **'¡Reporte de prueba generado! Revisa los reportes de errores para verlo.'**
  String get testReportGenerated;

  /// Email field label
  ///
  /// In es, this message translates to:
  /// **'Correo electrónico'**
  String get email;

  /// Password field label
  ///
  /// In es, this message translates to:
  /// **'Contraseña'**
  String get password;

  /// Full name field label
  ///
  /// In es, this message translates to:
  /// **'Nombre completo'**
  String get fullName;

  /// Login button text
  ///
  /// In es, this message translates to:
  /// **'Iniciar Sesión'**
  String get login;

  /// Signup button text
  ///
  /// In es, this message translates to:
  /// **'Registrarse'**
  String get signup;

  /// Forgot password link text
  ///
  /// In es, this message translates to:
  /// **'¿Olvidaste tu contraseña?'**
  String get forgotPassword;

  /// Reset password screen title
  ///
  /// In es, this message translates to:
  /// **'Restablecer Contraseña'**
  String get resetPassword;

  /// Send reset link button text
  ///
  /// In es, this message translates to:
  /// **'Enviar Enlace'**
  String get sendLink;

  /// Back to login button text
  ///
  /// In es, this message translates to:
  /// **'Volver a Iniciar Sesión'**
  String get backToLogin;

  /// Email validation message
  ///
  /// In es, this message translates to:
  /// **'Por favor ingresa tu correo'**
  String get pleaseEnterEmail;

  /// Valid email validation message
  ///
  /// In es, this message translates to:
  /// **'Ingresa un correo electrónico válido'**
  String get pleaseEnterValidEmail;

  /// Name validation message
  ///
  /// In es, this message translates to:
  /// **'Por favor ingresa tu nombre'**
  String get pleaseEnterName;

  /// Search field hint text
  ///
  /// In es, this message translates to:
  /// **'Buscar ubicaciones...'**
  String get searchForLocations;

  /// Location not found error message
  ///
  /// In es, this message translates to:
  /// **'Ubicación no encontrada: {location}'**
  String locationNotFound(String location);

  /// No crash reports title
  ///
  /// In es, this message translates to:
  /// **'Sin Reportes de Errores'**
  String get noCrashReports;

  /// App running smoothly message
  ///
  /// In es, this message translates to:
  /// **'¡Tu aplicación funciona sin problemas!\nToca el ícono de error para generar un error de prueba.'**
  String get appRunningSmooth;

  /// Crash reports found message
  ///
  /// In es, this message translates to:
  /// **'{count} reporte{plural} de error{plural} encontrado{plural}'**
  String crashReportsFound(int count, String plural);

  /// Debug report title
  ///
  /// In es, this message translates to:
  /// **'Reporte de Depuración'**
  String get debugReport;

  /// Widget error title
  ///
  /// In es, this message translates to:
  /// **'Error de Widget'**
  String get widgetError;

  /// System error title
  ///
  /// In es, this message translates to:
  /// **'Error del Sistema'**
  String get systemError;

  /// Loading message
  ///
  /// In es, this message translates to:
  /// **'Cargando...'**
  String get loading;

  /// Cancel button text
  ///
  /// In es, this message translates to:
  /// **'Cancelar'**
  String get cancel;

  /// OK button text
  ///
  /// In es, this message translates to:
  /// **'Aceptar'**
  String get ok;

  /// Confirm button text
  ///
  /// In es, this message translates to:
  /// **'Confirmar'**
  String get confirm;

  /// Error title
  ///
  /// In es, this message translates to:
  /// **'Error'**
  String get error;

  /// Success title
  ///
  /// In es, this message translates to:
  /// **'Éxito'**
  String get success;

  /// Warning title
  ///
  /// In es, this message translates to:
  /// **'Advertencia'**
  String get warning;

  /// Info title
  ///
  /// In es, this message translates to:
  /// **'Información'**
  String get info;

  /// Enter credentials message
  ///
  /// In es, this message translates to:
  /// **'Ingresa tus credenciales para continuar'**
  String get enterCredentials;

  /// Don't have account message
  ///
  /// In es, this message translates to:
  /// **'¿No tienes una cuenta?'**
  String get dontHaveAccount;

  /// Already have account message
  ///
  /// In es, this message translates to:
  /// **'¿Ya tienes una cuenta?'**
  String get alreadyHaveAccount;

  /// Create account button text
  ///
  /// In es, this message translates to:
  /// **'Crear Cuenta'**
  String get createAccount;

  /// Welcome message
  ///
  /// In es, this message translates to:
  /// **'Bienvenido'**
  String get welcome;

  /// Or continue with message for social login
  ///
  /// In es, this message translates to:
  /// **'O continúa con'**
  String get orContinueWith;

  /// Google social login button
  ///
  /// In es, this message translates to:
  /// **'Google'**
  String get google;

  /// Facebook social login button
  ///
  /// In es, this message translates to:
  /// **'Facebook'**
  String get facebook;

  /// Sign up prompt button text
  ///
  /// In es, this message translates to:
  /// **'Regístrate'**
  String get signUpPrompt;

  /// Must accept terms validation message
  ///
  /// In es, this message translates to:
  /// **'Debes aceptar los términos y condiciones'**
  String get mustAcceptTerms;

  /// Sign up to start using platform message
  ///
  /// In es, this message translates to:
  /// **'Regístrate para comenzar a utilizar la plataforma'**
  String get signUpToStart;

  /// Or sign up with email message
  ///
  /// In es, this message translates to:
  /// **'O regístrate con email'**
  String get orSignUpWithEmail;

  /// Confirm password field label
  ///
  /// In es, this message translates to:
  /// **'Confirmar contraseña'**
  String get confirmPassword;

  /// Password strength label
  ///
  /// In es, this message translates to:
  /// **'Fortaleza de la contraseña: '**
  String get passwordStrength;

  /// Very weak password strength
  ///
  /// In es, this message translates to:
  /// **'Muy débil'**
  String get veryWeak;

  /// Weak password strength
  ///
  /// In es, this message translates to:
  /// **'Débil'**
  String get weak;

  /// Moderate password strength
  ///
  /// In es, this message translates to:
  /// **'Moderada'**
  String get moderate;

  /// Strong password strength
  ///
  /// In es, this message translates to:
  /// **'Fuerte'**
  String get strong;

  /// Very strong password strength
  ///
  /// In es, this message translates to:
  /// **'Muy fuerte'**
  String get veryStrong;

  /// Accept terms prefix
  ///
  /// In es, this message translates to:
  /// **'Acepto los '**
  String get acceptTerms;

  /// Terms and conditions link text
  ///
  /// In es, this message translates to:
  /// **'términos y condiciones'**
  String get termsAndConditions;

  /// And the connector
  ///
  /// In es, this message translates to:
  /// **' y la '**
  String get andThe;

  /// Privacy policy link text
  ///
  /// In es, this message translates to:
  /// **'política de privacidad'**
  String get privacyPolicy;

  /// Done button text
  ///
  /// In es, this message translates to:
  /// **'Listo'**
  String get done;

  /// Home location marker title
  ///
  /// In es, this message translates to:
  /// **'Ubicación de Inicio'**
  String get homeLocation;

  /// Home location marker snippet
  ///
  /// In es, this message translates to:
  /// **'Tu área actual'**
  String get yourCurrentArea;

  /// Security point marker title
  ///
  /// In es, this message translates to:
  /// **'Punto de Seguridad {number}'**
  String securityPoint(String number);

  /// Security monitoring snippet
  ///
  /// In es, this message translates to:
  /// **'Monitoreo de seguridad activo'**
  String get activeSecurityMonitoring;

  /// Emergency response snippet
  ///
  /// In es, this message translates to:
  /// **'Respuesta de emergencia disponible'**
  String get emergencyResponseAvailable;

  /// Search result marker snippet
  ///
  /// In es, this message translates to:
  /// **'Resultado de búsqueda'**
  String get searchResult;

  /// Location found message
  ///
  /// In es, this message translates to:
  /// **'Encontrado: {location}'**
  String found(String location);

  /// Identity verification dialog title
  ///
  /// In es, this message translates to:
  /// **'🔐 Verificación de Identidad'**
  String get identityVerification;

  /// Identity verification dialog message
  ///
  /// In es, this message translates to:
  /// **'¡Bienvenido a República Seguridad! Para garantizar tu seguridad y acceso a todas las funciones de protección, por favor verifica tu identidad. Esto nos ayuda a proteger tu cuenta y brindar servicios de seguridad personalizados en tu área.'**
  String get identityVerificationMessage;

  /// Skip for now button text
  ///
  /// In es, this message translates to:
  /// **'Omitir por Ahora'**
  String get skipForNow;

  /// Verify identity button text
  ///
  /// In es, this message translates to:
  /// **'Verificar Identidad'**
  String get verifyIdentity;

  /// More options screen title
  ///
  /// In es, this message translates to:
  /// **'Más Opciones'**
  String get moreOptions;

  /// Logout button text
  ///
  /// In es, this message translates to:
  /// **'Cerrar sesión'**
  String get logout;

  /// Logout confirmation message
  ///
  /// In es, this message translates to:
  /// **'¿Estás seguro que deseas cerrar sesión?'**
  String get logoutConfirmation;

  /// Account settings option
  ///
  /// In es, this message translates to:
  /// **'Configuración de Cuenta'**
  String get accountSettings;

  /// Account settings description
  ///
  /// In es, this message translates to:
  /// **'Administra las preferencias de tu cuenta'**
  String get manageAccountPreferences;

  /// Notifications option
  ///
  /// In es, this message translates to:
  /// **'Notificaciones'**
  String get notifications;

  /// Notifications description
  ///
  /// In es, this message translates to:
  /// **'Controla la configuración de notificaciones'**
  String get controlNotificationSettings;

  /// Theme mode option
  ///
  /// In es, this message translates to:
  /// **'Modo de Tema'**
  String get themeMode;

  /// Light theme
  ///
  /// In es, this message translates to:
  /// **'Claro'**
  String get light;

  /// Dark theme
  ///
  /// In es, this message translates to:
  /// **'Oscuro'**
  String get dark;

  /// Privacy option
  ///
  /// In es, this message translates to:
  /// **'Privacidad'**
  String get privacy;

  /// Help and support option
  ///
  /// In es, this message translates to:
  /// **'Ayuda y Soporte'**
  String get helpSupport;

  /// Front side of the ID card
  ///
  /// In es, this message translates to:
  /// **'Frente del DNI'**
  String get idFrontSide;

  /// Back side of the ID card
  ///
  /// In es, this message translates to:
  /// **'Reverso del DNI'**
  String get idBackSide;

  /// Profile photo
  ///
  /// In es, this message translates to:
  /// **'Foto de Perfil'**
  String get profilePhoto;

  /// Review
  ///
  /// In es, this message translates to:
  /// **'Revisión'**
  String get review;

  /// Capture front side of the ID card
  ///
  /// In es, this message translates to:
  /// **'Capturar Frente del DNI'**
  String get captureIdFront;

  /// Capture back side of the ID card
  ///
  /// In es, this message translates to:
  /// **'Capturar Reverso del DNI'**
  String get captureIdBack;

  /// Capture profile photo
  ///
  /// In es, this message translates to:
  /// **'Capturar Foto de Perfil'**
  String get captureProfilePhoto;

  /// Retake
  ///
  /// In es, this message translates to:
  /// **'Volver a tomar'**
  String get retake;

  /// Next
  ///
  /// In es, this message translates to:
  /// **'Siguiente'**
  String get next;

  /// Back
  ///
  /// In es, this message translates to:
  /// **'Atrás'**
  String get back;

  /// Submit
  ///
  /// In es, this message translates to:
  /// **'Enviar'**
  String get submit;

  /// Identity verification completed
  ///
  /// In es, this message translates to:
  /// **'Verificación de identidad completada'**
  String get identityVerificationComplete;

  /// Submitting documents
  ///
  /// In es, this message translates to:
  /// **'Enviando tus documentos...'**
  String get submittingDocuments;

  /// Place your ID card inside the frame
  ///
  /// In es, this message translates to:
  /// **'Coloca tu DNI dentro del marco'**
  String get idFrontInstructions;

  /// Now capture the back of your ID card
  ///
  /// In es, this message translates to:
  /// **'Ahora captura el reverso de tu DNI'**
  String get idBackInstructions;

  /// Place your face inside the circle
  ///
  /// In es, this message translates to:
  /// **'Toma una selfie posicionando tu rostro dentro del marco ovalado'**
  String get profilePhotoInstructions;

  /// No description provided for @reviewInstructions.
  ///
  /// In es, this message translates to:
  /// **'Por favor revisa tus documentos antes de enviar'**
  String get reviewInstructions;

  /// No description provided for @selectFromGallery.
  ///
  /// In es, this message translates to:
  /// **'Seleccionar de la galería'**
  String get selectFromGallery;

  /// No description provided for @edit.
  ///
  /// In es, this message translates to:
  /// **'Editar'**
  String get edit;

  /// Identity verification title
  ///
  /// In es, this message translates to:
  /// **'Verificación de Identidad'**
  String get identityVerificationTitle;

  /// Identity verification subtitle
  ///
  /// In es, this message translates to:
  /// **'Verifica tu identidad para mayor seguridad'**
  String get identityVerificationSubtitle;

  /// Linked accounts section title
  ///
  /// In es, this message translates to:
  /// **'Cuentas Vinculadas'**
  String get linkedAccounts;

  /// Link additional accounts section title
  ///
  /// In es, this message translates to:
  /// **'Vincular Cuentas Adicionales'**
  String get linkAdditionalAccounts;

  /// Add more login options subtitle
  ///
  /// In es, this message translates to:
  /// **'Agrega más opciones de inicio de sesión'**
  String get addMoreLoginOptions;

  /// No linked accounts message
  ///
  /// In es, this message translates to:
  /// **'No tienes cuentas adicionales vinculadas'**
  String get noLinkedAccounts;

  /// Link Google account
  ///
  /// In es, this message translates to:
  /// **'Vincular Google'**
  String get linkGoogle;

  /// Link Facebook account
  ///
  /// In es, this message translates to:
  /// **'Vincular Facebook'**
  String get linkFacebook;

  /// Google account linked description
  ///
  /// In es, this message translates to:
  /// **'Cuenta de Google vinculada'**
  String get googleAccountLinked;

  /// Facebook account linked description
  ///
  /// In es, this message translates to:
  /// **'Cuenta de Facebook vinculada'**
  String get facebookAccountLinked;

  /// Email and password method
  ///
  /// In es, this message translates to:
  /// **'Correo y Contraseña'**
  String get emailPasswordMethod;

  /// Primary login method description
  ///
  /// In es, this message translates to:
  /// **'Método principal de inicio de sesión'**
  String get primaryLoginMethod;

  /// Quick login with Google description
  ///
  /// In es, this message translates to:
  /// **'Inicia sesión rápidamente con tu cuenta de Google'**
  String get quickLoginWithGoogle;

  /// Quick login with Facebook description
  ///
  /// In es, this message translates to:
  /// **'Inicia sesión rápidamente con tu cuenta de Facebook'**
  String get quickLoginWithFacebook;

  /// Unlink button text
  ///
  /// In es, this message translates to:
  /// **'Desvincular'**
  String get unlink;

  /// Primary account indicator
  ///
  /// In es, this message translates to:
  /// **'Principal'**
  String get primary;

  /// Verified status
  ///
  /// In es, this message translates to:
  /// **'Verificado'**
  String get verified;

  /// In process status
  ///
  /// In es, this message translates to:
  /// **'En Proceso'**
  String get inProcess;

  /// Under review status
  ///
  /// In es, this message translates to:
  /// **'En Revisión'**
  String get underReview;

  /// Rejected status
  ///
  /// In es, this message translates to:
  /// **'Rechazado'**
  String get rejected;

  /// Unverified status
  ///
  /// In es, this message translates to:
  /// **'Sin Verificar'**
  String get unverified;

  /// Unlink confirmation dialog message
  ///
  /// In es, this message translates to:
  /// **'¿Estás seguro de que quieres desvincular tu cuenta de {provider}? Podrás volver a vincularla más tarde.'**
  String unlinkConfirmationMessage(String provider);

  /// My Zones screen title
  ///
  /// In es, this message translates to:
  /// **'Mis Zonas'**
  String get myZones;

  /// Add zone button text
  ///
  /// In es, this message translates to:
  /// **'Agregar Zona'**
  String get addZone;

  /// Define zone screen title
  ///
  /// In es, this message translates to:
  /// **'Definir Zona'**
  String get defineZone;

  /// Zone details screen title
  ///
  /// In es, this message translates to:
  /// **'Detalles de Zona'**
  String get zoneDetails;

  /// Zone name field label
  ///
  /// In es, this message translates to:
  /// **'Nombre de la Zona'**
  String get zoneName;

  /// Zone name field hint text
  ///
  /// In es, this message translates to:
  /// **'Ej: Mi Casa, Oficina, Universidad'**
  String get zoneNameHint;

  /// Zone type field label
  ///
  /// In es, this message translates to:
  /// **'Tipo de Zona'**
  String get zoneType;

  /// Zone address field label
  ///
  /// In es, this message translates to:
  /// **'Dirección'**
  String get zoneAddress;

  /// Address search field placeholder
  ///
  /// In es, this message translates to:
  /// **'Buscar dirección...'**
  String get searchAddress;

  /// Presence hours field label
  ///
  /// In es, this message translates to:
  /// **'Horas de Presencia'**
  String get presenceHours;

  /// Presence hours field hint text
  ///
  /// In es, this message translates to:
  /// **'Ej: 8:00 AM - 6:00 PM'**
  String get presenceHoursHint;

  /// Confirm zone button text
  ///
  /// In es, this message translates to:
  /// **'Confirmar Zona'**
  String get confirmZone;

  /// Home zone type
  ///
  /// In es, this message translates to:
  /// **'Hogar'**
  String get zoneTypeHome;

  /// Work zone type
  ///
  /// In es, this message translates to:
  /// **'Trabajo'**
  String get zoneTypeWork;

  /// School zone type
  ///
  /// In es, this message translates to:
  /// **'Escuela'**
  String get zoneTypeSchool;

  /// University zone type
  ///
  /// In es, this message translates to:
  /// **'Universidad'**
  String get zoneTypeUniversity;

  /// Other zone type
  ///
  /// In es, this message translates to:
  /// **'Otro'**
  String get zoneTypeOther;

  /// Pending zone status
  ///
  /// In es, this message translates to:
  /// **'Pendiente'**
  String get zoneStatusPending;

  /// Validated zone status
  ///
  /// In es, this message translates to:
  /// **'Validada'**
  String get zoneStatusValidated;

  /// Rejected zone status
  ///
  /// In es, this message translates to:
  /// **'Rechazada'**
  String get zoneStatusRejected;

  /// Social validation method
  ///
  /// In es, this message translates to:
  /// **'Validación Social'**
  String get socialValidation;

  /// Automatic validation method
  ///
  /// In es, this message translates to:
  /// **'Validación Automática'**
  String get automaticValidation;

  /// Request social validation button text
  ///
  /// In es, this message translates to:
  /// **'Solicitar Validación Social'**
  String get requestSocialValidation;

  /// Button text to enable automatic validation
  ///
  /// In es, this message translates to:
  /// **'Habilitar Validación Automática'**
  String get enableAutomaticValidation;

  /// Title for validation progress section
  ///
  /// In es, this message translates to:
  /// **'Progreso de Validación'**
  String get validationProgress;

  /// Share validation link button text
  ///
  /// In es, this message translates to:
  /// **'Compartir Enlace'**
  String get shareValidationLink;

  /// QR code sharing instructions
  ///
  /// In es, this message translates to:
  /// **'Comparte este código QR con vecinos validados para que puedan validar tu zona'**
  String get qrCodeInstructions;

  /// Automatic validation explanation text
  ///
  /// In es, this message translates to:
  /// **'La validación automática monitorea tu presencia en la zona de forma consistente para validarla automáticamente'**
  String get automaticValidationExplanation;

  /// Location permission requirement
  ///
  /// In es, this message translates to:
  /// **'Permiso de Ubicación'**
  String get locationPermissionRequired;

  /// Enable location access button text
  ///
  /// In es, this message translates to:
  /// **'Habilitar Acceso a Ubicación'**
  String get enableLocationAccess;

  /// Zone validated status message
  ///
  /// In es, this message translates to:
  /// **'Zona Validada'**
  String get zoneValidated;

  /// Validation method label
  ///
  /// In es, this message translates to:
  /// **'Método de Validación'**
  String get validationMethod;

  /// No zones message
  ///
  /// In es, this message translates to:
  /// **'Aún no tienes zonas definidas'**
  String get noZonesYet;

  /// Create first zone message
  ///
  /// In es, this message translates to:
  /// **'Crea tu primera zona para comenzar'**
  String get createFirstZone;

  /// Maximum zones reached message
  ///
  /// In es, this message translates to:
  /// **'Has alcanzado el límite máximo de {max} zonas'**
  String maxZonesReached(int max);

  /// Zone created success message
  ///
  /// In es, this message translates to:
  /// **'Zona creada exitosamente'**
  String get zoneCreatedSuccessfully;

  /// Error creating zone message
  ///
  /// In es, this message translates to:
  /// **'Error al crear la zona'**
  String get errorCreatingZone;

  /// Select location on map instruction
  ///
  /// In es, this message translates to:
  /// **'Selecciona la ubicación en el mapa'**
  String get selectLocationOnMap;

  /// Zone radius display text
  ///
  /// In es, this message translates to:
  /// **'Radio de la zona: {radius}m'**
  String zoneRadius(int radius);

  /// Delete zone confirmation message
  ///
  /// In es, this message translates to:
  /// **'¿Estás seguro de que quieres eliminar esta zona?'**
  String get deleteZoneConfirmation;

  /// Zone deleted success message
  ///
  /// In es, this message translates to:
  /// **'Zona eliminada exitosamente'**
  String get zoneDeletedSuccessfully;

  /// Status text when validation is in progress
  ///
  /// In es, this message translates to:
  /// **'Validación en Progreso'**
  String get validationInProgress;

  /// Needs more validations status
  ///
  /// In es, this message translates to:
  /// **'Necesita más validaciones'**
  String get needsMoreValidations;

  /// Social validation description
  ///
  /// In es, this message translates to:
  /// **'Solicita a vecinos validados que confirmen tu presencia en esta zona'**
  String get socialValidationDescription;

  /// Description of automatic validation feature
  ///
  /// In es, this message translates to:
  /// **'Habilita la validación automática para verificar tu presencia en zonas de seguridad usando monitoreo de ubicación en segundo plano.'**
  String get automaticValidationDescription;

  /// Validation required message
  ///
  /// In es, this message translates to:
  /// **'Validación requerida'**
  String get validationRequired;

  /// Choose validation method instruction
  ///
  /// In es, this message translates to:
  /// **'Elige un método de validación para tu zona'**
  String get chooseValidationMethod;

  /// Validations needed message
  ///
  /// In es, this message translates to:
  /// **'Se necesitan {count} validaciones más'**
  String validationsNeeded(int count);

  /// Share zone validation title
  ///
  /// In es, this message translates to:
  /// **'Compartir Validación de Zona'**
  String get shareZoneValidation;

  /// Copy link button text
  ///
  /// In es, this message translates to:
  /// **'Copiar Enlace'**
  String get copyLink;

  /// Link copied message
  ///
  /// In es, this message translates to:
  /// **'Enlace copiado al portapapeles'**
  String get linkCopied;

  /// Enable automatic validation screen title
  ///
  /// In es, this message translates to:
  /// **'Habilitar Validación Automática'**
  String get enableAutomaticValidationTitle;

  /// Automatic validation enabled message
  ///
  /// In es, this message translates to:
  /// **'Validación automática habilitada'**
  String get automaticValidationEnabled;

  /// Automatic validation disabled message
  ///
  /// In es, this message translates to:
  /// **'Validación automática deshabilitada'**
  String get automaticValidationDisabled;

  /// Location access denied message
  ///
  /// In es, this message translates to:
  /// **'Acceso a ubicación denegado'**
  String get locationAccessDenied;

  /// Open settings button text
  ///
  /// In es, this message translates to:
  /// **'Abrir Configuración'**
  String get openSettings;

  /// Title for automatic validation screen
  ///
  /// In es, this message translates to:
  /// **'Validación Automática'**
  String get automaticValidationTitle;

  /// Button text to disable automatic validation
  ///
  /// In es, this message translates to:
  /// **'Deshabilitar Validación Automática'**
  String get disableAutomaticValidation;

  /// Title for permissions section
  ///
  /// In es, this message translates to:
  /// **'Permisos Requeridos'**
  String get permissionsRequired;

  /// Description of location permission requirement
  ///
  /// In es, this message translates to:
  /// **'Requerido para detectar cuando entras o sales de zonas de seguridad.'**
  String get locationPermissionDescription;

  /// Background location permission requirement
  ///
  /// In es, this message translates to:
  /// **'Permiso de Ubicación en Segundo Plano'**
  String get backgroundLocationPermissionRequired;

  /// Description of background location permission requirement
  ///
  /// In es, this message translates to:
  /// **'Requerido para monitorear tu ubicación incluso cuando la aplicación está cerrada.'**
  String get backgroundLocationPermissionDescription;

  /// Button text to request location permission
  ///
  /// In es, this message translates to:
  /// **'Solicitar Permiso de Ubicación'**
  String get requestLocationPermission;

  /// Button text to request background location permission
  ///
  /// In es, this message translates to:
  /// **'Solicitar Permiso de Segundo Plano'**
  String get requestBackgroundPermission;

  /// Status text when permission is granted
  ///
  /// In es, this message translates to:
  /// **'Permiso Concedido'**
  String get permissionGranted;

  /// Status text when permission is denied
  ///
  /// In es, this message translates to:
  /// **'Permiso Denegado'**
  String get permissionDenied;

  /// Title for presence hours configuration section
  ///
  /// In es, this message translates to:
  /// **'Configuración de Horas de Presencia'**
  String get presenceHoursConfiguration;

  /// Label for active days selection
  ///
  /// In es, this message translates to:
  /// **'Días Activos'**
  String get activeDays;

  /// Label for active time blocks
  ///
  /// In es, this message translates to:
  /// **'Bloques de Tiempo Activos'**
  String get activeTimeBlocks;

  /// Label for time blocks section
  ///
  /// In es, this message translates to:
  /// **'Bloques de Tiempo'**
  String get timeBlocks;

  /// Label for time spent in zone
  ///
  /// In es, this message translates to:
  /// **'Tiempo en Zona'**
  String get timeInZone;

  /// Label for number of zone entries
  ///
  /// In es, this message translates to:
  /// **'Entradas a Zona'**
  String get zoneEntries;

  /// Label for current validation status
  ///
  /// In es, this message translates to:
  /// **'Estado Actual'**
  String get currentStatus;

  /// Status text when user is in zone
  ///
  /// In es, this message translates to:
  /// **'En Zona'**
  String get inZone;

  /// Status text when user is outside zone
  ///
  /// In es, this message translates to:
  /// **'Fuera de Zona'**
  String get outsideZone;

  /// Title for recent activity section
  ///
  /// In es, this message translates to:
  /// **'Actividad Reciente'**
  String get recentActivity;

  /// Text for zone entry event
  ///
  /// In es, this message translates to:
  /// **'Entró a Zona'**
  String get enteredZone;

  /// Text for zone exit event
  ///
  /// In es, this message translates to:
  /// **'Salió de Zona'**
  String get exitedZone;

  /// Message when there are no recent validation events
  ///
  /// In es, this message translates to:
  /// **'Sin actividad reciente. Los eventos aparecerán aquí una vez que comience la validación automática.'**
  String get noRecentActivity;

  /// Title for privacy and security section
  ///
  /// In es, this message translates to:
  /// **'Privacidad y Seguridad'**
  String get privacyAndSecurity;

  /// Privacy policy description for location data
  ///
  /// In es, this message translates to:
  /// **'Tus datos de ubicación se usan únicamente para validación de zonas y no se comparten con terceros. El seguimiento de ubicación está activo solo cuando la validación automática está habilitada.'**
  String get privacyDescription;

  /// Title for validation criteria section
  ///
  /// In es, this message translates to:
  /// **'Criterios de Validación'**
  String get validationCriteria;

  /// Label for minimum time requirement
  ///
  /// In es, this message translates to:
  /// **'Tiempo Mínimo Requerido'**
  String get minimumTimeRequired;

  /// Label for minimum visits requirement
  ///
  /// In es, this message translates to:
  /// **'Visitas Mínimas Requeridas'**
  String get minimumVisitsRequired;

  /// Label for validation period
  ///
  /// In es, this message translates to:
  /// **'Período de Validación'**
  String get validationPeriod;

  /// Abbreviation for hours
  ///
  /// In es, this message translates to:
  /// **'h'**
  String get hoursAbbreviation;

  /// Abbreviation for minutes
  ///
  /// In es, this message translates to:
  /// **'m'**
  String get minutesAbbreviation;

  /// Status text when validation is complete
  ///
  /// In es, this message translates to:
  /// **'Validación Completa'**
  String get validationComplete;

  /// Status text when validation failed
  ///
  /// In es, this message translates to:
  /// **'Validación Fallida'**
  String get validationFailed;

  /// Message when location service is disabled
  ///
  /// In es, this message translates to:
  /// **'El servicio de ubicación está deshabilitado. Por favor habilítalo en la configuración de tu dispositivo.'**
  String get locationServiceDisabled;

  /// Message when background location permission is denied
  ///
  /// In es, this message translates to:
  /// **'El acceso a ubicación en segundo plano es requerido para la validación automática. Por favor habilítalo en la configuración de tu dispositivo.'**
  String get backgroundLocationDenied;

  /// Warning about battery optimization affecting background location
  ///
  /// In es, this message translates to:
  /// **'La optimización de batería puede afectar el monitoreo de ubicación en segundo plano. Considera deshabilitarla para esta aplicación.'**
  String get batteryOptimizationWarning;

  /// Notification message when automatic validation starts
  ///
  /// In es, this message translates to:
  /// **'Validación automática iniciada para esta zona'**
  String get automaticValidationStarted;

  /// Notification message when automatic validation stops
  ///
  /// In es, this message translates to:
  /// **'Validación automática detenida para esta zona'**
  String get automaticValidationStopped;

  /// Notification message when zone entry is detected
  ///
  /// In es, this message translates to:
  /// **'Entrada a zona detectada'**
  String get zoneEntryDetected;

  /// Notification message when zone exit is detected
  ///
  /// In es, this message translates to:
  /// **'Salida de zona detectada'**
  String get zoneExitDetected;

  /// Notification message when validation progress is updated
  ///
  /// In es, this message translates to:
  /// **'Progreso de validación actualizado'**
  String get validationProgressUpdate;

  /// Title for password reset confirmation dialog
  ///
  /// In es, this message translates to:
  /// **'Restablece Tu Contraseña'**
  String get resetPasswordTitle;

  /// Message confirming password reset email was sent
  ///
  /// In es, this message translates to:
  /// **'¡Correo enviado! Hemos enviado un email a tu cuenta con un enlace para que puedas establecer una nueva contraseña.'**
  String get resetPasswordEmailSent;

  /// Instructions for finding the password reset email
  ///
  /// In es, this message translates to:
  /// **'Por favor, revisa tu bandeja de entrada y, si no lo ves, no olvides mirar en tu carpeta de correo no deseado o spam.'**
  String get resetPasswordInstructions;

  /// Signature for password reset messages
  ///
  /// In es, this message translates to:
  /// **'Gracias por tu paciencia,\nEl equipo de ResPública Seguridad'**
  String get resetPasswordSignature;

  /// Button text to resend password reset email
  ///
  /// In es, this message translates to:
  /// **'Reenviar correo'**
  String get resendEmail;

  /// Close button text
  ///
  /// In es, this message translates to:
  /// **'Cerrar'**
  String get close;

  /// Email sent confirmation with email address
  ///
  /// In es, this message translates to:
  /// **'Correo enviado a {email}'**
  String emailSentTo(String email);

  /// Subtitle for security page
  ///
  /// In es, this message translates to:
  /// **'Tu seguridad es nuestra prioridad'**
  String get securityPageSubtitle;

  /// Emergency alert section title
  ///
  /// In es, this message translates to:
  /// **'Alerta de Emergencia'**
  String get emergencyAlert;

  /// Description for emergency alert functionality
  ///
  /// In es, this message translates to:
  /// **'Mantén presionado el botón por 3 segundos para enviar una alerta de emergencia a las autoridades y tus contactos de emergencia.'**
  String get emergencyAlertDescription;

  /// Text shown while holding panic button
  ///
  /// In es, this message translates to:
  /// **'Mantén presionado para activar alerta de emergencia...'**
  String get holdToActivate;

  /// Title for emergency alert confirmation dialog
  ///
  /// In es, this message translates to:
  /// **'Alerta de Emergencia Activada'**
  String get emergencyAlertActivated;

  /// Confirmation message for emergency alert
  ///
  /// In es, this message translates to:
  /// **'Los servicios de emergencia han sido notificados. La ayuda está en camino. Mantén la calma y sigue los protocolos de seguridad.'**
  String get emergencyAlertConfirmation;

  /// Button to cancel emergency alert
  ///
  /// In es, this message translates to:
  /// **'Cancelar Alerta'**
  String get cancelAlert;

  /// Confirmation button text
  ///
  /// In es, this message translates to:
  /// **'Confirmado'**
  String get confirmed;

  /// Message shown when emergency alert is active
  ///
  /// In es, this message translates to:
  /// **'Alerta de emergencia activa. La ayuda está en camino.'**
  String get emergencyAlertActive;

  /// Security features section title
  ///
  /// In es, this message translates to:
  /// **'Funciones de Seguridad'**
  String get securityFeatures;

  /// Location tracking feature title
  ///
  /// In es, this message translates to:
  /// **'Seguimiento de Ubicación'**
  String get locationTracking;

  /// Location tracking feature description
  ///
  /// In es, this message translates to:
  /// **'Monitoreo de ubicación en tiempo real para mayor seguridad'**
  String get locationTrackingDescription;

  /// Emergency contacts feature title
  ///
  /// In es, this message translates to:
  /// **'Contactos de Emergencia'**
  String get emergencyContacts;

  /// Emergency contacts feature description
  ///
  /// In es, this message translates to:
  /// **'Administra tu lista de contactos de emergencia'**
  String get emergencyContactsDescription;

  /// Safe zones feature title
  ///
  /// In es, this message translates to:
  /// **'Zonas Seguras'**
  String get safeZones;

  /// Safe zones feature description
  ///
  /// In es, this message translates to:
  /// **'Configura y monitorea tus zonas seguras'**
  String get safeZonesDescription;

  /// Incident reports feature title
  ///
  /// In es, this message translates to:
  /// **'Reportes de Incidentes'**
  String get incidentReports;

  /// Incident reports feature description
  ///
  /// In es, this message translates to:
  /// **'Ver y reportar incidentes de seguridad'**
  String get incidentReportsDescription;

  /// Status indicator for active features
  ///
  /// In es, this message translates to:
  /// **'Activo'**
  String get active;

  /// Safety tips section title
  ///
  /// In es, this message translates to:
  /// **'Consejos de Seguridad'**
  String get safetyTips;

  /// Safety tip title
  ///
  /// In es, this message translates to:
  /// **'Mantente Alerta'**
  String get stayAlert;

  /// Stay alert safety tip description
  ///
  /// In es, this message translates to:
  /// **'Siempre mantente consciente de tu entorno y confía en tus instintos.'**
  String get stayAlertDescription;

  /// Safety tip title
  ///
  /// In es, this message translates to:
  /// **'Mantén Contactos Actualizados'**
  String get keepContactsUpdated;

  /// Keep contacts updated safety tip description
  ///
  /// In es, this message translates to:
  /// **'Asegúrate de que tus contactos de emergencia estén actualizados y accesibles.'**
  String get keepContactsUpdatedDescription;

  /// Safety tip title
  ///
  /// In es, this message translates to:
  /// **'Comparte tu Ubicación'**
  String get shareYourLocation;

  /// Share location safety tip description
  ///
  /// In es, this message translates to:
  /// **'Permite que contactos de confianza sepan tu paradero cuando viajes.'**
  String get shareYourLocationDescription;

  /// Safety tip title
  ///
  /// In es, this message translates to:
  /// **'Mantén el Dispositivo Cargado'**
  String get keepDeviceCharged;

  /// Keep device charged safety tip description
  ///
  /// In es, this message translates to:
  /// **'Mantén la batería de tu teléfono para situaciones de emergencia.'**
  String get keepDeviceChargedDescription;

  /// Emergency numbers card title
  ///
  /// In es, this message translates to:
  /// **'Números de Emergencia'**
  String get emergencyNumbers;

  /// List of emergency numbers
  ///
  /// In es, this message translates to:
  /// **'Policía: 911 • Bomberos: 911 • Médico: 911'**
  String get emergencyNumbersList;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'es'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
