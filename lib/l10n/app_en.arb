{"@@locale": "en", "@@last_modified": "2025-06-27T00:00:00.000Z", "appTitle": "Republic Security", "@appTitle": {"description": "The title of the application"}, "home": "Home", "@home": {"description": "Home navigation label"}, "alerts": "<PERSON><PERSON><PERSON>", "@alerts": {"description": "Alerts navigation label"}, "security": "Security", "@security": {"description": "Security navigation label"}, "more": "More", "@more": {"description": "More navigation label"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "appearance": "Appearance", "@appearance": {"description": "Appearance section title in settings"}, "lightTheme": "Light Theme", "@lightTheme": {"description": "Light theme option"}, "lightThemeDescription": "Interface with light background", "@lightThemeDescription": {"description": "Light theme description"}, "darkTheme": "Dark Theme", "@darkTheme": {"description": "Dark theme option"}, "darkThemeDescription": "Interface with dark background", "@darkThemeDescription": {"description": "Dark theme description"}, "language": "Language", "@language": {"description": "Language section title"}, "spanish": "Español", "@spanish": {"description": "Spanish language option"}, "english": "English", "@english": {"description": "English language option"}, "debug": "🐛 Debug", "@debug": {"description": "Debug section title"}, "crashReports": "Crash Reports", "@crashReports": {"description": "Crash reports option"}, "crashReportsDescription": "View app crash reports and errors", "@crashReportsDescription": {"description": "Crash reports description"}, "generateTestReport": "Generate Test Report", "@generateTestReport": {"description": "Generate test report option"}, "generateTestReportDescription": "Create a test error report for debugging", "@generateTestReportDescription": {"description": "Generate test report description"}, "testReportGenerated": "Test report generated! Check crash reports to view it.", "@testReportGenerated": {"description": "Test report generated success message"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "fullName": "Full Name", "@fullName": {"description": "Full name field label"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "signup": "Sign Up", "@signup": {"description": "Signup button text"}, "forgotPassword": "Forgot your password?", "@forgotPassword": {"description": "Forgot password link text"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset password screen title"}, "sendLink": "Send Link", "@sendLink": {"description": "Send reset link button text"}, "backToLogin": "Back to Login", "@backToLogin": {"description": "Back to login button text"}, "pleaseEnterEmail": "Please enter your email", "@pleaseEnterEmail": {"description": "Email validation message"}, "pleaseEnterValidEmail": "Enter a valid email address", "@pleaseEnterValidEmail": {"description": "Valid email validation message"}, "pleaseEnterName": "Please enter your name", "@pleaseEnterName": {"description": "Name validation message"}, "searchForLocations": "Search for locations...", "@searchForLocations": {"description": "Search field hint text"}, "locationNotFound": "Location not found: {location}", "@locationNotFound": {"description": "Location not found error message", "placeholders": {"location": {"type": "String", "description": "The location name that was not found"}}}, "noCrashReports": "No Crash Reports", "@noCrashReports": {"description": "No crash reports title"}, "appRunningSmooth": "Your app is running smoothly!\nTap the bug icon to generate a test crash.", "@appRunningSmooth": {"description": "App running smoothly message"}, "crashReportsFound": "{count} crash report{plural} found", "@crashReportsFound": {"description": "Crash reports found message", "placeholders": {"count": {"type": "int", "description": "Number of crash reports"}, "plural": {"type": "String", "description": "Plural suffix"}}}, "debugReport": "Debug Report", "@debugReport": {"description": "Debug report title"}, "widgetError": "Widget E<PERSON>r", "@widgetError": {"description": "Widget error title"}, "systemError": "System Error", "@systemError": {"description": "System error title"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "error": "Error", "@error": {"description": "Error title"}, "success": "Success", "@success": {"description": "Success title"}, "warning": "Warning", "@warning": {"description": "Warning title"}, "info": "Information", "@info": {"description": "Info title"}, "enterCredentials": "Enter your credentials to continue", "@enterCredentials": {"description": "Enter credentials message"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Don't have account message"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Already have account message"}, "createAccount": "Create Account", "@createAccount": {"description": "Create account button text"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Or continue with message for social login"}, "google": "Google", "@google": {"description": "Google social login button"}, "facebook": "Facebook", "@facebook": {"description": "Facebook social login button"}, "signUpPrompt": "Sign Up", "@signUpPrompt": {"description": "Sign up prompt button text"}, "mustAcceptTerms": "You must accept the terms and conditions", "@mustAcceptTerms": {"description": "Must accept terms validation message"}, "signUpToStart": "Sign up to start using the platform", "@signUpToStart": {"description": "Sign up to start using platform message"}, "orSignUpWithEmail": "Or sign up with email", "@orSignUpWithEmail": {"description": "Or sign up with email message"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "passwordStrength": "Password strength: ", "@passwordStrength": {"description": "Password strength label"}, "veryWeak": "Very Weak", "@veryWeak": {"description": "Very weak password strength"}, "weak": "Weak", "@weak": {"description": "Weak password strength"}, "moderate": "Moderate", "@moderate": {"description": "Moderate password strength"}, "strong": "Strong", "@strong": {"description": "Strong password strength"}, "veryStrong": "Very Strong", "@veryStrong": {"description": "Very strong password strength"}, "acceptTerms": "I accept the ", "@acceptTerms": {"description": "Accept terms prefix"}, "termsAndConditions": "terms and conditions", "@termsAndConditions": {"description": "Terms and conditions link text"}, "andThe": " and the ", "@andThe": {"description": "And the connector"}, "privacyPolicy": "privacy policy", "@privacyPolicy": {"description": "Privacy policy link text"}, "done": "Done", "@done": {"description": "Done button text"}, "homeLocation": "Home Location", "@homeLocation": {"description": "Home location marker title"}, "yourCurrentArea": "Your current area", "@yourCurrentArea": {"description": "Home location marker snippet"}, "securityPoint": "Security Point {number}", "@securityPoint": {"description": "Security point marker title", "placeholders": {"number": {"type": "String", "description": "Security point number"}}}, "activeSecurityMonitoring": "Active security monitoring", "@activeSecurityMonitoring": {"description": "Security monitoring snippet"}, "emergencyResponseAvailable": "Emergency response available", "@emergencyResponseAvailable": {"description": "Emergency response snippet"}, "searchResult": "Search result", "@searchResult": {"description": "Search result marker snippet"}, "found": "Found: {location}", "@found": {"description": "Location found message", "placeholders": {"location": {"type": "String", "description": "The location name that was found"}}}, "identityVerification": "Identity Verification", "@identityVerification": {"description": "Identity verification screen title"}, "identityVerificationMessage": "Welcome to Republic Security! To ensure your safety and access to all protection features, please verify your identity. This helps us protect your account and provide personalized security services in your area.", "@identityVerificationMessage": {"description": "Identity verification dialog message"}, "skipForNow": "Skip for Now", "@skipForNow": {"description": "Skip for now button text"}, "verifyIdentity": "Verify Identity", "@verifyIdentity": {"description": "Verify identity button text"}, "moreOptions": "More Options", "@moreOptions": {"description": "More options screen title"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "logoutConfirmation": "Are you sure you want to logout?", "@logoutConfirmation": {"description": "Logout confirmation message"}, "accountSettings": "Account <PERSON><PERSON>", "@accountSettings": {"description": "Account settings option"}, "manageAccountPreferences": "Manage your account preferences", "@manageAccountPreferences": {"description": "Account settings description"}, "notifications": "Notifications", "@notifications": {"description": "Notifications option"}, "controlNotificationSettings": "Control your notification settings", "@controlNotificationSettings": {"description": "Notifications description"}, "themeMode": "Theme Mode", "@themeMode": {"description": "Theme mode option"}, "light": "Light", "@light": {"description": "Light theme"}, "dark": "Dark", "@dark": {"description": "Dark theme"}, "privacy": "Privacy", "@privacy": {"description": "Privacy option"}, "helpSupport": "Help & Support", "@helpSupport": {"description": "Help and support option"}, "idFrontSide": "ID Front Side", "@idFrontSide": {"description": "ID card front side text"}, "idBackSide": "ID Back Side", "@idBackSide": {"description": "ID card back side text"}, "profilePhoto": "Profile Photo", "@profilePhoto": {"description": "Profile photo text"}, "review": "Review", "@review": {"description": "Review step text"}, "captureIdFront": "Capture ID Front", "@captureIdFront": {"description": "Capture ID front button text"}, "captureIdBack": "Capture ID Back", "@captureIdBack": {"description": "Capture ID back button text"}, "captureProfilePhoto": "Capture Profile Photo", "@captureProfilePhoto": {"description": "Capture profile photo button text"}, "retake": "Retake", "@retake": {"description": "Retake photo button text"}, "next": "Next", "@next": {"description": "Next button text"}, "back": "Back", "@back": {"description": "Back button text"}, "submit": "Submit", "@submit": {"description": "Submit button text"}, "identityVerificationComplete": "Identity verification complete", "@identityVerificationComplete": {"description": "Identity verification complete message"}, "submittingDocuments": "Submitting your documents...", "@submittingDocuments": {"description": "Submitting documents message"}, "idFrontInstructions": "Position your ID card within the frame", "@idFrontInstructions": {"description": "ID front capture instructions"}, "idBackInstructions": "Now capture the back side of your ID card", "@idBackInstructions": {"description": "ID back capture instructions"}, "profilePhotoInstructions": "Take a selfie by positioning your face within the oval frame", "@profilePhotoInstructions": {"description": "Profile photo capture instructions"}, "reviewInstructions": "Please review your documents before submitting", "@reviewInstructions": {"description": "Review instructions text"}, "selectFromGallery": "Select from gallery", "@selectFromGallery": {"description": "Select image from gallery button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "identityVerificationTitle": "Identity Verification", "@identityVerificationTitle": {"description": "Identity verification title"}, "identityVerificationSubtitle": "Verify your identity for enhanced security", "@identityVerificationSubtitle": {"description": "Identity verification subtitle"}, "linkedAccounts": "Linked Accounts", "@linkedAccounts": {"description": "Linked accounts section title"}, "linkAdditionalAccounts": "Link Additional Accounts", "@linkAdditionalAccounts": {"description": "Link additional accounts section title"}, "addMoreLoginOptions": "Add more login options", "@addMoreLoginOptions": {"description": "Add more login options subtitle"}, "noLinkedAccounts": "You don't have any additional linked accounts", "@noLinkedAccounts": {"description": "No linked accounts message"}, "linkGoogle": "Link Google", "@linkGoogle": {"description": "Link Google account"}, "linkFacebook": "Link Facebook", "@linkFacebook": {"description": "Link Facebook account"}, "googleAccountLinked": "Google account linked", "@googleAccountLinked": {"description": "Google account linked description"}, "facebookAccountLinked": "Facebook account linked", "@facebookAccountLinked": {"description": "Facebook account linked description"}, "emailPasswordMethod": "Email & Password", "@emailPasswordMethod": {"description": "Email and password method"}, "primaryLoginMethod": "Primary login method", "@primaryLoginMethod": {"description": "Primary login method description"}, "quickLoginWithGoogle": "Sign in quickly with your Google account", "@quickLoginWithGoogle": {"description": "Quick login with Google description"}, "quickLoginWithFacebook": "Sign in quickly with your Facebook account", "@quickLoginWithFacebook": {"description": "Quick login with Facebook description"}, "unlink": "Unlink", "@unlink": {"description": "Unlink button text"}, "primary": "Primary", "@primary": {"description": "Primary account indicator"}, "verified": "Verified", "@verified": {"description": "Verified status"}, "inProcess": "In Process", "@inProcess": {"description": "In process status"}, "underReview": "Under Review", "@underReview": {"description": "Under review status"}, "rejected": "Rejected", "@rejected": {"description": "Rejected status"}, "unverified": "Unverified", "@unverified": {"description": "Unverified status"}, "unlinkConfirmationMessage": "Are you sure you want to unlink your {provider} account? You can link it again later.", "@unlinkConfirmationMessage": {"description": "Unlink confirmation dialog message", "placeholders": {"provider": {"type": "String", "description": "The provider name to unlink"}}}, "myZones": "My Zones", "@myZones": {"description": "My Zones screen title"}, "addZone": "Add Zone", "@addZone": {"description": "Add zone button text"}, "defineZone": "Define Zone", "@defineZone": {"description": "Define zone screen title"}, "zoneDetails": "Zone Details", "@zoneDetails": {"description": "Zone details screen title"}, "zoneName": "Zone Name", "@zoneName": {"description": "Zone name field label"}, "zoneNameHint": "e.g., My Home, Office, University", "@zoneNameHint": {"description": "Zone name field hint text"}, "zoneType": "Zone Type", "@zoneType": {"description": "Zone type field label"}, "zoneAddress": "Address", "@zoneAddress": {"description": "Zone address field label"}, "searchAddress": "Search address...", "@searchAddress": {"description": "Address search field placeholder"}, "presenceHours": "Presence Hours", "@presenceHours": {"description": "Presence hours field label"}, "presenceHoursHint": "e.g., 8:00 AM - 6:00 PM", "@presenceHoursHint": {"description": "Presence hours field hint text"}, "confirmZone": "Confirm Zone", "@confirmZone": {"description": "Confirm zone button text"}, "zoneTypeHome": "Home", "@zoneTypeHome": {"description": "Home zone type"}, "zoneTypeWork": "Work", "@zoneTypeWork": {"description": "Work zone type"}, "zoneTypeSchool": "School", "@zoneTypeSchool": {"description": "School zone type"}, "zoneTypeUniversity": "University", "@zoneTypeUniversity": {"description": "University zone type"}, "zoneTypeOther": "Other", "@zoneTypeOther": {"description": "Other zone type"}, "zoneStatusPending": "Pending", "@zoneStatusPending": {"description": "Pending zone status"}, "zoneStatusValidated": "Validated", "@zoneStatusValidated": {"description": "Validated zone status"}, "zoneStatusRejected": "Rejected", "@zoneStatusRejected": {"description": "Rejected zone status"}, "socialValidation": "Social Validation", "@socialValidation": {"description": "Social validation method"}, "automaticValidation": "Automatic Validation", "@automaticValidation": {"description": "Automatic validation method"}, "requestSocialValidation": "Request Social Validation", "@requestSocialValidation": {"description": "Request social validation button text"}, "enableAutomaticValidation": "Enable Automatic Validation", "@enableAutomaticValidation": {"description": "Button text to enable automatic validation"}, "validationProgress": "Validation Progress", "@validationProgress": {"description": "Title for validation progress section"}, "shareValidationLink": "Share Link", "@shareValidationLink": {"description": "Share validation link button text"}, "qrCodeInstructions": "Share this QR code with validated neighbors so they can validate your zone", "@qrCodeInstructions": {"description": "QR code sharing instructions"}, "automaticValidationExplanation": "Automatic validation monitors your consistent presence in the zone to validate it automatically", "@automaticValidationExplanation": {"description": "Automatic validation explanation text"}, "locationPermissionRequired": "Location Permission", "@locationPermissionRequired": {"description": "Location permission requirement"}, "enableLocationAccess": "Enable Location Access", "@enableLocationAccess": {"description": "Enable location access button text"}, "zoneValidated": "Zone Validated", "@zoneValidated": {"description": "Zone validated status message"}, "validationMethod": "Validation Method", "@validationMethod": {"description": "Validation method label"}, "noZonesYet": "You don't have any zones defined yet", "@noZonesYet": {"description": "No zones message"}, "createFirstZone": "Create your first zone to get started", "@createFirstZone": {"description": "Create first zone message"}, "maxZonesReached": "You have reached the maximum limit of {max} zones", "@maxZonesReached": {"description": "Maximum zones reached message", "placeholders": {"max": {"type": "int"}}}, "zoneCreatedSuccessfully": "Zone created successfully", "@zoneCreatedSuccessfully": {"description": "Zone created success message"}, "errorCreatingZone": "Error creating zone", "@errorCreatingZone": {"description": "Error creating zone message"}, "selectLocationOnMap": "Select location on the map", "@selectLocationOnMap": {"description": "Select location on map instruction"}, "zoneRadius": "Zone radius: {radius}m", "@zoneRadius": {"description": "Zone radius display text", "placeholders": {"radius": {"type": "int"}}}, "deleteZoneConfirmation": "Are you sure you want to delete this zone?", "@deleteZoneConfirmation": {"description": "Delete zone confirmation message"}, "zoneDeletedSuccessfully": "Zone deleted successfully", "@zoneDeletedSuccessfully": {"description": "Zone deleted success message"}, "validationInProgress": "Validation in Progress", "@validationInProgress": {"description": "Status text when validation is in progress"}, "needsMoreValidations": "Needs more validations", "@needsMoreValidations": {"description": "Needs more validations status"}, "socialValidationDescription": "Ask validated neighbors to confirm your presence in this zone", "@socialValidationDescription": {"description": "Social validation description"}, "automaticValidationDescription": "Enable automatic validation to verify your presence in security zones using background location monitoring.", "@automaticValidationDescription": {"description": "Description of automatic validation feature"}, "validationRequired": "Validation required", "@validationRequired": {"description": "Validation required message"}, "chooseValidationMethod": "Choose a validation method for your zone", "@chooseValidationMethod": {"description": "Choose validation method instruction"}, "validationsNeeded": "{count} more validations needed", "@validationsNeeded": {"description": "Validations needed message", "placeholders": {"count": {"type": "int"}}}, "shareZoneValidation": "Share Zone Validation", "@shareZoneValidation": {"description": "Share zone validation title"}, "copyLink": "Copy Link", "@copyLink": {"description": "Copy link button text"}, "linkCopied": "Link copied to clipboard", "@linkCopied": {"description": "Link copied message"}, "enableAutomaticValidationTitle": "Enable Automatic Validation", "@enableAutomaticValidationTitle": {"description": "Enable automatic validation screen title"}, "automaticValidationEnabled": "Automatic validation enabled", "@automaticValidationEnabled": {"description": "Automatic validation enabled message"}, "automaticValidationDisabled": "Automatic validation disabled", "@automaticValidationDisabled": {"description": "Automatic validation disabled message"}, "locationAccessDenied": "Location access denied", "@locationAccessDenied": {"description": "Location access denied message"}, "openSettings": "Open Settings", "@openSettings": {"description": "Open settings button text"}, "automaticValidationTitle": "Automatic Validation", "@automaticValidationTitle": {"description": "Title for automatic validation screen"}, "disableAutomaticValidation": "Disable Automatic Validation", "@disableAutomaticValidation": {"description": "Button text to disable automatic validation"}, "permissionsRequired": "Permissions Required", "@permissionsRequired": {"description": "Title for permissions section"}, "locationPermissionDescription": "Required to detect when you enter or exit security zones.", "@locationPermissionDescription": {"description": "Description of location permission requirement"}, "backgroundLocationPermissionRequired": "Background Location Permission", "@backgroundLocationPermissionRequired": {"description": "Background location permission requirement"}, "backgroundLocationPermissionDescription": "Required to monitor your location even when the app is closed.", "@backgroundLocationPermissionDescription": {"description": "Description of background location permission requirement"}, "requestLocationPermission": "Request Location Permission", "@requestLocationPermission": {"description": "Button text to request location permission"}, "requestBackgroundPermission": "Request Background Permission", "@requestBackgroundPermission": {"description": "Button text to request background location permission"}, "permissionGranted": "Permission Granted", "@permissionGranted": {"description": "Status text when permission is granted"}, "permissionDenied": "Permission Denied", "@permissionDenied": {"description": "Status text when permission is denied"}, "presenceHoursConfiguration": "Presence Hours Configuration", "@presenceHoursConfiguration": {"description": "Title for presence hours configuration section"}, "activeDays": "Active Days", "@activeDays": {"description": "Label for active days selection"}, "activeTimeBlocks": "Active Time Blocks", "@activeTimeBlocks": {"description": "Label for active time blocks"}, "timeBlocks": "Time Blocks", "@timeBlocks": {"description": "Label for time blocks section"}, "timeInZone": "Time in Zone", "@timeInZone": {"description": "Label for time spent in zone"}, "zoneEntries": "Zone Entries", "@zoneEntries": {"description": "Label for number of zone entries"}, "currentStatus": "Current Status", "@currentStatus": {"description": "Label for current validation status"}, "inZone": "In Zone", "@inZone": {"description": "Status text when user is in zone"}, "outsideZone": "Outside Zone", "@outsideZone": {"description": "Status text when user is outside zone"}, "recentActivity": "Recent Activity", "@recentActivity": {"description": "Title for recent activity section"}, "enteredZone": "Entered Zone", "@enteredZone": {"description": "Text for zone entry event"}, "exitedZone": "Exited Zone", "@exitedZone": {"description": "Text for zone exit event"}, "noRecentActivity": "No recent activity. Events will appear here once automatic validation starts.", "@noRecentActivity": {"description": "Message when there are no recent validation events"}, "privacyAndSecurity": "Privacy & Security", "@privacyAndSecurity": {"description": "Title for privacy and security section"}, "privacyDescription": "Your location data is used only for zone validation and is not shared with third parties. Location tracking is active only when automatic validation is enabled.", "@privacyDescription": {"description": "Privacy policy description for location data"}, "validationCriteria": "Validation Criteria", "@validationCriteria": {"description": "Title for validation criteria section"}, "minimumTimeRequired": "Minimum Time Required", "@minimumTimeRequired": {"description": "Label for minimum time requirement"}, "minimumVisitsRequired": "Minimum Visits Required", "@minimumVisitsRequired": {"description": "Label for minimum visits requirement"}, "validationPeriod": "Validation Period", "@validationPeriod": {"description": "Label for validation period"}, "hoursAbbreviation": "h", "@hoursAbbreviation": {"description": "Abbreviation for hours"}, "minutesAbbreviation": "m", "@minutesAbbreviation": {"description": "Abbreviation for minutes"}, "validationComplete": "Validation Complete", "@validationComplete": {"description": "Status text when validation is complete"}, "validationFailed": "Validation Failed", "@validationFailed": {"description": "Status text when validation failed"}, "locationServiceDisabled": "Location service is disabled. Please enable it in your device settings.", "@locationServiceDisabled": {"description": "Message when location service is disabled"}, "backgroundLocationDenied": "Background location access is required for automatic validation. Please enable it in your device settings.", "@backgroundLocationDenied": {"description": "Message when background location permission is denied"}, "batteryOptimizationWarning": "Battery optimization may affect background location monitoring. Consider disabling it for this app.", "@batteryOptimizationWarning": {"description": "Warning about battery optimization affecting background location"}, "automaticValidationStarted": "Automatic validation started for this zone", "@automaticValidationStarted": {"description": "Notification message when automatic validation starts"}, "automaticValidationStopped": "Automatic validation stopped for this zone", "@automaticValidationStopped": {"description": "Notification message when automatic validation stops"}, "zoneEntryDetected": "Zone entry detected", "@zoneEntryDetected": {"description": "Notification message when zone entry is detected"}, "zoneExitDetected": "Zone exit detected", "@zoneExitDetected": {"description": "Notification message when zone exit is detected"}, "validationProgressUpdate": "Validation progress updated", "@validationProgressUpdate": {"description": "Notification message when validation progress is updated"}, "resetPasswordTitle": "Reset Your Password", "@resetPasswordTitle": {"description": "Title for password reset confirmation dialog"}, "resetPasswordEmailSent": "Email sent! We have sent an email to your account with a link so you can set a new password.", "@resetPasswordEmailSent": {"description": "Message confirming password reset email was sent"}, "resetPasswordInstructions": "Please check your inbox and, if you don't see it, don't forget to check your spam or junk mail folder.", "@resetPasswordInstructions": {"description": "Instructions for finding the password reset email"}, "resetPasswordSignature": "Thank you for your patience,\nThe ResPública Security team", "@resetPasswordSignature": {"description": "Signature for password reset messages"}, "resendEmail": "Resend email", "@resendEmail": {"description": "Button text to resend password reset email"}, "close": "Close", "@close": {"description": "Close button text"}, "emailSentTo": "Email sent to {email}", "@emailSentTo": {"description": "Email sent confirmation with email address", "placeholders": {"email": {"type": "String", "description": "The email address where the reset link was sent"}}}, "securityPageSubtitle": "Your safety is our priority", "@securityPageSubtitle": {"description": "Subtitle for security page"}, "emergencyAlert": "Emergency Alert", "@emergencyAlert": {"description": "Emergency alert section title"}, "emergencyAlertDescription": "Hold the button below for 3 seconds to send an emergency alert to authorities and your emergency contacts.", "@emergencyAlertDescription": {"description": "Description for emergency alert functionality"}, "holdToActivate": "Hold to activate emergency alert...", "@holdToActivate": {"description": "Text shown while holding panic button"}, "emergencyAlertActivated": "Emergency Alert Activated", "@emergencyAlertActivated": {"description": "Title for emergency alert confirmation dialog"}, "emergencyAlertConfirmation": "Emergency services have been notified. Help is on the way. Stay calm and follow safety protocols.", "@emergencyAlertConfirmation": {"description": "Confirmation message for emergency alert"}, "cancelAlert": "<PERSON><PERSON>", "@cancelAlert": {"description": "Button to cancel emergency alert"}, "confirmed": "Confirmed", "@confirmed": {"description": "Confirmation button text"}, "emergencyAlertActive": "Emergency alert active. Help is on the way.", "@emergencyAlertActive": {"description": "Message shown when emergency alert is active"}, "securityFeatures": "Security Features", "@securityFeatures": {"description": "Security features section title"}, "locationTracking": "Location Tracking", "@locationTracking": {"description": "Location tracking feature title"}, "locationTrackingDescription": "Real-time location monitoring for enhanced safety", "@locationTrackingDescription": {"description": "Location tracking feature description"}, "emergencyContacts": "Emergency Contacts", "@emergencyContacts": {"description": "Emergency contacts feature title"}, "emergencyContactsDescription": "Manage your emergency contact list", "@emergencyContactsDescription": {"description": "Emergency contacts feature description"}, "safeZones": "Safe Zones", "@safeZones": {"description": "Safe zones feature title"}, "safeZonesDescription": "Set up and monitor your safe zones", "@safeZonesDescription": {"description": "Safe zones feature description"}, "incidentReports": "Incident Reports", "@incidentReports": {"description": "Incident reports feature title"}, "incidentReportsDescription": "View and report security incidents", "@incidentReportsDescription": {"description": "Incident reports feature description"}, "active": "Active", "@active": {"description": "Status indicator for active features"}, "safetyTips": "Safety Tips", "@safetyTips": {"description": "Safety tips section title"}, "stayAlert": "<PERSON>ert", "@stayAlert": {"description": "Safety tip title"}, "stayAlertDescription": "Always be aware of your surroundings and trust your instincts.", "@stayAlertDescription": {"description": "Stay alert safety tip description"}, "keepContactsUpdated": "Keep Contacts Updated", "@keepContactsUpdated": {"description": "Safety tip title"}, "keepContactsUpdatedDescription": "Ensure your emergency contacts are current and accessible.", "@keepContactsUpdatedDescription": {"description": "Keep contacts updated safety tip description"}, "shareYourLocation": "Share Your Location", "@shareYourLocation": {"description": "Safety tip title"}, "shareYourLocationDescription": "Let trusted contacts know your whereabouts when traveling.", "@shareYourLocationDescription": {"description": "Share location safety tip description"}, "keepDeviceCharged": "Keep Device Charged", "@keepDeviceCharged": {"description": "Safety tip title"}, "keepDeviceChargedDescription": "Maintain your phone battery for emergency situations.", "@keepDeviceChargedDescription": {"description": "Keep device charged safety tip description"}, "emergencyNumbers": "Emergency Numbers", "@emergencyNumbers": {"description": "Emergency numbers card title"}, "emergencyNumbersList": "Police: 911 • Fire: 911 • Medical: 911", "@emergencyNumbersList": {"description": "List of emergency numbers"}}