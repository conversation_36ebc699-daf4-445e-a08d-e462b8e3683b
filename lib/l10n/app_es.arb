{"@@locale": "es", "@@last_modified": "2025-06-27T00:00:00.000Z", "appTitle": "República Seguridad", "@appTitle": {"description": "The title of the application"}, "home": "<PERSON><PERSON>o", "@home": {"description": "Home navigation label"}, "alerts": "<PERSON><PERSON><PERSON>", "@alerts": {"description": "Alerts navigation label"}, "security": "Seguridad", "@security": {"description": "Security navigation label"}, "more": "Más", "@more": {"description": "More navigation label"}, "settings": "Configuración", "@settings": {"description": "Settings screen title"}, "appearance": "Apariencia", "@appearance": {"description": "Appearance section title in settings"}, "lightTheme": "<PERSON><PERSON> claro", "@lightTheme": {"description": "Light theme option"}, "lightThemeDescription": "Interfaz con fondo claro", "@lightThemeDescription": {"description": "Light theme description"}, "darkTheme": "Tema oscuro", "@darkTheme": {"description": "Dark theme option"}, "darkThemeDescription": "Interfaz con fondo oscuro", "@darkThemeDescription": {"description": "Dark theme description"}, "language": "Idioma", "@language": {"description": "Language section title"}, "spanish": "Español", "@spanish": {"description": "Spanish language option"}, "english": "English", "@english": {"description": "English language option"}, "debug": "🐛 Debug", "@debug": {"description": "Debug section title"}, "crashReports": "Reportes de Errores", "@crashReports": {"description": "Crash reports option"}, "crashReportsDescription": "Ver reportes de errores y fallos de la aplicación", "@crashReportsDescription": {"description": "Crash reports description"}, "generateTestReport": "Generar Reporte de Prueba", "@generateTestReport": {"description": "Generate test report option"}, "generateTestReportDescription": "Crear un reporte de error de prueba para depuración", "@generateTestReportDescription": {"description": "Generate test report description"}, "testReportGenerated": "¡Reporte de prueba generado! Revisa los reportes de errores para verlo.", "@testReportGenerated": {"description": "Test report generated success message"}, "email": "Correo electrónico", "@email": {"description": "Email field label"}, "password": "Contraseña", "@password": {"description": "Password field label"}, "fullName": "Nombre completo", "@fullName": {"description": "Full name field label"}, "login": "<PERSON><PERSON><PERSON>", "@login": {"description": "Login button text"}, "signup": "Registrarse", "@signup": {"description": "Signup button text"}, "forgotPassword": "¿Olvidaste tu contraseña?", "@forgotPassword": {"description": "Forgot password link text"}, "resetPassword": "Restable<PERSON>", "@resetPassword": {"description": "Reset password screen title"}, "sendLink": "<PERSON><PERSON><PERSON>", "@sendLink": {"description": "Send reset link button text"}, "backToLogin": "Volver a Iniciar <PERSON>", "@backToLogin": {"description": "Back to login button text"}, "pleaseEnterEmail": "Por favor ingresa tu correo", "@pleaseEnterEmail": {"description": "Email validation message"}, "pleaseEnterValidEmail": "Ingresa un correo electrónico válido", "@pleaseEnterValidEmail": {"description": "Valid email validation message"}, "pleaseEnterName": "Por favor ingresa tu nombre", "@pleaseEnterName": {"description": "Name validation message"}, "searchForLocations": "Buscar ubicaciones...", "@searchForLocations": {"description": "Search field hint text"}, "locationNotFound": "Ubicación no encontrada: {location}", "@locationNotFound": {"description": "Location not found error message", "placeholders": {"location": {"type": "String", "description": "The location name that was not found"}}}, "noCrashReports": "Sin Reportes de Errores", "@noCrashReports": {"description": "No crash reports title"}, "appRunningSmooth": "¡Tu aplicación funciona sin problemas!\nToca el ícono de error para generar un error de prueba.", "@appRunningSmooth": {"description": "App running smoothly message"}, "crashReportsFound": "{count} reporte{plural} de error{plural} encontrado{plural}", "@crashReportsFound": {"description": "Crash reports found message", "placeholders": {"count": {"type": "int", "description": "Number of crash reports"}, "plural": {"type": "String", "description": "Plural suffix"}}}, "debugReport": "Reporte de Depuración", "@debugReport": {"description": "Debug report title"}, "widgetError": "<PERSON><PERSON><PERSON>", "@widgetError": {"description": "Widget error title"}, "systemError": "Error del Sistema", "@systemError": {"description": "System error title"}, "loading": "Cargando...", "@loading": {"description": "Loading message"}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"description": "Cancel button text"}, "ok": "Aceptar", "@ok": {"description": "OK button text"}, "confirm": "Confirmar", "@confirm": {"description": "Confirm button text"}, "error": "Error", "@error": {"description": "Error title"}, "success": "Éxito", "@success": {"description": "Success title"}, "warning": "Advertencia", "@warning": {"description": "Warning title"}, "info": "Información", "@info": {"description": "Info title"}, "enterCredentials": "Ingresa tus credenciales para continuar", "@enterCredentials": {"description": "Enter credentials message"}, "dontHaveAccount": "¿No tienes una cuenta?", "@dontHaveAccount": {"description": "Don't have account message"}, "alreadyHaveAccount": "¿Ya tienes una cuenta?", "@alreadyHaveAccount": {"description": "Already have account message"}, "createAccount": "<PERSON><PERSON><PERSON>", "@createAccount": {"description": "Create account button text"}, "welcome": "Bienvenido", "@welcome": {"description": "Welcome message"}, "orContinueWith": "O continúa con", "@orContinueWith": {"description": "Or continue with message for social login"}, "google": "Google", "@google": {"description": "Google social login button"}, "facebook": "Facebook", "@facebook": {"description": "Facebook social login button"}, "signUpPrompt": "Regístrate", "@signUpPrompt": {"description": "Sign up prompt button text"}, "mustAcceptTerms": "Debes aceptar los términos y condiciones", "@mustAcceptTerms": {"description": "Must accept terms validation message"}, "signUpToStart": "Regístrate para comenzar a utilizar la plataforma", "@signUpToStart": {"description": "Sign up to start using platform message"}, "orSignUpWithEmail": "O regístrate con email", "@orSignUpWithEmail": {"description": "Or sign up with email message"}, "confirmPassword": "Confirmar con<PERSON>", "@confirmPassword": {"description": "Confirm password field label"}, "passwordStrength": "Fortaleza de la contraseña: ", "@passwordStrength": {"description": "Password strength label"}, "veryWeak": "<PERSON><PERSON>", "@veryWeak": {"description": "Very weak password strength"}, "weak": "<PERSON><PERSON><PERSON>", "@weak": {"description": "Weak password strength"}, "moderate": "Moderada", "@moderate": {"description": "Moderate password strength"}, "strong": "<PERSON><PERSON>e", "@strong": {"description": "Strong password strength"}, "veryStrong": "<PERSON><PERSON>uer<PERSON>", "@veryStrong": {"description": "Very strong password strength"}, "acceptTerms": "Acepto los ", "@acceptTerms": {"description": "Accept terms prefix"}, "termsAndConditions": "términos y condiciones", "@termsAndConditions": {"description": "Terms and conditions link text"}, "andThe": " y la ", "@andThe": {"description": "And the connector"}, "privacyPolicy": "política de privacidad", "@privacyPolicy": {"description": "Privacy policy link text"}, "done": "Listo", "@done": {"description": "Done button text"}, "homeLocation": "Ubicación de Inicio", "@homeLocation": {"description": "Home location marker title"}, "yourCurrentArea": "<PERSON> actual", "@yourCurrentArea": {"description": "Home location marker snippet"}, "securityPoint": "Punto de Seguridad {number}", "@securityPoint": {"description": "Security point marker title", "placeholders": {"number": {"type": "String", "description": "Security point number"}}}, "activeSecurityMonitoring": "Monitoreo de seguridad activo", "@activeSecurityMonitoring": {"description": "Security monitoring snippet"}, "emergencyResponseAvailable": "Respuesta de emergencia disponible", "@emergencyResponseAvailable": {"description": "Emergency response snippet"}, "searchResult": "Resultado de búsqueda", "@searchResult": {"description": "Search result marker snippet"}, "found": "Encontrado: {location}", "@found": {"description": "Location found message", "placeholders": {"location": {"type": "String", "description": "The location name that was found"}}}, "identityVerification": "🔐 Verificación de Identidad", "@identityVerification": {"description": "Identity verification dialog title"}, "identityVerificationMessage": "¡Bienvenido a República Seguridad! Para garantizar tu seguridad y acceso a todas las funciones de protección, por favor verifica tu identidad. Esto nos ayuda a proteger tu cuenta y brindar servicios de seguridad personalizados en tu área.", "@identityVerificationMessage": {"description": "Identity verification dialog message"}, "skipForNow": "<PERSON><PERSON><PERSON>", "@skipForNow": {"description": "Skip for now button text"}, "verifyIdentity": "Verificar Identidad", "@verifyIdentity": {"description": "Verify identity button text"}, "moreOptions": "Más Opciones", "@moreOptions": {"description": "More options screen title"}, "logout": "<PERSON><PERSON><PERSON>", "@logout": {"description": "Logout button text"}, "logoutConfirmation": "¿Estás seguro que deseas cerrar sesión?", "@logoutConfirmation": {"description": "Logout confirmation message"}, "accountSettings": "Configuración de Cuenta", "@accountSettings": {"description": "Account settings option"}, "manageAccountPreferences": "Administra las preferencias de tu cuenta", "@manageAccountPreferences": {"description": "Account settings description"}, "notifications": "Notificaciones", "@notifications": {"description": "Notifications option"}, "controlNotificationSettings": "Controla la configuración de notificaciones", "@controlNotificationSettings": {"description": "Notifications description"}, "themeMode": "<PERSON><PERSON>", "@themeMode": {"description": "Theme mode option"}, "light": "<PERSON><PERSON><PERSON>", "@light": {"description": "Light theme"}, "dark": "Oscuro", "@dark": {"description": "Dark theme"}, "privacy": "Privacidad", "@privacy": {"description": "Privacy option"}, "helpSupport": "Ayuda y Soporte", "@helpSupport": {"description": "Help and support option"}, "idFrontSide": "Frente del DNI", "@idFrontSide": {"description": "Front side of the ID card"}, "idBackSide": "Reverso del DNI", "@idBackSide": {"description": "Back side of the ID card"}, "profilePhoto": "Foto de Perfil", "@profilePhoto": {"description": "Profile photo"}, "review": "Revisión", "@review": {"description": "Review"}, "captureIdFront": "<PERSON><PERSON><PERSON> D<PERSON>", "@captureIdFront": {"description": "Capture front side of the ID card"}, "captureIdBack": "Capt<PERSON>r Rev<PERSON>o del DNI", "@captureIdBack": {"description": "Capture back side of the ID card"}, "captureProfilePhoto": "<PERSON><PERSON>r F<PERSON> de Perfil", "@captureProfilePhoto": {"description": "Capture profile photo"}, "retake": "<PERSON>ver a tomar", "@retake": {"description": "Retake"}, "next": "Siguient<PERSON>", "@next": {"description": "Next"}, "back": "Atrás", "@back": {"description": "Back"}, "submit": "Enviar", "@submit": {"description": "Submit"}, "identityVerificationComplete": "Verificación de identidad completada", "@identityVerificationComplete": {"description": "Identity verification completed"}, "submittingDocuments": "Enviando tus documentos...", "@submittingDocuments": {"description": "Submitting documents"}, "idFrontInstructions": "Coloca tu DNI dentro del marco", "@idFrontInstructions": {"description": "Place your ID card inside the frame"}, "idBackInstructions": "Ahora captura el reverso de tu DNI", "@idBackInstructions": {"description": "Now capture the back of your ID card"}, "profilePhotoInstructions": "Toma una selfie posicionando tu rostro dentro del marco ovalado", "@profilePhotoInstructions": {"description": "Place your face inside the circle"}, "reviewInstructions": "Por favor revisa tus documentos antes de enviar", "selectFromGallery": "Seleccionar de la galería", "edit": "<PERSON><PERSON>", "identityVerificationTitle": "Verificación de Identidad", "@identityVerificationTitle": {"description": "Identity verification title"}, "identityVerificationSubtitle": "Verifica tu identidad para mayor seguridad", "@identityVerificationSubtitle": {"description": "Identity verification subtitle"}, "linkedAccounts": "Cuentas Vinculadas", "@linkedAccounts": {"description": "Linked accounts section title"}, "linkAdditionalAccounts": "Vincular Cuentas Adicionales", "@linkAdditionalAccounts": {"description": "Link additional accounts section title"}, "addMoreLoginOptions": "Agrega más opciones de inicio de sesión", "@addMoreLoginOptions": {"description": "Add more login options subtitle"}, "noLinkedAccounts": "No tienes cuentas adicionales vinculadas", "@noLinkedAccounts": {"description": "No linked accounts message"}, "linkGoogle": "Vincular Google", "@linkGoogle": {"description": "Link Google account"}, "linkFacebook": "Vincular Facebook", "@linkFacebook": {"description": "Link Facebook account"}, "googleAccountLinked": "Cuenta de Google vinculada", "@googleAccountLinked": {"description": "Google account linked description"}, "facebookAccountLinked": "Cuenta de Facebook vinculada", "@facebookAccountLinked": {"description": "Facebook account linked description"}, "emailPasswordMethod": "Correo y Contraseña", "@emailPasswordMethod": {"description": "Email and password method"}, "primaryLoginMethod": "Método principal de inicio de sesión", "@primaryLoginMethod": {"description": "Primary login method description"}, "quickLoginWithGoogle": "Inicia sesión rápidamente con tu cuenta de Google", "@quickLoginWithGoogle": {"description": "Quick login with Google description"}, "quickLoginWithFacebook": "Inicia sesión rápidamente con tu cuenta de Facebook", "@quickLoginWithFacebook": {"description": "Quick login with Facebook description"}, "unlink": "Desvin<PERSON>", "@unlink": {"description": "Unlink button text"}, "primary": "Principal", "@primary": {"description": "Primary account indicator"}, "verified": "Verificado", "@verified": {"description": "Verified status"}, "inProcess": "En Proceso", "@inProcess": {"description": "In process status"}, "underReview": "En Revisión", "@underReview": {"description": "Under review status"}, "rejected": "<PERSON><PERSON><PERSON><PERSON>", "@rejected": {"description": "Rejected status"}, "unverified": "Sin Verificar", "@unverified": {"description": "Unverified status"}, "unlinkConfirmationMessage": "¿Estás seguro de que quieres desvincular tu cuenta de {provider}? Podrás volver a vincularla más tarde.", "@unlinkConfirmationMessage": {"description": "Unlink confirmation dialog message", "placeholders": {"provider": {"type": "String", "description": "The provider name to unlink"}}}, "myZones": "<PERSON><PERSON>", "@myZones": {"description": "My Zones screen title"}, "addZone": "Agregar Zona", "@addZone": {"description": "Add zone button text"}, "defineZone": "Definir <PERSON>", "@defineZone": {"description": "Define zone screen title"}, "zoneDetails": "Detalles de Zona", "@zoneDetails": {"description": "Zone details screen title"}, "zoneName": "Nombre de la Zona", "@zoneName": {"description": "Zone name field label"}, "zoneNameHint": "Ej: Mi Casa, Oficina, Universidad", "@zoneNameHint": {"description": "Zone name field hint text"}, "zoneType": "Tipo de Zona", "@zoneType": {"description": "Zone type field label"}, "zoneAddress": "Dirección", "@zoneAddress": {"description": "Zone address field label"}, "searchAddress": "Buscar dirección...", "@searchAddress": {"description": "Address search field placeholder"}, "presenceHours": "Horas de Presencia", "@presenceHours": {"description": "Presence hours field label"}, "presenceHoursHint": "Ej: 8:00 AM - 6:00 PM", "@presenceHoursHint": {"description": "Presence hours field hint text"}, "confirmZone": "Confirmar <PERSON>", "@confirmZone": {"description": "Confirm zone button text"}, "zoneTypeHome": "<PERSON><PERSON>", "@zoneTypeHome": {"description": "Home zone type"}, "zoneTypeWork": "Trabajo", "@zoneTypeWork": {"description": "Work zone type"}, "zoneTypeSchool": "Escuela", "@zoneTypeSchool": {"description": "School zone type"}, "zoneTypeUniversity": "Universidad", "@zoneTypeUniversity": {"description": "University zone type"}, "zoneTypeOther": "<PERSON><PERSON>", "@zoneTypeOther": {"description": "Other zone type"}, "zoneStatusPending": "Pendiente", "@zoneStatusPending": {"description": "Pending zone status"}, "zoneStatusValidated": "Validada", "@zoneStatusValidated": {"description": "Validated zone status"}, "zoneStatusRejected": "<PERSON><PERSON><PERSON><PERSON>", "@zoneStatusRejected": {"description": "Rejected zone status"}, "socialValidation": "Validación Social", "@socialValidation": {"description": "Social validation method"}, "automaticValidation": "Validación Automática", "@automaticValidation": {"description": "Automatic validation method"}, "requestSocialValidation": "Solicitar Validación Social", "@requestSocialValidation": {"description": "Request social validation button text"}, "enableAutomaticValidation": "Habilitar Validación Automática", "@enableAutomaticValidation": {"description": "Button text to enable automatic validation"}, "validationProgress": "Progreso de Validación", "@validationProgress": {"description": "Title for validation progress section"}, "shareValidationLink": "Compart<PERSON>", "@shareValidationLink": {"description": "Share validation link button text"}, "qrCodeInstructions": "Comparte este código QR con vecinos validados para que puedan validar tu zona", "@qrCodeInstructions": {"description": "QR code sharing instructions"}, "automaticValidationExplanation": "La validación automática monitorea tu presencia en la zona de forma consistente para validarla automáticamente", "@automaticValidationExplanation": {"description": "Automatic validation explanation text"}, "locationPermissionRequired": "Permiso de Ubicación", "@locationPermissionRequired": {"description": "Location permission requirement"}, "enableLocationAccess": "Habilitar Acceso a Ubicación", "@enableLocationAccess": {"description": "Enable location access button text"}, "zoneValidated": "Zona Validada", "@zoneValidated": {"description": "Zone validated status message"}, "validationMethod": "Método de Validación", "@validationMethod": {"description": "Validation method label"}, "noZonesYet": "Aún no tienes zonas definidas", "@noZonesYet": {"description": "No zones message"}, "createFirstZone": "<PERSON>rea tu primera zona para comenzar", "@createFirstZone": {"description": "Create first zone message"}, "maxZonesReached": "Has alcanzado el límite máximo de {max} zonas", "@maxZonesReached": {"description": "Maximum zones reached message", "placeholders": {"max": {"type": "int"}}}, "zoneCreatedSuccessfully": "Zona creada exitosamente", "@zoneCreatedSuccessfully": {"description": "Zone created success message"}, "errorCreatingZone": "Error al crear la zona", "@errorCreatingZone": {"description": "Error creating zone message"}, "selectLocationOnMap": "Selecciona la ubicación en el mapa", "@selectLocationOnMap": {"description": "Select location on map instruction"}, "zoneRadius": "Radio de la zona: {radius}m", "@zoneRadius": {"description": "Zone radius display text", "placeholders": {"radius": {"type": "int"}}}, "deleteZoneConfirmation": "¿Estás seguro de que quieres eliminar esta zona?", "@deleteZoneConfirmation": {"description": "Delete zone confirmation message"}, "zoneDeletedSuccessfully": "Zona eliminada exitosamente", "@zoneDeletedSuccessfully": {"description": "Zone deleted success message"}, "validationInProgress": "Validación en Progreso", "@validationInProgress": {"description": "Status text when validation is in progress"}, "needsMoreValidations": "Necesita más validaciones", "@needsMoreValidations": {"description": "Needs more validations status"}, "socialValidationDescription": "Solicita a vecinos validados que confirmen tu presencia en esta zona", "@socialValidationDescription": {"description": "Social validation description"}, "automaticValidationDescription": "Habilita la validación automática para verificar tu presencia en zonas de seguridad usando monitoreo de ubicación en segundo plano.", "@automaticValidationDescription": {"description": "Description of automatic validation feature"}, "validationRequired": "Validación requerida", "@validationRequired": {"description": "Validation required message"}, "chooseValidationMethod": "Elige un método de validación para tu zona", "@chooseValidationMethod": {"description": "Choose validation method instruction"}, "validationsNeeded": "Se necesitan {count} validaciones más", "@validationsNeeded": {"description": "Validations needed message", "placeholders": {"count": {"type": "int"}}}, "shareZoneValidation": "Compartir Validación de Zona", "@shareZoneValidation": {"description": "Share zone validation title"}, "copyLink": "<PERSON><PERSON><PERSON>", "@copyLink": {"description": "Copy link button text"}, "linkCopied": "Enlace copiado al portapapeles", "@linkCopied": {"description": "Link copied message"}, "enableAutomaticValidationTitle": "Habilitar Validación Automática", "@enableAutomaticValidationTitle": {"description": "Enable automatic validation screen title"}, "automaticValidationEnabled": "Validación automática habilitada", "@automaticValidationEnabled": {"description": "Automatic validation enabled message"}, "automaticValidationDisabled": "Validación automática deshabilitada", "@automaticValidationDisabled": {"description": "Automatic validation disabled message"}, "locationAccessDenied": "Acceso a ubicación denegado", "@locationAccessDenied": {"description": "Location access denied message"}, "openSettings": "Abrir Configuración", "@openSettings": {"description": "Open settings button text"}, "automaticValidationTitle": "Validación Automática", "@automaticValidationTitle": {"description": "Title for automatic validation screen"}, "disableAutomaticValidation": "Deshabilitar Validación Automática", "@disableAutomaticValidation": {"description": "Button text to disable automatic validation"}, "permissionsRequired": "Permisos <PERSON>", "@permissionsRequired": {"description": "Title for permissions section"}, "locationPermissionDescription": "Requerido para detectar cuando entras o sales de zonas de seguridad.", "@locationPermissionDescription": {"description": "Description of location permission requirement"}, "backgroundLocationPermissionRequired": "Permiso de Ubicación en Segundo Plano", "@backgroundLocationPermissionRequired": {"description": "Background location permission requirement"}, "backgroundLocationPermissionDescription": "Requerido para monitorear tu ubicación incluso cuando la aplicación está cerrada.", "@backgroundLocationPermissionDescription": {"description": "Description of background location permission requirement"}, "requestLocationPermission": "Solicitar Permiso de Ubicación", "@requestLocationPermission": {"description": "Button text to request location permission"}, "requestBackgroundPermission": "Solicitar Permiso de Segundo Plano", "@requestBackgroundPermission": {"description": "Button text to request background location permission"}, "permissionGranted": "Permiso Concedido", "@permissionGranted": {"description": "Status text when permission is granted"}, "permissionDenied": "<PERSON><PERSON><PERSON>", "@permissionDenied": {"description": "Status text when permission is denied"}, "presenceHoursConfiguration": "Configuración de Horas de Presencia", "@presenceHoursConfiguration": {"description": "Title for presence hours configuration section"}, "activeDays": "Días Activos", "@activeDays": {"description": "Label for active days selection"}, "activeTimeBlocks": "Bloques de Tiempo Activos", "@activeTimeBlocks": {"description": "Label for active time blocks"}, "timeBlocks": "Bloques de Tiempo", "@timeBlocks": {"description": "Label for time blocks section"}, "timeInZone": "Tiempo en Zona", "@timeInZone": {"description": "Label for time spent in zone"}, "zoneEntries": "Entradas a Zona", "@zoneEntries": {"description": "Label for number of zone entries"}, "currentStatus": "Estado Actual", "@currentStatus": {"description": "Label for current validation status"}, "inZone": "En Zona", "@inZone": {"description": "Status text when user is in zone"}, "outsideZone": "Fuera de Zona", "@outsideZone": {"description": "Status text when user is outside zone"}, "recentActivity": "Actividad Reciente", "@recentActivity": {"description": "Title for recent activity section"}, "enteredZone": "Entró a Zona", "@enteredZone": {"description": "Text for zone entry event"}, "exitedZone": "Salió de Zona", "@exitedZone": {"description": "Text for zone exit event"}, "noRecentActivity": "Sin actividad reciente. Los eventos aparecerán aquí una vez que comience la validación automática.", "@noRecentActivity": {"description": "Message when there are no recent validation events"}, "privacyAndSecurity": "Privacidad y Seguridad", "@privacyAndSecurity": {"description": "Title for privacy and security section"}, "privacyDescription": "Tus datos de ubicación se usan únicamente para validación de zonas y no se comparten con terceros. El seguimiento de ubicación está activo solo cuando la validación automática está habilitada.", "@privacyDescription": {"description": "Privacy policy description for location data"}, "validationCriteria": "Criterios de Validación", "@validationCriteria": {"description": "Title for validation criteria section"}, "minimumTimeRequired": "Tiempo M<PERSON>", "@minimumTimeRequired": {"description": "Label for minimum time requirement"}, "minimumVisitsRequired": "Visitas Mínimas Requeridas", "@minimumVisitsRequired": {"description": "Label for minimum visits requirement"}, "validationPeriod": "Período de Validación", "@validationPeriod": {"description": "Label for validation period"}, "hoursAbbreviation": "h", "@hoursAbbreviation": {"description": "Abbreviation for hours"}, "minutesAbbreviation": "m", "@minutesAbbreviation": {"description": "Abbreviation for minutes"}, "validationComplete": "Validación Completa", "@validationComplete": {"description": "Status text when validation is complete"}, "validationFailed": "Validación Fallida", "@validationFailed": {"description": "Status text when validation failed"}, "locationServiceDisabled": "El servicio de ubicación está deshabilitado. Por favor habilítalo en la configuración de tu dispositivo.", "@locationServiceDisabled": {"description": "Message when location service is disabled"}, "backgroundLocationDenied": "El acceso a ubicación en segundo plano es requerido para la validación automática. Por favor habilítalo en la configuración de tu dispositivo.", "@backgroundLocationDenied": {"description": "Message when background location permission is denied"}, "batteryOptimizationWarning": "La optimización de batería puede afectar el monitoreo de ubicación en segundo plano. Considera deshabilitarla para esta aplicación.", "@batteryOptimizationWarning": {"description": "Warning about battery optimization affecting background location"}, "automaticValidationStarted": "Validación automática iniciada para esta zona", "@automaticValidationStarted": {"description": "Notification message when automatic validation starts"}, "automaticValidationStopped": "Validación automática detenida para esta zona", "@automaticValidationStopped": {"description": "Notification message when automatic validation stops"}, "zoneEntryDetected": "Entrada a zona detectada", "@zoneEntryDetected": {"description": "Notification message when zone entry is detected"}, "zoneExitDetected": "Salida de zona detectada", "@zoneExitDetected": {"description": "Notification message when zone exit is detected"}, "validationProgressUpdate": "Progreso de validación actualizado", "@validationProgressUpdate": {"description": "Notification message when validation progress is updated"}, "resetPasswordTitle": "Restable<PERSON>", "@resetPasswordTitle": {"description": "Title for password reset confirmation dialog"}, "resetPasswordEmailSent": "¡Correo enviado! Hemos enviado un email a tu cuenta con un enlace para que puedas establecer una nueva contraseña.", "@resetPasswordEmailSent": {"description": "Message confirming password reset email was sent"}, "resetPasswordInstructions": "Por favor, revisa tu bandeja de entrada y, si no lo ves, no olvides mirar en tu carpeta de correo no deseado o spam.", "@resetPasswordInstructions": {"description": "Instructions for finding the password reset email"}, "resetPasswordSignature": "<PERSON><PERSON><PERSON> por tu paciencia,\nEl equipo de ResPública Seguridad", "@resetPasswordSignature": {"description": "Signature for password reset messages"}, "resendEmail": "<PERSON><PERSON><PERSON><PERSON> correo", "@resendEmail": {"description": "Button text to resend password reset email"}, "close": "<PERSON><PERSON><PERSON>", "@close": {"description": "Close button text"}, "emailSentTo": "Correo enviado a {email}", "@emailSentTo": {"description": "Email sent confirmation with email address", "placeholders": {"email": {"type": "String", "description": "The email address where the reset link was sent"}}}, "securityPageSubtitle": "Tu seguridad es nuestra prioridad", "@securityPageSubtitle": {"description": "Subtitle for security page"}, "emergencyAlert": "Alerta de Emergencia", "@emergencyAlert": {"description": "Emergency alert section title"}, "emergencyAlertDescription": "Mantén presionado el botón por 3 segundos para enviar una alerta de emergencia a las autoridades y tus contactos de emergencia.", "@emergencyAlertDescription": {"description": "Description for emergency alert functionality"}, "holdToActivate": "Mantén presionado para activar alerta de emergencia...", "@holdToActivate": {"description": "Text shown while holding panic button"}, "emergencyAlertActivated": "Alerta de Emergencia Activada", "@emergencyAlertActivated": {"description": "Title for emergency alert confirmation dialog"}, "emergencyAlertConfirmation": "Los servicios de emergencia han sido notificados. La ayuda está en camino. Mantén la calma y sigue los protocolos de seguridad.", "@emergencyAlertConfirmation": {"description": "Confirmation message for emergency alert"}, "cancelAlert": "<PERSON><PERSON><PERSON>", "@cancelAlert": {"description": "Button to cancel emergency alert"}, "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "@confirmed": {"description": "Confirmation button text"}, "emergencyAlertActive": "Alerta de emergencia activa. La ayuda está en camino.", "@emergencyAlertActive": {"description": "Message shown when emergency alert is active"}, "securityFeatures": "Funciones de Seguridad", "@securityFeatures": {"description": "Security features section title"}, "locationTracking": "Seguimiento de Ubicación", "@locationTracking": {"description": "Location tracking feature title"}, "locationTrackingDescription": "Monitoreo de ubicación en tiempo real para mayor seguridad", "@locationTrackingDescription": {"description": "Location tracking feature description"}, "emergencyContacts": "Contactos de Emergencia", "@emergencyContacts": {"description": "Emergency contacts feature title"}, "emergencyContactsDescription": "Administra tu lista de contactos de emergencia", "@emergencyContactsDescription": {"description": "Emergency contacts feature description"}, "safeZones": "<PERSON><PERSON><PERSON>", "@safeZones": {"description": "Safe zones feature title"}, "safeZonesDescription": "Configura y monitorea tus zonas seguras", "@safeZonesDescription": {"description": "Safe zones feature description"}, "incidentReports": "Reportes de Incidentes", "@incidentReports": {"description": "Incident reports feature title"}, "incidentReportsDescription": "Ver y reportar incidentes de seguridad", "@incidentReportsDescription": {"description": "Incident reports feature description"}, "active": "Activo", "@active": {"description": "Status indicator for active features"}, "safetyTips": "Consejos de Seguridad", "@safetyTips": {"description": "Safety tips section title"}, "stayAlert": "Mantente Alerta", "@stayAlert": {"description": "Safety tip title"}, "stayAlertDescription": "Siempre mantente consciente de tu entorno y confía en tus instintos.", "@stayAlertDescription": {"description": "Stay alert safety tip description"}, "keepContactsUpdated": "Mantén Contactos Actualizados", "@keepContactsUpdated": {"description": "Safety tip title"}, "keepContactsUpdatedDescription": "Asegúrate de que tus contactos de emergencia estén actualizados y accesibles.", "@keepContactsUpdatedDescription": {"description": "Keep contacts updated safety tip description"}, "shareYourLocation": "Comparte tu Ubicación", "@shareYourLocation": {"description": "Safety tip title"}, "shareYourLocationDescription": "Permite que contactos de confianza sepan tu paradero cuando viajes.", "@shareYourLocationDescription": {"description": "Share location safety tip description"}, "keepDeviceCharged": "Mantén el Dispositivo Cargado", "@keepDeviceCharged": {"description": "Safety tip title"}, "keepDeviceChargedDescription": "Mantén la batería de tu teléfono para situaciones de emergencia.", "@keepDeviceChargedDescription": {"description": "Keep device charged safety tip description"}, "emergencyNumbers": "Números de Emergencia", "@emergencyNumbers": {"description": "Emergency numbers card title"}, "emergencyNumbersList": "Policía: 911 • Bomberos: 911 • Médico: 911", "@emergencyNumbersList": {"description": "List of emergency numbers"}}