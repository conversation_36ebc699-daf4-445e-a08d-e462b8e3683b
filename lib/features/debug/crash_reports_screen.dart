import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/crash_reporting_service.dart';

/// Debug-only screen for viewing crash reports
class CrashReportsScreen extends StatefulWidget {
  const CrashReportsScreen({Key? key}) : super(key: key);

  @override
  State<CrashReportsScreen> createState() => _CrashReportsScreenState();
}

class _CrashReportsScreenState extends State<CrashReportsScreen> {
  List<CrashReport> _crashReports = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCrashReports();
  }

  Future<void> _loadCrashReports() async {
    if (!kDebugMode) return;

    setState(() => _isLoading = true);
    
    try {
      final reports = await CrashReportingService.getCrashReports();
      setState(() {
        _crashReports = reports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading crash reports: $e')),
        );
      }
    }
  }

  Future<void> _clearAllReports() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear All Reports', style: GoogleFonts.outfit()),
        content: Text(
          'Are you sure you want to clear all crash reports? This action cannot be undone.',
          style: GoogleFonts.outfit(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel', style: GoogleFonts.outfit()),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('Clear All', style: GoogleFonts.outfit(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await CrashReportingService.clearCrashReports();
      await _loadCrashReports();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('All crash reports cleared', style: GoogleFonts.outfit()),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _testCrash() async {
    await CrashReportingService.logCustomError(
      'Test crash generated from debug screen',
      StackTrace.current,
    );
    await _loadCrashReports();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Test crash report generated', style: GoogleFonts.outfit()),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Debug Feature', style: GoogleFonts.outfit()),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => NavigationService.navigateTo(context, 'settings'),
          ),
        ),
        body: Center(
          child: Text(
            'This feature is only available in debug mode',
            style: GoogleFonts.outfit(fontSize: 16.sp),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('🐛 Crash Reports', style: GoogleFonts.outfit()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => NavigationService.navigateTo(context, 'settings'),
        ),
        actions: [
          IconButton(
            icon: const Icon(FluentIcons.bug_24_regular),
            onPressed: _testCrash,
            tooltip: 'Generate Test Crash',
          ),
          IconButton(
            icon: const Icon(FluentIcons.delete_24_regular),
            onPressed: _crashReports.isNotEmpty ? _clearAllReports : null,
            tooltip: 'Clear All Reports',
          ),
          IconButton(
            icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
            onPressed: _loadCrashReports,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _crashReports.isEmpty
              ? _buildEmptyState()
              : _buildCrashReportsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.checkmark_circle_24_filled,
            size: 64.sp,
            color: Colors.green,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Crash Reports',
            style: GoogleFonts.outfit(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Your app is running smoothly!\nTap the bug icon to generate a test crash.',
            textAlign: TextAlign.center,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCrashReportsList() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16.w),
          color: Colors.blue[50],
          child: Row(
            children: [
              Icon(FluentIcons.info_24_regular, color: Colors.blue),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  '${_crashReports.length} crash report${_crashReports.length == 1 ? '' : 's'} found',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: Colors.blue[800],
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _crashReports.length,
            itemBuilder: (context, index) {
              final report = _crashReports[index];
              return _buildCrashReportTile(report);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCrashReportTile(CrashReport report) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final crashColor = _getColorForCrashType(report.type);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
      
      ),
      child: ExpansionTile(
        tilePadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        childrenPadding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
        leading: Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: crashColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            _getIconForCrashType(report.type),
            color: crashColor,
            size: 20.sp,
          ),
        ),
        title: Text(
          _getDisplayTitle(report.type),
          style: GoogleFonts.outfit(
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Text(
              _getShortDescription(report.error),
              style: GoogleFonts.outfit(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4.h),
            Text(
              _getTimeAgo(report.timestamp),
              style: GoogleFonts.outfit(
                fontSize: 11.sp,
                color: crashColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCompactDetailRow('Error', report.error),
              if (report.context != null) _buildCompactDetailRow('Context', report.context!),
              _buildCompactDetailRow('Stack Trace', report.stackTrace),
              SizedBox(height: 12.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _copyToClipboard(report),
                  icon: Icon(FluentIcons.copy_24_regular, size: 16.sp),
                  label: Text('Copy Details', style: GoogleFonts.outfit(fontSize: 13.sp)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: crashColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompactDetailRow(String label, String value) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.outfit(
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
              color: theme.textTheme.titleSmall?.color,
            ),
          ),
          SizedBox(height: 4.h),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[50],
              borderRadius: BorderRadius.circular(6.r),
              border: Border.all(
                color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
                width: 1,
              ),
            ),
            child: Text(
              value.length > 150 ? '${value.substring(0, 150)}...' : value,
              style: GoogleFonts.robotoMono(
                fontSize: 10.sp,
                color: theme.textTheme.bodySmall?.color,
                height: 1.2,
              ),
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayTitle(String type) {
    switch (type) {
      case 'Manual Error':
        return 'Debug Report';
      case 'Flutter Error':
        return 'Widget Error';
      case 'Platform Error':
        return 'System Error';
      default:
        return type;
    }
  }

  String _getShortDescription(String error) {
    if (error.contains('Test error report')) {
      return 'Debug test report';
    }
    if (error.contains('RenderFlex')) {
      return 'Layout overflow error';
    }
    if (error.contains('setState')) {
      return 'State management error';
    }
    if (error.contains('null')) {
      return 'Null reference error';
    }
    return error.split('\n').first;
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  IconData _getIconForCrashType(String type) {
    switch (type) {
      case 'Flutter Error':
        return FluentIcons.error_circle_24_filled;
      case 'Platform Error':
        return FluentIcons.warning_24_filled;
      case 'Manual Error':
        return FluentIcons.bug_24_filled;
      default:
        return FluentIcons.question_circle_24_regular;
    }
  }

  Color _getColorForCrashType(String type) {
    switch (type) {
      case 'Flutter Error':
        return Colors.red;
      case 'Platform Error':
        return Colors.orange;
      case 'Manual Error':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _copyToClipboard(CrashReport report) async {
    final details = '''
Crash Report Details
===================
Type: ${report.type}
Timestamp: ${report.timestamp}
Error: ${report.error}
${report.context != null ? 'Context: ${report.context}\n' : ''}${report.library != null ? 'Library: ${report.library}\n' : ''}
Stack Trace:
${report.stackTrace}

Device Info:
${report.deviceInfo.entries.map((e) => '${e.key}: ${e.value}').join('\n')}
''';

    await Clipboard.setData(ClipboardData(text: details));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Crash details copied to clipboard', style: GoogleFonts.outfit()),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
