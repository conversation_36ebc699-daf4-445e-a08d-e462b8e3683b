import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';

/// Service for parsing presence hours strings into structured configurations
/// 
/// This service converts simple presence hour strings (like "9:00 AM - 5:00 PM")
/// into structured PresenceHoursConfiguration objects required for automatic validation.
abstract class PresenceHoursParserService {
  /// Parse a presence hours string into a configuration object
  Either<Failure, PresenceHoursConfiguration> parsePresenceHours({
    required String presenceHoursString,
    required String zoneId,
  });
}

/// Implementation of PresenceHoursParserService
class PresenceHoursParserServiceImpl implements PresenceHoursParserService {
  
  @override
  Either<Failure, PresenceHoursConfiguration> parsePresenceHours({
    required String presenceHoursString,
    required String zoneId,
  }) {
    try {
      // Parse the presence hours string
      final timeBlock = _parseTimeBlock(presenceHoursString);
      if (timeBlock == null) {
        return Left(ValidationFailure('Invalid presence hours format'));
      }

      // Create configuration with automatic validation enabled
      final config = PresenceHoursConfiguration(
        zoneId: zoneId,
        isAutomaticValidationEnabled: true,
        timeBlocks: [timeBlock],
        createdAt: DateTime.now(),
      );

      return Right(config);
    } catch (e) {
      return Left(ValidationFailure('Failed to parse presence hours: ${e.toString()}'));
    }
  }

  /// Parse a time block from various string formats
  PresenceTimeBlock? _parseTimeBlock(String presenceHoursString) {
    final trimmed = presenceHoursString.trim();
    
    // Try to match against known presets first
    final preset = _matchPreset(trimmed);
    if (preset != null) {
      return preset;
    }

    // Try to parse custom time range formats
    return _parseCustomTimeRange(trimmed);
  }

  /// Match against known preset patterns
  PresenceTimeBlock? _matchPreset(String input) {
    // Check against preset labels
    final presets = {
      'Full-time (9 AM - 5 PM)': _createFullTimeBlock(),
      'Part-time (9 AM - 1 PM)': _createPartTimeBlock(),
      'Mornings (6 AM - 12 PM)': _createMorningsBlock(),
      'Afternoons (12 PM - 6 PM)': _createAfternoonsBlock(),
      'Evenings (6 PM - 10 PM)': _createEveningsBlock(),
      'Weekends (10 AM - 4 PM)': _createWeekendsBlock(),
    };

    return presets[input];
  }

  /// Parse custom time range formats like "9:00 AM - 5:00 PM" or "09:00 - 17:00"
  PresenceTimeBlock? _parseCustomTimeRange(String input) {
    // Common patterns to match
    final patterns = [
      RegExp(r'(\d{1,2}):(\d{2})\s*(AM|PM)?\s*-\s*(\d{1,2}):(\d{2})\s*(AM|PM)?', caseSensitive: false),
      RegExp(r'(\d{1,2})\s*(AM|PM)\s*-\s*(\d{1,2})\s*(AM|PM)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(input);
      if (match != null) {
        try {
          final startTime = _parseTime(match);
          final endTime = _parseEndTime(match);
          
          if (startTime != null && endTime != null) {
            return PresenceTimeBlock(
              id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
              startTime: startTime,
              endTime: endTime,
              activeDays: DayOfWeek.weekdays, // Default to weekdays for custom ranges
              isEnabled: true,
              label: input,
            );
          }
        } catch (e) {
          // Continue to next pattern if parsing fails
          continue;
        }
      }
    }

    return null;
  }

  /// Parse start time from regex match
  PresenceTimeOfDay? _parseTime(RegExpMatch match) {
    try {
      final hour = int.parse(match.group(1)!);
      final minute = int.parse(match.group(2)!);
      final period = match.group(3)?.toUpperCase();

      int adjustedHour = hour;
      if (period == 'PM' && hour != 12) {
        adjustedHour = hour + 12;
      } else if (period == 'AM' && hour == 12) {
        adjustedHour = 0;
      }

      if (adjustedHour >= 0 && adjustedHour <= 23 && minute >= 0 && minute <= 59) {
        return PresenceTimeOfDay(hour: adjustedHour, minute: minute);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  /// Parse end time from regex match
  PresenceTimeOfDay? _parseEndTime(RegExpMatch match) {
    try {
      final hour = int.parse(match.group(4)!);
      final minute = int.parse(match.group(5)!);
      final period = match.group(6)?.toUpperCase();

      int adjustedHour = hour;
      if (period == 'PM' && hour != 12) {
        adjustedHour = hour + 12;
      } else if (period == 'AM' && hour == 12) {
        adjustedHour = 0;
      }

      if (adjustedHour >= 0 && adjustedHour <= 23 && minute >= 0 && minute <= 59) {
        return PresenceTimeOfDay(hour: adjustedHour, minute: minute);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  // Preset time block creators
  PresenceTimeBlock _createFullTimeBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'full_time',
      startTime: '09:00',
      endTime: '17:00',
      activeDays: DayOfWeek.weekdays,
      label: 'Full-time (9 AM - 5 PM)',
    );
  }

  PresenceTimeBlock _createPartTimeBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'part_time',
      startTime: '09:00',
      endTime: '13:00',
      activeDays: DayOfWeek.weekdays,
      label: 'Part-time (9 AM - 1 PM)',
    );
  }

  PresenceTimeBlock _createMorningsBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'mornings',
      startTime: '06:00',
      endTime: '12:00',
      activeDays: DayOfWeek.all,
      label: 'Mornings (6 AM - 12 PM)',
    );
  }

  PresenceTimeBlock _createAfternoonsBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'afternoons',
      startTime: '12:00',
      endTime: '18:00',
      activeDays: DayOfWeek.all,
      label: 'Afternoons (12 PM - 6 PM)',
    );
  }

  PresenceTimeBlock _createEveningsBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'evenings',
      startTime: '18:00',
      endTime: '22:00',
      activeDays: DayOfWeek.all,
      label: 'Evenings (6 PM - 10 PM)',
    );
  }

  PresenceTimeBlock _createWeekendsBlock() {
    return PresenceTimeBlock.fromTimeStrings(
      id: 'weekends',
      startTime: '10:00',
      endTime: '16:00',
      activeDays: DayOfWeek.weekends,
      label: 'Weekends (10 AM - 4 PM)',
    );
  }
}
