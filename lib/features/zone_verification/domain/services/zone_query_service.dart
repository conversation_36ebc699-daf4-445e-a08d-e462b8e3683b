import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Enum for zone sorting options
enum ZoneSortBy {
  name,
  createdAt,
  validationStatus,
  type,
}

/// Service for querying and filtering zones
class ZoneQueryService {
  /// Filter zones by status
  static List<ZoneEntity> filterByStatus(
    List<ZoneEntity> zones,
    ZoneStatus? status,
  ) {
    if (status == null) return zones;
    return zones.where((zone) => zone.validationStatus == status).toList();
  }

  /// Filter zones by type
  static List<ZoneEntity> filterByType(
    List<ZoneEntity> zones,
    ZoneType? type,
  ) {
    if (type == null) return zones;
    return zones.where((zone) => zone.type == type).toList();
  }

  /// Filter zones by validation method
  static List<ZoneEntity> filterByValidationMethod(
    List<ZoneEntity> zones,
    ValidationMethod? validationMethod,
  ) {
    if (validationMethod == null) return zones;
    return zones.where((zone) => zone.validationMethod == validationMethod).toList();
  }

  /// Apply multiple filters to zones
  static List<ZoneEntity> applyFilters(
    List<ZoneEntity> zones, {
    ZoneStatus? status,
    ZoneType? type,
    ValidationMethod? validationMethod,
  }) {
    List<ZoneEntity> filteredZones = zones;

    filteredZones = filterByStatus(filteredZones, status);
    filteredZones = filterByType(filteredZones, type);
    filteredZones = filterByValidationMethod(filteredZones, validationMethod);

    return filteredZones;
  }

  /// Sort zones by specified criteria
  static List<ZoneEntity> sortZones(
    List<ZoneEntity> zones,
    ZoneSortBy sortBy, {
    bool descending = false,
  }) {
    final sortedZones = List<ZoneEntity>.from(zones);

    switch (sortBy) {
      case ZoneSortBy.name:
        sortedZones.sort((a, b) => a.name.compareTo(b.name));
        break;
      case ZoneSortBy.createdAt:
        sortedZones.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case ZoneSortBy.validationStatus:
        sortedZones.sort((a, b) => a.validationStatus.index.compareTo(b.validationStatus.index));
        break;
      case ZoneSortBy.type:
        sortedZones.sort((a, b) => a.type.index.compareTo(b.type.index));
        break;
    }

    if (descending) {
      return sortedZones.reversed.toList();
    }

    return sortedZones;
  }

  /// Query zones with filters and sorting
  static List<ZoneEntity> queryZones(
    List<ZoneEntity> zones, {
    ZoneStatus? status,
    ZoneType? type,
    ValidationMethod? validationMethod,
    ZoneSortBy sortBy = ZoneSortBy.createdAt,
    bool sortDescending = false,
  }) {
    // Apply filters
    List<ZoneEntity> result = applyFilters(
      zones,
      status: status,
      type: type,
      validationMethod: validationMethod,
    );

    // Apply sorting
    result = sortZones(result, sortBy, descending: sortDescending);

    return result;
  }

  /// Search zones by name or address
  static List<ZoneEntity> searchZones(
    List<ZoneEntity> zones,
    String query,
  ) {
    if (query.trim().isEmpty) return zones;

    final searchQuery = query.toLowerCase().trim();
    return zones.where((zone) {
      return zone.name.toLowerCase().contains(searchQuery) ||
             zone.address.toLowerCase().contains(searchQuery);
    }).toList();
  }

  /// Get zones within a radius of coordinates
  static List<ZoneEntity> getZonesNearLocation(
    List<ZoneEntity> zones, {
    required Coordinates location,
    required double radiusInMeters,
  }) {
    return zones.where((zone) {
      return zone.centerCoordinates.isWithinRadius(location, radiusInMeters);
    }).toList();
  }

  /// Get zones by validation status
  static List<ZoneEntity> getZonesByStatus(
    List<ZoneEntity> zones,
    ZoneStatus status,
  ) {
    return zones.where((zone) => zone.validationStatus == status).toList();
  }

  /// Get pending zones
  static List<ZoneEntity> getPendingZones(List<ZoneEntity> zones) {
    return getZonesByStatus(zones, ZoneStatus.pending);
  }

  /// Get validated zones
  static List<ZoneEntity> getValidatedZones(List<ZoneEntity> zones) {
    return getZonesByStatus(zones, ZoneStatus.validated);
  }

  /// Get rejected zones
  static List<ZoneEntity> getRejectedZones(List<ZoneEntity> zones) {
    return getZonesByStatus(zones, ZoneStatus.rejected);
  }

  /// Get zones that need community validation
  static List<ZoneEntity> getZonesNeedingCommunityValidation(List<ZoneEntity> zones) {
    return zones.where((zone) {
      return zone.validationMethod == ValidationMethod.social &&
             zone.validationStatus == ZoneStatus.pending;
    }).toList();
  }

  /// Get zones that use automatic validation
  static List<ZoneEntity> getAutomaticValidationZones(List<ZoneEntity> zones) {
    return zones.where((zone) {
      return zone.validationMethod == ValidationMethod.automatic;
    }).toList();
  }

  /// Get zone statistics
  static Map<String, int> getZoneStatistics(List<ZoneEntity> zones) {
    return {
      'total': zones.length,
      'pending': getPendingZones(zones).length,
      'validated': getValidatedZones(zones).length,
      'rejected': getRejectedZones(zones).length,
      'home': zones.where((z) => z.type == ZoneType.home).length,
      'work': zones.where((z) => z.type == ZoneType.work).length,
      'other': zones.where((z) => z.type == ZoneType.other).length,
      'social': zones.where((z) => z.validationMethod == ValidationMethod.social).length,
      'automatic': zones.where((z) => z.validationMethod == ValidationMethod.automatic).length,
    };
  }
}
