import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/coordinates.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

class ZoneEntity extends Equatable {
  final String id;
  final String userId;
  final String name;
  final ZoneType type;
  final String address;
  final Coordinates centerCoordinates; // Center of the 600m radius zone
  final double radiusInMeters; // Always 600 meters for zones
  final String? presenceHours; // Optional field for backward compatibility
  final PresenceHoursConfiguration? presenceHoursConfig; // Enhanced presence hours configuration
  final ZoneStatus validationStatus;
  final ValidationMethod validationMethod;
  final int communityValidationCount;
  final List<String> communityValidatedBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? validatedAt;
  final String? rejectionReason;
  final Map<String, dynamic>? metadata;

  const ZoneEntity({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
    required this.address,
    required this.centerCoordinates,
    this.radiusInMeters = 600.0, // Default 600 meters for zones
    this.presenceHours,
    this.presenceHoursConfig,
    this.validationStatus = ZoneStatus.pending,
    this.validationMethod = ValidationMethod.social,
    this.communityValidationCount = 0,
    this.communityValidatedBy = const [],
    required this.createdAt,
    this.updatedAt,
    this.validatedAt,
    this.rejectionReason,
    this.metadata,
  });

  /// Define the zone (initial creation)
  ZoneEntity define() {
    return copyWith(
      createdAt: DateTime.now(),
      validationStatus: ZoneStatus.pending,
    );
  }

  /// Update the validation status of the zone
  ZoneEntity updateStatus(ZoneStatus status, {String? rejectionReason}) {
    return copyWith(
      validationStatus: status,
      updatedAt: DateTime.now(),
      validatedAt: status == ZoneStatus.validated ? DateTime.now() : validatedAt,
      rejectionReason: status == ZoneStatus.rejected ? rejectionReason : null,
    );
  }

  /// Add a community validation
  ZoneEntity addCommunityValidation(String validatorUserId) {
    if (communityValidatedBy.contains(validatorUserId)) {
      return this; // Already validated by this user
    }

    final newValidatedBy = List<String>.from(communityValidatedBy)
      ..add(validatorUserId);

    return copyWith(
      communityValidatedBy: newValidatedBy,
      communityValidationCount: communityValidationCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Remove a community validation
  ZoneEntity removeCommunityValidation(String validatorUserId) {
    if (!communityValidatedBy.contains(validatorUserId)) {
      return this; // Not validated by this user
    }

    final newValidatedBy = List<String>.from(communityValidatedBy)
      ..remove(validatorUserId);

    return copyWith(
      communityValidatedBy: newValidatedBy,
      communityValidationCount: communityValidationCount - 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if the zone needs community validation
  bool get needsCommunityValidation {
    return validationMethod == ValidationMethod.social && 
           validationStatus == ZoneStatus.pending;
  }

  /// Check if the zone has enough community validations
  bool get hasEnoughCommunityValidations {
    const int requiredValidations = 3; // Configurable threshold
    return communityValidationCount >= requiredValidations;
  }

  /// Check if a user has already validated this zone
  bool isValidatedBy(String userId) {
    return communityValidatedBy.contains(userId);
  }

  /// Get the zone's validation progress (0.0 to 1.0)
  double get validationProgress {
    if (validationStatus == ZoneStatus.validated) return 1.0;
    if (validationStatus == ZoneStatus.rejected) return 0.0;
    
    const int requiredValidations = 3;
    return (communityValidationCount / requiredValidations).clamp(0.0, 1.0);
  }

  /// Check if the given location is within this zone's 600m radius
  bool isWithinZone(Coordinates location) {
    return centerCoordinates.isWithinRadius(location, radiusInMeters);
  }

  /// Check if the zone is within a certain radius of given coordinates
  bool isWithinRadius(Coordinates location, double radiusInMeters) {
    return centerCoordinates.isWithinRadius(location, radiusInMeters);
  }

  ZoneEntity copyWith({
    String? id,
    String? userId,
    String? name,
    ZoneType? type,
    String? address,
    Coordinates? centerCoordinates,
    double? radiusInMeters,
    String? presenceHours,
    PresenceHoursConfiguration? presenceHoursConfig,
    ZoneStatus? validationStatus,
    ValidationMethod? validationMethod,
    int? communityValidationCount,
    List<String>? communityValidatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? validatedAt,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return ZoneEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      centerCoordinates: centerCoordinates ?? this.centerCoordinates,
      radiusInMeters: radiusInMeters ?? this.radiusInMeters,
      presenceHours: presenceHours ?? this.presenceHours,
      presenceHoursConfig: presenceHoursConfig ?? this.presenceHoursConfig,
      validationStatus: validationStatus ?? this.validationStatus,
      validationMethod: validationMethod ?? this.validationMethod,
      communityValidationCount: communityValidationCount ?? this.communityValidationCount,
      communityValidatedBy: communityValidatedBy ?? this.communityValidatedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      validatedAt: validatedAt ?? this.validatedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        type,
        address,
        centerCoordinates,
        radiusInMeters,
        presenceHours,
        presenceHoursConfig,
        validationStatus,
        validationMethod,
        communityValidationCount,
        communityValidatedBy,
        createdAt,
        updatedAt,
        validatedAt,
        rejectionReason,
        metadata,
      ];

  static final empty = ZoneEntity(
    id: '',
    userId: '',
    name: '',
    type: ZoneType.other,
    address: '',
    centerCoordinates: Coordinates.empty,
    createdAt: DateTime.now(),
  );

  bool get isEmpty => this == ZoneEntity.empty;
  bool get isNotEmpty => this != ZoneEntity.empty;
  bool get isValidated => validationStatus == ZoneStatus.validated;
  bool get isPending => validationStatus == ZoneStatus.pending;
  bool get isRejected => validationStatus == ZoneStatus.rejected;

  // Automatic validation convenience methods
  bool get hasAutomaticValidation => validationMethod == ValidationMethod.automatic;
  bool get isAutomaticValidationEnabled => presenceHoursConfig?.isAutomaticValidationEnabled ?? false;
  bool get canUseAutomaticValidation => hasAutomaticValidation && isAutomaticValidationEnabled;

  /// Check if automatic validation is currently active
  bool isAutomaticValidationActiveAt(DateTime dateTime) {
    return canUseAutomaticValidation &&
           (presenceHoursConfig?.isActiveAt(dateTime) ?? false);
  }

  /// Get total hours per week for automatic validation
  double get automaticValidationHoursPerWeek {
    return presenceHoursConfig?.totalHoursPerWeek ?? 0.0;
  }
}
