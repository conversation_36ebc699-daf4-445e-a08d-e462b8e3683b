import 'package:respublicaseguridad/core/services/geofencing_service.dart';

/// Record of a geofence event for persistence and analytics
class GeofenceEventRecord {
  final String id;
  final String zoneId;
  final String userId;
  final GeofenceEventType eventType;
  final DateTime timestamp;
  final double latitude;
  final double longitude;
  final double accuracy;
  final Map<String, dynamic>? metadata;

  const GeofenceEventRecord({
    required this.id,
    required this.zoneId,
    required this.userId,
    required this.eventType,
    required this.timestamp,
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    this.metadata,
  });

  /// Create from geofence event
  factory GeofenceEventRecord.fromGeofenceEvent({
    required String id,
    required String userId,
    required GeofenceEvent event,
    Map<String, dynamic>? metadata,
  }) {
    return GeofenceEventRecord(
      id: id,
      zoneId: event.zoneId,
      userId: userId,
      eventType: event.type,
      timestamp: event.timestamp,
      latitude: event.position.latitude,
      longitude: event.position.longitude,
      accuracy: event.accuracy,
      metadata: metadata,
    );
  }

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'zoneId': zoneId,
      'userId': userId,
      'eventType': eventType.name,
      'timestamp': timestamp.toIso8601String(),
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'metadata': metadata,
    };
  }

  /// Create from map
  factory GeofenceEventRecord.fromMap(Map<String, dynamic> map) {
    return GeofenceEventRecord(
      id: map['id'] as String,
      zoneId: map['zoneId'] as String,
      userId: map['userId'] as String,
      eventType: GeofenceEventType.values.firstWhere(
        (e) => e.name == map['eventType'],
      ),
      timestamp: DateTime.parse(map['timestamp'] as String),
      latitude: map['latitude'] as double,
      longitude: map['longitude'] as double,
      accuracy: map['accuracy'] as double,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Check if this is an entry event
  bool get isEntry => eventType == GeofenceEventType.enter;

  /// Check if this is an exit event
  bool get isExit => eventType == GeofenceEventType.exit;

  /// Get formatted timestamp
  String get formattedTimestamp {
    return timestamp.toLocal().toString();
  }

  @override
  String toString() {
    return 'GeofenceEventRecord(id: $id, zoneId: $zoneId, type: $eventType, timestamp: $timestamp)';
  }
}

/// Result of validation criteria check
class ValidationCriteriaResult {
  final String zoneId;
  final String userId;
  final bool criteriaMetOverall;
  final Map<String, ValidationCriterion> criteria;
  final DateTime checkedAt;
  final String? message;
  final Map<String, dynamic>? metadata;

  const ValidationCriteriaResult({
    required this.zoneId,
    required this.userId,
    required this.criteriaMetOverall,
    required this.criteria,
    required this.checkedAt,
    this.message,
    this.metadata,
  });

  /// Get specific criterion result
  ValidationCriterion? getCriterion(String key) {
    return criteria[key];
  }

  /// Get all met criteria
  List<ValidationCriterion> get metCriteria {
    return criteria.values.where((criterion) => criterion.isMet).toList();
  }

  /// Get all unmet criteria
  List<ValidationCriterion> get unmetCriteria {
    return criteria.values.where((criterion) => !criterion.isMet).toList();
  }

  /// Get completion percentage
  double get completionPercentage {
    if (criteria.isEmpty) return 0.0;
    final metCount = metCriteria.length;
    return (metCount / criteria.length) * 100;
  }

  /// Get summary message
  String get summaryMessage {
    if (message != null) return message!;
    
    if (criteriaMetOverall) {
      return 'All validation criteria met';
    } else {
      final unmet = unmetCriteria.length;
      final total = criteria.length;
      return '$unmet of $total criteria not yet met';
    }
  }

  @override
  String toString() {
    return 'ValidationCriteriaResult(zoneId: $zoneId, met: $criteriaMetOverall, completion: ${completionPercentage.toStringAsFixed(1)}%)';
  }
}

/// Individual validation criterion
class ValidationCriterion {
  final String key;
  final String name;
  final String description;
  final bool isMet;
  final bool isRequired;
  final double? progress;
  final String? currentValue;
  final String? targetValue;
  final DateTime? lastUpdated;
  final Map<String, dynamic>? metadata;

  const ValidationCriterion({
    required this.key,
    required this.name,
    required this.description,
    required this.isMet,
    this.isRequired = true,
    this.progress,
    this.currentValue,
    this.targetValue,
    this.lastUpdated,
    this.metadata,
  });

  /// Create a presence time criterion
  factory ValidationCriterion.presenceTime({
    required Duration currentTime,
    required Duration requiredTime,
    DateTime? lastUpdated,
  }) {
    final progress = currentTime.inMinutes / requiredTime.inMinutes;
    final isMet = currentTime >= requiredTime;

    return ValidationCriterion(
      key: 'presence_time',
      name: 'Presence Time',
      description: 'Required time spent in zone',
      isMet: isMet,
      progress: progress.clamp(0.0, 1.0),
      currentValue: '${currentTime.inMinutes} minutes',
      targetValue: '${requiredTime.inMinutes} minutes',
      lastUpdated: lastUpdated,
    );
  }

  /// Create a presence hours criterion
  factory ValidationCriterion.presenceHours({
    required int currentDays,
    required int requiredDays,
    DateTime? lastUpdated,
  }) {
    final progress = currentDays / requiredDays;
    final isMet = currentDays >= requiredDays;

    return ValidationCriterion(
      key: 'presence_hours',
      name: 'Presence Hours',
      description: 'Required days with presence hours met',
      isMet: isMet,
      progress: progress.clamp(0.0, 1.0),
      currentValue: '$currentDays days',
      targetValue: '$requiredDays days',
      lastUpdated: lastUpdated,
    );
  }

  /// Create a location accuracy criterion
  factory ValidationCriterion.locationAccuracy({
    required double currentAccuracy,
    required double requiredAccuracy,
    DateTime? lastUpdated,
  }) {
    final isMet = currentAccuracy <= requiredAccuracy;

    return ValidationCriterion(
      key: 'location_accuracy',
      name: 'Location Accuracy',
      description: 'Required GPS accuracy for validation',
      isMet: isMet,
      currentValue: '${currentAccuracy.toStringAsFixed(1)}m',
      targetValue: '≤${requiredAccuracy.toStringAsFixed(1)}m',
      lastUpdated: lastUpdated,
    );
  }

  /// Get progress percentage
  double get progressPercentage {
    return (progress ?? (isMet ? 1.0 : 0.0)) * 100;
  }

  /// Get status display text
  String get statusText {
    if (isMet) return 'Met';
    if (progress != null && progress! > 0) {
      return '${progressPercentage.toStringAsFixed(0)}%';
    }
    return 'Not Met';
  }

  @override
  String toString() {
    return 'ValidationCriterion(key: $key, name: $name, met: $isMet, progress: ${progressPercentage.toStringAsFixed(1)}%)';
  }
}

/// Validation progress tracking
class ValidationProgress {
  final String zoneId;
  final String userId;
  final DateTime startedAt;
  final DateTime? completedAt;
  final Map<String, dynamic> progressData;
  final List<GeofenceEventRecord> events;

  const ValidationProgress({
    required this.zoneId,
    required this.userId,
    required this.startedAt,
    this.completedAt,
    required this.progressData,
    required this.events,
  });

  /// Check if validation is completed
  bool get isCompleted => completedAt != null;

  /// Get total duration
  Duration get totalDuration {
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt);
  }

  /// Get time spent in zone
  Duration get timeInZone {
    Duration total = Duration.zero;
    DateTime? entryTime;

    for (final event in events) {
      if (event.isEntry) {
        entryTime = event.timestamp;
      } else if (event.isExit && entryTime != null) {
        total += event.timestamp.difference(entryTime);
        entryTime = null;
      }
    }

    // If still in zone, add current time
    if (entryTime != null) {
      total += DateTime.now().difference(entryTime);
    }

    return total;
  }

  /// Get number of zone entries
  int get entryCount {
    return events.where((event) => event.isEntry).length;
  }

  /// Get number of zone exits
  int get exitCount {
    return events.where((event) => event.isExit).length;
  }

  /// Check if currently in zone
  bool get isCurrentlyInZone {
    if (events.isEmpty) return false;
    return events.last.isEntry;
  }
}
