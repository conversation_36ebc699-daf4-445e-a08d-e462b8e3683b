import 'package:equatable/equatable.dart';

/// Entity for managing limited location tracking configuration and usage
class LimitedTrackingEntity extends Equatable {
  final String userId;
  final DateTime? firstZoneAdded; // Changed from firstAppUsage to firstZoneAdded
  final int validationAttemptsUsed;
  final List<ValidationAttempt> attempts;
  final bool isWithinValidationWindow;
  final bool canPerformValidation;

  const LimitedTrackingEntity({
    required this.userId,
    this.firstZoneAdded, // Can be null if no zones added yet
    required this.validationAttemptsUsed,
    required this.attempts,
    required this.isWithinValidationWindow,
    required this.canPerformValidation,
  });

  /// Check if user is within the 3-day validation window (from first zone added)
  bool get isInValidationWindow {
    if (firstZoneAdded == null) return false; // No zones added yet
    final now = DateTime.now();
    final windowEnd = firstZoneAdded!.add(const Duration(days: 3));
    return now.isBefore(windowEnd);
  }

  /// Check if user has remaining validation attempts
  bool get hasRemainingAttempts {
    return validationAttemptsUsed < 5;
  }

  /// Check if user can perform a validation attempt
  bool get canAttemptValidation {
    return isInValidationWindow && hasRemainingAttempts;
  }

  /// Get remaining validation attempts
  int get remainingAttempts {
    return 5 - validationAttemptsUsed;
  }

  /// Get days remaining in validation window
  int get daysRemainingInWindow {
    if (!isInValidationWindow || firstZoneAdded == null) return 0;
    final now = DateTime.now();
    final windowEnd = firstZoneAdded!.add(const Duration(days: 3));
    return windowEnd.difference(now).inDays;
  }

  /// Create a copy with updated validation attempt
  LimitedTrackingEntity copyWithNewAttempt(ValidationAttempt attempt) {
    return LimitedTrackingEntity(
      userId: userId,
      firstZoneAdded: firstZoneAdded,
      validationAttemptsUsed: validationAttemptsUsed + 1,
      attempts: [...attempts, attempt],
      isWithinValidationWindow: isInValidationWindow,
      canPerformValidation: canAttemptValidation,
    );
  }

  /// Create initial tracking entity for new user (no zones added yet)
  factory LimitedTrackingEntity.initial(String userId) {
    return LimitedTrackingEntity(
      userId: userId,
      firstZoneAdded: null, // No zones added yet
      validationAttemptsUsed: 0,
      attempts: [],
      isWithinValidationWindow: false, // False until first zone is added
      canPerformValidation: false, // False until first zone is added
    );
  }

  /// Create a copy when first zone is added (starts the 3-day window)
  LimitedTrackingEntity copyWithFirstZone() {
    if (firstZoneAdded != null) return this; // Already has zones

    final now = DateTime.now();
    return LimitedTrackingEntity(
      userId: userId,
      firstZoneAdded: now, // Start the 3-day validation window
      validationAttemptsUsed: validationAttemptsUsed,
      attempts: attempts,
      isWithinValidationWindow: true, // Now within validation window
      canPerformValidation: true, // Can now perform validation
    );
  }

  /// Convert to map for Firestore storage
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'firstZoneAdded': firstZoneAdded?.toIso8601String(),
      'validationAttemptsUsed': validationAttemptsUsed,
      'attempts': attempts.map((attempt) => attempt.toMap()).toList(),
      'isWithinValidationWindow': isWithinValidationWindow,
      'canPerformValidation': canPerformValidation,
    };
  }

  /// Create from Firestore map
  factory LimitedTrackingEntity.fromMap(Map<String, dynamic> map) {
    return LimitedTrackingEntity(
      userId: map['userId'] ?? '',
      firstZoneAdded: map['firstZoneAdded'] != null
          ? DateTime.parse(map['firstZoneAdded'])
          : null,
      validationAttemptsUsed: map['validationAttemptsUsed'] ?? 0,
      attempts: (map['attempts'] as List<dynamic>?)
          ?.map((attemptMap) => ValidationAttempt.fromMap(attemptMap as Map<String, dynamic>))
          .toList() ?? [],
      isWithinValidationWindow: map['isWithinValidationWindow'] ?? false,
      canPerformValidation: map['canPerformValidation'] ?? false,
    );
  }

  @override
  List<Object?> get props => [
    userId,
    firstZoneAdded,
    validationAttemptsUsed,
    attempts,
    isWithinValidationWindow,
    canPerformValidation,
  ];
}

/// Individual validation attempt record
class ValidationAttempt extends Equatable {
  final String id;
  final String zoneId;
  final DateTime timestamp;
  final Duration sampleDuration;
  final ValidationAttemptResult result;
  final String? errorMessage;

  const ValidationAttempt({
    required this.id,
    required this.zoneId,
    required this.timestamp,
    required this.sampleDuration,
    required this.result,
    this.errorMessage,
  });

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'zoneId': zoneId,
      'timestamp': timestamp.toIso8601String(),
      'sampleDuration': sampleDuration.inSeconds,
      'result': result.name,
      'errorMessage': errorMessage,
    };
  }

  /// Create from map
  factory ValidationAttempt.fromMap(Map<String, dynamic> map) {
    return ValidationAttempt(
      id: map['id'] ?? '',
      zoneId: map['zoneId'] ?? '',
      timestamp: DateTime.parse(map['timestamp'] ?? DateTime.now().toIso8601String()),
      sampleDuration: Duration(seconds: map['sampleDuration'] ?? 5),
      result: ValidationAttemptResult.values.firstWhere(
        (e) => e.name == map['result'],
        orElse: () => ValidationAttemptResult.failed,
      ),
      errorMessage: map['errorMessage'],
    );
  }

  @override
  List<Object?> get props => [id, zoneId, timestamp, sampleDuration, result, errorMessage];
}

/// Result of a validation attempt
enum ValidationAttemptResult {
  success,
  failed,
  locationUnavailable,
  permissionDenied,
  timeout,
}
