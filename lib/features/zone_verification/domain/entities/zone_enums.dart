import 'package:flutter/material.dart';

enum ZoneType {
  home,
  work,
  university,
  other,
}

enum ZoneStatus {
  pending,
  validated,
  rejected,
}

enum ValidationMethod {
  social, // Secure QR-based proximity validation
  automatic,
}

enum ValidationEntityType {
  zone,
}

enum ValidationType {
  community, // Secure QR-based proximity validation
  automatic,
}

enum PresenceHourType {
  fullTime,
  partTime,
  mornings,
  afternoons,
  evenings,
  weekends,
  custom,
}

/// QR validation session status
enum QRValidationStatus {
  pending,
  active,
  completed,
  expired,
  failed,
  cancelled,
}

/// QR token status
enum QRTokenStatus {
  active,
  expired,
  used,
  invalid,
}

/// QR validation event types
enum QRValidationEventType {
  sessionCreated,
  tokenGenerated,
  tokenScanned,
  proximityVerified,
  validationCompleted,
  validationFailed,
  sessionExpired,
}

extension ZoneTypeExtension on ZoneType {
  
  String get svgIcon {
    switch (this) {
      case ZoneType.home:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
</svg>''';
      case ZoneType.work:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 21h16.5M4.5 3h15l.75 18H3.75L4.5 3ZM9 8.25h6M9 11.25h6m-6 3h6m-6 3h4.5" />
</svg>''';
      case ZoneType.university:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443a55.381 55.381 0 0 1 5.25 2.882V15" />
</svg>''';
      case ZoneType.other:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
  <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
</svg>''';
    }
  }
}

extension PresenceHourTypeExtension on PresenceHourType {
  String get label {
    switch (this) {
      case PresenceHourType.fullTime:
        return 'Full Time';
      case PresenceHourType.partTime:
        return 'Part Time';
      case PresenceHourType.mornings:
        return 'Mornings';
      case PresenceHourType.afternoons:
        return 'Afternoons';
      case PresenceHourType.evenings:
        return 'Evenings';
      case PresenceHourType.weekends:
        return 'Weekends';
      case PresenceHourType.custom:
        return 'Custom';
    }
  }

  String get timeRange {
    switch (this) {
      case PresenceHourType.fullTime:
        return '09:00 - 17:00';
      case PresenceHourType.partTime:
        return '09:00 - 13:00';
      case PresenceHourType.mornings:
        return '06:00 - 12:00';
      case PresenceHourType.afternoons:
        return '12:00 - 18:00';
      case PresenceHourType.evenings:
        return '18:00 - 22:00';
      case PresenceHourType.weekends:
        return '10:00 - 16:00';
      case PresenceHourType.custom:
        return '';
    }
  }

  /// Professional SVG icon data for each presence hour type
  String get svgIcon {
    switch (this) {
      case PresenceHourType.fullTime:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
</svg>''';
      case PresenceHourType.partTime:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6l4 2m6-2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
</svg>''';
      case PresenceHourType.mornings:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
</svg>''';
      case PresenceHourType.afternoons:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
</svg>''';
      case PresenceHourType.evenings:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
</svg>''';
      case PresenceHourType.weekends:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5a2.25 2.25 0 0 1 21 9v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008ZM16.5 15h.008v.008H16.5V15Zm0 2.25h.008v.008H16.5v-.008ZM14.25 15h.008v.008H14.25V15Zm0 2.25h.008v.008H14.25v-.008Z" />
</svg>''';
      case PresenceHourType.custom:
        return '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
</svg>''';
    }
  }

  Color get color {
    switch (this) {
      case PresenceHourType.fullTime:
        return const Color(0xFF2196F3); // Professional blue
      case PresenceHourType.partTime:
        return const Color(0xFF4CAF50); // Professional green
      case PresenceHourType.mornings:
        return const Color(0xFFFF9800); // Morning orange
      case PresenceHourType.afternoons:
        return const Color(0xFFF44336); // Afternoon red
      case PresenceHourType.evenings:
        return const Color(0xFF9C27B0); // Evening purple
      case PresenceHourType.weekends:
        return const Color(0xFF607D8B); // Weekend blue-gray
      case PresenceHourType.custom:
        return const Color(0xFF795548); // Custom brown
    }
  }
}

extension ZoneStatusExtension on ZoneStatus {
  bool get isValidated => this == ZoneStatus.validated;
  bool get isPending => this == ZoneStatus.pending;
  bool get isRejected => this == ZoneStatus.rejected;
}
