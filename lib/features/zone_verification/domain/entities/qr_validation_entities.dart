import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

/// Entity representing a QR validation session between two users
class QRValidationSessionEntity extends Equatable {
  final String id;
  final String zoneId;
  final String initiatorUserId;
  final String? validatorUserId;
  final QRValidationStatus status;
  final DateTime createdAt;
  final DateTime expiresAt;
  final DateTime? completedAt;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  const QRValidationSessionEntity({
    required this.id,
    required this.zoneId,
    required this.initiatorUserId,
    this.validatorUserId,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
    this.completedAt,
    this.failureReason,
    this.metadata,
  });

  /// Create a new validation session
  factory QRValidationSessionEntity.create({
    required String id,
    required String zoneId,
    required String initiatorUserId,
    Duration sessionDuration = const Duration(minutes: 10),
  }) {
    final now = DateTime.now();
    return QRValidationSessionEntity(
      id: id,
      zoneId: zoneId,
      initiatorUserId: initiatorUserId,
      status: QRValidationStatus.pending,
      createdAt: now,
      expiresAt: now.add(sessionDuration),
    );
  }

  /// Check if the session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if the session is active
  bool get isActive => status == QRValidationStatus.active && !isExpired;

  /// Check if the session can be joined by a validator
  bool canBeJoined(String userId) {
    return status == QRValidationStatus.pending &&
           !isExpired &&
           initiatorUserId != userId &&
           validatorUserId == null;
  }

  /// Join the session as a validator
  QRValidationSessionEntity joinAsValidator(String validatorUserId) {
    return copyWith(
      validatorUserId: validatorUserId,
      status: QRValidationStatus.active,
    );
  }

  /// Complete the validation session successfully
  QRValidationSessionEntity complete() {
    return copyWith(
      status: QRValidationStatus.completed,
      completedAt: DateTime.now(),
    );
  }

  /// Fail the validation session
  QRValidationSessionEntity fail(String reason) {
    return copyWith(
      status: QRValidationStatus.failed,
      completedAt: DateTime.now(),
      failureReason: reason,
    );
  }

  /// Expire the validation session
  QRValidationSessionEntity expire() {
    return copyWith(
      status: QRValidationStatus.expired,
      completedAt: DateTime.now(),
    );
  }

  /// Cancel the validation session
  QRValidationSessionEntity cancel() {
    return copyWith(
      status: QRValidationStatus.cancelled,
      completedAt: DateTime.now(),
    );
  }

  QRValidationSessionEntity copyWith({
    String? id,
    String? zoneId,
    String? initiatorUserId,
    String? validatorUserId,
    QRValidationStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return QRValidationSessionEntity(
      id: id ?? this.id,
      zoneId: zoneId ?? this.zoneId,
      initiatorUserId: initiatorUserId ?? this.initiatorUserId,
      validatorUserId: validatorUserId ?? this.validatorUserId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        zoneId,
        initiatorUserId,
        validatorUserId,
        status,
        createdAt,
        expiresAt,
        completedAt,
        failureReason,
        metadata,
      ];
}

/// Entity representing an ephemeral QR token for validation
class QRTokenEntity extends Equatable {
  final String id;
  final String sessionId;
  final String userId;
  final String encryptedData;
  final QRTokenStatus status;
  final DateTime createdAt;
  final DateTime expiresAt;
  final DateTime? usedAt;
  final String? usedByUserId;
  final Map<String, dynamic>? metadata;

  const QRTokenEntity({
    required this.id,
    required this.sessionId,
    required this.userId,
    required this.encryptedData,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
    this.usedAt,
    this.usedByUserId,
    this.metadata,
  });

  /// Create a new QR token
  factory QRTokenEntity.create({
    required String id,
    required String sessionId,
    required String userId,
    required String encryptedData,
    Duration tokenDuration = const Duration(seconds: 45),
  }) {
    final now = DateTime.now();
    return QRTokenEntity(
      id: id,
      sessionId: sessionId,
      userId: userId,
      encryptedData: encryptedData,
      status: QRTokenStatus.active,
      createdAt: now,
      expiresAt: now.add(tokenDuration),
    );
  }

  /// Create a new QR token for zone validation (without session)
  factory QRTokenEntity.createForZone({
    required String id,
    required String zoneId,
    required String userId,
    required String encryptedData,
    Duration tokenDuration = const Duration(seconds: 45),
  }) {
    final now = DateTime.now();
    return QRTokenEntity(
      id: id,
      sessionId: zoneId, // Use zoneId as sessionId for compatibility
      userId: userId,
      encryptedData: encryptedData,
      status: QRTokenStatus.active,
      createdAt: now,
      expiresAt: now.add(tokenDuration),
    );
  }

  /// Check if the token is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if the token is valid for use
  bool get isValid => status == QRTokenStatus.active && !isExpired;

  /// Check if the token can be used by a specific user
  bool canBeUsedBy(String scannerUserId) {
    return isValid && userId != scannerUserId;
  }

  /// Mark the token as used
  QRTokenEntity markAsUsed(String usedByUserId) {
    return copyWith(
      status: QRTokenStatus.used,
      usedAt: DateTime.now(),
      usedByUserId: usedByUserId,
    );
  }

  /// Mark the token as expired
  QRTokenEntity markAsExpired() {
    return copyWith(status: QRTokenStatus.expired);
  }

  /// Mark the token as invalid
  QRTokenEntity markAsInvalid() {
    return copyWith(status: QRTokenStatus.invalid);
  }

  QRTokenEntity copyWith({
    String? id,
    String? sessionId,
    String? userId,
    String? encryptedData,
    QRTokenStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? usedAt,
    String? usedByUserId,
    Map<String, dynamic>? metadata,
  }) {
    return QRTokenEntity(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      userId: userId ?? this.userId,
      encryptedData: encryptedData ?? this.encryptedData,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      usedAt: usedAt ?? this.usedAt,
      usedByUserId: usedByUserId ?? this.usedByUserId,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        sessionId,
        userId,
        encryptedData,
        status,
        createdAt,
        expiresAt,
        usedAt,
        usedByUserId,
        metadata,
      ];
}

/// Entity representing location data for proximity verification
class ProximityDataEntity extends Equatable {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;

  const ProximityDataEntity({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });

  /// Calculate distance to another location in meters
  double distanceTo(ProximityDataEntity other) {
    return Geolocator.distanceBetween(
      latitude,
      longitude,
      other.latitude,
      other.longitude,
    );
  }

  /// Check if this location is within proximity range of another location
  bool isWithinProximity(ProximityDataEntity other, double maxDistanceMeters) {
    return distanceTo(other) <= maxDistanceMeters;
  }

  @override
  List<Object?> get props => [latitude, longitude, accuracy, timestamp];
}
