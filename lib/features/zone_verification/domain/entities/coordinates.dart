import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';

class Coordinates extends Equatable {
  final double latitude;
  final double longitude;

  const Coordinates({
    required this.latitude,
    required this.longitude,
  });

  /// Calculate distance to another coordinate in meters
  double distanceTo(Coordinates other) {
    return Geolocator.distanceBetween(
      latitude,
      longitude,
      other.latitude,
      other.longitude,
    );
  }

  /// Check if this coordinate is within a certain radius of another coordinate
  bool isWithinRadius(Coordinates center, double radiusInMeters) {
    return distanceTo(center) <= radiusInMeters;
  }

  /// Create a copy with new values
  Coordinates copyWith({
    double? latitude,
    double? longitude,
  }) {
    return Coordinates(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  /// Convert to a map for serialization
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  /// Create from a map
  factory Coordinates.fromMap(Map<String, dynamic> map) {
    return Coordinates(
      latitude: (map['latitude'] as num).toDouble(),
      longitude: (map['longitude'] as num).toDouble(),
    );
  }

  @override
  List<Object?> get props => [latitude, longitude];

  @override
  String toString() => 'Coordinates(lat: $latitude, lng: $longitude)';

  /// Check if coordinates are valid
  bool get isValid {
    return latitude >= -90 && 
           latitude <= 90 && 
           longitude >= -180 && 
           longitude <= 180;
  }

  /// Empty coordinates constant
  static const empty = Coordinates(latitude: 0.0, longitude: 0.0);
  
  bool get isEmpty => this == Coordinates.empty;
  bool get isNotEmpty => this != Coordinates.empty;
}
