
import 'package:geolocator/geolocator.dart';

/// Constants for zone verification feature
class ZoneConstants {
  // Zone radius in meters - always 600m for all zones
  static const double zoneRadiusMeters = 600.0;
  
  // Community validation requirements
  static const int requiredCommunityValidations = 3;
  static const int maxCommunityValidationsPerUser = 5; // Per day
  
  // Zone limits per user
  static const int maxZonesPerUser = 10;
  
  // Location accuracy requirements
  static const double minimumLocationAccuracy = 50.0; // meters
  static const Duration locationUpdateInterval = Duration(minutes: 5);
  
  // Validation timeouts
  static const Duration validationTimeout = Duration(days: 30);
  static const Duration communityValidationWindow = Duration(days: 7);

  // QR Validation constants
  static const Duration qrSessionDuration = Duration(minutes: 10);
  static const Duration qrTokenDuration = Duration(seconds: 45);
  static const Duration qrTokenRefreshInterval = Duration(seconds: 5);
  static const double qrProximityDistanceMeters = 100.0;
  static const int maxQRSessionsPerUser = 3;
  static const int maxQRValidationAttemptsPerDay = 10;
  
  // Firestore collection names
  static const String zonesCollection = 'zones';
  static const String zoneValidationsCollection = 'zone_validations';
  static const String zoneUsersCollection = 'zone_users';

  // QR Validation collections (Realtime Database paths)
  static const String qrSessionsPath = 'qr_validation_sessions';
  static const String qrTokensPath = 'qr_tokens';
  static const String qrEventsPath = 'qr_validation_events';
  
  // Error messages
  static const String errorLocationPermissionDenied = 'Permisos de ubicación denegados';
  static const String errorLocationServiceDisabled = 'Servicios de ubicación deshabilitados';
  static const String errorInaccurateLocation = 'Ubicación no es lo suficientemente precisa';
  static const String errorZoneLimitReached = 'Has alcanzado el límite máximo de zonas';
  static const String errorAlreadyValidated = 'Ya has validado esta zona';
  static const String errorCannotValidateOwnZone = 'No puedes validar tu propia zona';
  static const String errorUserNotValidated = 'Debes estar verificado para validar zonas';
  
  // Success messages
  static const String successZoneCreated = 'Zona creada exitosamente';
  static const String successZoneValidated = 'Zona validada exitosamente';
  static const String successValidationSubmitted = 'Validación enviada exitosamente';
}

/// Zone validation rules and business logic
class ZoneValidationRules {
  /// Check if a user can create a new zone
  static bool canCreateZone({
    required int currentZoneCount,
    required bool hasLocationPermission,
    required bool isLocationAccurate,
  }) {
    return currentZoneCount < ZoneConstants.maxZonesPerUser &&
           hasLocationPermission &&
           isLocationAccurate;
  }
  
  /// Check if a user can validate a zone
  static bool canValidateZone({
    required bool isUserValidated,
    required bool isOwnZone,
    required bool alreadyValidated,
    required int dailyValidationCount,
  }) {
    return isUserValidated &&
           !isOwnZone &&
           !alreadyValidated &&
           dailyValidationCount < ZoneConstants.maxCommunityValidationsPerUser;
  }
  
  /// Check if a zone has enough validations to be approved
  static bool hasEnoughValidations(int validationCount) {
    return validationCount >= ZoneConstants.requiredCommunityValidations;
  }
  
  /// Check if a location is within zone radius
  static bool isLocationInZone({
    required double userLat,
    required double userLng,
    required double zoneLat,
    required double zoneLng,
  }) {
    final double distance = Geolocator.distanceBetween(
      userLat,
      userLng,
      zoneLat,
      zoneLng,
    );

    return distance <= ZoneConstants.zoneRadiusMeters;
  }
}
