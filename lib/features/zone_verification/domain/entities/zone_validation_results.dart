import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Result of zone validation operation
class ZoneValidationResult extends Equatable {
  final ZoneEntity zone;
  final bool validationAdded;
  final String validatorUserId;
  final DateTime validatedAt;
  final bool isZoneNowValidated;
  final int currentValidationCount;
  final int requiredValidationCount;
  final int remainingValidations;

  const ZoneValidationResult({
    required this.zone,
    required this.validationAdded,
    required this.validatorUserId,
    required this.validatedAt,
    required this.isZoneNowValidated,
    required this.currentValidationCount,
    required this.requiredValidationCount,
    required this.remainingValidations,
  });

  /// Get validation progress (0.0 to 1.0)
  double get progress => (currentValidationCount / requiredValidationCount).clamp(0.0, 1.0);

  @override
  List<Object?> get props => [
        zone,
        validationAdded,
        validatorUserId,
        validatedAt,
        isZoneNowValidated,
        currentValidationCount,
        requiredValidationCount,
        remainingValidations,
      ];
}

/// Result of automatic validation operation
class AutomaticValidationResult extends Equatable {
  final ZoneEntity zone;
  final bool isEnabled;
  final bool requiresLocationPermission;
  final bool locationPermissionGranted;
  final bool locationServiceEnabled;
  final bool locationAccuracyMet;
  final bool monitoringStarted;
  final Duration estimatedValidationTime;
  final DateTime nextLocationCheck;

  const AutomaticValidationResult({
    required this.zone,
    required this.isEnabled,
    required this.requiresLocationPermission,
    required this.locationPermissionGranted,
    required this.locationServiceEnabled,
    required this.locationAccuracyMet,
    required this.monitoringStarted,
    required this.estimatedValidationTime,
    required this.nextLocationCheck,
  });

  /// Check if all requirements are met
  bool get allRequirementsMet =>
      locationPermissionGranted && locationServiceEnabled && locationAccuracyMet;

  /// Get status message
  String get statusMessage {
    if (!locationPermissionGranted) {
      return 'Location permission required';
    }
    if (!locationServiceEnabled) {
      return 'Location services must be enabled';
    }
    if (!locationAccuracyMet) {
      return 'Location accuracy insufficient';
    }
    if (monitoringStarted) {
      return 'Automatic validation monitoring started';
    }
    return 'Automatic validation enabled';
  }

  @override
  List<Object?> get props => [
        zone,
        isEnabled,
        requiresLocationPermission,
        locationPermissionGranted,
        locationServiceEnabled,
        locationAccuracyMet,
        monitoringStarted,
        estimatedValidationTime,
        nextLocationCheck,
      ];
}

/// Social validation request entity
class SocialValidationRequest extends Equatable {
  final String id;
  final String zoneId;
  final String requesterId;
  final String zoneName;
  final String zoneAddress;
  final ZoneType zoneType;
  final String validationUrl;
  final String qrCodeData;
  final DateTime expiresAt;
  final DateTime createdAt;
  final int currentValidationCount;
  final int requiredValidationCount;

  const SocialValidationRequest({
    required this.id,
    required this.zoneId,
    required this.requesterId,
    required this.zoneName,
    required this.zoneAddress,
    required this.zoneType,
    required this.validationUrl,
    required this.qrCodeData,
    required this.expiresAt,
    required this.createdAt,
    required this.currentValidationCount,
    required this.requiredValidationCount,
  });

  /// Check if validation request is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Get validation progress (0.0 to 1.0)
  double get progress => (currentValidationCount / requiredValidationCount).clamp(0.0, 1.0);

  /// Check if validation is complete
  bool get isComplete => currentValidationCount >= requiredValidationCount;

  /// Get remaining validations needed
  int get remainingValidations => (requiredValidationCount - currentValidationCount).clamp(0, requiredValidationCount);

  @override
  List<Object?> get props => [
        id,
        zoneId,
        requesterId,
        zoneName,
        zoneAddress,
        zoneType,
        validationUrl,
        qrCodeData,
        expiresAt,
        createdAt,
        currentValidationCount,
        requiredValidationCount,
      ];
}
