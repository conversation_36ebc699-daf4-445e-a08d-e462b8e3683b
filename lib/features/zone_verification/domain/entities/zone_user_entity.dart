import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';

/// User entity specifically for zone management functionality
/// This is separate from identity validation to avoid mixing concerns
class ZoneUserEntity extends Equatable {
  final String id;
  final UserStatus validationStatus;
  final List<String> definedZoneIds;
  final String? currentZoneId; // Zone user is currently in
  final DateTime? lastLocationUpdate;
  final Map<String, dynamic>? metadata;

  const ZoneUserEntity({
    required this.id,
    this.validationStatus = UserStatus.unverified,
    this.definedZoneIds = const [],
    this.currentZoneId,
    this.lastLocationUpdate,
    this.metadata,
  });

  /// Define a new zone for this user
  ZoneUserEntity defineZone(ZoneEntity zone) {
    if (definedZoneIds.contains(zone.id)) {
      return this; // Zone already defined
    }

    final newDefinedZones = List<String>.from(definedZoneIds)..add(zone.id);
    
    return copyWith(
      definedZoneIds: newDefinedZones,
    );
  }

  /// Request zone validation for a specific zone
  ZoneUserEntity requestZoneValidation(ZoneEntity zone) {
    // This method would trigger the validation process
    // The actual validation logic would be handled by use cases
    return this;
  }

  /// Update the user's current zone based on location
  ZoneUserEntity updateCurrentZone(String? zoneId) {
    return copyWith(
      currentZoneId: zoneId,
      lastLocationUpdate: DateTime.now(),
    );
  }

  /// Remove a zone from user's defined zones
  ZoneUserEntity removeZone(String zoneId) {
    final newDefinedZones = List<String>.from(definedZoneIds)..remove(zoneId);
    
    return copyWith(
      definedZoneIds: newDefinedZones,
      currentZoneId: currentZoneId == zoneId ? null : currentZoneId,
    );
  }

  /// Update user's validation status
  ZoneUserEntity updateValidationStatus(UserStatus status) {
    return copyWith(validationStatus: status);
  }

  /// Check if user has defined any zones
  bool get hasDefinedZones => definedZoneIds.isNotEmpty;

  /// Check if user is currently in a zone
  bool get isInZone => currentZoneId != null;

  /// Check if user can define new zones
  bool get canDefineZones {
    // Users can define zones regardless of validation status
    // but validation affects zone validation process
    return true;
  }

  /// Check if user can validate other zones
  bool get canValidateZones {
    return validationStatus == UserStatus.validated;
  }

  /// Get the number of zones defined by this user
  int get definedZoneCount => definedZoneIds.length;

  /// Check if user has reached maximum zone limit
  bool get hasReachedZoneLimit {
    const int maxZones = 10; // Configurable limit
    return definedZoneCount >= maxZones;
  }

  ZoneUserEntity copyWith({
    String? id,
    UserStatus? validationStatus,
    List<String>? definedZoneIds,
    String? currentZoneId,
    DateTime? lastLocationUpdate,
    Map<String, dynamic>? metadata,
  }) {
    return ZoneUserEntity(
      id: id ?? this.id,
      validationStatus: validationStatus ?? this.validationStatus,
      definedZoneIds: definedZoneIds ?? this.definedZoneIds,
      currentZoneId: currentZoneId ?? this.currentZoneId,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        validationStatus,
        definedZoneIds,
        currentZoneId,
        lastLocationUpdate,
        metadata,
      ];

  static const empty = ZoneUserEntity(id: '');

  bool get isEmpty => this == ZoneUserEntity.empty;
  bool get isNotEmpty => this != ZoneUserEntity.empty;
  bool get isValidated => validationStatus == UserStatus.validated;
  bool get isPendingValidation => 
      validationStatus == UserStatus.pendingId || 
      validationStatus == UserStatus.pendingCommunity;
}
