import 'package:equatable/equatable.dart';

/// Represents a specific time block for presence hours
class PresenceTimeBlock extends Equatable {
  final String id;
  final PresenceTimeOfDay startTime;
  final PresenceTimeOfDay endTime;
  final Set<DayOfWeek> activeDays;
  final bool isEnabled;
  final String? label;

  const PresenceTimeBlock({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.activeDays,
    this.isEnabled = true,
    this.label,
  });

  /// Create a time block from time strings (HH:mm format)
  factory PresenceTimeBlock.fromTimeStrings({
    required String id,
    required String startTime,
    required String endTime,
    required Set<DayOfWeek> activeDays,
    bool isEnabled = true,
    String? label,
  }) {
    return PresenceTimeBlock(
      id: id,
      startTime: PresenceTimeOfDay.fromString(startTime),
      endTime: PresenceTimeOfDay.fromString(endTime),
      activeDays: activeDays,
      isEnabled: isEnabled,
      label: label,
    );
  }

  /// Check if this time block is active for a given DateTime
  bool isActiveAt(DateTime dateTime) {
    if (!isEnabled) return false;
    
    final dayOfWeek = DayOfWeek.fromDateTime(dateTime);
    if (!activeDays.contains(dayOfWeek)) return false;
    
    final currentTime = PresenceTimeOfDay.fromDateTime(dateTime);
    return currentTime.isWithinRange(startTime, endTime);
  }

  /// Get duration of this time block in minutes
  int get durationInMinutes {
    return endTime.differenceInMinutes(startTime);
  }

  /// Get formatted time range string
  String get timeRangeString {
    return '${startTime.format24Hour()} - ${endTime.format24Hour()}';
  }

  /// Get formatted days string
  String get daysString {
    if (activeDays.length == 7) return 'Every day';
    if (activeDays.containsAll(DayOfWeek.weekdays)) return 'Weekdays';
    if (activeDays.containsAll(DayOfWeek.weekends)) return 'Weekends';
    
    final sortedDays = activeDays.toList()..sort((a, b) => a.index.compareTo(b.index));
    return sortedDays.map((day) => day.shortName).join(', ');
  }

  PresenceTimeBlock copyWith({
    String? id,
    PresenceTimeOfDay? startTime,
    PresenceTimeOfDay? endTime,
    Set<DayOfWeek>? activeDays,
    bool? isEnabled,
    String? label,
  }) {
    return PresenceTimeBlock(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      activeDays: activeDays ?? this.activeDays,
      isEnabled: isEnabled ?? this.isEnabled,
      label: label ?? this.label,
    );
  }

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'startTime': startTime.toString(),
      'endTime': endTime.toString(),
      'activeDays': activeDays.map((day) => day.name).toList(),
      'isEnabled': isEnabled,
      'label': label,
    };
  }

  /// Create from map
  factory PresenceTimeBlock.fromMap(Map<String, dynamic> map) {
    return PresenceTimeBlock(
      id: map['id'] as String,
      startTime: PresenceTimeOfDay.fromString(map['startTime'] as String),
      endTime: PresenceTimeOfDay.fromString(map['endTime'] as String),
      activeDays: (map['activeDays'] as List<dynamic>)
          .map((dayName) => DayOfWeek.values.firstWhere((day) => day.name == dayName))
          .toSet(),
      isEnabled: map['isEnabled'] as bool? ?? true,
      label: map['label'] as String?,
    );
  }

  @override
  List<Object?> get props => [id, startTime, endTime, activeDays, isEnabled, label];
}

/// Enhanced presence hours configuration for automatic zone validation
class PresenceHoursConfiguration extends Equatable {
  final String zoneId;
  final bool isAutomaticValidationEnabled;
  final List<PresenceTimeBlock> timeBlocks;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const PresenceHoursConfiguration({
    required this.zoneId,
    this.isAutomaticValidationEnabled = false,
    this.timeBlocks = const [],
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Check if automatic validation is currently active
  bool isActiveAt(DateTime dateTime) {
    if (!isAutomaticValidationEnabled) return false;
    if (timeBlocks.isEmpty) return false;
    
    return timeBlocks.any((block) => block.isActiveAt(dateTime));
  }

  /// Get all active time blocks for a given DateTime
  List<PresenceTimeBlock> getActiveBlocksAt(DateTime dateTime) {
    if (!isAutomaticValidationEnabled) return [];
    
    return timeBlocks.where((block) => block.isActiveAt(dateTime)).toList();
  }

  /// Get total hours per week
  double get totalHoursPerWeek {
    double totalMinutes = 0;
    
    for (final block in timeBlocks) {
      if (block.isEnabled) {
        totalMinutes += block.durationInMinutes * block.activeDays.length;
      }
    }
    
    return totalMinutes / 60.0;
  }

  /// Check if configuration has any overlapping time blocks
  bool get hasOverlappingBlocks {
    for (int i = 0; i < timeBlocks.length; i++) {
      for (int j = i + 1; j < timeBlocks.length; j++) {
        if (timeBlocks[i].hasOverlapWith(timeBlocks[j])) {
          return true;
        }
      }
    }
    return false;
  }

  /// Get next activation time after given DateTime
  DateTime? getNextActivationAfter(DateTime dateTime) {
    if (!isAutomaticValidationEnabled || timeBlocks.isEmpty) return null;
    
    // Implementation would check all time blocks for the next activation
    // This is a simplified version
    return null;
  }

  PresenceHoursConfiguration copyWith({
    String? zoneId,
    bool? isAutomaticValidationEnabled,
    List<PresenceTimeBlock>? timeBlocks,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PresenceHoursConfiguration(
      zoneId: zoneId ?? this.zoneId,
      isAutomaticValidationEnabled: isAutomaticValidationEnabled ?? this.isAutomaticValidationEnabled,
      timeBlocks: timeBlocks ?? this.timeBlocks,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to map for Firebase storage
  Map<String, dynamic> toMap() {
    return {
      'zoneId': zoneId,
      'isAutomaticValidationEnabled': isAutomaticValidationEnabled,
      'timeBlocks': timeBlocks.map((block) => block.toMap()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from map
  factory PresenceHoursConfiguration.fromMap(Map<String, dynamic> map) {
    return PresenceHoursConfiguration(
      zoneId: map['zoneId'] as String,
      isAutomaticValidationEnabled: map['isAutomaticValidationEnabled'] as bool? ?? false,
      timeBlocks: (map['timeBlocks'] as List<dynamic>?)
          ?.map((blockMap) => PresenceTimeBlock.fromMap(blockMap as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  List<Object?> get props => [
    zoneId,
    isAutomaticValidationEnabled,
    timeBlocks,
    createdAt,
    updatedAt,
    metadata,
  ];

  static final empty = PresenceHoursConfiguration(
    zoneId: '',
    createdAt: DateTime.now(),
  );
}

/// Day of week enumeration
enum DayOfWeek {
  monday('Monday', 'Mon'),
  tuesday('Tuesday', 'Tue'),
  wednesday('Wednesday', 'Wed'),
  thursday('Thursday', 'Thu'),
  friday('Friday', 'Fri'),
  saturday('Saturday', 'Sat'),
  sunday('Sunday', 'Sun');

  const DayOfWeek(this.fullName, this.shortName);

  final String fullName;
  final String shortName;

  static DayOfWeek fromDateTime(DateTime dateTime) {
    // DateTime.weekday: Monday = 1, Sunday = 7
    // Our enum: Monday = 0, Sunday = 6
    final weekday = dateTime.weekday;
    return DayOfWeek.values[(weekday - 1) % 7];
  }

  static Set<DayOfWeek> get weekdays => {
    DayOfWeek.monday,
    DayOfWeek.tuesday,
    DayOfWeek.wednesday,
    DayOfWeek.thursday,
    DayOfWeek.friday,
  };

  static Set<DayOfWeek> get weekends => {
    DayOfWeek.saturday,
    DayOfWeek.sunday,
  };

  static Set<DayOfWeek> get all => Set.from(DayOfWeek.values);
}

/// Time of day representation for presence hours
class PresenceTimeOfDay extends Equatable {
  final int hour;
  final int minute;

  const PresenceTimeOfDay({required this.hour, required this.minute});

  factory PresenceTimeOfDay.fromString(String timeString) {
    final parts = timeString.split(':');
    if (parts.length != 2) {
      throw ArgumentError('Invalid time format. Expected HH:mm');
    }

    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);

    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw ArgumentError('Invalid time values');
    }

    return PresenceTimeOfDay(hour: hour, minute: minute);
  }

  factory PresenceTimeOfDay.fromDateTime(DateTime dateTime) {
    return PresenceTimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
  }

  /// Format as 24-hour string (HH:mm)
  String format24Hour() {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// Get total minutes since midnight
  int get totalMinutes => hour * 60 + minute;

  /// Check if this time is within a range (inclusive)
  bool isWithinRange(PresenceTimeOfDay start, PresenceTimeOfDay end) {
    final current = totalMinutes;
    final startMinutes = start.totalMinutes;
    final endMinutes = end.totalMinutes;

    if (startMinutes <= endMinutes) {
      return current >= startMinutes && current <= endMinutes;
    } else {
      return current >= startMinutes || current <= endMinutes;
    }
  }

  /// Get difference in minutes from another time
  int differenceInMinutes(PresenceTimeOfDay other) {
    final diff = totalMinutes - other.totalMinutes;
    return diff >= 0 ? diff : diff + (24 * 60); // Handle overnight
  }

  @override
  List<Object?> get props => [hour, minute];
}

/// Extension methods for PresenceTimeBlock
extension PresenceTimeBlockExtensions on PresenceTimeBlock {
  /// Check if this block overlaps with another block on any common day
  bool hasOverlapWith(PresenceTimeBlock other) {
    // Check if they share any common days
    final commonDays = activeDays.intersection(other.activeDays);
    if (commonDays.isEmpty) return false;
    
    // Check time overlap
    return startTime.totalMinutes < other.endTime.totalMinutes &&
           endTime.totalMinutes > other.startTime.totalMinutes;
  }
}
