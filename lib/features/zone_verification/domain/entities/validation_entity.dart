import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

class ValidationEntity extends Equatable {
  final String id;
  final String validatedEntityId; // Zone ID being validated
  final ValidationEntityType entityType; // Always zone for this feature
  final ValidationType type; // community or automatic
  final String validatorUserId; // User who performed the validation
  final DateTime validatedAt;
  final String? validationNote;
  final Map<String, dynamic>? metadata;

  const ValidationEntity({
    required this.id,
    required this.validatedEntityId,
    required this.entityType,
    required this.type,
    required this.validatorUserId,
    required this.validatedAt,
    this.validationNote,
    this.metadata,
  });

  /// Perform a community validation for a zone
  static ValidationEntity performCommunityValidation({
    required String id,
    required String zoneId,
    required String validatorUserId,
    String? note,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationEntity(
      id: id,
      validatedEntityId: zoneId,
      entityType: ValidationEntityType.zone,
      type: ValidationType.community,
      validatorUserId: validatorUserId,
      validatedAt: DateTime.now(),
      validationNote: note,
      metadata: metadata,
    );
  }

  /// Perform an automatic validation for a zone
  static ValidationEntity performAutomaticValidation({
    required String id,
    required String zoneId,
    required String systemUserId,
    String? note,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationEntity(
      id: id,
      validatedEntityId: zoneId,
      entityType: ValidationEntityType.zone,
      type: ValidationType.automatic,
      validatorUserId: systemUserId,
      validatedAt: DateTime.now(),
      validationNote: note,
      metadata: metadata,
    );
  }

  /// Check if this is a community validation
  bool get isCommunityValidation => type == ValidationType.community;

  /// Check if this is an automatic validation
  bool get isAutomaticValidation => type == ValidationType.automatic;

  /// Check if this validation is for a zone
  bool get isZoneValidation => entityType == ValidationEntityType.zone;

  ValidationEntity copyWith({
    String? id,
    String? validatedEntityId,
    ValidationEntityType? entityType,
    ValidationType? type,
    String? validatorUserId,
    DateTime? validatedAt,
    String? validationNote,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationEntity(
      id: id ?? this.id,
      validatedEntityId: validatedEntityId ?? this.validatedEntityId,
      entityType: entityType ?? this.entityType,
      type: type ?? this.type,
      validatorUserId: validatorUserId ?? this.validatorUserId,
      validatedAt: validatedAt ?? this.validatedAt,
      validationNote: validationNote ?? this.validationNote,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        validatedEntityId,
        entityType,
        type,
        validatorUserId,
        validatedAt,
        validationNote,
        metadata,
      ];

  static final empty = ValidationEntity(
    id: '',
    validatedEntityId: '',
    entityType: ValidationEntityType.zone,
    type: ValidationType.community,
    validatorUserId: '',
    validatedAt: DateTime.now(),
  );

  bool get isEmpty => this == ValidationEntity.empty;
  bool get isNotEmpty => this != ValidationEntity.empty;
}
