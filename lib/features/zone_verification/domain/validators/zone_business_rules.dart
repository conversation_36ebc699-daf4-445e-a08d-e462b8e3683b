import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Business rules validator for zone operations
class ZoneBusinessRules {
  /// Check if a zone can be deleted
  static String? canDeleteZone(ZoneEntity zone, String userId) {
    // Only zone owner can delete
    if (zone.userId != userId) {
      return 'Only the zone owner can delete this zone';
    }

    // Cannot delete validated zones (business rule)
    if (zone.validationStatus == ZoneStatus.validated) {
      return 'Cannot delete validated zones';
    }

    return null;
  }

  /// Check if a user can validate a zone
  static String? canUserValidateZone(ZoneEntity zone, String validatorUserId) {
    // Check if user is trying to validate their own zone
    if (zone.userId == validatorUserId) {
      return 'You cannot validate your own zone';
    }

    // Check if zone is already validated
    if (zone.validationStatus == ZoneStatus.validated) {
      return 'Zone is already validated';
    }

    // Check if zone is rejected
    if (zone.validationStatus == ZoneStatus.rejected) {
      return 'Cannot validate rejected zones';
    }

    // Check if user has already validated this zone
    if (zone.isValidatedBy(validatorUserId)) {
      return 'You have already validated this zone';
    }

    // Check if zone uses social validation method
    if (zone.validationMethod != ValidationMethod.social) {
      return 'This zone does not use social validation';
    }

    return null;
  }

  /// Check if automatic validation can be enabled for a zone
  static String? canEnableAutomaticValidation(ZoneEntity zone, String userId) {
    // Only zone owner can enable automatic validation
    if (zone.userId != userId) {
      return 'Only the zone owner can enable automatic validation';
    }

    // Zone must be pending
    if (zone.validationStatus != ZoneStatus.pending) {
      return 'Automatic validation can only be enabled for pending zones';
    }

    // Zone must use automatic validation method
    if (zone.validationMethod != ValidationMethod.automatic) {
      return 'Zone must be configured for automatic validation';
    }

    return null;
  }

  /// Check if social validation can be requested for a zone
  static String? canRequestSocialValidation(ZoneEntity zone, String userId) {
    // Only zone owner can request social validation
    if (zone.userId != userId) {
      return 'Only the zone owner can request social validation';
    }

    // Zone must be pending
    if (zone.validationStatus != ZoneStatus.pending) {
      return 'Social validation can only be requested for pending zones';
    }

    // Zone must use social validation method
    if (zone.validationMethod != ValidationMethod.social) {
      return 'Zone must be configured for social validation';
    }

    return null;
  }

  /// Check if a zone needs community validation
  static bool needsCommunityValidation(ZoneEntity zone) {
    return zone.validationMethod == ValidationMethod.social && 
           zone.validationStatus == ZoneStatus.pending;
  }

  /// Check if a zone has enough community validations
  static bool hasEnoughCommunityValidations(ZoneEntity zone) {
    const int requiredValidations = 3; // Configurable threshold
    return zone.communityValidationCount >= requiredValidations;
  }

  /// Check if a zone should be auto-validated after community validation
  static bool shouldAutoValidateAfterCommunityValidation(ZoneEntity zone) {
    return hasEnoughCommunityValidations(zone) && 
           zone.validationStatus == ZoneStatus.pending;
  }

  /// Get validation progress for a zone (0.0 to 1.0)
  static double getValidationProgress(ZoneEntity zone) {
    if (zone.validationStatus == ZoneStatus.validated) return 1.0;
    if (zone.validationStatus == ZoneStatus.rejected) return 0.0;
    
    const int requiredValidations = 3;
    return (zone.communityValidationCount / requiredValidations).clamp(0.0, 1.0);
  }

  /// Get remaining validations needed for a zone
  static int getRemainingValidations(ZoneEntity zone) {
    const int requiredValidations = 3;
    return (requiredValidations - zone.communityValidationCount).clamp(0, requiredValidations);
  }

  /// Check if automatic validation requirements are met
  static bool areAutomaticValidationRequirementsMet({
    required bool locationPermissionGranted,
    required bool locationServiceEnabled,
    required bool locationAccuracyMet,
  }) {
    return locationPermissionGranted && locationServiceEnabled && locationAccuracyMet;
  }

  /// Calculate estimated validation time for automatic validation
  static Duration calculateEstimatedValidationTime(ZoneEntity zone) {
    // Base time for automatic validation
    const baseTime = Duration(hours: 24);
    
    // Adjust based on zone type (business rule)
    switch (zone.type) {
      case ZoneType.home:
        return baseTime;
      case ZoneType.work:
        return const Duration(hours: 8);
   
      case ZoneType.university:
        return const Duration(hours: 16);
      case ZoneType.other:
        return const Duration(hours: 48);
    }
  }
}
