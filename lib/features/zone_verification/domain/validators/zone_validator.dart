import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Validator for zone-related operations
class ZoneValidator {
  /// Validate zone creation parameters
  static String? validateZoneCreation({
    required String name,
    required String address,
    required Coordinates centerCoordinates,
    required String presenceHours,
  }) {
    // Check zone name
    final nameValidation = validateZoneName(name);
    if (nameValidation != null) return nameValidation;

    // Check address
    final addressValidation = validateZoneAddress(address);
    if (addressValidation != null) return addressValidation;

    // Check coordinates
    final coordinatesValidation = validateCoordinates(centerCoordinates);
    if (coordinatesValidation != null) return coordinatesValidation;

    // Check presence hours
    final hoursValidation = validatePresenceHours(presenceHours);
    if (hoursValidation != null) return hoursValidation;

    return null; // No validation errors
  }

  /// Validate zone name
  static String? validateZoneName(String name) {
    if (name.trim().isEmpty) {
      return 'Zone name cannot be empty';
    }

    if (name.trim().length < 2) {
      return 'Zone name must be at least 2 characters long';
    }

    if (name.trim().length > 50) {
      return 'Zone name cannot exceed 50 characters';
    }

    return null;
  }

  /// Validate zone address
  static String? validateZoneAddress(String address) {
    if (address.trim().isEmpty) {
      return 'Zone address cannot be empty';
    }

    if (address.trim().length < 5) {
      return 'Zone address must be at least 5 characters long';
    }

    return null;
  }

  /// Validate coordinates
  static String? validateCoordinates(Coordinates coordinates) {
    if (coordinates.isEmpty) {
      return 'Zone coordinates are required';
    }

    // Validate latitude and longitude ranges
    if (coordinates.latitude < -90 || coordinates.latitude > 90) {
      return 'Invalid latitude value';
    }

    if (coordinates.longitude < -180 || coordinates.longitude > 180) {
      return 'Invalid longitude value';
    }

    return null;
  }

  /// Validate presence hours
  static String? validatePresenceHours(String presenceHours) {
    if (presenceHours.trim().isEmpty) {
      return 'Presence hours cannot be empty';
    }

    if (presenceHours.trim().length < 3) {
      return 'Presence hours must be specified';
    }

    return null;
  }

  /// Validate user ID
  static String? validateUserId(String userId) {
    if (userId.trim().isEmpty) {
      return 'User ID cannot be empty';
    }

    return null;
  }

  /// Validate zone ID
  static String? validateZoneId(String zoneId) {
    if (zoneId.trim().isEmpty) {
      return 'Zone ID cannot be empty';
    }

    return null;
  }

  /// Validate delete zone parameters
  static String? validateDeleteZoneParams({
    required String zoneId,
    required String userId,
  }) {
    final zoneIdValidation = validateZoneId(zoneId);
    if (zoneIdValidation != null) return zoneIdValidation;

    final userIdValidation = validateUserId(userId);
    if (userIdValidation != null) return userIdValidation;

    return null;
  }

  /// Validate zone validation parameters
  static String? validateZoneValidationParams({
    required String validatorUserId,
    required String zoneId,
  }) {
    final userIdValidation = validateUserId(validatorUserId);
    if (userIdValidation != null) return userIdValidation;

    final zoneIdValidation = validateZoneId(zoneId);
    if (zoneIdValidation != null) return zoneIdValidation;

    return null;
  }

  /// Validate social validation request parameters
  static String? validateSocialValidationRequestParams({
    required String userId,
    required String zoneId,
  }) {
    final userIdValidation = validateUserId(userId);
    if (userIdValidation != null) return userIdValidation;

    final zoneIdValidation = validateZoneId(zoneId);
    if (zoneIdValidation != null) return zoneIdValidation;

    return null;
  }

  /// Validate automatic validation parameters
  static String? validateAutomaticValidationParams({
    required String zoneId,
    required String userId,
  }) {
    final zoneIdValidation = validateZoneId(zoneId);
    if (zoneIdValidation != null) return zoneIdValidation;

    final userIdValidation = validateUserId(userId);
    if (userIdValidation != null) return userIdValidation;

    return null;
  }
}
