import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

/// Business rules validator for QR-based community validation
class QRValidationBusinessRules {
  
  /// Maximum distance in meters for proximity verification
  static const double maxProximityDistanceMeters = 100.0;
  
  /// Minimum location accuracy required in meters
  static const double minLocationAccuracyMeters = 50.0;
  
  /// Maximum session duration in minutes
  static const Duration maxSessionDuration = Duration(minutes: 10);
  
  /// Default QR token duration in seconds
  static const Duration defaultTokenDuration = Duration(seconds: 45);
  
  /// Minimum time between QR token generations in seconds
  static const Duration minTokenGenerationInterval = Duration(seconds: 5);
  
  /// Maximum number of active sessions per user
  static const int maxActiveSessionsPerUser = 3;
  
  /// Maximum number of validation attempts per day per user
  static const int maxValidationAttemptsPerDay = 10;

  /// Check if a user can initiate a QR validation session
  static String? canInitiateQRValidation({
    required String userId,
    required ZoneEntity zone,
    required bool isUserValidated,
    required int activeSessionsCount,
    required int dailyValidationCount,
  }) {
    // User must be identity validated or pending review
    if (!isUserValidated) {
      return 'User must complete identity verification or wait for review completion to initiate QR validation';
    }

    // Zone creator can initiate QR validation for their own zone
    // This allows them to generate QR codes for others to scan and validate

    if (zone.validationMethod != ValidationMethod.social) {
      return 'Zone does not support community validation';
    }

    // Zone must be pending validation
    if (zone.validationStatus != ZoneStatus.pending) {
      return 'Zone is not pending validation';
    }

    // // Check active sessions limit
    // if (activeSessionsCount >= maxActiveSessionsPerUser) {
    //   return 'Maximum number of active validation sessions reached';
    // }

    // Check daily validation limit
    if (dailyValidationCount >= maxValidationAttemptsPerDay) {
      return 'Daily validation limit reached';
    }

    return null; // Validation passed
  }

  /// Check if a user can join a QR validation session
  static String? canJoinQRValidationSession({
    required String userId,
    required QRValidationSessionEntity session,
    required bool isUserValidated,
    required int dailyValidationCount,
  }) {
    // User must be identity validated or pending review
    if (!isUserValidated) {
      return 'User must complete identity verification or wait for review completion to join validation';
    }

    // Cannot join own session
    if (session.initiatorUserId == userId) {
      return 'Cannot join your own validation session';
    }

    // Session must be joinable
    if (!session.canBeJoined(userId)) {
      return 'Validation session is not available for joining';
    }

    // Check daily validation limit
    if (dailyValidationCount >= maxValidationAttemptsPerDay) {
      return 'Daily validation limit reached';
    }

    return null; // Validation passed
  }

  /// Check if a QR token can be generated
  static String? canGenerateQRToken({
    required String userId,
    required DateTime? lastTokenGeneratedAt,
  }) {
    // Check minimum interval between token generations
    if (lastTokenGeneratedAt != null) {
      final timeSinceLastToken = DateTime.now().difference(lastTokenGeneratedAt);
      if (timeSinceLastToken < minTokenGenerationInterval) {
        final remainingSeconds = minTokenGenerationInterval.inSeconds - timeSinceLastToken.inSeconds;
        return 'Please wait $remainingSeconds seconds before generating a new token';
      }
    }

    return null; // Validation passed
  }

  /// Check if a QR token can be scanned
  static String? canScanQRToken({
    required String scannerUserId,
    required QRTokenEntity token,
  }) {
    // // Token must be valid
    // if (!token.isValid) {
    //   if (token.isExpired) {
    //     return 'QR token has expired';
    //   }
    //   if (token.status == QRTokenStatus.used) {
    //     return 'QR token has already been used';
    //   }
    //   if (token.status == QRTokenStatus.invalid) {
    //     return 'QR token is invalid';
    //   }
    //   return 'QR token is not valid';
    // }

    // // Scanner must be able to use the token
    // if (!token.canBeUsedBy(scannerUserId)) {
    //   return 'Cannot scan your own QR token';
    // }

    return null; // Validation passed
  }

  /// Check if proximity verification can be performed
  static String? canPerformProximityVerification({
    required ProximityDataEntity initiatorLocation,
    required ProximityDataEntity validatorLocation,
    required ZoneEntity zone,
  }) {
    // Check location accuracy
    if (initiatorLocation.accuracy > minLocationAccuracyMeters) {
      return 'Initiator location accuracy is insufficient (${initiatorLocation.accuracy.toStringAsFixed(1)}m)';
    }

    if (validatorLocation.accuracy > minLocationAccuracyMeters) {
      return 'Validator location accuracy is insufficient (${validatorLocation.accuracy.toStringAsFixed(1)}m)';
    }

    // Check if both users are within the zone
    final initiatorInZone = _isLocationInZone(initiatorLocation, zone);
    final validatorInZone = _isLocationInZone(validatorLocation, zone);

    if (!initiatorInZone) {
      return 'Initiator is not within the zone boundaries';
    }

    if (!validatorInZone) {
      return 'Validator is not within the zone boundaries';
    }

    // Check proximity between users
    final distance = initiatorLocation.distanceTo(validatorLocation);
    if (distance > maxProximityDistanceMeters) {
      return 'Users are too far apart (${distance.toStringAsFixed(1)}m). Must be within ${maxProximityDistanceMeters.toStringAsFixed(0)}m';
    }

    return null; // Validation passed
  }

  /// Check if a validation session can be completed
  static String? canCompleteValidation({
    required QRValidationSessionEntity session,
    required bool proximityVerified,
    required bool bothTokensScanned,
  }) {
    // Session must be active
    if (!session.isActive) {
      return 'Validation session is not active';
    }

    // Both users must have joined
    if (session.validatorUserId == null) {
      return 'Waiting for validator to join the session';
    }

    // Proximity must be verified
    if (!proximityVerified) {
      return 'Proximity verification is required';
    }

    // Both tokens must be scanned
    if (!bothTokensScanned) {
      return 'Both users must scan each other\'s QR codes';
    }

    return null; // Validation passed
  }

  /// Check if location is within zone boundaries
  static bool _isLocationInZone(ProximityDataEntity location, ZoneEntity zone) {
    final double distance = Geolocator.distanceBetween(
      location.latitude,
      location.longitude,
      zone.centerCoordinates.latitude,
      zone.centerCoordinates.longitude,
    );
    return distance <= zone.radiusInMeters;
  }

  /// Get remaining time for a session
  static Duration getSessionRemainingTime(QRValidationSessionEntity session) {
    final now = DateTime.now();
    if (now.isAfter(session.expiresAt)) {
      return Duration.zero;
    }
    return session.expiresAt.difference(now);
  }

  /// Get remaining time for a token
  static Duration getTokenRemainingTime(QRTokenEntity token) {
    final now = DateTime.now();
    if (now.isAfter(token.expiresAt)) {
      return Duration.zero;
    }
    return token.expiresAt.difference(now);
  }

  /// Check if a zone needs QR community validation
  static bool needsQRCommunityValidation(ZoneEntity zone) {
    return zone.validationMethod == ValidationMethod.social && 
           zone.validationStatus == ZoneStatus.pending;
  }

  /// Calculate validation progress percentage
  static double calculateValidationProgress({
    required bool sessionActive,
    required bool validatorJoined,
    required bool proximityVerified,
    required bool bothTokensScanned,
  }) {
    double progress = 0.0;
    
    if (sessionActive) progress += 0.25;
    if (validatorJoined) progress += 0.25;
    if (proximityVerified) progress += 0.25;
    if (bothTokensScanned) progress += 0.25;
    
    return progress;
  }

  /// Validate session timeout
  static bool isSessionTimedOut(QRValidationSessionEntity session) {
    return DateTime.now().isAfter(session.expiresAt);
  }

  /// Validate token timeout
  static bool isTokenTimedOut(QRTokenEntity token) {
    return DateTime.now().isAfter(token.expiresAt);
  }
}
