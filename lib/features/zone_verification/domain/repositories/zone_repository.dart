import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/validation_entities.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';

/// Repository interface for zone management operations
abstract class ZoneRepository {
  /// Create a new zone
  Future<Either<Failure, ZoneEntity>> createZone(ZoneEntity zone);

  /// Get all zones for a specific user
  Future<Either<Failure, List<ZoneEntity>>> getUserZones(String userId);

  /// Get a specific zone by ID
  Future<Either<Failure, ZoneEntity>> getZoneById(String zoneId);

  /// Watch a specific zone for real-time updates
  Stream<Either<Failure, ZoneEntity>> watchZone(String zoneId);

  /// Update an existing zone
  Future<Either<Failure, ZoneEntity>> updateZone(ZoneEntity zone);

  /// Delete a zone
  Future<Either<Failure, void>> deleteZone(String zoneId);

  /// Get zones within a specific radius of coordinates
  Future<Either<Failure, List<ZoneEntity>>> getZonesNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  });

  /// Add a community validation to a zone
  Future<Either<Failure, ZoneEntity>> addCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  });

  /// Remove a community validation from a zone
  Future<Either<Failure, ZoneEntity>> removeCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  });

  /// Update zone validation status
  Future<Either<Failure, ZoneEntity>> updateZoneValidationStatus({
    required String zoneId,
    required ZoneStatus status,
    String? rejectionReason,
  });

  /// Get validation history for a zone
  Future<Either<Failure, List<ValidationEntity>>> getZoneValidationHistory(String zoneId);

  /// Check if user can validate a specific zone
  Future<Either<Failure, bool>> canUserValidateZone({
    required String userId,
    required String zoneId,
  });

  /// Get daily validation count for a user
  Future<Either<Failure, int>> getUserDailyValidationCount(String userId);

  /// Enable automatic validation for a zone
  Future<Either<Failure, ZoneEntity>> enableAutomaticValidation(String zoneId);

  /// Disable automatic validation for a zone
  Future<Either<Failure, ZoneEntity>> disableAutomaticValidation(String zoneId);

  /// Get zones that need automatic validation check
  Future<Either<Failure, List<ZoneEntity>>> getZonesForAutomaticValidation();

  /// Process automatic validation for a zone
  Future<Either<Failure, ZoneEntity>> processAutomaticValidation({
    required String zoneId,
    required String userId,
    required double userLatitude,
    required double userLongitude,
  });

  /// Get zone statistics for a user
  Future<Either<Failure, Map<String, dynamic>>> getUserZoneStatistics(String userId);

  /// Search zones by name or address
  Future<Either<Failure, List<ZoneEntity>>> searchZones({
    required String query,
    String? userId,
  });

  /// Get zones by type
  Future<Either<Failure, List<ZoneEntity>>> getZonesByType({
    required ZoneType type,
    String? userId,
  });

  /// Get zones by validation status
  Future<Either<Failure, List<ZoneEntity>>> getZonesByStatus({
    required ZoneStatus status,
    String? userId,
  });

  /// Batch update zones
  Future<Either<Failure, List<ZoneEntity>>> batchUpdateZones(List<ZoneEntity> zones);

  /// Get zone validation requirements
  Future<Either<Failure, Map<String, dynamic>>> getZoneValidationRequirements(String zoneId);

  /// Check if location is within any user zone
  Future<Either<Failure, ZoneEntity?>> getZoneAtLocation({
    required String userId,
    required double latitude,
    required double longitude,
  });

  /// Get zones that are pending validation
  Future<Either<Failure, List<ZoneEntity>>> getPendingValidationZones({String? userId});

  /// Get zones that are validated
  Future<Either<Failure, List<ZoneEntity>>> getValidatedZones({String? userId});

  /// Get community validation requests for a user
  Future<Either<Failure, List<ZoneEntity>>> getCommunityValidationRequests(String userId);

  /// Stream of zone updates for real-time UI updates
  Stream<Either<Failure, List<ZoneEntity>>> watchUserZones(String userId);

  /// Stream of zone validation updates
  Stream<Either<Failure, ZoneEntity>> watchZoneValidation(String zoneId);

  // Enhanced Automatic Validation Methods

  /// Get zones with automatic validation enabled for a user
  Future<Either<Failure, List<ZoneEntity>>> getAutomaticValidationZones(String userId);

  /// Update presence hours configuration for a zone
  Future<Either<Failure, ZoneEntity>> updatePresenceHours({
    required String zoneId,
    required PresenceHoursConfiguration presenceHours,
  });

  /// Record geofence event for a zone
  Future<Either<Failure, void>> recordGeofenceEvent({
    required String zoneId,
    required String userId,
    required GeofenceEventType eventType,
    required DateTime timestamp,
    required double latitude,
    required double longitude,
    required double accuracy,
  });

  /// Get geofence events for a zone
  Future<Either<Failure, List<GeofenceEventRecord>>> getGeofenceEvents({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });

  /// Update automatic validation progress
  Future<Either<Failure, ZoneEntity>> updateValidationProgress({
    required String zoneId,
    required Map<String, dynamic> progressData,
  });

  /// Get zones that require presence validation at current time
  Future<Either<Failure, List<ZoneEntity>>> getActivePresenceZones({
    required String userId,
    DateTime? currentTime,
  });

  /// Check if zone validation criteria are met
  Future<Either<Failure, ValidationCriteriaResult>> checkValidationCriteria({
    required String zoneId,
    required String userId,
  });

  /// Complete automatic validation for a zone
  Future<Either<Failure, ZoneEntity>> completeAutomaticValidation({
    required String zoneId,
    required String userId,
    required Map<String, dynamic> validationData,
  });

  /// Get validation analytics for a zone
  Future<Either<Failure, Map<String, dynamic>>> getZoneValidationAnalytics({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Batch process automatic validation for multiple zones
  Future<Either<Failure, List<ZoneEntity>>> batchProcessAutomaticValidation({
    required List<String> zoneIds,
    required String userId,
    required double latitude,
    required double longitude,
  });
}
