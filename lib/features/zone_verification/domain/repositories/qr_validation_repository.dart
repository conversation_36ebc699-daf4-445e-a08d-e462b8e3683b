import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';

/// Repository interface for QR validation operations
abstract class QRValidationRepository {
  
  // Session Management
  
  /// Create a new QR validation session
  Future<Either<Failure, QRValidationSessionEntity>> createValidationSession({
    required String zoneId,
    required String initiatorUserId,
  });

  /// Get a validation session by ID
  Future<Either<Failure, QRValidationSessionEntity>> getValidationSession(String sessionId);

  /// Join a validation session as a validator
  Future<Either<Failure, QRValidationSessionEntity>> joinValidationSession({
    required String sessionId,
    required String validatorUserId,
  });

  /// Update validation session status
  Future<Either<Failure, QRValidationSessionEntity>> updateValidationSession(
    QRValidationSessionEntity session,
  );

  /// Get active sessions for a user
  Future<Either<Failure, List<QRValidationSessionEntity>>> getActiveSessionsForUser(String userId);

  /// Get user's daily validation count
  Future<Either<Failure, int>> getUserDailyValidationCount(String userId);

  /// Watch validation session changes in real-time
  Stream<Either<Failure, QRValidationSessionEntity>> watchValidationSession(String sessionId);

  // Token Management

  /// Generate a new QR token for a session
  Future<Either<Failure, QRTokenEntity>> generateQRToken({
    required String sessionId,
    required String userId,
  });

  /// Generate a new QR token directly for a zone (without session)
  Future<Either<Failure, QRTokenEntity>> generateQRTokenForZone({
    required String zoneId,
    required String userId,
  });

  /// Get a QR token by ID
  Future<Either<Failure, QRTokenEntity>> getQRToken(String tokenId);

  /// Validate and use a QR token
  Future<Either<Failure, QRTokenEntity>> validateQRToken({
    required String tokenId,
    required String scannerUserId,
  });

  /// Get active tokens for a session
  Future<Either<Failure, List<QRTokenEntity>>> getActiveTokensForSession(String sessionId);

  /// Watch QR token changes in real-time
  Stream<Either<Failure, QRTokenEntity>> watchQRToken(String tokenId);

  // Proximity Verification
  
  /// Record proximity verification data
  Future<Either<Failure, void>> recordProximityVerification({
    required String sessionId,
    required ProximityDataEntity initiatorLocation,
    required ProximityDataEntity validatorLocation,
    required bool isVerified,
  });

  /// Get proximity verification status for a session
  Future<Either<Failure, bool>> getProximityVerificationStatus(String sessionId);

  // Validation Completion
  
  /// Complete a validation session
  Future<Either<Failure, QRValidationSessionEntity>> completeValidation({
    required String sessionId,
    required String zoneId,
  });

  /// Record validation failure
  Future<Either<Failure, QRValidationSessionEntity>> recordValidationFailure({
    required String sessionId,
    required String reason,
  });

  // Cleanup and Maintenance
  
  /// Clean up expired sessions and tokens
  Future<Either<Failure, void>> cleanupExpiredData();

  /// Get validation statistics for a user
  Future<Either<Failure, Map<String, dynamic>>> getValidationStatistics(String userId);

  // Event Logging
  
  /// Log validation event for audit purposes
  Future<Either<Failure, void>> logValidationEvent({
    required String sessionId,
    required String eventType,
    required Map<String, dynamic> eventData,
  });

  /// Get validation events for a session
  Future<Either<Failure, List<Map<String, dynamic>>>> getValidationEvents(String sessionId);
}
