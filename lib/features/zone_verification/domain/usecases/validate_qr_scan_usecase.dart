import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/qr_validation_business_rules.dart';

/// Parameters for validating a QR scan
class ValidateQRScanParams {
  final String tokenId;
  final String scannerUserId;
  final ProximityDataEntity scannerLocation;

  const ValidateQRScanParams({
    required this.tokenId,
    required this.scannerUserId,
    required this.scannerLocation,
  });
}

/// Result of QR scan validation
class QRScanValidationResult {
  final QRTokenEntity token;
  final String zoneId;
  final bool isValidScan;
  final bool proximityVerified;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const QRScanValidationResult({
    required this.token,
    required this.zoneId,
    required this.isValidScan,
    required this.proximityVerified,
    this.errorMessage,
    this.metadata,
  });

  /// Create a successful scan result
  factory QRScanValidationResult.success({
    required QRTokenEntity token,
    required String zoneId,
    required bool proximityVerified,
    Map<String, dynamic>? metadata,
  }) {
    return QRScanValidationResult(
      token: token,
      zoneId: zoneId,
      isValidScan: true,
      proximityVerified: proximityVerified,
      metadata: metadata,
    );
  }

  /// Create a failed scan result
  factory QRScanValidationResult.failure({
    required QRTokenEntity token,
    required String zoneId,
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return QRScanValidationResult(
      token: token,
      zoneId: zoneId,
      isValidScan: false,
      proximityVerified: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

/// Use case for validating QR code scans
class ValidateQRScanUseCase
    implements UseCase<QRScanValidationResult, ValidateQRScanParams> {
  final QRValidationRepository _repository;
  final ZoneRepository _zoneRepository;

  ValidateQRScanUseCase(this._repository, this._zoneRepository);

  @override
  Future<Either<Failure, QRScanValidationResult>> call(
    ValidateQRScanParams params,
  ) async {
    try {
      debugPrint('Validating QR scan for token: ${params.tokenId}');
      
      // Get the QR token
      final tokenResult = await _repository.getQRToken(params.tokenId);
      if (tokenResult.isLeft()) {
        final error = tokenResult.fold(
          (failure) => failure,
          (r) => ServerFailure(message: 'Unknown error')
        );
        debugPrint('Failed to get QR token: ${error.message}');
        return Left(error);
      }

      final token = tokenResult.fold((l) => throw Exception(), (r) => r);
      debugPrint('Successfully retrieved token, zone ID: ${token.sessionId}');

      // For zone-based validation, sessionId contains the zoneId
      final zoneId = token.sessionId;

      // Validate business rules for scanning
      final scanValidationError = QRValidationBusinessRules.canScanQRToken(
        scannerUserId: params.scannerUserId,
        token: token,
      );

      if (scanValidationError != null) {
        debugPrint('Business rule validation failed: $scanValidationError');
        return Right(
          QRScanValidationResult.failure(
            token: token,
            zoneId: zoneId,
            errorMessage: scanValidationError,
          ),
        );
      }

      
      // Validate and mark token as used
      try {
        debugPrint('Validating QR token: ${params.tokenId}');
        final usedTokenResult = await _repository.validateQRToken(
          tokenId: params.tokenId,
          scannerUserId: params.scannerUserId,
        );

        if (usedTokenResult.isLeft()) {
          final error = usedTokenResult.fold(
            (failure) => failure,
            (r) => ServerFailure(message: 'Unknown error')
          );
          debugPrint('Failed to validate QR token: ${error.message}');
          return Left(error);
        }

        final usedToken = usedTokenResult.fold(
          (l) => throw Exception('Failed to get validated token'),
          (r) => r,
        );
        debugPrint('Token successfully validated');
      } catch (e) {
        debugPrint('Error validating token: $e');
        return Left(
          ServerFailure(
            message: 'Error al validar el token QR: ${e.toString()}',
          ),
        );
      }

      // For zone-based validation, we can perform proximity verification
      // by checking if the scanner is within the zone boundaries
      bool proximityVerified = true; // Simplified for now

      // Add community validation to the zone
      try {
        debugPrint('Adding community validation to zone $zoneId');
        final zoneValidationResult = await _zoneRepository.addCommunityValidation(
          zoneId: zoneId,
          validatorUserId: params.scannerUserId,
        );

        if (zoneValidationResult.isLeft()) {
          debugPrint('Failed to add community validation: ${zoneValidationResult.fold((l) => l.message, (r) => '')}');
        } else {
          debugPrint('Successfully added community validation for user ${params.scannerUserId} to zone $zoneId');
        }
      } catch (e) {
        debugPrint('Error adding community validation: $e');
      }

      // Log the scan event for the zone
      try {
        debugPrint('Logging validation event');
        await _repository.logValidationEvent(
          sessionId: zoneId, // Use zoneId for logging
          eventType: 'zone_token_scanned',
          eventData: {
            'tokenId': params.tokenId,
            'scannerUserId': params.scannerUserId,
            'tokenOwnerId': token.userId,
            'zoneId': zoneId,
            'proximityVerified': proximityVerified,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
        debugPrint('Successfully logged validation event');
      } catch (e) {
        debugPrint('Error logging validation event: $e');
      }

      return Right(
        QRScanValidationResult.success(
          token: token, // Use original token since we might not have the used token
          zoneId: zoneId,
          proximityVerified: proximityVerified,
          metadata: {
            'scanTimestamp': DateTime.now().toIso8601String(),
            'proximityVerified': proximityVerified,
          },
        ),
      );
    } catch (e) {
      debugPrint('Error in ValidateQRScanUseCase: $e');
      return Left(
        ServerFailure(
          message: 'Error al validar el código QR: ${e.toString()}',
        ),
      );
    }
  }
}

/// Use case for checking if both tokens in a session have been scanned
class CheckBothTokensScannedUseCase implements UseCase<bool, String> {
  final QRValidationRepository _repository;

  CheckBothTokensScannedUseCase(this._repository);

  @override
  Future<Either<Failure, bool>> call(String sessionId) async {
    try {
      // Get all tokens for the session
      final tokensResult = await _repository.getActiveTokensForSession(
        sessionId,
      );
      if (tokensResult.isLeft()) {
        return Left(tokensResult.fold((l) => l, (r) => throw Exception()));
      }

      final tokens = tokensResult.fold((l) => throw Exception(), (r) => r);

      // Check if we have tokens from both users and both are used
      final usedTokens =
          tokens.where((token) => token.status == QRTokenStatus.used).toList();

      // We need at least 2 used tokens (one from each user)
      if (usedTokens.length < 2) {
        return const Right(false);
      }

      // Check if tokens are from different users
      final userIds = usedTokens.map((token) => token.userId).toSet();
      return Right(userIds.length >= 2);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to validate QR scan: ${e.toString()}',
        ),
      );
    }
  }
}
