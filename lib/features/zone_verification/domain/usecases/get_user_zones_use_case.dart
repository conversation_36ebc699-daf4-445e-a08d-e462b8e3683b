import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/zone_query_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';

/// Use case for getting all zones for a specific user
///
/// This use case orchestrates the process of retrieving user zones by:
/// 1. Validating the user ID
/// 2. Fetching zones from the repository
/// 3. Applying filters and sorting through the query service
class GetUserZonesUseCase implements UseCase<List<ZoneEntity>, GetUserZonesParams> {
  final ZoneRepository _repository;

  const GetUserZonesUseCase(this._repository);

  @override
  Future<Either<Failure, List<ZoneEntity>>> call(GetUserZonesParams params) async {
    // Validate user ID
    final validationResult = ZoneValidator.validateUserId(params.userId);
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Get zones from repository
    final result = await _repository.getUserZones(params.userId);

    return result.fold(
      (failure) => Left(failure),
      (zones) {
        // Apply filters and sorting using the query service
        final processedZones = ZoneQueryService.queryZones(
          zones,
          status: params.status,
          type: params.type,
          validationMethod: params.validationMethod,
          sortBy: params.sortBy,
          sortDescending: params.sortDescending,
        );

        return Right(processedZones);
      },
    );
  }
}

/// Parameters for getting user zones
class GetUserZonesParams extends Equatable {
  final String userId;
  final ZoneStatus? status;
  final ZoneType? type;
  final ValidationMethod? validationMethod;
  final ZoneSortBy sortBy;
  final bool sortDescending;

  const GetUserZonesParams({
    required this.userId,
    this.status,
    this.type,
    this.validationMethod,
    this.sortBy = ZoneSortBy.createdAt,
    this.sortDescending = false,
  });

  @override
  List<Object?> get props => [
        userId,
        status,
        type,
        validationMethod,
        sortBy,
        sortDescending,
      ];
}
