import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/qr_validation_business_rules.dart';

/// Parameters for generating a QR token
class GenerateQRTokenParams {
  final String zoneId;
  final String userId;
  final DateTime? lastTokenGeneratedAt;

  const GenerateQRTokenParams({
    required this.zoneId,
    required this.userId,
    this.lastTokenGeneratedAt,
  });
}

/// Use case for generating secure QR tokens for validation
class GenerateQRTokenUseCase implements UseCase<QRTokenEntity, GenerateQRTokenParams> {
  final QRValidationRepository _repository;

  GenerateQRTokenUseCase(this._repository);

  @override
  Future<Either<Failure, QRTokenEntity>> call(GenerateQRTokenParams params) async {
    try {
      // Validate business rules
      final validationError = QRValidationBusinessRules.canGenerateQRToken(
        userId: params.userId,
        lastTokenGeneratedAt: params.lastTokenGeneratedAt,
      );

      if (validationError != null) {
        return Left(ValidationFailure(validationError));
      }

      // Generate the QR token directly for the zone
      final tokenResult = await _repository.generateQRTokenForZone(
        zoneId: params.zoneId,
        userId: params.userId,
      );

      return tokenResult;
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to generate QR token: ${e.toString()}'));
    }
  }
}

class QRTokenGenerationResult {
  final QRTokenEntity token;
  final Duration remainingTime;
  final bool canGenerateNext;

  const QRTokenGenerationResult({
    required this.token,
    required this.remainingTime,
    required this.canGenerateNext,
  });

  /// Create a successful result
  factory QRTokenGenerationResult.success({
    required QRTokenEntity token,
  }) {
    final remainingTime = QRValidationBusinessRules.getTokenRemainingTime(token);
    return QRTokenGenerationResult(
      token: token,
      remainingTime: remainingTime,
      canGenerateNext: remainingTime.inSeconds <= 5, // Can generate next when 5 seconds or less remaining
    );
  }
}

/// Enhanced use case that returns additional metadata
class GenerateQRTokenWithMetadataUseCase implements UseCase<QRTokenGenerationResult, GenerateQRTokenParams> {
  final GenerateQRTokenUseCase _generateTokenUseCase;

  GenerateQRTokenWithMetadataUseCase(this._generateTokenUseCase);

  @override
  Future<Either<Failure, QRTokenGenerationResult>> call(GenerateQRTokenParams params) async {
    final result = await _generateTokenUseCase(params);
    
    return result.fold(
      (failure) => Left(failure),
      (token) => Right(QRTokenGenerationResult.success(token: token)),
    );
  }
}
