import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

/// Parameters for checking zone creation prerequisites
class CheckZoneCreationPrerequisitesParams {
  final String userId;

  const CheckZoneCreationPrerequisitesParams({
    required this.userId,
  });
}

/// Result of zone creation prerequisites check
class ZoneCreationPrerequisitesResult {
  final bool canCreateZone;
  final String? blockingReason;
  final ValidationStatus userValidationStatus;
  final bool requiresIdentityValidation;

  const ZoneCreationPrerequisitesResult({
    required this.canCreateZone,
    this.blockingReason,
    required this.userValidationStatus,
    required this.requiresIdentityValidation,
  });

  /// Factory constructor for when identity validation is required
  factory ZoneCreationPrerequisitesResult.identityValidationRequired(
    ValidationStatus currentStatus,
  ) {
    return ZoneCreationPrerequisitesResult(
      canCreateZone: false,
      blockingReason: _getIdentityValidationMessage(currentStatus),
      userValidationStatus: currentStatus,
      requiresIdentityValidation: true,
    );
  }

  /// Factory constructor for when zone creation is allowed
  factory ZoneCreationPrerequisitesResult.allowed(
    ValidationStatus currentStatus,
  ) {
    return ZoneCreationPrerequisitesResult(
      canCreateZone: true,
      blockingReason: null,
      userValidationStatus: currentStatus,
      requiresIdentityValidation: false,
    );
  }

  /// Get appropriate message based on validation status
  static String _getIdentityValidationMessage(ValidationStatus status) {
    switch (status) {
      case ValidationStatus.unverified:
        return 'Identity verification is required before creating zones. Please complete the identity verification process to continue.';
      case ValidationStatus.pendingId:
        return 'Your identity verification is being processed. Please wait for verification to complete before creating zones.';
      case ValidationStatus.pendingAutomaticVerification:
        return 'Your identity is being automatically verified. Please wait for the process to complete before creating zones.';
      case ValidationStatus.pendingReview:
        return 'Your identity verification is under manual review. Please wait for the review to complete before creating zones.';
      case ValidationStatus.rejected:
        return 'Your identity verification was rejected. Please contact support or resubmit your verification documents.';
      case ValidationStatus.suspended:
        return 'Your account is suspended. Please contact support for assistance.';
      case ValidationStatus.validated:
        return 'Identity verification completed successfully.';
    }
  }
}

/// Use case for checking if a user meets the prerequisites for creating a zone
/// 
/// This use case verifies that:
/// 1. The user has completed identity validation (ValidationStatus.validated)
/// 2. Any other future prerequisites for zone creation
class CheckZoneCreationPrerequisitesUseCase 
    implements UseCase<ZoneCreationPrerequisitesResult, CheckZoneCreationPrerequisitesParams> {
  
  final IdentityVerificationRepository _identityRepository;

  const CheckZoneCreationPrerequisitesUseCase(this._identityRepository);

  @override
  Future<Either<Failure, ZoneCreationPrerequisitesResult>> call(
    CheckZoneCreationPrerequisitesParams params,
  ) async {
    try {
      // Get user's identity verification status
      final userResult = await _identityRepository.getIdentityVerificationStatus(params.userId);
      
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      // Check if user has validated identity
      if (user.validationStatus == ValidationStatus.validated) {
        // User meets all prerequisites for zone creation
        return Right(ZoneCreationPrerequisitesResult.allowed(user.validationStatus));
      } else {
        // User does not meet identity validation requirement
        return Right(ZoneCreationPrerequisitesResult.identityValidationRequired(user.validationStatus));
      }
    } catch (e) {
      return Left(ServerFailure(
        message: 'Failed to check zone creation prerequisites: ${e.toString()}',
      ));
    }
  }
}
