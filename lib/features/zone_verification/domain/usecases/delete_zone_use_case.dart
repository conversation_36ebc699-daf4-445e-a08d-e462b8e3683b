import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_business_rules.dart';

/// Use case for deleting a zone
///
/// This use case orchestrates the zone deletion process by:
/// 1. Validating input parameters
/// 2. Retrieving the zone to be deleted
/// 3. Checking business rules for deletion
/// 4. Performing the deletion through the repository
class DeleteZoneUseCase implements UseCase<void, DeleteZoneParams> {
  final ZoneRepository _repository;

  const DeleteZoneUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(DeleteZoneParams params) async {
    // Validate parameters
    final validationResult = ZoneValidator.validateDeleteZoneParams(
      zoneId: params.zoneId,
      userId: params.userId,
    );
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Get the zone to delete
    final zoneResult = await _repository.getZoneById(params.zoneId);

    return zoneResult.fold(
      (failure) => Left(failure),
      (zone) async {
        // Check business rules for deletion
        final businessRuleCheck = ZoneBusinessRules.canDeleteZone(zone, params.userId);
        if (businessRuleCheck != null) {
          return Left(ValidationFailure(businessRuleCheck));
        }

        // Delete the zone
        return await _repository.deleteZone(params.zoneId);
      },
    );
  }
}

/// Parameters for deleting a zone
class DeleteZoneParams extends Equatable {
  final String userId;
  final String zoneId;

  const DeleteZoneParams({
    required this.userId,
    required this.zoneId,
  });

  @override
  List<Object?> get props => [userId, zoneId];
}


