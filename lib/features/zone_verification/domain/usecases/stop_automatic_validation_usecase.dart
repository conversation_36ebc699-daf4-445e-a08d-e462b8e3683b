import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';
import 'package:respublicaseguridad/core/services/background_service.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/start_automatic_validation_usecase.dart'
    hide ValidationFailure;

/// Use case for stopping automatic validation for a zone
class StopAutomaticValidationUseCase
    implements
        UseCase<AutomaticValidationResult, StopAutomaticValidationParams> {
  final ZoneRepository zoneRepository;
  final LocationService locationService;
  final GeofencingService geofencingService;
  final BackgroundService backgroundService;
  final NotificationService notificationService;

  StopAutomaticValidationUseCase({
    required this.zoneRepository,
    required this.locationService,
    required this.geofencingService,
    required this.backgroundService,
    required this.notificationService,
  });

  @override
  Future<Either<Failure, AutomaticValidationResult>> call(
    StopAutomaticValidationParams params,
  ) async {
    try {
      final zone = params.zone;

      // 1. Remove geofence for the zone
      await geofencingService.removeGeofence(zone.id);

      // 2. Stop background service if no other zones are being monitored
      if (params.stopAllMonitoring) {
        await BackgroundService.stopAllTasks();
        await geofencingService.stopGeofencing();
        await locationService.stopLocationMonitoring();
      }

      // 3. Update zone status
      final updatedZone = zone.copyWith(
        validationMethod:
            ValidationMethod.social, // Revert to social validation
        updatedAt: DateTime.now(),
      );

      final updateResult = await zoneRepository.updateZone(updatedZone);
      if (updateResult.isLeft()) {
        return Left(ValidationFailure('Failed to update zone status'));
      }

      // 4. Show notification if requested
      if (params.showNotification) {
        await notificationService.showZoneValidationNotification(
          zone: zone,
          type: ZoneValidationNotificationType.automaticValidationCompleted,
          customMessage: 'Automatic validation stopped for "${zone.name}"',
        );
      }

      return Right(
        AutomaticValidationResult(
          zone: updatedZone,
          isActive: false,
          startedAt: params.startedAt ?? DateTime.now(),
          completedAt: DateTime.now(),
          message: 'Automatic validation stopped successfully',
        ),
      );
    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }
}

/// Parameters for stopping automatic validation
class StopAutomaticValidationParams {
  final ZoneEntity zone;
  final bool stopAllMonitoring;
  final bool showNotification;
  final DateTime? startedAt;

  const StopAutomaticValidationParams({
    required this.zone,
    this.stopAllMonitoring = false,
    this.showNotification = true,
    this.startedAt,
  });
}
