import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_validation_results.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/zone_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';

/// Use case for validating a zone (community validation)
///
/// This use case orchestrates the zone validation process by:
/// 1. Validating input parameters
/// 2. Delegating the validation logic to the domain service
/// 3. Returning the validation result
class ValidateZoneUseCase implements UseCase<ZoneValidationResult, ValidateZoneParams> {
  final ZoneValidationService _validationService;

  const ValidateZoneUseCase(this._validationService);

  @override
  Future<Either<Failure, ZoneValidationResult>> call(ValidateZoneParams params) async {
    // Validate parameters
    final validationResult = ZoneValidator.validateZoneValidationParams(
      validatorUserId: params.validatorUserId,
      zoneId: params.zoneId,
    );
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Delegate to validation service
    return await _validationService.processCommunityValidation(
      zoneId: params.zoneId,
      validatorUserId: params.validatorUserId,
      validationNote: params.validationNote,
    );
  }
}



/// Parameters for validating a zone
class ValidateZoneParams extends Equatable {
  final String validatorUserId;
  final String zoneId;
  final String? validationNote;

  const ValidateZoneParams({
    required this.validatorUserId,
    required this.zoneId,
    this.validationNote,
  });

  @override
  List<Object?> get props => [validatorUserId, zoneId, validationNote];
}


