import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/presence_hours_parser_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/check_zone_creation_prerequisites_usecase.dart';

/// Use case for creating a new zone
///
/// This use case orchestrates the zone creation process by:
/// 1. Validating input parameters
/// 2. Creating the zone entity
/// 3. Converting presence hours to configuration if automatic validation is selected
/// 4. Persisting it through the repository
/// 5. Automatically starting validation if automatic validation method is selected
class CreateZoneUseCase implements UseCase<ZoneEntity, CreateZoneParams> {
  final ZoneRepository _repository;
  final PresenceHoursParserService _presenceHoursParser;
  final CheckZoneCreationPrerequisitesUseCase _prerequisitesUseCase;
  final AutomaticValidationService _automaticValidationService;

  const CreateZoneUseCase(
    this._repository,
    this._presenceHoursParser,
    this._prerequisitesUseCase,
    this._automaticValidationService,
  );

  @override
  Future<Either<Failure, ZoneEntity>> call(CreateZoneParams params) async {
    // Check prerequisites first (identity validation)
    final prerequisitesResult = await _prerequisitesUseCase(
      CheckZoneCreationPrerequisitesParams(userId: params.userId),
    );

    if (prerequisitesResult.isLeft()) {
      return prerequisitesResult.fold((failure) => Left(failure), (_) => throw Exception());
    }

    final prerequisites = prerequisitesResult.fold((_) => throw Exception(), (r) => r);
    if (!prerequisites.canCreateZone) {
      if (prerequisites.requiresIdentityValidation) {
        return Left(IdentityValidationRequiredFailure(
          message: prerequisites.blockingReason ?? 'Identity validation is required before creating zones',
          userId: params.userId,
          validationStatus: prerequisites.userValidationStatus.name,
        ));
      } else {
        return Left(ValidationFailure(
          prerequisites.blockingReason ?? 'Zone creation prerequisites not met',
        ));
      }
    }

    // Validate zone creation parameters
    final validationResult = ZoneValidator.validateZoneCreation(
      name: params.name,
      address: params.address,
      centerCoordinates: params.centerCoordinates,
      presenceHours: params.presenceHours,
    );

    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Create zone entity first
    final zone = ZoneEntity(
      id: '', // Will be generated by repository
      userId: params.userId,
      name: params.name.trim(),
      type: params.type,
      address: params.address.trim(),
      centerCoordinates: params.centerCoordinates,
      radiusInMeters: ZoneConstants.zoneRadiusMeters,
      presenceHours: params.presenceHours.trim(),
      validationStatus: ZoneStatus.pending,
      validationMethod: params.validationMethod,
      createdAt: DateTime.now(),
    );

    // Create zone in repository
    final createResult = await _repository.createZone(zone);

    // If zone creation failed, return the failure
    if (createResult.isLeft()) {
      return createResult;
    }

    // If automatic validation is selected and presence hours are provided, update with presence hours configuration
    if (params.validationMethod == ValidationMethod.automatic &&
        params.presenceHours.trim().isNotEmpty) {
      return createResult.fold(
        (failure) => Left(failure),
        (createdZone) async {
          final parseResult = _presenceHoursParser.parsePresenceHours(
            presenceHoursString: params.presenceHours.trim(),
            zoneId: createdZone.id,
          );

          if (parseResult.isLeft()) {
            return Left(parseResult.fold((failure) => failure, (_) => throw Exception()));
          }

          final presenceHoursConfig = parseResult.fold((_) => null, (config) => config);

          // Update zone with presence hours configuration
          final updatedZone = createdZone.copyWith(
            presenceHoursConfig: presenceHoursConfig,
          );

          final updateResult = await _repository.updateZone(updatedZone);
          
          // Automatically start validation session after zone creation
          return updateResult.fold(
            (failure) => Left(failure),
            (finalZone) async {
              try {
                // Trigger automatic validation in the background
                await _automaticValidationService.triggerAutomaticValidation(finalZone.id);
                return Right(finalZone);
              } catch (e) {
                // If validation trigger fails, still return the created zone
                // The user can manually start validation later
                return Right(finalZone);
              }
            },
          );
        },
      );
    }

    // If automatic validation is selected but no presence hours provided, still trigger validation
    if (params.validationMethod == ValidationMethod.automatic) {
      return createResult.fold(
        (failure) => Left(failure),
        (createdZone) async {
          try {
            // Trigger automatic validation in the background
            await _automaticValidationService.triggerAutomaticValidation(createdZone.id);
            return Right(createdZone);
          } catch (e) {
            // If validation trigger fails, still return the created zone
            // The user can manually start validation later
            return Right(createdZone);
          }
        },
      );
    }

    return createResult;
  }
}

/// Parameters for creating a zone
class CreateZoneParams extends Equatable {
  final String userId;
  final String name;
  final ZoneType type;
  final String address;
  final Coordinates centerCoordinates;
  final String presenceHours;
  final ValidationMethod validationMethod;

  const CreateZoneParams({
    required this.userId,
    required this.name,
    required this.type,
    required this.address,
    required this.centerCoordinates,
    required this.presenceHours,
    this.validationMethod = ValidationMethod.social,
  });

  @override
  List<Object?> get props => [
        userId,
        name,
        type,
        address,
        centerCoordinates,
        presenceHours,
        validationMethod,
      ];
}


