import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_validation_results.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/zone_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';

/// Use case for enabling automatic validation for a zone
///
/// This use case orchestrates the automatic validation enablement process by:
/// 1. Validating input parameters
/// 2. Delegating the enablement logic to the domain service
/// 3. Returning the enablement result
class EnableAutomaticValidationUseCase implements UseCase<AutomaticValidationResult, EnableAutomaticValidationParams> {
  final ZoneValidationService _validationService;

  const EnableAutomaticValidationUseCase(this._validationService);

  @override
  Future<Either<Failure, AutomaticValidationResult>> call(EnableAutomaticValidationParams params) async {
    // Validate parameters
    final validationResult = ZoneValidator.validateAutomaticValidationParams(
      zoneId: params.zoneId,
      userId: params.userId,
    );
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Delegate to validation service
    return await _validationService.processAutomaticValidationEnablement(
      zoneId: params.zoneId,
      userId: params.userId,
      hasLocationPermission: params.hasLocationPermission,
      isLocationServiceEnabled: params.isLocationServiceEnabled,
      isLocationAccurate: params.isLocationAccurate,
    );
  }
}



/// Parameters for enabling automatic validation
class EnableAutomaticValidationParams extends Equatable {
  final String userId;
  final String zoneId;
  final bool hasLocationPermission;
  final bool isLocationServiceEnabled;
  final bool isLocationAccurate;

  const EnableAutomaticValidationParams({
    required this.userId,
    required this.zoneId,
    required this.hasLocationPermission,
    required this.isLocationServiceEnabled,
    required this.isLocationAccurate,
  });

  @override
  List<Object?> get props => [
        userId,
        zoneId,
        hasLocationPermission,
        isLocationServiceEnabled,
        isLocationAccurate,
      ];
}


