import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';

/// Use case for updating zone validation status when it reaches required validations
class UpdateZoneValidationStatusUseCase implements UseCase<ZoneEntity, UpdateZoneValidationStatusParams> {
  final ZoneRepository _repository;

  const UpdateZoneValidationStatusUseCase(this._repository);

  @override
  Future<Either<Failure, ZoneEntity>> call(UpdateZoneValidationStatusParams params) async {
    try {
      // Get current zone data
      final zoneResult = await _repository.getZoneById(params.zoneId);
      
      return zoneResult.fold(
        (failure) => Left(failure),
        (zone) async {
          // Check if zone should be auto-validated
          if (zone.communityValidationCount >= 3 && zone.validationStatus == ZoneStatus.pending) {
            // Update zone to validated status
            final updatedZone = zone.copyWith(
              validationStatus: ZoneStatus.validated,
              validatedAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );
            
            // Save updated zone
            final updateResult = await _repository.updateZone(updatedZone);
            return updateResult.fold(
              (failure) => Left(failure),
              (updated) => Right(updated),
            );
          }
          
          // No update needed
          return Right(zone);
        },
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update zone validation status: ${e.toString()}'));
    }
  }
}

/// Parameters for updating zone validation status
class UpdateZoneValidationStatusParams extends Equatable {
  final String zoneId;

  const UpdateZoneValidationStatusParams({
    required this.zoneId,
  });

  @override
  List<Object?> get props => [zoneId];
}
