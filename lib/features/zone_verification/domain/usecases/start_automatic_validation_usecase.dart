import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';
import 'package:respublicaseguridad/core/services/background_service.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/core/services/permission_service.dart';

/// Use case for starting automatic validation for a zone
class StartAutomaticValidationUseCase implements UseCase<AutomaticValidationResult, StartAutomaticValidationParams> {
  final ZoneRepository zoneRepository;
  final LocationService locationService;
  final GeofencingService geofencingService;
  final BackgroundService backgroundService;
  final NotificationService notificationService;
  final PermissionService permissionService;

  StartAutomaticValidationUseCase({
    required this.zoneRepository,
    required this.locationService,
    required this.geofencingService,
    required this.backgroundService,
    required this.notificationService,
    required this.permissionService,
  });

  @override
  Future<Either<Failure, AutomaticValidationResult>> call(StartAutomaticValidationParams params) async {
    try {
      // 1. Check permissions
      final permissionStatus = await permissionService.checkAutomaticValidationPermissions();
      if (!permissionStatus.allGranted) {
        return Left(PermissionFailure(
          message: 'Required permissions not granted: ${permissionStatus.summaryMessage}',
          missingPermissions: permissionStatus.deniedPermissions,
        ));
      }

      // 2. Validate zone configuration
      final zone = params.zone;
      if (!zone.canUseAutomaticValidation) {
        return Left(ValidationFailure(
          message: 'Zone is not configured for automatic validation',
        ));
      }

      // 3. Check if presence hours are configured
      if (zone.presenceHoursConfig == null || zone.presenceHoursConfig!.timeBlocks.isEmpty) {
        return Left(ValidationFailure(
          message: 'Presence hours must be configured for automatic validation',
        ));
      }

      // 4. Start location monitoring
      final locationStarted = await locationService.startLocationMonitoring();
      if (!locationStarted) {
        return Left(LocationFailure(
          message: 'Failed to start location monitoring',
        ));
      }

      // 5. Start geofencing
      final geofencingStarted = await geofencingService.startGeofencing();
      if (!geofencingStarted) {
        await locationService.stopLocationMonitoring(); // Cleanup
        return Left(LocationFailure(
          message: 'Failed to start geofencing service',
        ));
      }

      // 6. Add geofence for the zone
      final geofenceAdded = await geofencingService.addGeofence(zone);
      if (!geofenceAdded) {
        await _cleanup();
        return Left(ValidationFailure(
          message: 'Failed to add geofence for zone',
        ));
      }

      // 7. Start background service
      try {
        await BackgroundService.startAutomaticValidation(zone.id);
      } catch (e) {
        await _cleanup();
        return Left(ValidationFailure(
          message: 'Failed to start background monitoring: $e',
        ));
      }

      // 8. Update zone status to indicate automatic validation is active
      final updatedZone = zone.copyWith(
        validationMethod: ValidationMethod.automatic,
        updatedAt: DateTime.now(),
      );

      final updateResult = await zoneRepository.updateZone(updatedZone);
      if (updateResult.isLeft()) {
        await _cleanup();
        return Left(ValidationFailure(
          message: 'Failed to update zone status',
        ));
      }

      // 9. Show notification
      await notificationService.showZoneValidationNotification(
        zone: zone,
        type: ZoneValidationNotificationType.automaticValidationStarted,
      );

      return Right(AutomaticValidationResult(
        zone: updatedZone,
        isActive: true,
        startedAt: DateTime.now(),
        message: 'Automatic validation started successfully',
      ));

    } catch (e) {
      await _cleanup();
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Cleanup resources in case of failure
  Future<void> _cleanup() async {
    try {
      await BackgroundService.stopAllTasks();
      await geofencingService.stopGeofencing();
      await locationService.stopLocationMonitoring();
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

/// Parameters for starting automatic validation
class StartAutomaticValidationParams {
  final ZoneEntity zone;
  final Duration monitoringFrequency;
  final bool enableNotifications;

  const StartAutomaticValidationParams({
    required this.zone,
    this.monitoringFrequency = const Duration(minutes: 15),
    this.enableNotifications = true,
  });
}

/// Result of automatic validation operation
class AutomaticValidationResult {
  final ZoneEntity zone;
  final bool isActive;
  final DateTime startedAt;
  final DateTime? completedAt;
  final String message;
  final Map<String, dynamic>? metadata;

  const AutomaticValidationResult({
    required this.zone,
    required this.isActive,
    required this.startedAt,
    this.completedAt,
    required this.message,
    this.metadata,
  });

  bool get isCompleted => completedAt != null;
  Duration get duration => (completedAt ?? DateTime.now()).difference(startedAt);
}

/// Custom failures for automatic validation
class PermissionFailure extends Failure {
  final List<PermissionType> missingPermissions;

  const PermissionFailure({
    required String message,
    required this.missingPermissions,
  }) : super(message: message);
}

class LocationFailure extends Failure {
  const LocationFailure({required String message}) : super(message: message);
}

class ValidationFailure extends Failure {
  const ValidationFailure({required String message}) : super(message: message);
}

class UnexpectedFailure extends Failure {
  const UnexpectedFailure({required String message}) : super(message: message);
}
