import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/start_automatic_validation_usecase.dart';

/// Use case for checking the current validation status of zones
class CheckValidationStatusUseCase implements UseCase<ValidationStatusResult, CheckValidationStatusParams> {
  final ZoneRepository zoneRepository;
  final GeofencingService geofencingService;
  final LocationService locationService;

  CheckValidationStatusUseCase({
    required this.zoneRepository,
    required this.geofencingService,
    required this.locationService,
  });

  @override
  Future<Either<Failure, ValidationStatusResult>> call(CheckValidationStatusParams params) async {
    try {
      final zoneStatuses = <String, ZoneValidationStatus>{};

      for (final zoneId in params.zoneIds) {
        // Get zone from repository
        final zoneResult = await zoneRepository.getZoneById(zoneId);
        if (zoneResult.isLeft()) {
          zoneStatuses[zoneId] = ZoneValidationStatus(
            zoneId: zoneId,
            status: ValidationStatus.error,
            message: 'Failed to retrieve zone',
          );
          continue;
        }

        final zone = zoneResult.getOrElse(() => throw Exception('Zone not found'));
        final status = await _checkZoneValidationStatus(zone);
        zoneStatuses[zoneId] = status;
      }

      return Right(ValidationStatusResult(
        zoneStatuses: zoneStatuses,
        checkedAt: DateTime.now(),
        overallStatus: _calculateOverallStatus(zoneStatuses.values.toList()),
      ));

    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Check validation status for a specific zone
  Future<ZoneValidationStatus> _checkZoneValidationStatus(ZoneEntity zone) async {
    try {
      // Check if automatic validation is enabled
      if (!zone.canUseAutomaticValidation) {
        return ZoneValidationStatus(
          zoneId: zone.id,
          zone: zone,
          status: ValidationStatus.notConfigured,
          message: 'Automatic validation not configured',
        );
      }

      // Check if zone is currently being monitored
      final isMonitored = geofencingService.activeGeofences.containsKey(zone.id);
      if (!isMonitored) {
        return ZoneValidationStatus(
          zoneId: zone.id,
          zone: zone,
          status: ValidationStatus.inactive,
          message: 'Zone monitoring is not active',
        );
      }

      // Check current location status
      final currentPosition = locationService.lastKnownPosition;
      if (currentPosition == null) {
        return ZoneValidationStatus(
          zoneId: zone.id,
          zone: zone,
          status: ValidationStatus.locationUnavailable,
          message: 'Current location unavailable',
        );
      }

      // Check if user is currently inside the zone
      final isInside = locationService.isPositionInZone(
        currentPosition,
        zone.centerCoordinates,
        zone.radiusInMeters,
      );

      // Check if current time is within presence hours
      final isWithinPresenceHours = zone.isAutomaticValidationActiveAt(DateTime.now());

      // Determine validation status
      ValidationStatus status;
      String message;

      if (isInside && isWithinPresenceHours) {
        status = ValidationStatus.activeInZone;
        message = 'Currently inside zone during presence hours';
      } else if (isInside && !isWithinPresenceHours) {
        status = ValidationStatus.inZoneOutsideHours;
        message = 'Inside zone but outside presence hours';
      } else if (!isInside && isWithinPresenceHours) {
        status = ValidationStatus.outsideZoneDuringHours;
        message = 'Outside zone during presence hours';
      } else {
        status = ValidationStatus.outsideZoneOutsideHours;
        message = 'Outside zone and outside presence hours';
      }

      // Get additional metadata
      final geofenceState = geofencingService.getGeofenceState(zone.id);
      final distanceToZone = geofencingService.getDistanceToZone(zone.id);

      return ZoneValidationStatus(
        zoneId: zone.id,
        zone: zone,
        status: status,
        message: message,
        isInside: isInside,
        isWithinPresenceHours: isWithinPresenceHours,
        geofenceState: geofenceState,
        distanceToZone: distanceToZone,
        lastLocationUpdate: currentPosition.timestamp,
        locationAccuracy: currentPosition.accuracy,
      );

    } catch (e) {
      return ZoneValidationStatus(
        zoneId: zone.id,
        zone: zone,
        status: ValidationStatus.error,
        message: 'Error checking validation status: ${e.toString()}',
      );
    }
  }

  /// Calculate overall validation status
  ValidationStatus _calculateOverallStatus(List<ZoneValidationStatus> statuses) {
    if (statuses.isEmpty) return ValidationStatus.notConfigured;

    final statusCounts = <ValidationStatus, int>{};
    for (final status in statuses) {
      statusCounts[status.status] = (statusCounts[status.status] ?? 0) + 1;
    }

    // Priority order for overall status
    if (statusCounts.containsKey(ValidationStatus.error)) {
      return ValidationStatus.error;
    }
    if (statusCounts.containsKey(ValidationStatus.activeInZone)) {
      return ValidationStatus.activeInZone;
    }
    if (statusCounts.containsKey(ValidationStatus.inZoneOutsideHours)) {
      return ValidationStatus.inZoneOutsideHours;
    }
    if (statusCounts.containsKey(ValidationStatus.outsideZoneDuringHours)) {
      return ValidationStatus.outsideZoneDuringHours;
    }
    if (statusCounts.containsKey(ValidationStatus.inactive)) {
      return ValidationStatus.inactive;
    }
    if (statusCounts.containsKey(ValidationStatus.locationUnavailable)) {
      return ValidationStatus.locationUnavailable;
    }
    if (statusCounts.containsKey(ValidationStatus.notConfigured)) {
      return ValidationStatus.notConfigured;
    }

    return ValidationStatus.outsideZoneOutsideHours;
  }
}

/// Parameters for checking validation status
class CheckValidationStatusParams {
  final List<String> zoneIds;
  final bool includeInactiveZones;

  const CheckValidationStatusParams({
    required this.zoneIds,
    this.includeInactiveZones = true,
  });

  /// Create params for a single zone
  factory CheckValidationStatusParams.singleZone(String zoneId) {
    return CheckValidationStatusParams(zoneIds: [zoneId]);
  }
}

/// Result of validation status check
class ValidationStatusResult {
  final Map<String, ZoneValidationStatus> zoneStatuses;
  final DateTime checkedAt;
  final ValidationStatus overallStatus;

  const ValidationStatusResult({
    required this.zoneStatuses,
    required this.checkedAt,
    required this.overallStatus,
  });

  /// Get status for a specific zone
  ZoneValidationStatus? getZoneStatus(String zoneId) {
    return zoneStatuses[zoneId];
  }

  /// Get all zones with a specific status
  List<ZoneValidationStatus> getZonesWithStatus(ValidationStatus status) {
    return zoneStatuses.values.where((zoneStatus) => zoneStatus.status == status).toList();
  }

  /// Get zones currently being validated
  List<ZoneValidationStatus> get activeZones {
    return getZonesWithStatus(ValidationStatus.activeInZone);
  }

  /// Get zones with errors
  List<ZoneValidationStatus> get errorZones {
    return getZonesWithStatus(ValidationStatus.error);
  }

  /// Check if any zones are actively being validated
  bool get hasActiveValidation {
    return activeZones.isNotEmpty;
  }
}

/// Validation status for a specific zone
class ZoneValidationStatus {
  final String zoneId;
  final ZoneEntity? zone;
  final ValidationStatus status;
  final String message;
  final bool? isInside;
  final bool? isWithinPresenceHours;
  final GeofenceState? geofenceState;
  final double? distanceToZone;
  final DateTime? lastLocationUpdate;
  final double? locationAccuracy;

  const ZoneValidationStatus({
    required this.zoneId,
    this.zone,
    required this.status,
    required this.message,
    this.isInside,
    this.isWithinPresenceHours,
    this.geofenceState,
    this.distanceToZone,
    this.lastLocationUpdate,
    this.locationAccuracy,
  });

  /// Check if validation is currently active
  bool get isActive {
    return status == ValidationStatus.activeInZone;
  }

  /// Check if there's an error
  bool get hasError {
    return status == ValidationStatus.error;
  }

  /// Get formatted distance string
  String? get formattedDistance {
    if (distanceToZone == null) return null;
    
    if (distanceToZone! < 1000) {
      return '${distanceToZone!.round()}m';
    } else {
      return '${(distanceToZone! / 1000).toStringAsFixed(1)}km';
    }
  }
}

/// Possible validation statuses
enum ValidationStatus {
  notConfigured,
  inactive,
  activeInZone,
  inZoneOutsideHours,
  outsideZoneDuringHours,
  outsideZoneOutsideHours,
  locationUnavailable,
  error,
}

/// Extension for ValidationStatus
extension ValidationStatusExtension on ValidationStatus {
  String get displayName {
    switch (this) {
      case ValidationStatus.notConfigured:
        return 'Not Configured';
      case ValidationStatus.inactive:
        return 'Inactive';
      case ValidationStatus.activeInZone:
        return 'Active in Zone';
      case ValidationStatus.inZoneOutsideHours:
        return 'In Zone (Outside Hours)';
      case ValidationStatus.outsideZoneDuringHours:
        return 'Outside Zone (During Hours)';
      case ValidationStatus.outsideZoneOutsideHours:
        return 'Outside Zone';
      case ValidationStatus.locationUnavailable:
        return 'Location Unavailable';
      case ValidationStatus.error:
        return 'Error';
    }
  }

  bool get isActive {
    return this == ValidationStatus.activeInZone;
  }

  bool get hasError {
    return this == ValidationStatus.error;
  }
}
