import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_validation_results.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/services/zone_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_validator.dart';

/// Use case for requesting social validation for a zone
///
/// This use case orchestrates the social validation request process by:
/// 1. Validating input parameters
/// 2. Delegating the request creation to the domain service
/// 3. Returning the validation request
class RequestSocialValidationUseCase implements UseCase<SocialValidationRequest, RequestSocialValidationParams> {
  final ZoneValidationService _validationService;

  const RequestSocialValidationUseCase(this._validationService);

  @override
  Future<Either<Failure, SocialValidationRequest>> call(RequestSocialValidationParams params) async {
    // Validate parameters
    final validationResult = ZoneValidator.validateSocialValidationRequestParams(
      userId: params.userId,
      zoneId: params.zoneId,
    );
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    // Delegate to validation service
    return await _validationService.processSocialValidationRequest(
      zoneId: params.zoneId,
      userId: params.userId,
    );
  }
}



/// Parameters for requesting social validation
class RequestSocialValidationParams extends Equatable {
  final String userId;
  final String zoneId;

  const RequestSocialValidationParams({
    required this.userId,
    required this.zoneId,
  });

  @override
  List<Object?> get props => [userId, zoneId];
}


