import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/qr_validation_business_rules.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

/// Parameters for creating a QR validation session
class CreateQRValidationSessionParams {
  final String zoneId;
  final String initiatorUserId;

  const CreateQRValidationSessionParams({
    required this.zoneId,
    required this.initiatorUserId,
  });
}

/// Result of creating a QR validation session
class QRValidationSessionResult {
  final QRValidationSessionEntity session;
  final ZoneEntity zone;
  final Duration remainingTime;
  final bool canProceed;
  final String? errorMessage;

  const QRValidationSessionResult({
    required this.session,
    required this.zone,
    required this.remainingTime,
    required this.canProceed,
    this.errorMessage,
  });

  /// Create a successful session result
  factory QRValidationSessionResult.success({
    required QRValidationSessionEntity session,
    required ZoneEntity zone,
  }) {
    final remainingTime = QRValidationBusinessRules.getSessionRemainingTime(session);
    return QRValidationSessionResult(
      session: session,
      zone: zone,
      remainingTime: remainingTime,
      canProceed: true,
    );
  }

  /// Create a failed session result
  factory QRValidationSessionResult.failure({
    required QRValidationSessionEntity session,
    required ZoneEntity zone,
    required String errorMessage,
  }) {
    return QRValidationSessionResult(
      session: session,
      zone: zone,
      remainingTime: Duration.zero,
      canProceed: false,
      errorMessage: errorMessage,
    );
  }
}

/// Use case for creating QR validation sessions
class CreateQRValidationSessionUseCase implements UseCase<QRValidationSessionResult, CreateQRValidationSessionParams> {
  final QRValidationRepository _qrRepository;
  final ZoneRepository _zoneRepository;
  final IdentityVerificationRepository _identityRepository;

  CreateQRValidationSessionUseCase(
    this._qrRepository,
    this._zoneRepository,
    this._identityRepository,
  );

  @override
  Future<Either<Failure, QRValidationSessionResult>> call(CreateQRValidationSessionParams params) async {
    try {
      // Get the zone
      final zoneResult = await _zoneRepository.getZoneById(params.zoneId);
      if (zoneResult.isLeft()) {
        return Left(zoneResult.fold((l) => l, (r) => throw Exception()));
      }

      final zone = zoneResult.fold((l) => throw Exception(), (r) => r);

      // Check if user is identity validated
      final userStatusResult = await _identityRepository.getIdentityVerificationStatus(params.initiatorUserId);
      if (userStatusResult.isLeft()) {
        return Left(userStatusResult.fold((l) => l, (r) => throw Exception()));
      }

      final userStatus = userStatusResult.fold((l) => throw Exception(), (r) => r);
      final isUserValidated = userStatus.validationStatus == ValidationStatus.validated ||
                              userStatus.validationStatus == ValidationStatus.pendingReview;

      // Get user's active sessions count
      final activeSessionsResult = await _qrRepository.getActiveSessionsForUser(params.initiatorUserId);
      if (activeSessionsResult.isLeft()) {
        return Left(activeSessionsResult.fold((l) => l, (r) => throw Exception()));
      }

      final activeSessions = activeSessionsResult.fold((l) => throw Exception(), (r) => r);

      // Get user's daily validation count
      final dailyCountResult = await _qrRepository.getUserDailyValidationCount(params.initiatorUserId);
      if (dailyCountResult.isLeft()) {
        return Left(dailyCountResult.fold((l) => l, (r) => throw Exception()));
      }

      final dailyCount = dailyCountResult.fold((l) => throw Exception(), (r) => r);

      // Validate business rules
      final validationError = QRValidationBusinessRules.canInitiateQRValidation(
        userId: params.initiatorUserId,
        zone: zone,
        isUserValidated: isUserValidated,
        activeSessionsCount: activeSessions.length,
        dailyValidationCount: dailyCount,
      );

      if (validationError != null) {
        // Create a dummy session for error reporting
        final dummySession = QRValidationSessionEntity.create(
          id: 'error',
          zoneId: params.zoneId,
          initiatorUserId: params.initiatorUserId,
        );
        
        return Right(QRValidationSessionResult.failure(
          session: dummySession,
          zone: zone,
          errorMessage: validationError,
        ));
      }

      // Create the validation session
      final sessionResult = await _qrRepository.createValidationSession(
        zoneId: params.zoneId,
        initiatorUserId: params.initiatorUserId,
      );

      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // Log session creation event
      await _qrRepository.logValidationEvent(
        sessionId: session.id,
        eventType: 'session_created',
        eventData: {
          'zoneId': params.zoneId,
          'initiatorUserId': params.initiatorUserId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return Right(QRValidationSessionResult.success(
        session: session,
        zone: zone,
      ));

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to create QR validation session: ${e.toString()}'));
    }
  }
}

/// Use case for joining an existing QR validation session
class JoinQRValidationSessionUseCase implements UseCase<QRValidationSessionResult, JoinQRValidationSessionParams> {
  final QRValidationRepository _qrRepository;
  final ZoneRepository _zoneRepository;
  final IdentityVerificationRepository _identityRepository;

  JoinQRValidationSessionUseCase(
    this._qrRepository,
    this._zoneRepository,
    this._identityRepository,
  );

  @override
  Future<Either<Failure, QRValidationSessionResult>> call(JoinQRValidationSessionParams params) async {
    try {
      // Get the validation session
      final sessionResult = await _qrRepository.getValidationSession(params.sessionId);
      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // Get the zone
      final zoneResult = await _zoneRepository.getZoneById(session.zoneId);
      if (zoneResult.isLeft()) {
        return Left(zoneResult.fold((l) => l, (r) => throw Exception()));
      }

      final zone = zoneResult.fold((l) => throw Exception(), (r) => r);

      // Check if user is identity validated
      final userStatusResult = await _identityRepository.getIdentityVerificationStatus(params.validatorUserId);
      if (userStatusResult.isLeft()) {
        return Left(userStatusResult.fold((l) => l, (r) => throw Exception()));
      }

      final userStatus = userStatusResult.fold((l) => throw Exception(), (r) => r);
      final isUserValidated = userStatus.validationStatus == ValidationStatus.validated ||
                              userStatus.validationStatus == ValidationStatus.pendingReview;

      // Get user's daily validation count
      final dailyCountResult = await _qrRepository.getUserDailyValidationCount(params.validatorUserId);
      if (dailyCountResult.isLeft()) {
        return Left(dailyCountResult.fold((l) => l, (r) => throw Exception()));
      }

      final dailyCount = dailyCountResult.fold((l) => throw Exception(), (r) => r);

      // Validate business rules
      final validationError = QRValidationBusinessRules.canJoinQRValidationSession(
        userId: params.validatorUserId,
        session: session,
        isUserValidated: isUserValidated,
        dailyValidationCount: dailyCount,
      );

      if (validationError != null) {
        return Right(QRValidationSessionResult.failure(
          session: session,
          zone: zone,
          errorMessage: validationError,
        ));
      }

      // Join the session
      final joinResult = await _qrRepository.joinValidationSession(
        sessionId: params.sessionId,
        validatorUserId: params.validatorUserId,
      );

      if (joinResult.isLeft()) {
        return Left(joinResult.fold((l) => l, (r) => throw Exception()));
      }

      final updatedSession = joinResult.fold((l) => throw Exception(), (r) => r);

      // Log session join event
      await _qrRepository.logValidationEvent(
        sessionId: updatedSession.id,
        eventType: 'session_joined',
        eventData: {
          'validatorUserId': params.validatorUserId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return Right(QRValidationSessionResult.success(
        session: updatedSession,
        zone: zone,
      ));

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to join QR validation session: ${e.toString()}'));
    }
  }
}

/// Parameters for joining a QR validation session
class JoinQRValidationSessionParams {
  final String sessionId;
  final String validatorUserId;

  const JoinQRValidationSessionParams({
    required this.sessionId,
    required this.validatorUserId,
  });
}
