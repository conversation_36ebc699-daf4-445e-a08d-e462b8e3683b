import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/qr_validation_business_rules.dart';

/// Parameters for completing QR validation
class CompleteQRValidationParams {
  final String sessionId;

  const CompleteQRValidationParams({
    required this.sessionId,
  });
}

/// Result of completing QR validation
class QRValidationCompletionResult {
  final QRValidationSessionEntity session;
  final ZoneEntity? updatedZone;
  final bool isSuccessful;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const QRValidationCompletionResult({
    required this.session,
    this.updatedZone,
    required this.isSuccessful,
    this.errorMessage,
    this.metadata,
  });

  /// Create a successful completion result
  factory QRValidationCompletionResult.success({
    required QRValidationSessionEntity session,
    ZoneEntity? updatedZone,
    Map<String, dynamic>? metadata,
  }) {
    return QRValidationCompletionResult(
      session: session,
      updatedZone: updatedZone,
      isSuccessful: true,
      metadata: metadata,
    );
  }

  /// Create a failed completion result
  factory QRValidationCompletionResult.failure({
    required QRValidationSessionEntity session,
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return QRValidationCompletionResult(
      session: session,
      isSuccessful: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

/// Use case for completing QR validation and updating zone status
class CompleteQRValidationUseCase implements UseCase<QRValidationCompletionResult, CompleteQRValidationParams> {
  final QRValidationRepository _qrRepository;
  final ZoneRepository _zoneRepository;

  CompleteQRValidationUseCase(
    this._qrRepository,
    this._zoneRepository,
  );

  @override
  Future<Either<Failure, QRValidationCompletionResult>> call(CompleteQRValidationParams params) async {
    try {
      // Get the validation session
      final sessionResult = await _qrRepository.getValidationSession(params.sessionId);
      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // Get the zone
      final zoneResult = await _zoneRepository.getZoneById(session.zoneId);
      if (zoneResult.isLeft()) {
        return Left(zoneResult.fold((l) => l, (r) => throw Exception()));
      }

      final zone = zoneResult.fold((l) => throw Exception(), (r) => r);

      // Check proximity verification status
      final proximityResult = await _qrRepository.getProximityVerificationStatus(params.sessionId);
      if (proximityResult.isLeft()) {
        return Left(proximityResult.fold((l) => l, (r) => throw Exception()));
      }

      final proximityVerified = proximityResult.fold((l) => throw Exception(), (r) => r);

      // Check if both tokens have been scanned
      final tokensResult = await _qrRepository.getActiveTokensForSession(params.sessionId);
      if (tokensResult.isLeft()) {
        return Left(tokensResult.fold((l) => l, (r) => throw Exception()));
      }

      final tokens = tokensResult.fold((l) => throw Exception(), (r) => r);
      final usedTokens = tokens.where((token) => token.status == QRTokenStatus.used).toList();
      final bothTokensScanned = usedTokens.length >= 2 && 
          usedTokens.map((token) => token.userId).toSet().length >= 2;

      // Validate completion requirements
      final validationError = QRValidationBusinessRules.canCompleteValidation(
        session: session,
        proximityVerified: proximityVerified,
        bothTokensScanned: bothTokensScanned,
      );

      if (validationError != null) {
        return Right(QRValidationCompletionResult.failure(
          session: session,
          errorMessage: validationError,
        ));
      }

      // Complete the validation session
      final completionResult = await _qrRepository.completeValidation(
        sessionId: params.sessionId,
        zoneId: session.zoneId,
      );

      if (completionResult.isLeft()) {
        return Left(completionResult.fold((l) => l, (r) => throw Exception()));
      }

      final completedSession = completionResult.fold((l) => throw Exception(), (r) => r);

      // Add community validation to the zone
      ZoneEntity? updatedZone;
      try {
        final validationResult = await _zoneRepository.addCommunityValidation(
          zoneId: session.zoneId,
          validatorUserId: session.validatorUserId!,
        );

        if (validationResult.isRight()) {
          updatedZone = validationResult.fold((l) => throw Exception(), (r) => r);
        }
      } catch (e) {
        // Log the error but don't fail the entire operation
        await _qrRepository.logValidationEvent(
          sessionId: params.sessionId,
          eventType: 'zone_update_failed',
          eventData: {
            'error': e.toString(),
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      // Log completion event
      await _qrRepository.logValidationEvent(
        sessionId: params.sessionId,
        eventType: 'validation_completed',
        eventData: {
          'zoneId': session.zoneId,
          'initiatorUserId': session.initiatorUserId,
          'validatorUserId': session.validatorUserId,
          'proximityVerified': proximityVerified,
          'bothTokensScanned': bothTokensScanned,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return Right(QRValidationCompletionResult.success(
        session: completedSession,
        updatedZone: updatedZone,
        metadata: {
          'completionTimestamp': DateTime.now().toIso8601String(),
          'proximityVerified': proximityVerified,
          'bothTokensScanned': bothTokensScanned,
          'zoneUpdated': updatedZone != null,
        },
      ));

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to complete QR validation: ${e.toString()}'));
    }
  }
}

/// Use case for cancelling a QR validation session
class CancelQRValidationUseCase implements UseCase<QRValidationSessionEntity, String> {
  final QRValidationRepository _qrRepository;

  CancelQRValidationUseCase(this._qrRepository);

  @override
  Future<Either<Failure, QRValidationSessionEntity>> call(String sessionId) async {
    try {
      // Get the validation session
      final sessionResult = await _qrRepository.getValidationSession(sessionId);
      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // Cancel the session
      final cancelledSession = session.cancel();

      // Update the session in the repository
      final updateResult = await _qrRepository.updateValidationSession(cancelledSession);
      if (updateResult.isLeft()) {
        return Left(updateResult.fold((l) => l, (r) => throw Exception()));
      }

      final updatedSession = updateResult.fold((l) => throw Exception(), (r) => r);

      // Log cancellation event
      await _qrRepository.logValidationEvent(
        sessionId: sessionId,
        eventType: 'session_cancelled',
        eventData: {
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return Right(updatedSession);

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel QR validation session: ${e.toString()}'));
    }
  }
}

/// Use case for handling session timeouts
class HandleQRValidationTimeoutUseCase implements UseCase<QRValidationSessionEntity, String> {
  final QRValidationRepository _qrRepository;

  HandleQRValidationTimeoutUseCase(this._qrRepository);

  @override
  Future<Either<Failure, QRValidationSessionEntity>> call(String sessionId) async {
    try {
      // Get the validation session
      final sessionResult = await _qrRepository.getValidationSession(sessionId);
      if (sessionResult.isLeft()) {
        return Left(sessionResult.fold((l) => l, (r) => throw Exception()));
      }

      final session = sessionResult.fold((l) => throw Exception(), (r) => r);

      // Check if session is actually expired
      if (!session.isExpired) {
        return Right(session);
      }

      // Expire the session
      final expiredSession = session.expire();

      // Update the session in the repository
      final updateResult = await _qrRepository.updateValidationSession(expiredSession);
      if (updateResult.isLeft()) {
        return Left(updateResult.fold((l) => l, (r) => throw Exception()));
      }

      final updatedSession = updateResult.fold((l) => throw Exception(), (r) => r);

      // Log expiration event
      await _qrRepository.logValidationEvent(
        sessionId: sessionId,
        eventType: 'session_expired',
        eventData: {
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return Right(updatedSession);

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to handle QR validation timeout: ${e.toString()}'));
    }
  }
}
