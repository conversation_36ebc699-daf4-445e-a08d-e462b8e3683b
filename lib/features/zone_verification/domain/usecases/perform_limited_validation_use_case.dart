import 'package:respublicaseguridad/core/services/limited_tracking_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/limited_tracking_entity.dart';

/// Use case for performing limited validation attempts (5 attempts in 3 days, 5-second samples)
class PerformLimitedValidationUseCase {
  final LimitedTrackingService _limitedTrackingService;

  PerformLimitedValidationUseCase(this._limitedTrackingService);

  /// Perform a limited validation attempt for a zone
  Future<LimitedValidationResult> call(ZoneEntity zone) async {
    try {
      // Check if user can perform validation
      final canValidate = await _limitedTrackingService.canPerformValidation();
      if (!canValidate) {
        final remainingAttempts = await _limitedTrackingService.getRemainingAttempts();
        final daysRemaining = await _limitedTrackingService.getDaysRemainingInWindow();
        
        return LimitedValidationResult.failure(
          error: remainingAttempts > 0 
              ? 'Validation window expired. You had $remainingAttempts attempts remaining.'
              : 'All validation attempts used (5/5). No more attempts available.',
          remainingAttempts: remainingAttempts,
          daysRemainingInWindow: daysRemaining,
        );
      }

      // Perform the validation attempt
      final result = await _limitedTrackingService.performValidationAttempt(zone);
      
      // Get updated status
      final remainingAttempts = await _limitedTrackingService.getRemainingAttempts();
      final daysRemaining = await _limitedTrackingService.getDaysRemainingInWindow();

      switch (result) {
        case ValidationAttemptResult.success:
          return LimitedValidationResult.success(
            message: 'Zone validation successful! You were detected within the zone during the 5-second sample.',
            remainingAttempts: remainingAttempts,
            daysRemainingInWindow: daysRemaining,
          );
          
        case ValidationAttemptResult.failed:
          return LimitedValidationResult.failure(
            error: 'Validation failed. You were not detected within the zone during the 5-second sample.',
            remainingAttempts: remainingAttempts,
            daysRemainingInWindow: daysRemaining,
          );
          
        case ValidationAttemptResult.locationUnavailable:
          return LimitedValidationResult.failure(
            error: 'Location unavailable. Please ensure GPS is enabled and try again.',
            remainingAttempts: remainingAttempts,
            daysRemainingInWindow: daysRemaining,
          );
          
        case ValidationAttemptResult.permissionDenied:
          return LimitedValidationResult.failure(
            error: 'Location permission denied. Please grant location access to perform validation.',
            remainingAttempts: remainingAttempts,
            daysRemainingInWindow: daysRemaining,
          );
          
        case ValidationAttemptResult.timeout:
          return LimitedValidationResult.failure(
            error: 'Validation timed out. Please ensure you have a strong GPS signal and try again.',
            remainingAttempts: remainingAttempts,
            daysRemainingInWindow: daysRemaining,
          );
      }
    } catch (e) {
      return LimitedValidationResult.failure(
        error: 'An unexpected error occurred: ${e.toString()}',
        remainingAttempts: 0,
        daysRemainingInWindow: 0,
      );
    }
  }

  /// Get user's current validation status
  Future<LimitedValidationStatus> getValidationStatus() async {
    try {
      final tracking = await _limitedTrackingService.getUserTrackingStatus();
      if (tracking == null) {
        return const LimitedValidationStatus(
          canPerformValidation: false,
          remainingAttempts: 0,
          daysRemainingInWindow: 0,
          attemptsUsed: 0,
          isInValidationWindow: false,
        );
      }

      return LimitedValidationStatus(
        canPerformValidation: tracking.canAttemptValidation,
        remainingAttempts: tracking.remainingAttempts,
        daysRemainingInWindow: tracking.daysRemainingInWindow,
        attemptsUsed: tracking.validationAttemptsUsed,
        isInValidationWindow: tracking.isInValidationWindow,
        firstZoneAdded: tracking.firstZoneAdded,
        validationHistory: tracking.attempts,
      );
    } catch (e) {
      return const LimitedValidationStatus(
        canPerformValidation: false,
        remainingAttempts: 0,
        daysRemainingInWindow: 0,
        attemptsUsed: 0,
        isInValidationWindow: false,
      );
    }
  }
}

/// Result of a limited validation attempt
class LimitedValidationResult {
  final bool isSuccess;
  final String message;
  final int remainingAttempts;
  final int daysRemainingInWindow;

  const LimitedValidationResult._({
    required this.isSuccess,
    required this.message,
    required this.remainingAttempts,
    required this.daysRemainingInWindow,
  });

  factory LimitedValidationResult.success({
    required String message,
    required int remainingAttempts,
    required int daysRemainingInWindow,
  }) {
    return LimitedValidationResult._(
      isSuccess: true,
      message: message,
      remainingAttempts: remainingAttempts,
      daysRemainingInWindow: daysRemainingInWindow,
    );
  }

  factory LimitedValidationResult.failure({
    required String error,
    required int remainingAttempts,
    required int daysRemainingInWindow,
  }) {
    return LimitedValidationResult._(
      isSuccess: false,
      message: error,
      remainingAttempts: remainingAttempts,
      daysRemainingInWindow: daysRemainingInWindow,
    );
  }
}

/// Current validation status for limited tracking
class LimitedValidationStatus {
  final bool canPerformValidation;
  final int remainingAttempts;
  final int daysRemainingInWindow;
  final int attemptsUsed;
  final bool isInValidationWindow;
  final DateTime? firstZoneAdded; // Changed from firstAppUsage
  final List<ValidationAttempt>? validationHistory;

  const LimitedValidationStatus({
    required this.canPerformValidation,
    required this.remainingAttempts,
    required this.daysRemainingInWindow,
    required this.attemptsUsed,
    required this.isInValidationWindow,
    this.firstZoneAdded,
    this.validationHistory,
  });
}
