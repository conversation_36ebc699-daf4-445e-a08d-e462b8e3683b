import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/stream_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';

/// Use case for watching zone validation changes in real-time
class WatchZoneValidationUseCase implements StreamUseCase<ZoneEntity, WatchZoneValidationParams> {
  final ZoneRepository _repository;

  const WatchZoneValidationUseCase(this._repository);

  @override
  Stream<Either<Failure, ZoneEntity>> call(WatchZoneValidationParams params) {
    try {
      return _repository.watchZone(params.zoneId).map(
        (zoneResult) => zoneResult.fold(
          (failure) => Left(failure),
          (zone) => Right(zone),
        ),
      );
    } catch (e) {
      return Stream.value(
        Left(ServerFailure(message: 'Failed to watch zone validation: ${e.toString()}')),
      );
    }
  }
}

/// Parameters for watching zone validation
class WatchZoneValidationParams extends Equatable {
  final String zoneId;

  const WatchZoneValidationParams({
    required this.zoneId,
  });

  @override
  List<Object?> get props => [zoneId];
}
