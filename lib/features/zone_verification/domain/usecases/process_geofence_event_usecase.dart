import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/start_automatic_validation_usecase.dart';

/// Use case for processing geofence events (enter/exit)
class ProcessGeofenceEventUseCase implements UseCase<GeofenceProcessingResult, ProcessGeofenceEventParams> {
  final ZoneRepository zoneRepository;
  final NotificationService notificationService;

  ProcessGeofenceEventUseCase({
    required this.zoneRepository,
    required this.notificationService,
  });

  @override
  Future<Either<Failure, GeofenceProcessingResult>> call(ProcessGeofenceEventParams params) async {
    try {
      final event = params.event;
      final zone = event.zone;

      // 1. Check if automatic validation is enabled for this zone
      if (!zone.canUseAutomaticValidation) {
        return Right(GeofenceProcessingResult(
          event: event,
          processed: false,
          message: 'Automatic validation not enabled for this zone',
        ));
      }

      // 2. Check if event occurred during presence hours
      final isWithinPresenceHours = _isEventWithinPresenceHours(event, zone);
      if (!isWithinPresenceHours) {
        return Right(GeofenceProcessingResult(
          event: event,
          processed: false,
          message: 'Event occurred outside of configured presence hours',
        ));
      }

      // 3. Check location accuracy
      if (!event.hasGoodAccuracy) {
        return Right(GeofenceProcessingResult(
          event: event,
          processed: false,
          message: 'Location accuracy insufficient for validation (${event.accuracy}m)',
        ));
      }

      // 4. Process the event based on type
      final processingResult = await _processEventByType(event, zone);
      if (processingResult.isLeft()) {
        return Left(processingResult.fold((failure) => failure, (_) => UnexpectedFailure(message: 'Unexpected error')));
      }

      // 5. Show notification if configured
      if (params.showNotifications) {
        await _showGeofenceNotification(event);
      }

      // 6. Record the event for validation tracking
      await _recordGeofenceEvent(event, zone);

      return Right(GeofenceProcessingResult(
        event: event,
        processed: true,
        message: 'Geofence event processed successfully',
        validationTriggered: event.type.isEntry,
      ));

    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Check if event occurred within configured presence hours
  bool _isEventWithinPresenceHours(GeofenceEvent event, ZoneEntity zone) {
    final presenceConfig = zone.presenceHoursConfig;
    if (presenceConfig == null) return false;

    return presenceConfig.isActiveAt(event.timestamp);
  }

  /// Process event based on its type (enter/exit)
  Future<Either<Failure, void>> _processEventByType(GeofenceEvent event, ZoneEntity zone) async {
    switch (event.type) {
      case GeofenceEventType.enter:
        return await _processZoneEntry(event, zone);
      
      case GeofenceEventType.exit:
        return await _processZoneExit(event, zone);
    }
  }

  /// Process zone entry event
  Future<Either<Failure, void>> _processZoneEntry(GeofenceEvent event, ZoneEntity zone) async {
    try {
      // For zone entry, we might want to:
      // 1. Start tracking presence time
      // 2. Begin validation countdown
      // 3. Update zone metadata with entry information

      final updatedZone = zone.copyWith(
        metadata: {
          ...zone.metadata ?? {},
          'lastEntry': event.timestamp.toIso8601String(),
          'lastEntryAccuracy': event.accuracy,
        },
        updatedAt: DateTime.now(),
      );

      final updateResult = await zoneRepository.updateZone(updatedZone);
      return updateResult.fold(
        (failure) => Left(failure),
        (_) => const Right(null),
      );
    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Process zone exit event
  Future<Either<Failure, void>> _processZoneExit(GeofenceEvent event, ZoneEntity zone) async {
    try {
      // For zone exit, we might want to:
      // 1. Calculate dwell time
      // 2. Determine if validation criteria were met
      // 3. Update zone status if validation is complete

      final metadata = zone.metadata ?? {};
      final lastEntryString = metadata['lastEntry'] as String?;
      
      Duration? dwellTime;
      if (lastEntryString != null) {
        final lastEntry = DateTime.parse(lastEntryString);
        dwellTime = event.timestamp.difference(lastEntry);
      }

      final updatedZone = zone.copyWith(
        metadata: {
          ...metadata,
          'lastExit': event.timestamp.toIso8601String(),
          'lastExitAccuracy': event.accuracy,
          if (dwellTime != null) 'lastDwellTimeMinutes': dwellTime.inMinutes,
        },
        updatedAt: DateTime.now(),
      );

      final updateResult = await zoneRepository.updateZone(updatedZone);
      return updateResult.fold(
        (failure) => Left(failure),
        (_) => const Right(null),
      );
    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Show geofence notification
  Future<void> _showGeofenceNotification(GeofenceEvent event) async {
    final notificationType = event.type.isEntry 
        ? GeofenceNotificationType.entered 
        : GeofenceNotificationType.exited;

    await notificationService.showGeofenceNotification(
      zoneName: event.zone.name,
      type: notificationType,
      timestamp: event.timestamp,
    );
  }

  /// Record geofence event for validation tracking
  Future<void> _recordGeofenceEvent(GeofenceEvent event, ZoneEntity zone) async {
    // This could involve storing the event in a local database or sending to backend
    // For now, we'll just update the zone metadata
    // In a full implementation, you might want a separate repository for events
  }
}

/// Parameters for processing geofence events
class ProcessGeofenceEventParams {
  final GeofenceEvent event;
  final bool showNotifications;
  final bool recordEvent;

  const ProcessGeofenceEventParams({
    required this.event,
    this.showNotifications = true,
    this.recordEvent = true,
  });
}

/// Result of geofence event processing
class GeofenceProcessingResult {
  final GeofenceEvent event;
  final bool processed;
  final String message;
  final bool validationTriggered;
  final Duration? dwellTime;
  final Map<String, dynamic>? metadata;

  const GeofenceProcessingResult({
    required this.event,
    required this.processed,
    required this.message,
    this.validationTriggered = false,
    this.dwellTime,
    this.metadata,
  });

  bool get wasSuccessful => processed;
  String get zoneId => event.zoneId;
  String get zoneName => event.zone.name;
  GeofenceEventType get eventType => event.type;
}
