import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/core/services/qr_validation_error_handler.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/qr_validation_bloc.dart';

/// Enhanced error dialog for QR validation with recovery suggestions
class QRErrorDialog extends StatelessWidget {
  final QRValidationErrorState errorState;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const QRErrorDialog({
    super.key,
    required this.errorState,
    this.onRetry,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error icon
            Container(
              width: 64.w,
              height: 64.h,
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getErrorIcon(),
                size: 32.sp,
                color: theme.colorScheme.error,
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Error title
            Text(
              _getErrorTitle(),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 8.h),
            
            // Error message
            Text(
              errorState.message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            
            // Recovery suggestions
            if (errorState.recoverySuggestions.isNotEmpty) ...[
              SizedBox(height: 16.h),
              _buildRecoverySuggestions(context),
            ],
            
            SizedBox(height: 24.h),
            
            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoverySuggestions(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FluentIcons.lightbulb_24_regular,
                size: 16.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                'Sugerencias para resolver el problema:',
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          ...errorState.recoverySuggestions.map((suggestion) => Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 4.w,
                  height: 4.h,
                  margin: EdgeInsets.only(top: 8.h, right: 8.w),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    suggestion,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        // Cancel button
        Expanded(
          child: OutlinedButton(
            onPressed: onCancel ?? () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              'Cancelar',
              style: theme.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        // Retry button (only if error is recoverable)
        if (errorState.isRecoverable && onRetry != null) ...[
          SizedBox(width: 12.w),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry!();
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'Reintentar',
                style: theme.textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  IconData _getErrorIcon() {
    switch (errorState.errorType) {
      case QRValidationErrorType.network:
        return FluentIcons.wifi_off_24_regular;
      case QRValidationErrorType.location:
        return FluentIcons.location_off_24_regular;
      case QRValidationErrorType.permission:
        return FluentIcons.shield_error_24_regular;
      case QRValidationErrorType.timeout:
        return FluentIcons.clock_24_regular;
      case QRValidationErrorType.validation:
        return FluentIcons.error_circle_24_regular;
      case QRValidationErrorType.server:
        return FluentIcons.server_24_regular;
      case QRValidationErrorType.security:
        return FluentIcons.shield_keyhole_24_regular;
      case QRValidationErrorType.unknown:
      default:
        return FluentIcons.warning_24_regular;
    }
  }

  String _getErrorTitle() {
    switch (errorState.errorType) {
      case QRValidationErrorType.network:
        return 'Error de Conexión';
      case QRValidationErrorType.location:
        return 'Error de Ubicación';
      case QRValidationErrorType.permission:
        return 'Permisos Requeridos';
      case QRValidationErrorType.timeout:
        return 'Tiempo Agotado';
      case QRValidationErrorType.validation:
        return 'Error de Validación';
      case QRValidationErrorType.server:
        return 'Error del Servidor';
      case QRValidationErrorType.security:
        return 'Error de Seguridad';
      case QRValidationErrorType.unknown:
      default:
        return 'Error Inesperado';
    }
  }

  /// Show error dialog with enhanced error handling
  static Future<void> show({
    required BuildContext context,
    required QRValidationErrorState errorState,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => QRErrorDialog(
        errorState: errorState,
        onRetry: onRetry,
        onCancel: onCancel,
      ),
    );
  }
}

/// Simple error snackbar for less critical errors
class QRErrorSnackBar {
  static void show({
    required BuildContext context,
    required String message,
    QRValidationErrorType? errorType,
    VoidCallback? onRetry,
  }) {
    final theme = Theme.of(context);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getErrorIcon(errorType),
              color: Colors.white,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        action: onRetry != null
            ? SnackBarAction(
                label: 'Reintentar',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  static IconData _getErrorIcon(QRValidationErrorType? errorType) {
    switch (errorType) {
      case QRValidationErrorType.network:
        return Icons.wifi_off;
      case QRValidationErrorType.location:
        return Icons.location_off;
      case QRValidationErrorType.permission:
        return Icons.security;
      case QRValidationErrorType.timeout:
        return Icons.access_time;
      case QRValidationErrorType.validation:
        return Icons.error_outline;
      case QRValidationErrorType.server:
        return Icons.dns;
      case QRValidationErrorType.security:
        return Icons.security;
      case QRValidationErrorType.unknown:
      default:
        return Icons.warning;
    }
  }
}
