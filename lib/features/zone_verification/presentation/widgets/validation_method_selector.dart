import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Reusable validation method selector component
class ValidationMethodSelector extends StatelessWidget {
  final ValidationMethod? selectedMethod;
  final ValueChanged<ValidationMethod> onMethodSelected;
  final bool isRequired;
  final String? errorText;
  final bool showDescriptions;

  const ValidationMethodSelector({
    super.key,
    this.selectedMethod,
    required this.onMethodSelected,
    this.isRequired = false,
    this.errorText,
    this.showDescriptions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Validation Method',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        _buildMethodCards(context),
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMethodCards(BuildContext context) {
    return Column(
      children: ValidationMethod.values.map((method) =>
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildMethodCard(context, method),
        ),
      ).toList(),
    );
  }

  Widget _buildMethodCard(BuildContext context, ValidationMethod method) {
    final isSelected = selectedMethod == method;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onMethodSelected(method),
          borderRadius: BorderRadius.circular(4.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: isDark ? theme.cardColor : Colors.white,
              borderRadius: BorderRadius.circular(4.r),
              border: isSelected
                  ? Border.all(
                      color: theme.colorScheme.primary,
                      width: 1.5,
                    )
                  : null,
              boxShadow: isDark
                  ? []
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        offset: const Offset(0, 2),
                        blurRadius: 8.r,
                      ),
                    ],
            ),
            child: Stack(
              children: [
                // Subtle pattern for selected cards
                if (isSelected)
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(2.r),
                      child: CustomPaint(
                        painter: ValidationPatternPainter(
                          opacity: 0.03,
                          isDark: isDark,
                          primaryColor: theme.colorScheme.primary,
                          method: method,
                        ),
                        child: Container(),
                      ),
                    ),
                  ),
                // Main content
                Row(
                  children: [
                    Container(
                      width: 44.w,
                      height: 44.h,
                      decoration: BoxDecoration(
                        color: _getMethodColor(context, method).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Icon(
                        _getMethodIcon(method),
                        size: 22.sp,
                        color: _getMethodColor(context, method),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getMethodTitle(method),
                            style: theme.textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (showDescriptions) ...[
                            SizedBox(height: 2.h),
                            Text(
                              _getMethodDescription(method),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (isSelected)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          'Seleccionado',
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getMethodColor(BuildContext context, ValidationMethod method) {
    switch (method) {
      case ValidationMethod.social:
        return Theme.of(context).colorScheme.primary;
      case ValidationMethod.automatic:
        return Theme.of(context).colorScheme.secondary;
    }
  }



  IconData _getMethodIcon(ValidationMethod method) {
    switch (method) {
      case ValidationMethod.social:
        return FluentIcons.qr_code_24_filled; // Now uses QR icon for social validation
      case ValidationMethod.automatic:
        return FluentIcons.location_24_filled;
    }
  }

  String _getMethodTitle(ValidationMethod method) {
    switch (method) {
      case ValidationMethod.social:
        return 'Social Validation';
      case ValidationMethod.automatic:
        return 'Automatic Validation';
    }
  }

  String _getMethodDescription(ValidationMethod method) {
    switch (method) {
      case ValidationMethod.social:
        return 'Secure QR-based validation requiring physical proximity between users';
      case ValidationMethod.automatic:
        return 'Always-on validation using location tracking during your presence hours';
    }
  }


}

/// Compact version of validation method selector
class ValidationMethodToggle extends StatelessWidget {
  final ValidationMethod selectedMethod;
  final ValueChanged<ValidationMethod> onMethodChanged;

  const ValidationMethodToggle({
    super.key,
    required this.selectedMethod,
    required this.onMethodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleOption(
              context,
              ValidationMethod.social,
              'Social',
              Icons.people,
            ),
          ),
          Expanded(
            child: _buildToggleOption(
              context,
              ValidationMethod.automatic,
              'Automatic',
              Icons.auto_mode,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption(
    BuildContext context,
    ValidationMethod method,
    String label,
    IconData icon,
  ) {
    final isSelected = selectedMethod == method;

    return GestureDetector(
      onTap: () => onMethodChanged(method),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isSelected ? Colors.white : Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ValidationPatternPainter extends CustomPainter {
  final double opacity;
  final bool isDark;
  final Color primaryColor;
  final ValidationMethod method;

  ValidationPatternPainter({
    required this.opacity,
    required this.isDark,
    required this.primaryColor,
    required this.method,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor.withValues(alpha: opacity)
      ..style = PaintingStyle.fill;

    if (method == ValidationMethod.social) {
      // Draw QR code pattern for social validation (now QR-based)
      _drawQRPattern(canvas, size, paint);
    } else if (method == ValidationMethod.automatic) {
      // Draw location/GPS pattern
      _drawAutomaticPattern(canvas, size, paint);
    }
  }



  void _drawAutomaticPattern(Canvas canvas, Size size, Paint paint) {
    // Draw subtle location pin and signal patterns
    final centerX = size.width * 0.8;
    final centerY = size.height * 0.4;

    // Location pin shape
    final path = Path();
    path.addOval(Rect.fromCircle(center: Offset(centerX, centerY), radius: 12));
    canvas.drawPath(path, paint);

    // Signal rings
    for (int i = 1; i <= 3; i++) {
      final strokePaint = Paint()
        ..color = primaryColor.withValues(alpha: opacity * 0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      canvas.drawCircle(
        Offset(centerX, centerY),
        12.0 + (i * 8),
        strokePaint,
      );
    }
  }

  void _drawQRPattern(Canvas canvas, Size size, Paint paint) {
    // Draw subtle QR code pattern
    final centerX = size.width * 0.8;
    final centerY = size.height * 0.4;
    final squareSize = 4.0;

    // Draw QR-like grid pattern
    for (int i = 0; i < 5; i++) {
      for (int j = 0; j < 5; j++) {
        // Create a checkerboard-like pattern
        if ((i + j) % 2 == 0) {
          final rect = Rect.fromLTWH(
            centerX - 10 + (i * squareSize),
            centerY - 10 + (j * squareSize),
            squareSize - 0.5,
            squareSize - 0.5,
          );
          canvas.drawRect(rect, paint);
        }
      }
    }

    // Draw corner squares (typical QR code markers)
    final cornerPaint = Paint()
      ..color = primaryColor.withValues(alpha: opacity * 0.7)
      ..style = PaintingStyle.fill;

    // Top-left corner
    canvas.drawRect(
      Rect.fromLTWH(centerX - 12, centerY - 12, 6, 6),
      cornerPaint,
    );

    // Top-right corner
    canvas.drawRect(
      Rect.fromLTWH(centerX + 6, centerY - 12, 6, 6),
      cornerPaint,
    );

    // Bottom-left corner
    canvas.drawRect(
      Rect.fromLTWH(centerX - 12, centerY + 6, 6, 6),
      cornerPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
