import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:qr_flutter/qr_flutter.dart';

/// Professional QR Code widget with logo embedding and customizable styling
class ProfessionalQRWidget extends StatelessWidget {
  final String data;
  final String? title;
  final String? subtitle;
  final double? size;
  final Color? primaryColor;
  final Color? backgroundColor;
  final String? logoAssetPath;
  final double? logoSize;
  final VoidCallback? onTap;
  final bool showBorder;
  final bool showShadow;
  final String? semanticsLabel;

  const ProfessionalQRWidget({
    super.key,
    required this.data,
    this.title,
    this.subtitle,
    this.size,
    this.primaryColor,
    this.backgroundColor,
    this.logoAssetPath = 'assets/logo.png',
    this.logoSize,
    this.onTap,
    this.showBorder = true,
    this.showShadow = true,
    this.semanticsLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final qrSize = size ?? 240.w;
    final effectiveLogoSize = logoSize ?? (qrSize * 0.23); // 23% of QR size
    final effectivePrimaryColor = primaryColor ?? const Color(0xFF003B8E);
    final effectiveBackgroundColor = backgroundColor ?? Colors.white;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: showBorder
            ? Border.all(
                color: effectivePrimaryColor.withValues(alpha: 0.1),
                width: 1,
              )
            : null,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.08),
                  blurRadius: 6.r,
                  offset: Offset(0, 2.h),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header section
                if (title != null) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          color: effectivePrimaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Icon(
                          FluentIcons.qr_code_24_filled,
                          color: effectivePrimaryColor,
                          size: 20.w,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Flexible(
                        child: Text(
                          title!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: effectivePrimaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: 8.h),
                    Text(
                      subtitle!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  SizedBox(height: 20.h),
                ],

                // QR Code with professional styling
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: effectiveBackgroundColor,
                    borderRadius: BorderRadius.circular(10.r),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.1),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                        blurRadius: 4.r,
                        offset: Offset(0, 1.h),
                      ),
                    ],
                  ),
                  child: QrImageView(
                    data: data,
                    size: qrSize,
                    backgroundColor: effectiveBackgroundColor,
                    errorCorrectionLevel: QrErrorCorrectLevel.H,
                    embeddedImage: logoAssetPath != null
                        ? AssetImage(logoAssetPath!)
                        : null,
                    embeddedImageStyle: logoAssetPath != null
                        ? QrEmbeddedImageStyle(
                            size: Size(effectiveLogoSize, effectiveLogoSize),
                            color: null, // Keep original logo colors
                          )
                        : null,
                    eyeStyle: QrEyeStyle(
                      eyeShape: QrEyeShape.square,
                      color: effectivePrimaryColor,
                    ),
                    dataModuleStyle: QrDataModuleStyle(
                      dataModuleShape: QrDataModuleShape.square,
                      color: effectivePrimaryColor,
                    ),
                    padding: EdgeInsets.all(12.w),
                    gapless: true,
                    semanticsLabel: semanticsLabel ?? 'QR Code',
                  ),
                ),

                // Tap hint if onTap is provided
                if (onTap != null) ...[
                  SizedBox(height: 12.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerLowest,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          FluentIcons.tap_single_24_regular,
                          size: 14.w,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'Tap for options',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 11.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Compact version of the professional QR widget for smaller spaces
class CompactQRWidget extends StatelessWidget {
  final String data;
  final double? size;
  final Color? primaryColor;
  final String? logoAssetPath;
  final VoidCallback? onTap;

  const CompactQRWidget({
    super.key,
    required this.data,
    this.size,
    this.primaryColor,
    this.logoAssetPath = 'assets/logo.png',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final qrSize = size ?? 120.w;
    final effectivePrimaryColor = primaryColor ?? const Color(0xFF003B8E);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.08),
                blurRadius: 4.r,
                offset: Offset(0, 2.h),
              ),
            ],
          ),
          child: QrImageView(
            data: data,
            size: qrSize,
            backgroundColor: Colors.white,
            errorCorrectionLevel: QrErrorCorrectLevel.H,
            embeddedImage: logoAssetPath != null
                ? AssetImage(logoAssetPath!)
                : null,
            embeddedImageStyle: logoAssetPath != null
                ? QrEmbeddedImageStyle(
                    size: Size(qrSize * 0.2, qrSize * 0.2),
                    color: null,
                  )
                : null,
            eyeStyle: QrEyeStyle(
              eyeShape: QrEyeShape.square,
              color: effectivePrimaryColor,
            ),
            dataModuleStyle: QrDataModuleStyle(
              dataModuleShape: QrDataModuleShape.square,
              color: effectivePrimaryColor,
            ),
            padding: EdgeInsets.all(8.w),
            gapless: true,
          ),
        ),
      ),
    );
  }
}
