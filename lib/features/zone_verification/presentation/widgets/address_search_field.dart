import 'package:flutter/material.dart';

/// Data class for address suggestions
class AddressSuggestion {
  final String address;
  final String? description;
  final double? latitude;
  final double? longitude;
  final String? placeId;

  const AddressSuggestion({
    required this.address,
    this.description,
    this.latitude,
    this.longitude,
    this.placeId,
  });
}

/// Reusable address search field with autocomplete
class AddressSearchField extends StatefulWidget {
  final String? initialValue;
  final ValueChanged<AddressSuggestion>? onAddressSelected;
  final ValueChanged<String>? onTextChanged;
  final String? hintText;
  final String? labelText;
  final bool isRequired;
  final String? errorText;
  final Future<List<AddressSuggestion>> Function(String query)? onSearch;
  final VoidCallback? onCurrentLocationPressed;
  final bool showCurrentLocationButton;

  const AddressSearchField({
    super.key,
    this.initialValue,
    this.onAddressSelected,
    this.onTextChanged,
    this.hintText,
    this.labelText,
    this.isRequired = false,
    this.errorText,
    this.onSearch,
    this.onCurrentLocationPressed,
    this.showCurrentLocationButton = true,
  });

  @override
  State<AddressSearchField> createState() => _AddressSearchFieldState();
}

class _AddressSearchFieldState extends State<AddressSearchField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<AddressSuggestion> _suggestions = [];
  bool _isLoading = false;
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    widget.onTextChanged?.call(text);
    
    if (text.length >= 3 && widget.onSearch != null) {
      _searchAddresses(text);
    } else {
      setState(() {
        _suggestions = [];
        _showSuggestions = false;
      });
    }
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  Future<void> _searchAddresses(String query) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final suggestions = await widget.onSearch!(query);
      if (mounted) {
        setState(() {
          _suggestions = suggestions;
          _showSuggestions = suggestions.isNotEmpty;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _suggestions = [];
          _showSuggestions = false;
          _isLoading = false;
        });
      }
    }
  }

  void _onSuggestionSelected(AddressSuggestion suggestion) {
    _controller.text = suggestion.address;
    widget.onAddressSelected?.call(suggestion);
    setState(() {
      _showSuggestions = false;
    });
    _focusNode.unfocus();
  }

  

  @override
  Widget build(BuildContext context) {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          Row(
            children: [
              Text(
                widget.labelText!,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        Stack(
          children: [
            TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Enter address',
                errorText: widget.errorText,
                prefixIcon: const Icon(Icons.location_on_outlined),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_isLoading)
                      const Padding(
                        padding: EdgeInsets.all(12),
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                    if (widget.showCurrentLocationButton)
                      IconButton(
                        onPressed: widget.onCurrentLocationPressed,
                        icon: const Icon(Icons.my_location),
                        tooltip: 'Use current location',
                      ),
                  ],
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              ),
            ),
            if (_showSuggestions && _suggestions.isNotEmpty)
              Positioned(
                top: 56,
                left: 0,
                right: 0,
                child: _buildSuggestionsList(),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildSuggestionsList() {
    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).colorScheme.outline),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: _suggestions.length,
          separatorBuilder: (context, index) => Divider(
            height: 1,
            color: Theme.of(context).colorScheme.outline,
          ),
          itemBuilder: (context, index) {
            final suggestion = _suggestions[index];
            return ListTile(
              dense: true,
              leading: const Icon(
                Icons.location_on_outlined,
                size: 20,
              ),
              title: Text(
                suggestion.address,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: suggestion.description != null
                  ? Text(
                      suggestion.description!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    )
                  : null,
              onTap: () => _onSuggestionSelected(suggestion),
            );
          },
        ),
      ),
    );
  }
}

/// Simple address input field without autocomplete
class SimpleAddressField extends StatelessWidget {
  final TextEditingController? controller;
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final String? hintText;
  final String? labelText;
  final bool isRequired;
  final String? errorText;
  final VoidCallback? onCurrentLocationPressed;
  final bool showCurrentLocationButton;
  final int maxLines;

  const SimpleAddressField({
    super.key,
    this.controller,
    this.initialValue,
    this.onChanged,
    this.hintText,
    this.labelText,
    this.isRequired = false,
    this.errorText,
    this.onCurrentLocationPressed,
    this.showCurrentLocationButton = true,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Row(
            children: [
              Text(
                labelText!,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: controller,
          initialValue: controller == null ? initialValue : null,
          onChanged: onChanged,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hintText ?? 'Enter address',
            errorText: errorText,
            prefixIcon: const Icon(Icons.location_on_outlined),
            suffixIcon: showCurrentLocationButton
                ? IconButton(
                    onPressed: onCurrentLocationPressed,
                    icon: const Icon(Icons.my_location),
                    tooltip: 'Use current location',
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
        ),
      ],
    );
  }
}
