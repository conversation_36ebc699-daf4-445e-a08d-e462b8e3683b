import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/zone_verification_widgets.dart';

class FullScreenLocationPicker extends StatefulWidget {
  final LatLng? initialLocation;
  final String? initialAddress;
  final Function(LatLng location, String address, String locationName)? onLocationSelected;
  final VoidCallback? onCancel;

  const FullScreenLocationPicker({
    super.key,
    this.initialLocation,
    this.initialAddress,
    this.onLocationSelected,
    this.onCancel,
  });

  @override
  State<FullScreenLocationPicker> createState() => _FullScreenLocationPickerState();
}

class _FullScreenLocationPickerState extends State<FullScreenLocationPicker>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  String _selectedAddress = '';
  String _locationName = '';
  bool _isLoadingAddress = false;
  bool _showSearchOverlay = false;

  // Animation controllers
  late AnimationController _markerAnimationController;
  late AnimationController _overlayAnimationController;
  late Animation<double> _markerScaleAnimation;
  late Animation<double> _overlayOpacityAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _markerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _overlayAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _markerScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _markerAnimationController,
      curve: Curves.elasticOut,
    ));

    _overlayOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayAnimationController,
      curve: Curves.easeInOut,
    ));

    // Set initial location if provided
    if (widget.initialLocation != null) {
      _selectedLocation = widget.initialLocation;
      _selectedAddress = widget.initialAddress ?? '';
      _locationName = widget.initialAddress ?? '';
      // Animate marker to show the location is selected
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _markerAnimationController.forward();
      });
    }
    // Note: We don't automatically get current location anymore
    // User must explicitly tap the current location button or select a location
  }



  @override
  void dispose() {
    _markerAnimationController.dispose();
    _overlayAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Full screen map
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _selectedLocation ?? widget.initialLocation ?? const LatLng(40.7128, -74.0060),
              zoom: 15.0,
            ),
            onTap: _onMapTapped,
            markers: _buildMarkers(),
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            compassEnabled: true,
            style: _getMapStyle(theme),
          ),

          // Top app bar
          _buildTopAppBar(theme),

        
          if (_selectedLocation != null) _buildBottomInfoPanel(theme),

          // // Floating action buttons
          // _buildFloatingButtons(theme),

          // Loading overlay when getting location
          if (_isLoadingAddress)
            _buildLoadingOverlay(theme),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay(ThemeData theme) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Getting your current location...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppBar(ThemeData theme) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 8.h,
          left: 16.w,
          right: 16.w,
          bottom: 16.h,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            // Back button
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: IconButton(
                onPressed: widget.onCancel,
                icon: Icon(
                  CupertinoIcons.back,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
            
            SizedBox(width: 16.w),
            
            // Title
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  'Select Location',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ),
          
          ],
        ),
      ),
    );
  }

  Widget _buildSearchOverlay(ThemeData theme) {
    return AnimatedBuilder(
      animation: _overlayOpacityAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _overlayOpacityAnimation.value,
          child: Container(
            margin: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 80.h,
              left: 16.w,
              right: 16.w,
            ),
            child: AddressSearchField(
              initialValue: _selectedAddress,
              hintText: 'Search location...',
              onAddressSelected: _onAddressSearchSelected,
              onCurrentLocationPressed: _requestCurrentLocation,
              showCurrentLocationButton: true,
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomInfoPanel(ThemeData theme) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location name
            Row(
              children: [
                Icon(
                  FluentIcons.location_24_filled,
                  color: theme.colorScheme.primary,
                  size: 20.r,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    _isLoadingAddress ? 'Loading location...' : _locationName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Address
            Text(
              _selectedAddress,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            
            SizedBox(height: 8.h),
            
            // Coordinates
            Row(
              children: [
                Icon(
                  FluentIcons.location_arrow_24_regular,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  size: 16.r,
                ),
                SizedBox(width: 4.w),
                Text(
                  'Lat: ${_selectedLocation!.latitude.toStringAsFixed(6)}, '
                  'Lng: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 20.h),
            
            // Confirm button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoadingAddress ? null : _confirmSelection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: _isLoadingAddress
                    ? SizedBox(
                        height: 20.h,
                        width: 20.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.onPrimary,
                          ),
                        ),
                      )
                    : Text(
                        'Confirm Location',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingButtons(ThemeData theme) {
    return Positioned(
      right: 16.w,
      bottom: _selectedLocation != null ? 200.h : 100.h,
      child: Column(
        children: [
          // Current location button
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              onPressed: _requestCurrentLocation,
              icon: Icon(
                FluentIcons.my_location_24_regular,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Set<Marker> _buildMarkers() {
    final markers = <Marker>{};

    if (_selectedLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: _selectedLocation!,
          infoWindow: InfoWindow(
            title: _locationName,
            snippet: _selectedAddress,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }

    return markers;
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _onMapTapped(LatLng location) async {
    setState(() {
      _selectedLocation = location;
      _isLoadingAddress = true;
      _selectedAddress = '${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}';
      _locationName = 'Selected Location';
    });

    _markerAnimationController.reset();
    _markerAnimationController.forward();

    // Get better address and location name
    try {
      final locationService = LocationService.instance;
      final address = await locationService.getAddressFromCoordinates(
        location.latitude,
        location.longitude,
      );
      final locationName = await locationService.getBetterLocationName(
        location.latitude,
        location.longitude,
      );

      if (mounted) {
        setState(() {
          _selectedAddress = address;
          _locationName = locationName;
          _isLoadingAddress = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAddress = false;
        });
      }
    }
  }

  void _onAddressSearchSelected(AddressSuggestion address) {
    if (address.latitude != null && address.longitude != null) {
      final location = LatLng(address.latitude!, address.longitude!);
      setState(() {
        _selectedLocation = location;
        _selectedAddress = address.address;
        _locationName = address.address;
        _showSearchOverlay = false;
      });

      _markerAnimationController.reset();
      _markerAnimationController.forward();

      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(location, 16.0),
        );
      }
    }
  }

  void _requestCurrentLocation() async {
    try {
      final locationService = LocationService.instance;
      final position = await locationService.getCurrentPosition();

      if (position != null && mounted) {
        final location = LatLng(position.latitude, position.longitude);

        setState(() {
          _selectedLocation = location;
          _isLoadingAddress = true;
          _selectedAddress = 'Current Location';
          _locationName = 'Current Location';
        });

        _markerAnimationController.reset();
        _markerAnimationController.forward();

        if (_mapController != null) {
          _mapController!.animateCamera(
            CameraUpdate.newLatLngZoom(location, 16.0),
          );
        }

        // Get better address and location name
        try {
          final address = await locationService.getAddressFromCoordinates(
            location.latitude,
            location.longitude,
          );
          final locationName = await locationService.getBetterLocationName(
            location.latitude,
            location.longitude,
          );

          if (mounted) {
            setState(() {
              _selectedAddress = address;
              _locationName = locationName;
              _isLoadingAddress = false;
            });
          }
        } catch (e) {
          if (mounted) {
            setState(() {
              _isLoadingAddress = false;
            });
          }
        }
      }
    } catch (e) {
      // Handle location error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unable to get current location: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _toggleSearchOverlay() {
    setState(() {
      _showSearchOverlay = !_showSearchOverlay;
    });

    if (_showSearchOverlay) {
      _overlayAnimationController.forward();
    } else {
      _overlayAnimationController.reverse();
    }
  }

  void _confirmSelection() {
    if (_selectedLocation != null) {
      widget.onLocationSelected?.call(
        _selectedLocation!,
        _selectedAddress,
        _locationName,
      );
    }
  }

  String? _getMapStyle(ThemeData theme) {
    if (theme.brightness == Brightness.dark) {
      return '''[
        {
          "elementType": "geometry",
          "stylers": [{"color": "#212121"}]
        },
        {
          "elementType": "labels.icon",
          "stylers": [{"visibility": "off"}]
        },
        {
          "elementType": "labels.text.fill",
          "stylers": [{"color": "#757575"}]
        },
        {
          "elementType": "labels.text.stroke",
          "stylers": [{"color": "#212121"}]
        },
        {
          "featureType": "administrative",
          "elementType": "geometry",
          "stylers": [{"color": "#757575"}]
        },
        {
          "featureType": "road",
          "elementType": "geometry.fill",
          "stylers": [{"color": "#2c2c2c"}]
        },
        {
          "featureType": "road.arterial",
          "elementType": "labels.text.fill",
          "stylers": [{"color": "#757575"}]
        },
        {
          "featureType": "water",
          "elementType": "geometry",
          "stylers": [{"color": "#000000"}]
        }
      ]''';
    }
    return null; // Use default style for light theme
  }
}
