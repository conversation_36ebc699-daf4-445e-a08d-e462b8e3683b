import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Professional zone type selector with clean card design
class ZoneTypeSelector extends StatelessWidget {
  final ZoneType? selectedType;
  final ValueChanged<ZoneType> onTypeSelected;
  final bool isRequired;
  final String? errorText;
  final bool isCompact;

  const ZoneTypeSelector({
    super.key,
    this.selectedType,
    required this.onTypeSelected,
    this.isRequired = false,
    this.errorText,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isCompact) ...[
          Text(
            'Zone Type${isRequired ? ' *' : ''}',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF150050),
            ),
          ),
          SizedBox(height: 12.h),
        ],
        _buildTypeGrid(context),
        if (errorText != null) ...[
          SizedBox(height: 8.h),
          Text(
            errorText!,
            style: GoogleFonts.outfit(
              fontSize: 12.sp,
              color: const Color(0xFFD80032),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTypeGrid(BuildContext context) {
    final types = ZoneType.values;

    if (isCompact) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: types.map((type) =>
            Padding(
              padding: EdgeInsets.only(right: 12.w),
              child: _buildCompactChip(context, type),
            ),
          ).toList(),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        childAspectRatio: 3.0, // Even more compact
      ),
      itemCount: types.length,
      itemBuilder: (context, index) => _buildZoneTypeCard(context, types[index]),
    );
  }

  Widget _buildZoneTypeCard(BuildContext context, ZoneType type) {
    final isSelected = selectedType == type;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => onTypeSelected(type),
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1A1D2E) : Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: isSelected
              ? Border.all(color: _getTypeColor(context, type), width: 2)
              : null,
          boxShadow: isDark
              ? []
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          child: Row(
            children: [
              // Icon container
              Container(
                width: 36.w,
                height: 36.h,
                decoration: BoxDecoration(
                  color: _getTypeColor(context, type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: SvgPicture.string(
                    type.svgIcon,
                    width: 20.w,
                    height: 20.h,
                    colorFilter: ColorFilter.mode(
                      _getTypeColor(context, type),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              // Type name and selection indicator
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _getTypeText(type),
                      style: GoogleFonts.outfit(
                        fontSize: 14.sp,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? _getTypeColor(context, type)
                            : (isDark ? Colors.white : const Color(0xFF150050)),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (isSelected) ...[
                      SizedBox(height: 4.h),
                      Container(
                        width: 4.w,
                        height: 4.h,
                        decoration: BoxDecoration(
                          color: _getTypeColor(context, type),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactChip(BuildContext context, ZoneType type) {
    final isSelected = selectedType == type;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => onTypeSelected(type),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        decoration: BoxDecoration(
          color: isSelected
              ? _getTypeColor(context, type).withValues(alpha: 0.1)
              : (isDark ? const Color(0xFF1A1D2E) : Colors.white),
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(
            color: isSelected
                ? _getTypeColor(context, type)
                : (isDark ? Colors.white24 : const Color(0xFFE0E0E0)),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isDark
              ? []
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.string(
              type.svgIcon,
              width: 16.w,
              height: 16.h,
              colorFilter: ColorFilter.mode(
                _getTypeColor(context, type),
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              _getTypeText(type),
              style: GoogleFonts.outfit(
                fontSize: 13.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? _getTypeColor(context, type)
                    : (isDark ? Colors.white : const Color(0xFF150050)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(BuildContext context, ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return const Color(0xFF4CAF50); // Professional green
      case ZoneType.work:
        return const Color(0xFF3F0071); // Professional purple (matching PropertyCard)
      case ZoneType.university:
        return const Color(0xFFFF9800); // Professional orange
      case ZoneType.other:
        return const Color(0xFF666666); // Professional gray
    }
  }

  String _getTypeText(ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return 'Home';
      case ZoneType.work:
        return 'Work';
      case ZoneType.university:
        return 'University';
      case ZoneType.other:
        return 'Other';
    }
  }
}

/// Dropdown version of zone type selector
class ZoneTypeDropdown extends StatelessWidget {
  final ZoneType? selectedType;
  final ValueChanged<ZoneType?> onChanged;
  final String? hintText;
  final bool isRequired;
  final String? errorText;

  const ZoneTypeDropdown({
    super.key,
    this.selectedType,
    required this.onChanged,
    this.hintText,
    this.isRequired = false,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<ZoneType>(
          value: selectedType,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hintText ?? 'Select Zone Type',
            errorText: errorText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
          items: ZoneType.values.map((type) {
            return DropdownMenuItem<ZoneType>(
              value: type,
              child: Row(
                children: [
                  SvgPicture.string(
                    type.svgIcon,
                    width: 18,
                    height: 18,
                    colorFilter: ColorFilter.mode(
                      _getTypeColor(context, type),
                      BlendMode.srcIn,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getTypeText(type),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Color _getTypeColor(BuildContext context, ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return const Color(0xFF4CAF50); // Professional green
      case ZoneType.work:
        return const Color(0xFF3F0071); // Professional purple (matching PropertyCard)
      case ZoneType.university:
        return const Color(0xFFFF9800); // Professional orange
      case ZoneType.other:
        return const Color(0xFF666666); // Professional gray
    }
  }

  String _getTypeText(ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return 'Home';
      case ZoneType.work:
        return 'Work';
      case ZoneType.university:
        return 'University';
      case ZoneType.other:
        return 'Other';
    }
  }
}
