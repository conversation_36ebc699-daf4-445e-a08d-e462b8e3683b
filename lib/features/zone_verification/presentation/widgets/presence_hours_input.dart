import 'package:flutter/material.dart' hide TimeOfDay;
import 'package:flutter/material.dart' as material show TimeOfDay;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Data class for time range
class TimeRange {
  final material.TimeOfDay start;
  final material.TimeOfDay end;

  const TimeRange({
    required this.start,
    required this.end,
  });

  @override
  String toString() {
    return '${_formatTime(start)} - ${_formatTime(end)}';
  }

  String _formatTime(material.TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  bool isValid() {
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;
    return endMinutes > startMinutes;
  }
}

/// Reusable presence hours input component
class PresenceHoursInput extends StatefulWidget {
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final String? labelText;
  final bool isRequired;
  final String? errorText;
  final bool allowCustomInput;

  const PresenceHoursInput({
    super.key,
    this.initialValue,
    this.onChanged,
    this.labelText,
    this.isRequired = false,
    this.errorText,
    this.allowCustomInput = true,
  });

  @override
  State<PresenceHoursInput> createState() => _PresenceHoursInputState();
}

class _PresenceHoursInputState extends State<PresenceHoursInput> {
  final TextEditingController _controller = TextEditingController();
  String? _selectedPreset;
  bool _isCustom = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
      _checkIfPreset(widget.initialValue!);
    }
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onChanged?.call(_controller.text);
    _checkIfPreset(_controller.text);
  }

  void _checkIfPreset(String value) {
    final presets = _getPresets();
    final preset = presets.firstWhere(
      (type) => type.timeRange == value,
      orElse: () => PresenceHourType.custom,
    );

    setState(() {
      _selectedPreset = preset == PresenceHourType.custom ? null : preset.label;
      _isCustom = preset == PresenceHourType.custom && value.isNotEmpty;
    });
  }

  void _selectPreset(PresenceHourType type) {
    _controller.text = type.timeRange;
    setState(() {
      _selectedPreset = type.label;
      _isCustom = false;
    });
  }

  void _selectCustom() {
    setState(() {
      _selectedPreset = null;
      _isCustom = true;
    });
  }

  Future<void> _selectTimeRange() async {
    final startTime = await showTimePicker(
      context: context,
      initialTime: const material.TimeOfDay(hour: 9, minute: 0),
      helpText: 'Select start time',
    );

    if (startTime != null && mounted) {
      final endTime = await showTimePicker(
        context: context,
        initialTime: material.TimeOfDay(
          hour: (startTime.hour + 8) % 24,
          minute: startTime.minute,
        ),
        helpText: 'Select end time',
      );

      if (endTime != null && mounted) {
        final timeRange = TimeRange(start: startTime, end: endTime);
        if (timeRange.isValid()) {
          _controller.text = timeRange.toString();
          setState(() {
            _selectedPreset = null;
            _isCustom = true;
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Invalid time range'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final presets = _getPresets();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          Row(
            children: [
              Text(
                widget.labelText!,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        _buildPresetChips(presets),
        const SizedBox(height: 8),
        if (_isCustom || widget.allowCustomInput) ...[
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1A1D2E)
                  : Colors.white,
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: widget.errorText != null
                    ? Theme.of(context).colorScheme.error
                    : Theme.of(context).colorScheme.outline,
                width: 1,
              ),
              boxShadow: Theme.of(context).brightness == Brightness.dark
                  ? []
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: TextFormField(
              controller: _controller,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : const Color(0xFF150050),
              ),
              decoration: InputDecoration(
                hintText: 'Enter custom presence hours',
                hintStyle: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.6)
                      : const Color(0xFF666666),
                ),
                errorText: widget.errorText,
                errorStyle: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: Theme.of(context).colorScheme.error,
                ),
                prefixIcon: Container(
                  width: 24.w,
                  height: 24.h,
                  margin: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: PresenceHourType.custom.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Center(
                    child: SvgPicture.string(
                      '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
</svg>''',
                      width: 16.w,
                      height: 16.h,
                      colorFilter: ColorFilter.mode(
                        PresenceHourType.custom.color,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
                suffixIcon: GestureDetector(
                  onTap: _selectTimeRange,
                  child: Container(
                    width: 24.w,
                    height: 24.h,
                    margin: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Center(
                      child: SvgPicture.string(
                        '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5M9 12.75h6m-6 3h6" />
</svg>''',
                        width: 16.w,
                        height: 16.h,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).colorScheme.primary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              ),
            ),
          ),
        ] else if (_selectedPreset != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              border: Border.all(color: Theme.of(context).colorScheme.primary),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _controller.text,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (widget.allowCustomInput)
                  TextButton(
                    onPressed: _selectCustom,
                    child: const Text('Customize'),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPresetChips(List<PresenceHourType> presets) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 6.h,
        childAspectRatio: 2.5, // Much higher ratio for shorter height
      ),
      itemCount: presets.length + (widget.allowCustomInput ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < presets.length) {
          return _buildPresenceHourChip(presets[index]);
        } else {
          return _buildCustomChip();
        }
      },
    );
  }

  Widget _buildPresenceHourChip(PresenceHourType type) {
    final isSelected = _selectedPreset == type.label;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _selectPreset(type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1A1D2E) : Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: isSelected
              ? Border.all(color: type.color, width: 2)
              : null,
          boxShadow: isDark
              ? isSelected
                  ? [
                      BoxShadow(
                        color: type.color.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
              : isSelected
                  ? [
                      BoxShadow(
                        color: type.color.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: type.color.withValues(alpha: 0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.04),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                        spreadRadius: 0,
                      ),
                    ],
        ),
        child: Row(
          children: [
            Container(
              width: 18.w,
              height: 18.h,
              decoration: BoxDecoration(
                color: type.color.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: SvgPicture.string(
                  type.svgIcon,
                  width: 12.w,
                  height: 12.h,
                  colorFilter: ColorFilter.mode(
                    type.color,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    type.label,
                    style: GoogleFonts.outfit(
                      fontSize: 10.sp,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected
                          ? type.color
                          : (isDark ? Colors.white : const Color(0xFF150050)),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    type.timeRange,
                    style: GoogleFonts.outfit(
                      fontSize: 8.sp,
                      fontWeight: FontWeight.w400,
                      color: isDark
                          ? Colors.white.withValues(alpha: 0.7)
                          : const Color(0xFF666666),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (isSelected) ...[
              Container(
                width: 6.w,
                height: 6.h,
                decoration: BoxDecoration(
                  color: type.color,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomChip() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: _selectCustom,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1A1D2E) : Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: _isCustom
              ? Border.all(color: PresenceHourType.custom.color, width: 2)
              : null,
          boxShadow: isDark
              ? _isCustom
                  ? [
                      BoxShadow(
                        color: PresenceHourType.custom.color.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
              : _isCustom
                  ? [
                      BoxShadow(
                        color: PresenceHourType.custom.color.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: PresenceHourType.custom.color.withValues(alpha: 0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.04),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                        spreadRadius: 0,
                      ),
                    ],
        ),
        child: Row(
          children: [
            Container(
              width: 18.w,
              height: 18.h,
              decoration: BoxDecoration(
                color: PresenceHourType.custom.color.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: SvgPicture.string(
                  PresenceHourType.custom.svgIcon,
                  width: 12.w,
                  height: 12.h,
                  colorFilter: ColorFilter.mode(
                    PresenceHourType.custom.color,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Text(
                'Custom',
                style: GoogleFonts.outfit(
                  fontSize: 10.sp,
                  fontWeight: _isCustom ? FontWeight.w600 : FontWeight.w500,
                  color: _isCustom
                      ? PresenceHourType.custom.color
                      : (isDark ? Colors.white : const Color(0xFF150050)),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (_isCustom) ...[
              Container(
                width: 6.w,
                height: 6.h,
                decoration: BoxDecoration(
                  color: PresenceHourType.custom.color,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<PresenceHourType> _getPresets() {
    return [
      PresenceHourType.fullTime,
      PresenceHourType.partTime,
      PresenceHourType.mornings,
      PresenceHourType.afternoons,
      PresenceHourType.evenings,
      PresenceHourType.weekends,
    ];
  }
}

/// Simple presence hours field without presets
class SimplePresenceHoursField extends StatelessWidget {
  final TextEditingController? controller;
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final String? hintText;
  final String? labelText;
  final bool isRequired;
  final String? errorText;

  const SimplePresenceHoursField({
    super.key,
    this.controller,
    this.initialValue,
    this.onChanged,
    this.hintText,
    this.labelText,
    this.isRequired = false,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Row(
            children: [
              Text(
                labelText!,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: controller,
          initialValue: controller == null ? initialValue : null,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hintText ?? 'Enter presence hours',
            errorText: errorText,
            prefixIcon: const Icon(Icons.schedule_outlined),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
        ),
      ],
    );
  }
}
