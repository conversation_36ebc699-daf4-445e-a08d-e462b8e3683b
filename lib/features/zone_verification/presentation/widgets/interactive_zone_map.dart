import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/address_search_field.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/full_screen_location_picker.dart';


class InteractiveZoneMap extends StatefulWidget {
  final LatLng? initialLocation;
  final String? initialAddress;
  final double height;
  final Function(LatLng location, String address)? onLocationSelected;
  final Function(AddressSuggestion address)? onAddressSelected;
  final Function()? onCurrentLocationRequested;
  final Function(bool isFullScreen)? onFullScreenChanged;
  final bool showLocationInfo;
  final bool showSearchOverlay;
  final bool isInteractive;
  final bool autoRequestLocation;
  final Set<Marker>? additionalMarkers;
  final Set<Circle>? circles;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final bool showFullScreenToggle;

  const InteractiveZoneMap({
    super.key,
    this.initialLocation,
    this.initialAddress,
    this.height = 200,
    this.onLocationSelected,
    this.onAddressSelected,
    this.onCurrentLocationRequested,
    this.onFullScreenChanged,
    this.showLocationInfo = true,
    this.showSearchOverlay = false,
    this.isInteractive = true,
    this.autoRequestLocation = true,
    this.additionalMarkers,
    this.circles,
    this.margin,
    this.borderRadius,
    this.showFullScreenToggle = false,
  });

  @override
  State<InteractiveZoneMap> createState() => _InteractiveZoneMapState();
}

class _InteractiveZoneMapState extends State<InteractiveZoneMap>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  String _selectedAddress = '';
  bool _showSearchOverlay = false;
  bool _isFullScreen = false;

  // Animation controllers for smooth transitions
  late AnimationController _markerAnimationController;
  late AnimationController _overlayAnimationController;
  late Animation<double> _markerScaleAnimation;
  late Animation<double> _overlayOpacityAnimation;

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _selectedAddress = widget.initialAddress ?? '';
    _showSearchOverlay = widget.showSearchOverlay;

    // Initialize animations
    _markerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _overlayAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _markerScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _markerAnimationController,
      curve: Curves.elasticOut,
    ));

    _overlayOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayAnimationController,
      curve: Curves.easeInOut,
    ));

    if (_selectedLocation != null) {
      _markerAnimationController.forward();
    }

    // Auto-request current location if enabled and no initial location provided
    if (widget.autoRequestLocation && widget.initialLocation == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _requestCurrentLocation();
      });
    }
  }

  @override
  void dispose() {
    _markerAnimationController.dispose();
    _overlayAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: _isFullScreen ? MediaQuery.of(context).size.height : widget.height.h,
      margin: _isFullScreen ? EdgeInsets.zero : (widget.margin ?? EdgeInsets.all(16.w)),
      decoration: BoxDecoration(
        borderRadius: _isFullScreen ? BorderRadius.zero : (widget.borderRadius ?? BorderRadius.circular(4.r)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        boxShadow: _isFullScreen ? [] : [
          // Main shadow
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.15),
            blurRadius: 24,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          // Inner glow effect
          BoxShadow(
            color: theme.colorScheme.surface,
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: -4,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Main map container
          _buildMapContainer(theme),
         
          if (widget.showFullScreenToggle)
            _buildFullScreenToggle(theme),
        ],
      ),
    );
  }

  Widget _buildMapContainer(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(_isFullScreen ? 0 : 3.w), // Padding for the border effect
      decoration: BoxDecoration(
        borderRadius: _isFullScreen ? BorderRadius.zero : (widget.borderRadius ?? BorderRadius.circular(20.r)),
        gradient: _isFullScreen ? null : LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.3),
            theme.colorScheme.secondary.withValues(alpha: 0.2),
            theme.colorScheme.tertiary.withValues(alpha: 0.1),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: ClipRRect(
        borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(17.r), // Slightly smaller radius for inner content
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(17.r),
            border: _isFullScreen ? null : Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
              width: 0.5,
            ),
          ),
          child: ClipRRect(
            borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(16.5.r),
            child: GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: CameraPosition(
            target: widget.initialLocation ?? const LatLng(40.7128, -74.0060),
            zoom: 15.0,
          ),
          onTap: widget.isInteractive ? _onMapTapped : null,
          markers: _buildMarkers(),
          circles: widget.circles ?? {},
          myLocationEnabled: true,
          myLocationButtonEnabled: false, // We'll use custom button
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: false,
          style: _getMapStyle(theme),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchOverlay(ThemeData theme) {
    return AnimatedBuilder(
      animation: _overlayOpacityAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _overlayOpacityAnimation.value,
          child: Container(
            margin: EdgeInsets.all(12.w),
            child: AddressSearchField(
              initialValue: _selectedAddress,
              hintText: 'Search location...',
              onAddressSelected: _onAddressSearchSelected,
              onCurrentLocationPressed: widget.onCurrentLocationRequested,
              showCurrentLocationButton: widget.autoRequestLocation,
            ),
          ),
        );
      },
    );
  }

  Widget _buildLocationInfoOverlay(ThemeData theme) {
    return Positioned(
      bottom: 12.h,
      left: 12.w,
      right: 60.w, // Leave space for control buttons
      child: AnimatedBuilder(
        animation: _markerScaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _markerScaleAnimation.value,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    FluentIcons.location_24_filled,
                    color: theme.colorScheme.primary,
                    size: 16.r,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedAddress.isNotEmpty
                              ? _selectedAddress
                              : 'Selected Location',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (_selectedLocation != null) ...[
                          SizedBox(height: 2.h),
                          Text(
                            '${_selectedLocation!.latitude.toStringAsFixed(4)}, ${_selectedLocation!.longitude.toStringAsFixed(4)}',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLocationPlaceholder(ThemeData theme) {
    return Positioned(
      bottom: 12.h,
      left: 12.w,
      right: 60.w, // Leave space for control buttons
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              FluentIcons.location_add_24_regular,
              color: theme.colorScheme.primary,
              size: 16.r,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                'Tap on the map to select location',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Set<Marker> _buildMarkers() {
    final markers = <Marker>{};
    
    // Add selected location marker
    if (_selectedLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: _selectedLocation!,
          infoWindow: InfoWindow(
            title: 'Selected Location',
            snippet: _selectedAddress,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }
    
    // Add additional markers
    if (widget.additionalMarkers != null) {
      markers.addAll(widget.additionalMarkers!);
    }
    
    return markers;
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _requestCurrentLocation() {
    // Trigger the callback to request current location
    widget.onCurrentLocationRequested?.call();
  }

  void _onMapTapped(LatLng location) async {
    // Get current location first to use as initial location
    LatLng? currentLocation;
    String currentAddress = 'Getting current location...';

    try {
      final locationService = LocationService.instance;
      final position = await locationService.getCurrentPosition();

      if (position != null) {
        currentLocation = LatLng(position.latitude, position.longitude);
        currentAddress = await locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
      }
    } catch (e) {
      // Fallback to tapped location if current location fails
      currentLocation = location;
      currentAddress = 'Tapped Location';
    }

    // Open full-screen location picker starting with current location
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => FullScreenLocationPicker(
          initialLocation: currentLocation ?? location,
          initialAddress: currentAddress,
          onLocationSelected: (selectedLocation, address, locationName) {
            Navigator.of(context).pop({
              'location': selectedLocation,
              'address': address,
              'locationName': locationName,
            });
          },
          onCancel: () => Navigator.of(context).pop(),
        ),
      ),
    );

    // Handle the result from the full-screen picker
    if (result != null && mounted) {
      final selectedLocation = result['location'] as LatLng;
      final address = result['address'] as String;

      setState(() {
        _selectedLocation = selectedLocation;
        _selectedAddress = address;
      });

      _markerAnimationController.reset();
      _markerAnimationController.forward();

      // Move camera to selected location
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(selectedLocation, 16.0),
        );
      }

      // Notify parent about location selection
      widget.onLocationSelected?.call(selectedLocation, address);
    }
  }

  void _onAddressSearchSelected(AddressSuggestion address) {
    if (address.latitude != null && address.longitude != null) {
      final location = LatLng(address.latitude!, address.longitude!);
      setState(() {
        _selectedLocation = location;
        _selectedAddress = address.address;
      });
      
      _markerAnimationController.reset();
      _markerAnimationController.forward();
      
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(location, 16.0),
        );
      }
      
      widget.onAddressSelected?.call(address);
      widget.onLocationSelected?.call(location, address.address);
    }
  }



  String? _getMapStyle(ThemeData theme) {
    // Custom map style for professional appearance
    if (theme.brightness == Brightness.dark) {
      return '''[
        {
          "elementType": "geometry",
          "stylers": [{"color": "#212121"}]
        },
        {
          "elementType": "labels.icon",
          "stylers": [{"visibility": "off"}]
        },
        {
          "elementType": "labels.text.fill",
          "stylers": [{"color": "#757575"}]
        },
        {
          "elementType": "labels.text.stroke",
          "stylers": [{"color": "#212121"}]
        },
        {
          "featureType": "administrative",
          "elementType": "geometry",
          "stylers": [{"color": "#757575"}]
        },
        {
          "featureType": "administrative.country",
          "elementType": "labels.text.fill",
          "stylers": [{"color": "#9e9e9e"}]
        },
        {
          "featureType": "road",
          "elementType": "geometry.fill",
          "stylers": [{"color": "#2c2c2c"}]
        },
        {
          "featureType": "road.arterial",
          "elementType": "labels.text.fill",
          "stylers": [{"color": "#757575"}]
        },
        {
          "featureType": "water",
          "elementType": "geometry",
          "stylers": [{"color": "#000000"}]
        }
      ]''';
    }
    return null; // Use default style for light theme
  }

  /// Update location from external source
  void updateLocation(LatLng location, String address) {
    setState(() {
      _selectedLocation = location;
      _selectedAddress = address;
    });

    _markerAnimationController.reset();
    _markerAnimationController.forward();

    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(location, 16.0),
      );
    }
  }

  /// Get current selected location
  LatLng? get selectedLocation => _selectedLocation;

  /// Get current selected address
  String get selectedAddress => _selectedAddress;

  /// Build the full screen toggle chip
  Widget _buildFullScreenToggle(ThemeData theme) {
    // Calculate bottom padding to avoid overflow
    // Add extra padding when in full screen mode to account for the 173px overflow
    final bottomPadding = _isFullScreen 
        ? 24.h + MediaQuery.of(context).padding.bottom + 173.0
        : 24.h;
        
    return Positioned(
      bottom: bottomPadding,
      left: 0,
      right: 0,
      child: Center(
        child: InkWell(
          onTap: () {
            setState(() {
              _isFullScreen = !_isFullScreen;
            });
            // Notify parent about full screen state change
            widget.onFullScreenChanged?.call(_isFullScreen);
            
            // Notify map controller to resize
            if (_mapController != null) {
              Future.delayed(const Duration(milliseconds: 300), () {
                _mapController!.moveCamera(CameraUpdate.zoomBy(0.01));
                _mapController!.moveCamera(CameraUpdate.zoomBy(-0.01));
              });
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withOpacity(0.8),
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.4),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _isFullScreen 
                      ? FluentIcons.full_screen_minimize_24_regular 
                      : FluentIcons.full_screen_maximize_24_regular,
                  color: theme.colorScheme.primary,
                  size: 20.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  _isFullScreen ? 'Exit Full Screen' : 'Full Screen',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
