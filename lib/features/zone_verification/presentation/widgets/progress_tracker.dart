import 'package:flutter/material.dart';

/// Reusable progress tracker component for displaying validation progress
class ProgressTracker extends StatelessWidget {
  final int current;
  final int total;
  final String? label;
  final bool showProgress;
  final bool showNumbers;
  final Color? progressColor;
  final Color? backgroundColor;
  final double height;
  final bool isCompact;

  const ProgressTracker({
    super.key,
    required this.current,
    required this.total,
    this.label,
    this.showProgress = true,
    this.showNumbers = true,
    this.progressColor,
    this.backgroundColor,
    this.height = 6,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final progress = total > 0 ? (current / total).clamp(0.0, 1.0) : 0.0;
    final effectiveProgressColor = progressColor ?? Theme.of(context).colorScheme.primary;
    final effectiveBackgroundColor = backgroundColor ?? Theme.of(context).colorScheme.surface;

    if (isCompact) {
      return _buildCompactTracker(progress, effectiveProgressColor, effectiveBackgroundColor);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null || showNumbers) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (label != null)
                Text(
                  label!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (showNumbers)
                Text(
                  '$current/$total',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        if (showProgress)
          _buildProgressBar(progress, effectiveProgressColor, effectiveBackgroundColor),
      ],
    );
  }

  Widget _buildCompactTracker(double progress, Color progressColor, Color backgroundColor) {
    return Row(
      children: [
        Expanded(
          child: _buildProgressBar(progress, progressColor, backgroundColor),
        ),
        if (showNumbers) ...[
          const SizedBox(width: 8),
          Text(
            '$current/$total',
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildProgressBar(double progress, Color progressColor, Color backgroundColor) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(height / 2),
        child: LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
        ),
      ),
    );
  }
}

/// Specialized progress tracker for validation steps
class ValidationProgressTracker extends StatelessWidget {
  final List<ValidationStep> steps;
  final int currentStep;
  final bool isVertical;

  const ValidationProgressTracker({
    super.key,
    required this.steps,
    required this.currentStep,
    this.isVertical = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isVertical) {
      return _buildVerticalTracker(context);
    } else {
      return _buildHorizontalTracker(context);
    }
  }

  Widget _buildHorizontalTracker(BuildContext context) {
    return Row(
      children: [
        for (int i = 0; i < steps.length; i++) ...[
          _buildStepIndicator(context, i),
          if (i < steps.length - 1)
            Expanded(
              child: _buildConnector(i < currentStep),
            ),
        ],
      ],
    );
  }

  Widget _buildVerticalTracker(BuildContext context) {
    return Column(
      children: [
        for (int i = 0; i < steps.length; i++) ...[
          _buildVerticalStepItem(context, i),
          if (i < steps.length - 1)
            _buildVerticalConnector(i < currentStep),
        ],
      ],
    );
  }

  Widget _buildStepIndicator(BuildContext context, int index) {
    final step = steps[index];
    final isCompleted = index < currentStep;
    final isCurrent = index == currentStep;
    final isUpcoming = index > currentStep;

    Color backgroundColor;
    Color borderColor;
    Color iconColor;
    Widget icon;

    if (isCompleted) {
      backgroundColor = Colors.green;
      borderColor = Colors.green;
      iconColor = Colors.white;
      icon = const Icon(Icons.check, size: 16);
    } else if (isCurrent) {
      backgroundColor = Theme.of(context).colorScheme.primary;
      borderColor = Theme.of(context).colorScheme.primary;
      iconColor = Colors.white;
      icon = Text(
        '${index + 1}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      );
    } else {
      backgroundColor = Theme.of(context).colorScheme.surface;
      borderColor = Theme.of(context).colorScheme.outline;
      iconColor = Theme.of(context).colorScheme.onSurfaceVariant;
      icon = Text(
        '${index + 1}',
        style: TextStyle(
          color: iconColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      );
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: borderColor, width: 2),
        shape: BoxShape.circle,
      ),
      child: Center(child: icon),
    );
  }

  Widget _buildVerticalStepItem(BuildContext context, int index) {
    final step = steps[index];
    final isCompleted = index < currentStep;
    final isCurrent = index == currentStep;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStepIndicator(context, index),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                step.title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w500,
                  color: isCompleted || isCurrent
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              if (step.description != null) ...[
                const SizedBox(height: 2),
                Text(
                  step.description!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildConnector(bool isCompleted) {
    return Container(
      height: 2,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isCompleted ? Colors.green : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  Widget _buildVerticalConnector(bool isCompleted) {
    return Container(
      width: 2,
      height: 24,
      margin: const EdgeInsets.only(left: 15, top: 4, bottom: 4),
      decoration: BoxDecoration(
        color: isCompleted ? Colors.green : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }
}

/// Data class for validation steps
class ValidationStep {
  final String title;
  final String? description;
  final IconData? icon;

  const ValidationStep({
    required this.title,
    this.description,
    this.icon,
  });
}

/// Circular progress indicator with percentage
class CircularProgressTracker extends StatelessWidget {
  final double progress;
  final String? label;
  final Color? progressColor;
  final Color? backgroundColor;
  final double size;
  final double strokeWidth;
  final bool showPercentage;

  const CircularProgressTracker({
    super.key,
    required this.progress,
    this.label,
    this.progressColor,
    this.backgroundColor,
    this.size = 60,
    this.strokeWidth = 4,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveProgressColor = progressColor ?? Theme.of(context).colorScheme.primary;
    final effectiveBackgroundColor = backgroundColor ?? Theme.of(context).colorScheme.surface;
    final percentage = (progress * 100).round();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: 1.0,
                strokeWidth: strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(effectiveBackgroundColor),
              ),
              CircularProgressIndicator(
                value: progress,
                strokeWidth: strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
                backgroundColor: Colors.transparent,
              ),
              if (showPercentage)
                Center(
                  child: Text(
                    '$percentage%',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
            ],
          ),
        ),
        if (label != null) ...[
          const SizedBox(height: 8),
          Text(
            label!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
