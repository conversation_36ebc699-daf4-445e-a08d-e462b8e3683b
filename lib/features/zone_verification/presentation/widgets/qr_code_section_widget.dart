import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/qr_validation_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/professional_qr_widget.dart';

class QRCodeSectionWidget extends StatefulWidget {
  final int remainingSeconds;

  const QRCodeSectionWidget({
    super.key,
    required this.remainingSeconds,
  });

  @override
  State<QRCodeSectionWidget> createState() => _QRCodeSectionWidgetState();
}

class _QRCodeSectionWidgetState extends State<QRCodeSectionWidget> {
  bool _hasShownExpirationWarning = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<QRValidationBloc, QRValidationState>(
      listener: (context, state) {
        _handleStateNotifications(context, state);
      },
      builder: (context, state) {
        // Check for expiration warning using post-frame callback to avoid build-time dialog
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _checkExpirationWarning(context, state);
          }
        });
        return _buildQRContent(context, theme, state);
      },
    );
  }

  void _checkExpirationWarning(BuildContext context, QRValidationState state) {
    if (state is QRTokenGenerated &&
        widget.remainingSeconds <= 10 &&
        widget.remainingSeconds > 0 &&
        !_hasShownExpirationWarning) {
      _hasShownExpirationWarning = true;
      _showExpirationWarning(context);
    }

    // Reset warning flag when new token is generated
    if (state is QRTokenGenerated && widget.remainingSeconds > 10) {
      _hasShownExpirationWarning = false;
    }
  }

  void _handleStateNotifications(BuildContext context, QRValidationState state) {
    if (state is QRTokenScanned) {
      // Show notification when someone scans the QR code
      _showScanNotification(context, state);
    } else if (state is QRValidationCompleted) {
      // Show success notification when validation is completed and zone count increases
      _showValidationCompletedNotification(context, state);
    }
  }

  void _showScanNotification(BuildContext context, QRTokenScanned state) {
    // Check if context is still valid and widget is mounted
    if (!mounted || !context.mounted) return;

    IosDialog.showAlertDialog(
      context: context,
      title: '¡QR Escaneado!',
      message: 'Tu código QR ha sido escaneado exitosamente. '
          'Esperando confirmación de validación...',
      confirmText: 'Entendido',
    );
  }

  void _showValidationCompletedNotification(BuildContext context, QRValidationCompleted state) {
    // Check if context is still valid and widget is mounted
    if (!mounted || !context.mounted) return;

    final isSuccessful = state.result.isSuccessful;
    final hasZoneUpdate = state.result.updatedZone != null;

    if (isSuccessful && hasZoneUpdate) {
      // Zone validation count increased - show success message
      IosDialog.showAlertDialog(
        context: context,
        title: '¡Validación Exitosa!',
        message: 'La validación social se completó correctamente. '
            'El contador de validaciones de la zona ha sido actualizado.',
        confirmText: 'Excelente',
      );
    } else if (isSuccessful) {
      // Validation completed but no zone update
      IosDialog.showAlertDialog(
        context: context,
        title: 'Validación Completada',
        message: 'La validación social se completó exitosamente.',
        confirmText: 'OK',
      );
    } else {
      // Validation failed
      IosDialog.showAlertDialog(
        context: context,
        title: 'Validación Fallida',
        message: state.result.errorMessage ?? 'La validación no pudo completarse.',
        confirmText: 'Entendido',
      );
    }
  }

  void _showExpirationWarning(BuildContext context) {
    // Check if context is still valid and widget is mounted
    if (!mounted || !context.mounted) return;

    IosDialog.showAlertDialog(
      context: context,
      title: '⏰ Código Expirando',
      message: 'Tu código QR expirará en menos de 10 segundos. '
          'Asegúrate de que tu compañero lo escanee pronto.',
      confirmText: 'Entendido',
    );
  }

  Widget _buildQRContent(BuildContext context, ThemeData theme, QRValidationState state) {

    // Show loading state when generating token
    if (state is QRValidationLoading &&
        (state.message?.contains('token') == true || state.message?.contains('Generar') == true)) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Lottie loading animation
          Container(
            width: 200.w,
            height: 200.w,
            child: Lottie.asset(
              'assets/animations/qr_loading.json',
              width: 200.w,
              height: 200.w,
              fit: BoxFit.contain,
              repeat: true,
              // Fallback to simple animation if file doesn't exist
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 200.w,
                  height: 200.w,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 50.r,
                        height: 50.r,
                        child: CircularProgressIndicator(
                          strokeWidth: 4,
                          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'Generando QR...',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'Creando código QR seguro',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    // Show generated QR token
    if (state is QRTokenGenerated) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Clean QR code without borders
          ProfessionalQRWidget(
            data: state.token.encryptedData,
            size: 220.w,
            logoAssetPath: 'assets/logo.png',
            semanticsLabel: 'QR Code for validation',
          ),

          SizedBox(height: 16.h),

          // Simple counter text below QR
          Text(
            'Expira en ${widget.remainingSeconds} segundos',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: widget.remainingSeconds <= 10
                ? theme.colorScheme.error
                : theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 8.h),

          Text(
            'Pide a tu compañero que escanee este código',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    // Clean empty state - just show minimal content
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 100.h), // Space where QR will appear

        Icon(
          Icons.qr_code_2_rounded,
          size: 60.r,
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),

        SizedBox(height: 16.h),

        Text(
          'Preparando código QR...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: 100.h), // Space for balance
      ],
    );
  }
}
