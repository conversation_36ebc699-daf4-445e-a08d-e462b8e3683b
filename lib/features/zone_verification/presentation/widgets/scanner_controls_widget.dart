import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';

class ScannerControlsWidget extends StatelessWidget {
  final VoidCallback onCancel;

  const ScannerControlsWidget({
    super.key,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      color: theme.colorScheme.surface,
      child: Center(
        child: CustomButton(
          text: 'Cancelar Escaneo',
          onPressed: onCancel,
          prefixIcon: Icon(
            Icons.close,
            color: theme.colorScheme.error,
          ),
          buttonType: ButtonType.outlined,
          height: 48.h,
          width: 200.w,
        ),
      ),
    );
  }
}
