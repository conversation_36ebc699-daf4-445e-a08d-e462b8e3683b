import 'package:flutter/material.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Size options for status indicator
enum StatusIndicatorSize {
  small,
  medium,
  large,
}

/// Reusable status indicator component for displaying validation status
class StatusIndicator extends StatelessWidget {
  final ZoneStatus status;
  final StatusIndicatorSize size;
  final bool showLabel;
  final bool showIcon;

  const StatusIndicator({
    super.key,
    required this.status,
    this.size = StatusIndicatorSize.medium,
    this.showLabel = false,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: Border.all(
          color: _getBorderColor(context),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              _getIcon(),
              size: _getIconSize(),
              color: _getIconColor(context),
            ),
            if (showLabel) SizedBox(width: _getSpacing()),
          ],
          if (showLabel)
            Text(
              _getLabel(),
              style: _getTextStyle(context),
            ),
        ],
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case StatusIndicatorSize.small:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 6, vertical: 2)
            : const EdgeInsets.all(4);
      case StatusIndicatorSize.medium:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
            : const EdgeInsets.all(6);
      case StatusIndicatorSize.large:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 12, vertical: 6)
            : const EdgeInsets.all(8);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 4;
      case StatusIndicatorSize.medium:
        return 6;
      case StatusIndicatorSize.large:
        return 8;
    }
  }

  double _getIconSize() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 12;
      case StatusIndicatorSize.medium:
        return 16;
      case StatusIndicatorSize.large:
        return 20;
    }
  }

  double _getSpacing() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 4;
      case StatusIndicatorSize.medium:
        return 6;
      case StatusIndicatorSize.large:
        return 8;
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final fontSize = switch (size) {
      StatusIndicatorSize.small => 11.0,
      StatusIndicatorSize.medium => 12.0,
      StatusIndicatorSize.large => 14.0,
    };

    return TextStyle(
      fontSize: fontSize,
      color: _getTextColor(context),
      fontWeight: FontWeight.w500,
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    switch (status) {
      case ZoneStatus.validated:
        return Colors.green.withValues(alpha: 0.1);
      case ZoneStatus.pending:
        return Colors.orange.withValues(alpha: 0.1);
      case ZoneStatus.rejected:
        return Theme.of(context).colorScheme.error.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor(BuildContext context) {
    switch (status) {
      case ZoneStatus.validated:
        return Colors.green.withValues(alpha: 0.3);
      case ZoneStatus.pending:
        return Colors.orange.withValues(alpha: 0.3);
      case ZoneStatus.rejected:
        return Theme.of(context).colorScheme.error.withValues(alpha: 0.3);
    }
  }

  Color _getIconColor(BuildContext context) {
    switch (status) {
      case ZoneStatus.validated:
        return Colors.green;
      case ZoneStatus.pending:
        return Colors.orange;
      case ZoneStatus.rejected:
        return Theme.of(context).colorScheme.error;
    }
  }

  Color _getTextColor(BuildContext context) {
    switch (status) {
      case ZoneStatus.validated:
        return Colors.green;
      case ZoneStatus.pending:
        return Colors.orange;
      case ZoneStatus.rejected:
        return Theme.of(context).colorScheme.error;
    }
  }

  IconData _getIcon() {
    switch (status) {
      case ZoneStatus.validated:
        return Icons.check_circle;
      case ZoneStatus.pending:
        return Icons.schedule;
      case ZoneStatus.rejected:
        return Icons.cancel;
    }
  }

  String _getLabel() {
    switch (status) {
      case ZoneStatus.validated:
        return 'Validated';
      case ZoneStatus.pending:
        return 'Pending';
      case ZoneStatus.rejected:
        return 'Rejected';
    }
  }
}

/// Specialized status indicator for validation method
class ValidationMethodIndicator extends StatelessWidget {
  final ValidationMethod method;
  final StatusIndicatorSize size;
  final bool showLabel;

  const ValidationMethodIndicator({
    super.key,
    required this.method,
    this.size = StatusIndicatorSize.medium,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: Border.all(
          color: _getBorderColor(context),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIcon(),
            size: _getIconSize(),
            color: _getIconColor(context),
          ),
          if (showLabel) ...[
            SizedBox(width: _getSpacing()),
            Text(
              _getLabel(),
              style: _getTextStyle(context),
            ),
          ],
        ],
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case StatusIndicatorSize.small:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 6, vertical: 2)
            : const EdgeInsets.all(4);
      case StatusIndicatorSize.medium:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
            : const EdgeInsets.all(6);
      case StatusIndicatorSize.large:
        return showLabel 
            ? const EdgeInsets.symmetric(horizontal: 12, vertical: 6)
            : const EdgeInsets.all(8);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 4;
      case StatusIndicatorSize.medium:
        return 6;
      case StatusIndicatorSize.large:
        return 8;
    }
  }

  double _getIconSize() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 12;
      case StatusIndicatorSize.medium:
        return 16;
      case StatusIndicatorSize.large:
        return 20;
    }
  }

  double _getSpacing() {
    switch (size) {
      case StatusIndicatorSize.small:
        return 4;
      case StatusIndicatorSize.medium:
        return 6;
      case StatusIndicatorSize.large:
        return 8;
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final fontSize = switch (size) {
      StatusIndicatorSize.small => 11.0,
      StatusIndicatorSize.medium => 12.0,
      StatusIndicatorSize.large => 14.0,
    };

    return TextStyle(
      fontSize: fontSize,
      color: _getTextColor(context),
      fontWeight: FontWeight.w500,
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    switch (method) {
      case ValidationMethod.social:
        return Theme.of(context).colorScheme.primary.withValues(alpha: 0.1);
      case ValidationMethod.automatic:
        return Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor(BuildContext context) {
    switch (method) {
      case ValidationMethod.social:
        return Theme.of(context).colorScheme.primary.withValues(alpha: 0.3);
      case ValidationMethod.automatic:
        return Theme.of(context).colorScheme.secondary.withValues(alpha: 0.3);
    }
  }

  Color _getIconColor(BuildContext context) {
    switch (method) {
      case ValidationMethod.social:
        return Theme.of(context).colorScheme.primary;
      case ValidationMethod.automatic:
        return Theme.of(context).colorScheme.secondary;
    }
  }

  Color _getTextColor(BuildContext context) {
    switch (method) {
      case ValidationMethod.social:
        return Theme.of(context).colorScheme.primary;
      case ValidationMethod.automatic:
        return Theme.of(context).colorScheme.secondary;
    }
  }

  IconData _getIcon() {
    switch (method) {
      case ValidationMethod.social:
        return Icons.qr_code; // Social validation now uses QR code
      case ValidationMethod.automatic:
        return Icons.auto_mode;
    }
  }

  String _getLabel() {
    switch (method) {
      case ValidationMethod.social:
        return 'Social Validation';
      case ValidationMethod.automatic:
        return 'Automatic Validation';
    }
  }
}
