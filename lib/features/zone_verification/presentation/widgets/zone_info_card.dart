import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/status_indicator.dart';

/// Enum for validation attempt status in heatmap
enum ValidationAttemptStatus {
  none,     // No attempt (gray)
  failed,   // Failed attempt (red)
  partial,  // Partial success (yellow)
  success,  // Successful attempt (green)
}

/// A high-quality, slick flat zone information card with GitHub-like validation progress
class ZoneInfoCard extends StatelessWidget {
  final ZoneEntity zone;

  const ZoneInfoCard({
    super.key,
    required this.zone,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          _buildDivider(context),
          _buildInfoSection(context),
          _buildValidationProgress(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  zone.name,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.5,
                  ),
                ),
              ),
              StatusIndicator(
                status: zone.validationStatus,
                size: StatusIndicatorSize.medium,
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Row(
            children: [
              Icon(
                FluentIcons.location_24_regular,
                size: 14.r,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Text(
                  zone.address,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 1,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            theme.colorScheme.outline.withValues(alpha: 0.1),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildInfoRow(
            context,
            'Zone Type',
            _getZoneTypeLabel(zone.type),
            FluentIcons.building_24_regular,
          ),
          SizedBox(height: 8.h),
          _buildInfoRow(
            context,
            'Presence Hours',
            zone.presenceHours ?? 'Not specified',
            FluentIcons.clock_24_regular,
          ),
          SizedBox(height: 8.h),
          _buildInfoRow(
            context,
            'Validation Method',
            _getValidationMethodLabel(zone.validationMethod),
            FluentIcons.shield_checkmark_24_regular,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          width: 28.w,
          height: 28.w,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            size: 14.r,
            color: theme.colorScheme.primary,
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                  fontSize: 11.sp,
                ),
              ),
              SizedBox(height: 1.h),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  height: 1.2,
                  fontSize: 13.sp,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildValidationProgress(BuildContext context) {
    final theme = Theme.of(context);

    final isSocialValidation = zone.validationMethod == ValidationMethod.social;
    final title = isSocialValidation ? 'Validation Attempts' : 'Automatic Validation Activity';
    final statusText = isSocialValidation
        ? '${zone.communityValidationCount}/3 successful'
        : _getAutomaticValidationStatus();

    final remaining = isSocialValidation ? 3 - zone.communityValidationCount : 0;

    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 13.sp,
                ),
              ),
              Text(
                statusText,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                  fontSize: 11.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          _buildValidationHeatmap(context),
          SizedBox(height: 6.h),
          _buildHeatmapLegend(context),
          SizedBox(height: 4.h),
          Text(
            _getProgressDescription(remaining, isSocialValidation),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationHeatmap(BuildContext context) {
    final theme = Theme.of(context);

    // Use real validation attempt data (currently empty since no attempts have been made)
    final validationAttempts = _getRealValidationAttempts();

    return SizedBox(
      height: 40.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(7, (weekDay) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(3, (week) {
              final dayIndex = week * 7 + weekDay;
              final attemptStatus = dayIndex < validationAttempts.length
                  ? validationAttempts[dayIndex]
                  : ValidationAttemptStatus.none;

              return Container(
                width: 8.w,
                height: 8.w,
                decoration: BoxDecoration(
                  color: _getHeatmapColor(context, attemptStatus),
                  borderRadius: BorderRadius.circular(1.5.r),
                  border: attemptStatus != ValidationAttemptStatus.none
                      ? Border.all(
                          color: theme.colorScheme.outline.withValues(alpha: 0.2),
                          width: 0.5,
                        )
                      : null,
                ),
              );
            }),
          );
        }),
      ),
    );
  }

  Widget _buildHeatmapLegend(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Less',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontSize: 9.sp,
          ),
        ),
        Row(
          children: [
            _buildLegendSquare(context, ValidationAttemptStatus.none),
            SizedBox(width: 2.w),
            _buildLegendSquare(context, ValidationAttemptStatus.failed),
            SizedBox(width: 2.w),
            _buildLegendSquare(context, ValidationAttemptStatus.partial),
            SizedBox(width: 2.w),
            _buildLegendSquare(context, ValidationAttemptStatus.success),
          ],
        ),
        Text(
          'More',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontSize: 9.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildLegendSquare(BuildContext context, ValidationAttemptStatus status) {
    return Container(
      width: 6.w,
      height: 6.w,
      decoration: BoxDecoration(
        color: _getHeatmapColor(context, status),
        borderRadius: BorderRadius.circular(1.r),
        border: status != ValidationAttemptStatus.none
            ? Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 0.5,
              )
            : null,
      ),
    );
  }

  String _getZoneTypeLabel(ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return 'Home';
      case ZoneType.work:
        return 'Work';
      case ZoneType.university:
        return 'University';
      case ZoneType.other:
        return 'Other';
    }
  }

  String _getValidationMethodLabel(ValidationMethod method) {
    switch (method) {
      case ValidationMethod.social:
        return 'Social Validation';
      case ValidationMethod.automatic:
        return 'Automatic Validation';
    }
  }

  /// Get automatic validation status text
  String _getAutomaticValidationStatus() {
    switch (zone.validationStatus) {
      case ZoneStatus.pending:
        return 'Monitoring enabled';
      case ZoneStatus.validated:
        return 'Successfully validated';
      case ZoneStatus.rejected:
        return 'Validation failed';
    }
  }

  /// Get progress description based on validation method and status
  String _getProgressDescription(int remaining, bool isSocialValidation) {
    if (isSocialValidation) {
      return remaining > 0
          ? '$remaining more successful validation${remaining > 1 ? 's' : ''} needed'
          : 'Validation complete!';
    } else {
      // Automatic validation description
      switch (zone.validationStatus) {
        case ZoneStatus.pending:
          return 'System will automatically validate during presence hours';
        case ZoneStatus.validated:
          return 'Zone successfully validated automatically';
        case ZoneStatus.rejected:
          return 'Automatic validation failed - check presence hours';
      }
    }
  }

  /// Get real validation attempts data
  /// Currently returns empty data since no validation attempts have been made yet
  List<ValidationAttemptStatus> _getRealValidationAttempts() {
    // In a real implementation, this would come from:
    // - zone.validationAttempts (if such field exists)
    // - Firebase Firestore collection of validation attempts
    // - Or passed as a parameter to this widget

    // For now, return all empty since no validation attempts have been made
    // This will show all gray squares until real validation attempts occur
    return List.generate(21, (index) => ValidationAttemptStatus.none);
  }

  /// Get color for heatmap square based on validation attempt status
  Color _getHeatmapColor(BuildContext context, ValidationAttemptStatus status) {
    final theme = Theme.of(context);

    switch (status) {
      case ValidationAttemptStatus.none:
        return theme.colorScheme.outline.withValues(alpha: 0.1);
      case ValidationAttemptStatus.failed:
        return Colors.red.shade400;
      case ValidationAttemptStatus.partial:
        return Colors.amber.shade400;
      case ValidationAttemptStatus.success:
        return Colors.green.shade400;
    }
  }
}
