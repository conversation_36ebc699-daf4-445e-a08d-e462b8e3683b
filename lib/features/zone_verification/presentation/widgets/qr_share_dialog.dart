import 'dart:ui' as ui;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:gal/gal.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/professional_qr_widget.dart';

/// Awesome iOS-style QR sharing dialog with download and share options
class QRShareDialog {
  /// Shows a professional QR sharing dialog with iOS styling
  static Future<void> show({
    required BuildContext context,
    required ZoneEntity zone,
  }) {
    return showCupertinoModalPopup<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => _QRShareDialogContent(zone: zone),
    );
  }
}

class _QRShareDialogContent extends StatefulWidget {
  final ZoneEntity zone;

  const _QRShareDialogContent({required this.zone});

  @override
  State<_QRShareDialogContent> createState() => _QRShareDialogContentState();
}

class _QRShareDialogContentState extends State<_QRShareDialogContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  String? _validationCode;
  String? _shareableLink;
  final GlobalKey _qrKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _generateValidationData();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _generateValidationData() {
    _validationCode = 'ZONE_${widget.zone.id.substring(0, 8).toUpperCase()}';
    _shareableLink = 'https://respublicaseguridad.app/validate/$_validationCode';
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Center(
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        maxWidth: 320.w,
                        maxHeight: mediaQuery.size.height * 0.7,
                      ),
                      decoration: BoxDecoration(
                        color: CupertinoTheme.of(context).scaffoldBackgroundColor,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.15),
                            blurRadius: 8.r,
                            offset: Offset(0, 4.h),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildHeader(context),
                          Expanded(
                            child: SingleChildScrollView(
                              padding: EdgeInsets.zero,
                              child: Column(
                                children: [
                                  _buildQRSection(context),
                                  _buildZoneInfo(context),
                                ],
                              ),
                            ),
                          ),
                          _buildActionButtons(context),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 16.w, 12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              FluentIcons.qr_code_24_filled,
              color: theme.colorScheme.primary,
              size: 24.w,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Share Zone QR',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.none,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  'Let neighbors validate your zone',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: theme.colorScheme.onSurfaceVariant,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(6.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                FluentIcons.dismiss_24_regular,
                size: 18.w,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQRSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
      child: RepaintBoundary(
        key: _qrKey,
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.all(16.w),
          child: ProfessionalQRWidget(
            data: _shareableLink ?? '',
            size: 160.w,
            showBorder: false,
            showShadow: false,
            semanticsLabel: 'QR Code for zone validation: ${widget.zone.name}',
          ),
        ),
      ),
    );
  }

  Widget _buildZoneInfo(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FluentIcons.location_24_filled,
                color: theme.colorScheme.primary,
                size: 16.w,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  widget.zone.name,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                    decoration: TextDecoration.none,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(
                FluentIcons.map_24_regular,
                color: theme.colorScheme.onSurfaceVariant,
                size: 14.w,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  widget.zone.address,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: theme.colorScheme.onSurfaceVariant,
                    decoration: TextDecoration.none,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Code: ${_validationCode ?? ''}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.none,
                  ),
                ),
                SizedBox(width: 8.w),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: _copyValidationCode,
                  child: Icon(
                    FluentIcons.copy_24_regular,
                    size: 16.w,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 8.w, 16.w, 16.w),
      child: Column(
        children: [
          // Primary action - Share
          Container(
            width: double.infinity,
            height: 44.h,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(8.r),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  blurRadius: 4.r,
                  offset: Offset(0, 2.h),
                ),
              ],
            ),
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _shareQRCode,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FluentIcons.share_24_filled,
                    color: Colors.white,
                    size: 20.w,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Share QR Code',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          SizedBox(height: 10.h),

          // Secondary actions row
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: _saveQRCode,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FluentIcons.save_24_regular,
                          color: theme.colorScheme.onSurfaceVariant,
                          size: 18.w,
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          'Save',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: theme.colorScheme.onSurfaceVariant,
                            decoration: TextDecoration.none,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Container(
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: _copyValidationCode,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FluentIcons.copy_24_regular,
                          color: theme.colorScheme.onSurfaceVariant,
                          size: 18.w,
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          'Copy',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: theme.colorScheme.onSurfaceVariant,
                            decoration: TextDecoration.none,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _shareQRCode() {
    final message = '''
🏠 Zone Validation Request

Zone: ${widget.zone.name}
Address: ${widget.zone.address}

Please help validate my zone by scanning the QR code or clicking this link:
${_shareableLink ?? ''}

Validation Code: ${_validationCode ?? ''}

Thank you for helping make our community safer! 🛡️
''';

    Share.share(
      message,
      subject: 'Zone Validation - ${widget.zone.name}',
    );
    
    Navigator.of(context).pop();
  }

  void _copyValidationCode() {
    Clipboard.setData(ClipboardData(text: _validationCode ?? ''));
    
    // Show haptic feedback
    HapticFeedback.lightImpact();
    
    // Show success feedback
    _showSuccessMessage('Validation code copied!');
  }

  Future<void> _saveQRCode() async {
    try {
      // Show haptic feedback
      HapticFeedback.lightImpact();

      // Capture the QR widget as image
      final RenderRepaintBoundary boundary =
          _qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Generate filename
      final String fileName = 'QR_${widget.zone.name.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.png';

      // Save to gallery
      await Gal.putImageBytes(pngBytes, name: fileName);

    } catch (e) {
      print('Error saving QR code: $e');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              FluentIcons.checkmark_circle_24_filled,
              color: Colors.white,
              size: 20.w,
            ),
            SizedBox(width: 8.w),
            Text(
              message,
              style: GoogleFonts.outfit(
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        margin: EdgeInsets.all(16.w),
      ),
    );
  }


}
