import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/qr_share_dialog.dart';

/// Reusable card component for displaying zone information
/// Features built-in QR sharing dialog with professional styling
class ZoneCard extends StatelessWidget {
  final ZoneEntity zone;
  final VoidCallback? onTap;
  final VoidCallback? onValidate;
  final VoidCallback? onShare; // Deprecated: QR sharing is now handled internally
  final VoidCallback? onDelete; // New delete callback
  final bool showActions;
  final bool isCompact;

  const ZoneCard({
    super.key,
    required this.zone,
    this.onTap,
    this.onValidate,
    this.onShare,
    this.onDelete,
    this.showActions = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.r),
        boxShadow: [
          BoxShadow(
            color: _getStatusAccentColor(context).withValues(alpha: 0.15),
            blurRadius: 8.r,
            spreadRadius: 2.r,
            offset: Offset(0, 2.h),
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4.r,
            spreadRadius: 1.r,
            offset: Offset(0, 1.h),
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isDark ? theme.appBarTheme.backgroundColor : const Color(0xFFFFFFFF),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.r),
          side: BorderSide(
            color: _getStatusAccentColor(context).withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            // Subtle pattern overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.r),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getStatusAccentColor(context).withValues(alpha: 0.02),
                      Colors.transparent,
                      _getStatusAccentColor(context).withValues(alpha: 0.01),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
                child: CustomPaint(
                  painter: _PatternPainter(
                    color: _getStatusAccentColor(context).withValues(alpha: 0.03),
                  ),
                ),
              ),
            ),
            // Card content
            InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(6.r),
        splashColor: _getStatusAccentColor(context).withValues(alpha: 0.1),
        highlightColor: _getStatusAccentColor(context).withValues(alpha: 0.05),
        child: Container(
          padding: EdgeInsets.all(isCompact ? 10.w : 14.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              if (!isCompact) ...[
                SizedBox(height: 10.h),
                _buildContent(context),
                SizedBox(height: 10.h),
                _buildFooter(context),
              ],
              if (showActions && !isCompact) ...[
                SizedBox(height: 12.h),
                _buildActions(context),
              ],
            ],
          ),
        ),
        )],
        ),
      ),
    );
  }

  /// Shows the awesome QR share dialog
  void _showQRShareDialog(BuildContext context) {
    QRShareDialog.show(
      context: context,
      zone: zone,
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        // Compact zone type icon
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: _getZoneTypeColor(context).withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
              color: _getZoneTypeColor(context).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Center(
            child: SvgPicture.string(
              zone.type.svgIcon,
              width: 20.w,
              height: 20.w,
              colorFilter: ColorFilter.mode(
                _getZoneTypeColor(context),
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Zone name with compact typography
              Text(
                zone.name,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 14.sp,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              // Zone type with compact styling
              Text(
                _getZoneTypeText(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: _getZoneTypeColor(context),
                  fontWeight: FontWeight.w500,
                  fontSize: 11.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 8.w),
        // Popup menu for actions
        if (onDelete != null) ...[
          _buildPopupMenu(context),
          SizedBox(width: 8.w),
        ],
        // Compact status indicator
        _buildStatusBadge(context),
      ],
    );
  }

  // Compact status badge
  Widget _buildStatusBadge(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _getStatusAccentColor(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 4.w),
          Text(
            _getStatusText(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  // Compact content section
  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Address with compact icon
        Row(
          children: [
            Icon(
              FluentIcons.location_24_regular,
              size: 14.w,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                zone.address,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 6.h),
        // Presence hours with compact styling
        Row(
          children: [
            Icon(
              FluentIcons.clock_24_regular,
              size: 14.w,
              color: theme.colorScheme.secondary,
            ),
            SizedBox(width: 8.w),
            Text(
              zone.presenceHours ?? 'Not specified',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Compact footer with validation info
  Widget _buildFooter(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: _buildValidationContent(context),
    );
  }

  Widget _buildValidationContent(BuildContext context) {
    final theme = Theme.of(context);

    if (zone.validationStatus == ZoneStatus.validated) {
      return Row(
        children: [
          Icon(
            FluentIcons.checkmark_circle_24_filled,
            size: 16.w,
            color: Colors.green,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Validated Zone',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (zone.validatedAt != null)
            Text(
              _formatDate(zone.validatedAt!),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 10.sp,
              ),
            ),
        ],
      );
    }

    if (zone.validationStatus == ZoneStatus.rejected) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FluentIcons.dismiss_circle_24_filled,
                size: 16.w,
                color: theme.colorScheme.error,
              ),
              SizedBox(width: 8.w),
              Text(
                'Validation Rejected',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (zone.rejectionReason != null) ...[
            SizedBox(height: 4.h),
            Text(
              zone.rejectionReason!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 10.sp,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    }

    // Pending status with enhanced progress
    if (zone.validationMethod == ValidationMethod.social) {
      return _buildSocialValidationProgress(context);
    }

    // Automatic validation pending
    return _buildAutomaticValidationStatus(context);
  }

  // Compact social validation progress
  Widget _buildSocialValidationProgress(BuildContext context) {
    final theme = Theme.of(context);
    final progress = zone.communityValidationCount / 3.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FluentIcons.people_24_regular,
              size: 16.w,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                'Community Validation',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Text(
              '${zone.communityValidationCount}/3',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
                fontSize: 10.sp,
              ),
            ),
          ],
        ),
        SizedBox(height: 6.h),
        // Compact progress bar
        Container(
          height: 4.h,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(2.r),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Compact automatic validation status
  Widget _buildAutomaticValidationStatus(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          FluentIcons.bot_24_regular,
          size: 16.w,
          color: Colors.orange,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            'Automatic Validation',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(
          width: 16.w,
          height: 16.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
        ),
      ],
    );
  }

  // Enhanced actions with modern button design
  Widget _buildActions(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        if (zone.validationStatus == ZoneStatus.pending &&
            zone.validationMethod == ValidationMethod.social) ...[
          if (onValidate != null) ...[
            Expanded(
              child: Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: onValidate,
                    borderRadius: BorderRadius.circular(8.r),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FluentIcons.vote_24_regular,
                            size: 18.w,
                            color: theme.colorScheme.onPrimary,
                          ),
                          SizedBox(width: 8.w),
                          Flexible(
                          child: Text(
                            'Vote',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ] else if (zone.validationStatus == ZoneStatus.pending &&
                   zone.validationMethod == ValidationMethod.automatic) ...[
          Expanded(
            child: Container(
              height: 44.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: theme.colorScheme.secondary.withValues(alpha: 0.3),
                  width: 1.5,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // Switch to social validation
                  },
                  borderRadius: BorderRadius.circular(12.r),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FluentIcons.people_24_regular,
                          size: 18.w,
                          color: theme.colorScheme.secondary,
                        ),
                        SizedBox(width: 8.w),
                        Flexible(
                          child: Text(
                            'Social',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.secondary,
                              fontWeight: FontWeight.w600,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Build popup menu for zone actions
  Widget _buildPopupMenu(BuildContext context) {
    final theme = Theme.of(context);

    return PopupMenuButton<String>(
      icon: Icon(
        FluentIcons.more_vertical_24_regular,
        size: 20.w,
        color: theme.colorScheme.onSurfaceVariant,
      ),
      iconSize: 20.w,
      offset: Offset(0, 40.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      elevation: 8,
      color: theme.cardColor,
      onSelected: (value) {
        switch (value) {
          case 'share':
            _showQRShareDialog(context);
            break;
          case 'delete':
            if (onDelete != null) {
              _showDeleteConfirmDialog(context);
            }
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem<String>(
          value: 'share',
          child: Row(
            children: [
              Icon(
                FluentIcons.qr_code_24_regular,
                size: 18.w,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 12.w),
              Text(
                'Share QR',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                FluentIcons.delete_24_regular,
                size: 18.w,
                color: theme.colorScheme.error,
              ),
              SizedBox(width: 12.w),
              Text(
                'Delete Zone',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Show delete confirmation dialog
  void _showDeleteConfirmDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            Icon(
              FluentIcons.warning_24_regular,
              size: 24.w,
              color: theme.colorScheme.error,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'Delete Zone',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: theme.textTheme.bodyMedium,
                children: [
                  const TextSpan(text: 'Are you sure you want to delete '),
                  TextSpan(
                    text: '"${zone.name}"',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const TextSpan(text: '?'),
                ],
              ),
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: theme.colorScheme.error.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    FluentIcons.info_24_regular,
                    size: 16.w,
                    color: theme.colorScheme.error,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'This action cannot be undone.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.onSurfaceVariant,
            ),
            child: Text(
              'Cancel',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              onDelete?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
              elevation: 0,
            ),
            icon: Icon(FluentIcons.delete_24_regular, size: 16.w),
            label: Text(
              'Delete',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get status accent color
  Color _getStatusAccentColor(BuildContext context) {
    switch (zone.validationStatus) {
      case ZoneStatus.validated:
        return Colors.green;
      case ZoneStatus.pending:
        return Colors.orange;
      case ZoneStatus.rejected:
        return Theme.of(context).colorScheme.error;
    }
  }

  // Helper method to get status text
  String _getStatusText() {
    switch (zone.validationStatus) {
      case ZoneStatus.validated:
        return 'Validated';
      case ZoneStatus.pending:
        return 'Pending';
      case ZoneStatus.rejected:
        return 'Rejected';
    }
  }

  Color _getZoneTypeColor(BuildContext context) {
    switch (zone.type) {
      case ZoneType.home:
        return Colors.green;
      case ZoneType.work:
        return Theme.of(context).colorScheme.primary;
      case ZoneType.university:
        return Colors.orange;
      case ZoneType.other:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  String _getZoneTypeText() {
    switch (zone.type) {
      case ZoneType.home:
        return 'Home';
      case ZoneType.work:
        return 'Work';
      case ZoneType.university:
        return 'University';
      case ZoneType.other:
        return 'Other';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Hoy';
    } else if (difference.inDays == 1) {
      return 'Ayer';
    } else if (difference.inDays < 7) {
      return 'Hace ${difference.inDays} días';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

// Custom painter for subtle pattern overlay
class _PatternPainter extends CustomPainter {
  final Color color;

  _PatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // Create a subtle dot pattern
    const double spacing = 20.0;
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), 0.5, paint);
      }
    }

    // Add some diagonal lines for texture
    paint.strokeWidth = 0.3;
    for (double i = -size.height; i < size.width; i += spacing * 2) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
