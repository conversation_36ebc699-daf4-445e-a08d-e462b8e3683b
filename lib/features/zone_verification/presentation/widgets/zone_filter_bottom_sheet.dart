import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// iOS-style filter dialog for zone filtering
class ZoneFilterDialog extends StatefulWidget {
  final ZoneType? initialTypeFilter;
  final ValidationMethod? initialMethodFilter;
  final Function(ZoneType? typeFilter, ValidationMethod? methodFilter) onApplyFilters;

  const ZoneFilterDialog({
    super.key,
    this.initialTypeFilter,
    this.initialMethodFilter,
    required this.onApplyFilters,
  });

  /// Shows the filter dialog
  static Future<void> show({
    required BuildContext context,
    ZoneType? initialTypeFilter,
    ValidationMethod? initialMethodFilter,
    required Function(ZoneType? typeFilter, ValidationMethod? methodFilter) onApplyFilters,
  }) {
    return showCupertinoDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => ZoneFilterDialog(
        initialTypeFilter: initialTypeFilter,
        initialMethodFilter: initialMethodFilter,
        onApplyFilters: onApplyFilters,
      ),
    );
  }

  @override
  State<ZoneFilterDialog> createState() => _ZoneFilterDialogState();
}

class _ZoneFilterDialogState extends State<ZoneFilterDialog> {
  late ZoneType? _selectedTypeFilter;
  late ValidationMethod? _selectedMethodFilter;

  @override
  void initState() {
    super.initState();
    _selectedTypeFilter = widget.initialTypeFilter;
    _selectedMethodFilter = widget.initialMethodFilter;
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text(
        'Filter Zones',
        style: GoogleFonts.outfit(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: Container(
        height: 270.h, // More height for better spacing
        width: double.maxFinite, // Use full available width instead of fixed width
        constraints: BoxConstraints(
          maxWidth: 700.w,
          minWidth: 500.w,
        ),
        child: _buildContent(context),
      ),
      actions: [
        CupertinoDialogAction(
          isDestructiveAction: true,
          onPressed: _clearAllFilters,
          child: Text(
            'Clear All',
            style: GoogleFonts.outfit(fontWeight: FontWeight.w500),
          ),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: GoogleFonts.outfit(fontWeight: FontWeight.w500),
          ),
        ),
        CupertinoDialogAction(
          isDefaultAction: true,
          onPressed: _applyFilters,
          child: Text(
            'Apply',
            style: GoogleFonts.outfit(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 12.h),
          _buildSectionHeader('Zone Type'),
          SizedBox(height: 6.h),
          _buildZoneTypeGrid(),

          SizedBox(height: 20.h),

          _buildSectionHeader('Validation Method'),
          SizedBox(height: 8.h),
          _buildValidationMethodGrid(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(left: 4.w),
      child: Text(
        title,
        style: GoogleFonts.outfit(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: CupertinoColors.secondaryLabel.resolveFrom(context),
        ),
      ),
    );
  }

  Widget _buildZoneTypeGrid() {
    final allOptions = [
      ...ZoneType.values.map((type) => {
        'title': _getZoneTypeLabel(type),
        'value': type,
      }),
    ];

    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: allOptions.map((option) => FractionallySizedBox(
        widthFactor: 0.45, // Each item takes 45% of available width (2 items per row with spacing)
        child: _buildCompactFilterOption(
          title: option['title'] as String,
          isSelected: _selectedTypeFilter == option['value'],
          onTap: () => setState(() => _selectedTypeFilter = option['value'] as ZoneType?),
        ),
      )).toList(),
    );
  }

  Widget _buildValidationMethodGrid() {
    final allOptions = [
      {'title': 'Social Validation', 'value': ValidationMethod.social},
      {'title': 'Automatic Validation', 'value': ValidationMethod.automatic},
    ];

    return Column(
      children: allOptions.map((option) => Container(
        width: double.infinity, 
        margin: EdgeInsets.only(bottom: 8.h),
        child: _buildCompactFilterOption(
          title: option['title'] as String,
          isSelected: _selectedMethodFilter == option['value'],
          onTap: () => setState(() => _selectedMethodFilter = option['value'] as ValidationMethod?),
        ),
      )).toList(),
    );
  }



  Widget _buildCompactFilterOption({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        margin: EdgeInsets.only(bottom: 4.h),
        decoration: BoxDecoration(
          color: isSelected
              ? CupertinoColors.activeBlue.withValues(alpha: 0.15)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: isSelected
              ? Border.all(
                  color: CupertinoColors.activeBlue.withValues(alpha: 0.3),
                  width: 1,
                )
              : Border.all(
                  color: CupertinoColors.separator.resolveFrom(context),
                  width: 0.5,
                ),
        ),
        child: Center(
          child: Text(
            title,
            style: GoogleFonts.outfit(
              color: isSelected
                  ? CupertinoColors.activeBlue
                  : CupertinoTheme.of(context).textTheme.textStyle.color,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              fontSize: 13.sp,
            ),
            textAlign: TextAlign.center,
            maxLines: 2, // Increased from 1 to 2 to handle longer text better
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  String _getZoneTypeLabel(ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return 'Home';
      case ZoneType.work:
        return 'Work';
     
      case ZoneType.university:
        return 'University';
      case ZoneType.other:
        return 'Other';
    }
  }

  void _clearAllFilters() {
    setState(() {
      _selectedTypeFilter = null;
      _selectedMethodFilter = null;
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(_selectedTypeFilter, _selectedMethodFilter);
    Navigator.of(context).pop();
  }
}

