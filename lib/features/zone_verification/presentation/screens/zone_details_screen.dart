import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_bloc_exports.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/zone_verification_widgets.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'dart:async';

class ZoneDetailsScreen extends StatefulWidget {
  final String zoneId;
  final ZoneEntity? zone;

  const ZoneDetailsScreen({
    super.key,
    required this.zoneId,
    this.zone,
  });

  @override
  State<ZoneDetailsScreen> createState() => _ZoneDetailsScreenState();
}

class _ZoneDetailsScreenState extends State<ZoneDetailsScreen> {
  GoogleMapController? _mapController;
  ValidationSessionStatus? _validationStatus;
  Timer? _statusTimer;

  @override
  void initState() {
    super.initState();
    if (widget.zone == null) {
      // Load zone details if not provided
      _loadZoneDetails();
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _statusTimer?.cancel();
    super.dispose();
  }

  void _loadZoneDetails() {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      context.read<ZoneValidationBloc>().add(
            LoadUserZones(userId: authState.user.id),
          );
    }
  }

  void _handleBackNavigation(BuildContext context) {
    // Ensure any open modals are closed before navigating back
    if (Navigator.of(context).canPop()) {
      // Check if there are any modal routes (like dialogs) open
      final modalRoute = ModalRoute.of(context);
      if (modalRoute != null && modalRoute.isCurrent) {
        // If we're on the current route, proceed with normal navigation
        if (context.canPop()) {
          context.pop();
        } else {
          context.go('/my-zones');
        }
      }
    } else {
      context.go('/my-zones');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Zone Details'),
        leading: IconButton(
          onPressed: () => _handleBackNavigation(context),
          icon: const Icon(CupertinoIcons.back),
        ),

        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    const Icon(FluentIcons.edit_24_regular),
                    SizedBox(width: 8.w),
                    const Text('Edit'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(FluentIcons.delete_24_regular, color: theme.colorScheme.error),
                    SizedBox(width: 8.w),
                    Text('Delete', style: TextStyle(color: theme.colorScheme.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocBuilder<ZoneValidationBloc, ZoneValidationState>(
        builder: (context, state) {
          // Handle loading state
          if (state.isLoading && widget.zone == null) {
            return const Center(child: CircularProgressIndicator());
          }

          // Try to find the zone
          ZoneEntity? zone = widget.zone;
          if (zone == null && state.zones.isNotEmpty) {
            try {
              zone = state.zones.firstWhere((z) => z.id == widget.zoneId);
            } catch (e) {
              // Zone not found in state
              zone = null;
            }
          }

          // Handle case where zone is not found
          if (zone == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FluentIcons.location_off_24_regular,
                    size: 64.r,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Zone not found',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'The requested zone could not be loaded.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  ElevatedButton(
                    onPressed: () => context.go('/my-zones'),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            );
          }

          // Start status updates if this is an automatic validation zone
          if (zone.validationMethod == ValidationMethod.automatic && 
              zone.validationStatus != ZoneStatus.validated &&
              zone.validationStatus != ZoneStatus.rejected) {
            final zoneId = zone.id; // Capture zone ID before callback
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _startStatusUpdates(zoneId);
            });
          }

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Zone Map
                _buildZoneMap(zone),

                // Zone Information
                ZoneInfoCard(zone: zone),

                // Validation Section
                _buildValidationSection(zone, state),

                SizedBox(height: 32.h),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildZoneMap(ZoneEntity zone) {
    final theme = Theme.of(context);

    return Container(
      height: 200.h,
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GoogleMap(
        onMapCreated: (controller) {
          _mapController = controller;
        },
        initialCameraPosition: CameraPosition(
          target: LatLng(
            zone.centerCoordinates.latitude,
            zone.centerCoordinates.longitude,
          ),
          zoom: 16.0,
        ),
        markers: {
          Marker(
            markerId: MarkerId(zone.id),
            position: LatLng(
              zone.centerCoordinates.latitude,
              zone.centerCoordinates.longitude,
            ),
            infoWindow: InfoWindow(
              title: zone.name,
              snippet: zone.address,
            ),
          ),
        },
        circles: {
          Circle(
            circleId: CircleId(zone.id),
            center: LatLng(
              zone.centerCoordinates.latitude,
              zone.centerCoordinates.longitude,
            ),
            radius: zone.radiusInMeters,
            fillColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
            strokeColor: Theme.of(context).primaryColor,
            strokeWidth: 2,
          ),
        },
        zoomControlsEnabled: false,
        mapToolbarEnabled: false,
      ),
    );
  }



  Widget _buildValidationSection(ZoneEntity zone, ZoneValidationState state) {
    if (zone.validationStatus == ZoneStatus.validated) {
      return _buildValidatedStatus(zone);
    } else if (zone.validationStatus == ZoneStatus.rejected) {
      return _buildRejectedStatus(zone);
    } else if (zone.validationMethod == ValidationMethod.automatic) {
      return _buildAutomaticValidationProgress(zone, state);
    } else {
      return _buildPendingValidation(zone, state);
    }
  }

  Widget _buildValidatedStatus(ZoneEntity zone) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.06),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            FluentIcons.checkmark_circle_24_filled,
            color: theme.colorScheme.primary,
            size: 48.r,
          ),
          SizedBox(height: 12.h),
          Text(
            'Zone Validated',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Your zone has been successfully validated and is now active in the security network.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRejectedStatus(ZoneEntity zone) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.06),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            FluentIcons.dismiss_circle_24_filled,
            color: theme.colorScheme.error,
            size: 48.r,
          ),
          SizedBox(height: 12.h),
          Text(
            'Zone Rejected',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            zone.rejectionReason ?? 'Your zone validation was rejected. Please review the requirements and try again.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPendingValidation(ZoneEntity zone, ZoneValidationState state) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Validation Method',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          ValidationMethodSelector(
            selectedMethod: zone.validationMethod,
            onMethodSelected: (method) {
              if (method == ValidationMethod.social) {
                _startQRValidation(zone); // Social validation now uses QR validation
              } else if (method == ValidationMethod.automatic) {
                _enableAutomaticValidation(zone);
              }
            },
          ),
        ],
      ),
    );
  }



  void _enableAutomaticValidation(ZoneEntity zone) {
    // Show iOS-style loading dialog
    IosDialog.showLoadingDialog(
      context: context,
      message: 'Starting automatic validation...',
    );

    // Auto-start Cloud Function-based automatic validation
    _autoStartCloudAutomaticValidation(zone);
  }

  /// Auto-start automatic validation - no manual enable needed
  /// Validation starts immediately if presence hours are active
  void _autoStartCloudAutomaticValidation(ZoneEntity zone) async {
    try {
      final automaticValidationService = getIt<AutomaticValidationService>();
      
      // Auto-start validation based on presence hours
      final result = await automaticValidationService.autoStartValidationIfNeeded(zone.id);
      
      // Dismiss loading dialog
      Navigator.of(context).pop();
      
      if (result.success) {
        // Show success message
        IosDialog.showAlertDialog(
          context: context,
          title: 'Automatic Validation Started',
          message: result.detailedMessage,
          confirmText: 'View Progress',
          onConfirm: () {
            // Navigate to automatic validation screen using correct route
            NavigationService.navigateTo(
              context, 
              'automatic-validation', 
              params: {'zoneId': zone.id}, 
              extra: zone
            );
          },
        );
        
        // Trigger immediate location check feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✓ Validation will start in 1-2 minutes during presence hours'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      } else {
        // Show status message (e.g., waiting for presence hours)
        IosDialog.showAlertDialog(
          context: context,
          title: 'Automatic Validation Configured',
          message: result.message,
          confirmText: 'OK',
        );
      }
    } catch (e) {
      // Dismiss loading dialog
      Navigator.of(context).pop();
      
      // Show error
      IosDialog.showAlertDialog(
        context: context,
        title: 'Error',
        message: 'Failed to start automatic validation: $e',
        confirmText: 'OK',
      );
    }
  }

  void _showSuccessDialog(String message) {
    if (!mounted) return;
    IosDialog.showAlertDialog(
      context: context,
      title: 'Success',
      message: message,
      confirmText: 'OK',
    );
  }

  void _showErrorDialog(String message) {
    if (!mounted) return;
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: message,
      confirmText: 'OK',
    );
  }

  void _startQRValidation(ZoneEntity zone) {
    // Navigate to QR validation screen
    context.push('/qr-validation/${zone.id}', extra: zone);
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _navigateToEditZone();
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
    }
  }

  void _navigateToEditZone() {
    // For now, navigate to define zone screen with the current zone data
    // In a future implementation, this could be a dedicated edit screen
    context.push('/zones/define', extra: widget.zone);
  }

  void _showDeleteConfirmation() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Delete Zone',
      message: 'Are you sure you want to delete this zone? This action cannot be undone.',
      cancelText: 'Cancel',
      confirmText: 'Delete',
      onConfirm: () {
        context.read<ZoneValidationBloc>().add(DeleteZone(widget.zoneId));
        context.go('/my-zones');
      },
    );
  }

  /// Builds the automatic validation progress section with real-time updates
  Widget _buildAutomaticValidationProgress(ZoneEntity zone, ZoneValidationState state) {
    return Column(
      children: [
        _buildProgressCard(zone),
        SizedBox(height: 16.h),
        _buildValidationHeatmap(zone),
        SizedBox(height: 16.h),
        _buildProgressActions(zone),
      ],
    );
  }

  /// Main progress card showing overall status
  Widget _buildProgressCard(ZoneEntity zone) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FluentIcons.clock_24_regular,
                color: theme.colorScheme.primary,
                size: 24.r,
              ),
              SizedBox(width: 12.w),
              Text(
                'Automatic Validation in Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildProgressInfo(),
          SizedBox(height: 16.h),
          _buildProgressBar(),
        ],
      ),
    );
  }

  /// Progress information widget with real-time status
  Widget _buildProgressInfo() {
    if (_validationStatus == null) {
      return _buildLoadingProgress();
    }

    final theme = Theme.of(context);
    final status = _validationStatus!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress: ${status.attempts ?? 0}/${status.maxAttempts ?? 7} attempts',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              status.timeRemaining,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          status.message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: status.isStuck 
              ? theme.colorScheme.error 
              : theme.colorScheme.onSurfaceVariant,
          ),
        ),
        if (status.lastActivity != null) ...[
          SizedBox(height: 8.h),
          Text(
            'Last check: ${_formatLastActivity(status.lastActivity!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  /// Loading state for progress info
  Widget _buildLoadingProgress() {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        SizedBox(
          width: 16.r,
          height: 16.r,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        ),
        SizedBox(width: 12.w),
        Text(
          'Loading validation status...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Progress bar showing completion percentage
  Widget _buildProgressBar() {
    final theme = Theme.of(context);
    double progress = 0.0;
    
    if (_validationStatus != null) {
      final attempts = _validationStatus!.attempts ?? 0;
      final maxAttempts = _validationStatus!.maxAttempts ?? 7;
      progress = maxAttempts > 0 ? attempts / maxAttempts : 0.0;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Validation Progress',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
        ),
        SizedBox(height: 8.h),
        Text(
          '${(progress * 100).round()}% Complete',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Visual heatmap showing validation attempts over days
  Widget _buildValidationHeatmap(ZoneEntity zone) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Validation Schedule (3 Days)',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          _buildDayRows(),
          SizedBox(height: 12.h),
          _buildHeatmapLegend(),
        ],
      ),
    );
  }

  /// Builds the day rows for the validation heatmap
  Widget _buildDayRows() {
    return Column(
      children: List.generate(3, (dayIndex) => _buildDayRow(dayIndex + 1)),
    );
  }

  /// Builds a single day row in the heatmap
  Widget _buildDayRow(int day) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          SizedBox(
            width: 60.w,
            child: Text(
              'Day $day',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Row(
              children: [
                if (day == 1) _buildAttemptBox('Initial', _getAttemptStatus(day, 0)),
                if (day == 1) SizedBox(width: 8.w),
                _buildAttemptBox('Sample 1', _getAttemptStatus(day, 1)),
                SizedBox(width: 8.w),
                _buildAttemptBox('Sample 2', _getAttemptStatus(day, 2)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds an individual attempt box in the heatmap
  Widget _buildAttemptBox(String label, AttemptStatus status) {
    final theme = Theme.of(context);
    
    Color backgroundColor;
    Color borderColor;
    IconData? icon;
    
    switch (status) {
      case AttemptStatus.completed:
        backgroundColor = theme.colorScheme.primary.withValues(alpha: 0.2);
        borderColor = theme.colorScheme.primary;
        icon = FluentIcons.checkmark_16_filled;
        break;
      case AttemptStatus.failed:
        backgroundColor = theme.colorScheme.error.withValues(alpha: 0.2);
        borderColor = theme.colorScheme.error;
        icon = FluentIcons.dismiss_16_filled;
        break;
      case AttemptStatus.pending:
        backgroundColor = theme.colorScheme.surfaceVariant;
        borderColor = theme.colorScheme.outline;
        icon = FluentIcons.clock_16_regular;
        break;
      case AttemptStatus.upcoming:
        backgroundColor = theme.colorScheme.surface;
        borderColor = theme.colorScheme.outline.withValues(alpha: 0.3);
        break;
    }

    return Expanded(
      child: Container(
        height: 32.h,
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) 
              Icon(icon, size: 12.r, color: borderColor)
            else
              SizedBox(height: 12.r),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: 8.sp,
                color: borderColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the legend for the heatmap
  Widget _buildHeatmapLegend() {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        _buildLegendItem('✓ Completed', theme.colorScheme.primary),
        SizedBox(width: 16.w),
        _buildLegendItem('✗ Failed', theme.colorScheme.error),
        SizedBox(width: 16.w),
        _buildLegendItem('⏱ Pending', theme.colorScheme.onSurfaceVariant),
        SizedBox(width: 16.w),
        _buildLegendItem('○ Upcoming', theme.colorScheme.outline),
      ],
    );
  }

  /// Builds a single legend item
  Widget _buildLegendItem(String label, Color color) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8.r,
          height: 8.r,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.3),
            border: Border.all(color: color, width: 1),
          ),
        ),
        SizedBox(width: 4.w),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontSize: 10.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Action buttons for managing validation progress
  Widget _buildProgressActions(ZoneEntity zone) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _refreshValidationStatus(zone.id),
              icon: Icon(FluentIcons.arrow_clockwise_24_regular, size: 16.r),
              label: const Text('Refresh Status'),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _validationStatus?.isStuck == true 
                ? () => _forceRestartValidation(zone.id)
                : null,
              icon: Icon(
                FluentIcons.arrow_reset_24_regular, 
                size: 16.r,
                color: _validationStatus?.isStuck == true 
                  ? theme.colorScheme.error 
                  : null,
              ),
              label: Text(
                'Restart Session',
                style: TextStyle(
                  color: _validationStatus?.isStuck == true 
                    ? theme.colorScheme.error 
                    : null,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Gets the status of a specific validation attempt
  AttemptStatus _getAttemptStatus(int day, int attempt) {
    // This would be based on real data from _validationStatus
    // For now, return mock status based on current attempts
    if (_validationStatus == null) return AttemptStatus.upcoming;
    
    final currentAttempts = _validationStatus!.attempts ?? 0;
    int attemptNumber = 0;
    
    if (day == 1) {
      if (attempt == 0) attemptNumber = 1; // Initial
      if (attempt == 1) attemptNumber = 2; // Sample 1
      if (attempt == 2) attemptNumber = 3; // Sample 2
    } else if (day == 2) {
      if (attempt == 1) attemptNumber = 4; // Sample 1
      if (attempt == 2) attemptNumber = 5; // Sample 2
    } else if (day == 3) {
      if (attempt == 1) attemptNumber = 6; // Sample 1  
      if (attempt == 2) attemptNumber = 7; // Sample 2
    }
    
    if (attemptNumber <= currentAttempts) {
      // For now, assume completed - in real implementation, 
      // this would check actual success/failure status
      return AttemptStatus.completed;
    } else if (attemptNumber == currentAttempts + 1) {
      return AttemptStatus.pending;
    } else {
      return AttemptStatus.upcoming;
    }
  }

  /// Formats last activity timestamp
  String _formatLastActivity(DateTime lastActivity) {
    final now = DateTime.now();
    final difference = now.difference(lastActivity);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  /// Starts periodic status updates
  void _startStatusUpdates(String zoneId) {
    _statusTimer?.cancel();
    _refreshValidationStatus(zoneId);
    
    _statusTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _refreshValidationStatus(zoneId);
      } else {
        timer.cancel();
      }
    });
  }

  /// Refreshes validation status
  Future<void> _refreshValidationStatus(String zoneId) async {
    try {
      final automaticValidationService = getIt<AutomaticValidationService>();
      final status = await automaticValidationService.getValidationSessionStatus(zoneId);
      
      if (mounted) {
        setState(() {
          _validationStatus = status;
        });
      }
    } catch (e) {
      print('Error refreshing validation status: $e');
    }
  }

  /// Forces restart of stuck validation
  Future<void> _forceRestartValidation(String zoneId) async {
    try {
      IosDialog.showLoadingDialog(
        context: context,
        message: 'Restarting validation session...',
      );

      final automaticValidationService = getIt<AutomaticValidationService>();
      final result = await automaticValidationService.forceRestartAutomaticValidation(zoneId);
      
      Navigator.of(context).pop(); // Dismiss loading
      
      if (result.success) {
        _showSuccessDialog('Validation session restarted successfully!');
        _refreshValidationStatus(zoneId);
      } else {
        _showErrorDialog('Failed to restart validation: ${result.message}');
      }
    } catch (e) {
      Navigator.of(context).pop(); // Dismiss loading
      _showErrorDialog('Error restarting validation: $e');
    }
  }
}

/// Enum for tracking validation attempt status
enum AttemptStatus {
  completed,
  failed,
  pending,
  upcoming,
}
