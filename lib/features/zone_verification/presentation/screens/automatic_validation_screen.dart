import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/trigger_automatic_validation_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:respublicaseguridad/core/utils/logger.dart';

/// Screen demonstrating the new automatic zone validation system
/// 
/// This integrates with the clean Cloud Functions v2 implementation
/// that uses geolib for distance calculations and efficient Firestore operations.
class AutomaticValidationScreen extends StatefulWidget {
  final ZoneEntity zone;

  const AutomaticValidationScreen({
    Key? key,
    required this.zone,
  }) : super(key: key);

  @override
  State<AutomaticValidationScreen> createState() => _AutomaticValidationScreenState();
}

class _AutomaticValidationScreenState extends State<AutomaticValidationScreen> {
  final AutomaticValidationService _automaticValidationService = getIt<AutomaticValidationService>();
  
  bool _isLoading = false;
  bool _isMonitoring = false;
  String? _statusMessage;
  AutomaticValidationResult? _lastResult;
  List<LocationUpdateResult> _recentUpdates = [];
  
  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  Future<void> _initializeScreen() async {
    // Check current validation status
    await _checkValidationStatus();
    
    // Process any pending location updates from background monitoring
    await _processPendingUpdates();
  }

  /// Process pending location updates collected during background monitoring
  Future<void> _processPendingUpdates() async {
    try {
      Logger.info('Processing pending location updates for zone: ${widget.zone.id}');
      
      final pendingUpdates = await _automaticValidationService.processPendingLocationUpdates(widget.zone.id);
      
      if (pendingUpdates.isNotEmpty) {
        setState(() {
          _recentUpdates = pendingUpdates;
          _statusMessage = 'Processed ${pendingUpdates.length} background location updates';
        });
        
        Logger.info('Processed ${pendingUpdates.length} pending location updates');
      }
    } catch (e) {
      Logger.error('Error processing pending updates: $e');
      setState(() {
        _statusMessage = 'Error processing background updates: $e';
      });
    }
  }


  /// Check automatic validation status
  Future<void> _checkValidationStatus() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Checking validation status...';
    });

    try {
      // Use the auto-start method to check current status
      final result = await _automaticValidationService.autoStartValidationIfNeeded(widget.zone.id);
      
      setState(() {
        _isLoading = false;
        _statusMessage = result.message;
        _isMonitoring = result.success && result.isMonitoring;
      });

      // If validation is active, show immediate feedback
      if (result.success && result.isMonitoring) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✓ Automatic validation is active - location check scheduled'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Status check failed: $e';
      });
    }
  }

  /// Send current location update
  Future<void> _sendLocationUpdate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 30),
      );

      // Send location update
      final result = await _automaticValidationService.processLocationUpdate(
        zoneId: widget.zone.id,
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
      );

      setState(() {
        _isLoading = false;
        _recentUpdates = [result, ..._recentUpdates.take(9)]; // Keep last 10
        _statusMessage = result.message;
      });

      if (result.isWithinZone) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location recorded within zone (${result.distanceText})'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Location update failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Automatic Validation'),
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
           context.go('/my-zones');
          },
          icon: Icon(FluentIcons.arrow_left_24_regular),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Zone Info Header
            _buildZoneInfoHeader(context, isDark),
            
            SizedBox(height: 24.h),
            
            // Validation Status Section
            _buildValidationStatusSection(context, isDark),
            
            SizedBox(height: 24.h),
            
            // Action Buttons Section
            _buildActionsSection(context, isDark),
            
            SizedBox(height: 24.h),
            
          ],
        ),
      ),
    );
  }

  Widget _buildZoneInfoHeader(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              FluentIcons.location_24_filled,
              color: Colors.white,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.zone.name,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Automatic Validation Zone',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          _buildStatusBadge(context),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    final isActive = widget.zone.presenceHoursConfig?.isActiveAt(DateTime.now()) ?? false;
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: isActive ? Colors.green.withValues(alpha: 0.3) : Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isActive ? FluentIcons.radio_button_24_filled : FluentIcons.pause_circle_24_regular,
            size: 14.sp,
            color: isActive ? Colors.green : Colors.orange,
          ),
          SizedBox(width: 4.w),
          Text(
            isActive ? 'Active' : 'Paused',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: isActive ? Colors.green : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationStatusSection(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  FluentIcons.checkmark_circle_24_regular,
                  size: 18.sp,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'Validation Status',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          if (_lastResult != null) ...[
            _buildModernStatusChip(_lastResult!.status),
            SizedBox(height: 12.h),
          ],
          
          if (_statusMessage != null) ...[
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: theme.dividerColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    FluentIcons.info_24_regular,
                    size: 16.sp,
                    color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _statusMessage!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12.h),
          ],
          
          if (_isMonitoring) ...[
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    FluentIcons.radio_button_24_filled,
                    color: Colors.green,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'Background monitoring active',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  FluentIcons.play_24_regular,
                  size: 18.sp,
                  color: Colors.blue,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'Quick Actions',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // Check Status Button
          _buildActionButton(
            context,
            icon: FluentIcons.arrow_clockwise_24_regular,
            title: 'Check Status',
            subtitle: 'Refresh validation status',
            onTap: _isLoading ? null : _checkValidationStatus,
            isLoading: _isLoading,
            color: theme.colorScheme.primary,
          ),
          
          SizedBox(height: 12.h),
          
          // Send Location Button
          _buildActionButton(
            context,
            icon: FluentIcons.location_24_regular,
            title: 'Send Location Update',
            subtitle: 'Manually trigger location check',
            onTap: _isLoading ? null : _sendLocationUpdate,
            color: Colors.green,
          ),
          
          SizedBox(height: 16.h),
          
          // Info Container
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: Colors.blue.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FluentIcons.info_24_regular,
                      color: Colors.blue,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Simple Automatic Validation',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  '• Initial check: 1-2 minutes after activation\n'
                  '• Duration: 3 days maximum\n'
                  '• Frequency: 2 random samples per day\n'
                  '• Total attempts: Up to 7 validation checks\n'
                  '• Lightweight: No continuous monitoring\n'
                  '• Automatic: Works during your presence hours',
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 12.sp,
                  ),
                ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        FluentIcons.checkmark_circle_24_filled,
                        color: Colors.green.shade600,
                        size: 14.sp,
                      ),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Text(
                          'Efficient & lightweight - no battery drain or resource usage',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontSize: 11.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    required Color color,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: isDark ? theme.colorScheme.surface : theme.colorScheme.surface.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: isLoading
                    ? SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: color,
                        ),
                      )
                    : Icon(icon, color: color, size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                FluentIcons.chevron_right_24_regular,
                size: 16.sp,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildModernStatusChip(String status) {
    Color color;
    IconData icon;
    
    switch (status) {
      case 'validated':
        color = Colors.green;
        icon = FluentIcons.checkmark_circle_24_filled;
        break;
      case 'monitoring_started':
        color = Colors.blue;
        icon = FluentIcons.clock_24_filled;
        break;
      case 'insufficient_presence':
        color = Colors.orange;
        icon = FluentIcons.warning_24_filled;
        break;
      case 'error':
        color = Colors.red;
        icon = FluentIcons.error_circle_24_filled;
        break;
      default:
        color = Colors.grey;
        icon = FluentIcons.question_circle_24_regular;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16.sp),
          SizedBox(width: 6.w),
          Text(
            status.replaceAll('_', ' ').toUpperCase(),
            style: TextStyle(
              color: color,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
