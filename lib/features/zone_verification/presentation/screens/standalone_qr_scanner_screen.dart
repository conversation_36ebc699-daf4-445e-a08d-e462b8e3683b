import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/standalone_qr_scanner_cubit.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui';

/// Standalone QR scanner for users without zones to validate other users' zones
class StandaloneQRScannerScreen extends StatelessWidget {
  final String currentUserId;

  const StandaloneQRScannerScreen({
    super.key,
    required this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize the scanner when the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StandaloneQRScannerCubit>().initializeScanner();
    });

    return _StandaloneQRScannerView(currentUserId: currentUserId);
  }
}

// Custom clipper to create a transparent hole in the blur overlay
class QRScanner<PERSON>lipper extends CustomClipper<Path> {
  final double boxSize;
  final Offset boxPosition;

  QRScannerClipper({required this.boxSize, required this.boxPosition});

  @override
  Path getClip(Size size) {
    // Create a path for the entire screen
    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    
    // Create a path for the QR box
    final holePath = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(boxPosition.dx, boxPosition.dy, boxSize, boxSize),
          const Radius.circular(12),
        )
      );
    
    // Subtract the QR box path from the full screen path
    return Path.combine(PathOperation.difference, path, holePath);
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}

class _StandaloneQRScannerView extends StatefulWidget {
  final String currentUserId;

  const _StandaloneQRScannerView({
    required this.currentUserId,
  });

  @override
  State<_StandaloneQRScannerView> createState() => _StandaloneQRScannerViewState();
}

class _StandaloneQRScannerViewState extends State<_StandaloneQRScannerView>
    with WidgetsBindingObserver {
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Start scanning when ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StandaloneQRScannerCubit>().startScanning();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Make sure to dispose the controller if we own it
    final cubit = context.read<StandaloneQRScannerCubit>();
    cubit.disposeScanner();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Get the cubit safely
    StandaloneQRScannerCubit? cubit;
    try {
      cubit = context.read<StandaloneQRScannerCubit>();
    } catch (e) {
      // Context might not be available
      return;
    }
    
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        cubit.pauseScanning();
        break;
      case AppLifecycleState.resumed:
        cubit.resumeScanning();
        break;
      case AppLifecycleState.detached:
        cubit.disposeScanner();
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  // Safe navigation helper using go_router
  void _navigateBack(BuildContext context) {
    // Make sure to clean up before navigation
    try {
      final cubit = context.read<StandaloneQRScannerCubit>();
      cubit.pauseScanning();
    } catch (e) {
      // Ignore if cubit can't be accessed
    }
    
    if (context.canPop()) {
      context.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => _navigateBack(context),
        ),
        title: Text(
          'Escanear QR',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        elevation: 0,
      ),
      body: BlocConsumer<StandaloneQRScannerCubit, StandaloneQRScannerState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          return Column(
            children: [
              // Instruction bar
              _buildInstructionBar(context, state),
              
              // Scanner area
              Expanded(
                child: _buildScannerArea(context, state),
              ),
              
              // Controls
              _buildControls(context, state),
            ],
          );
        },
      ),
    );
  }

  void _handleStateChanges(BuildContext context, StandaloneQRScannerState state) {
    if (state is QRScannerError) {
      _showErrorDialog(context, state.message, state.canRetry);
    } else if (state is QRScannerSuccess) {
      _showSuccessDialog(context, state.result);
    }
  }

  Widget _buildInstructionBar(BuildContext context, StandaloneQRScannerState state) {
    final theme = Theme.of(context);
    
    String message;
    Color backgroundColor;
    Color textColor;
    
    switch (state.runtimeType) {
      case QRScannerInitial:
        message = 'Inicializando cámara...';
        backgroundColor = theme.colorScheme.surface;
        textColor = theme.colorScheme.onSurface;
        break;
      case QRScannerReady:
        message = 'Preparando escáner...';
        backgroundColor = theme.colorScheme.surface;
        textColor = theme.colorScheme.onSurface;
        break;
      case QRScannerScanning:
        message = 'Apunta la cámara al código QR para validar la zona';
        backgroundColor = theme.colorScheme.primaryContainer;
        textColor = theme.colorScheme.onPrimaryContainer;
        break;
      case QRScannerProcessing:
        final processingState = state as QRScannerProcessing;
        message = processingState.message;
        backgroundColor = theme.colorScheme.secondaryContainer;
        textColor = theme.colorScheme.onSecondaryContainer;
        break;
      case QRScannerPaused:
        message = 'Escáner pausado';
        backgroundColor = theme.colorScheme.surface;
        textColor = theme.colorScheme.onSurface;
        break;
      case QRScannerError:
        message = 'Error en el escáner';
        backgroundColor = theme.colorScheme.errorContainer;
        textColor = theme.colorScheme.onErrorContainer;
        break;
      default:
        message = 'Escáner QR';
        backgroundColor = theme.colorScheme.surface;
        textColor = theme.colorScheme.onSurface;
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: backgroundColor,
      child: Row(
        children: [
          if (state is QRScannerProcessing) ...[
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: textColor,
              ),
            ),
            SizedBox(width: 12.w),
          ],
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerArea(BuildContext context, StandaloneQRScannerState state) {
    final cubit = context.read<StandaloneQRScannerCubit>();
    final controller = cubit.controller;
    
    if (state is QRScannerInitial || controller == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    // Define the size of the scanner box
    final scannerBoxSize = 250.w;
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        // Camera view wrapped in a try-catch to prevent crashes
        Positioned.fill(
          child: Builder(
            builder: (context) {
              try {
                return MobileScanner(
                  controller: controller,
                  onDetect: (capture) => _onQRDetected(context, capture),
                );
              } catch (e) {
                // If the controller is disposed, show a placeholder
                return Container(
                  color: Colors.black,
                  child: const Center(
                    child: Text(
                      'Cámara no disponible',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                );
              }
            },
          ),
        ),
        
        // Blur overlay with cutout for QR box
        ClipPath(
          // Use a custom clipper to create a hole where the QR code should be visible
          clipper: QRScannerClipper(
            boxSize: scannerBoxSize,
            boxPosition: Offset(
              (screenSize.width - scannerBoxSize) / 2,
              (screenSize.height - scannerBoxSize - 80.h) / 2, // Adjust for instructions and controls
            ),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: Container(
              width: screenSize.width,
              height: screenSize.height,
              color: Colors.black.withOpacity(0.4),
            ),
          ),
        ),
        
        // Processing overlay
        if (state is QRScannerProcessing)
          Container(
            color: Colors.black54,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16.h),
                  Text(
                    state.message,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildControls(BuildContext context, StandaloneQRScannerState state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Cancel button
          ElevatedButton.icon(
            onPressed: () => _navigateBack(context),
            icon: const Icon(Icons.close),
            label: const Text('Cancelar'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[800],
              foregroundColor: Colors.white,
            ),
          ),

          // Retry/Resume button
          if (state is QRScannerError || state is QRScannerPaused)
            ElevatedButton.icon(
              onPressed: () {
                final cubit = context.read<StandaloneQRScannerCubit>();
                if (state is QRScannerError) {
                  cubit.resetScanner();
                  cubit.startScanning();
                } else {
                  cubit.resumeScanning();
                }
              },
              icon: Icon(state is QRScannerError ? Icons.refresh : Icons.play_arrow),
              label: Text(state is QRScannerError ? 'Reintentar' : 'Reanudar'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  void _onQRDetected(BuildContext context, BarcodeCapture capture) {
    final cubit = context.read<StandaloneQRScannerCubit>();
    final state = cubit.state;

    // Only process if scanning and not already processing
    if (state is! QRScannerScanning) return;

    for (final barcode in capture.barcodes) {
      if (barcode.rawValue != null && barcode.rawValue!.isNotEmpty) {
        cubit.processQRCode(barcode.rawValue!, widget.currentUserId);
        break; // Process only the first valid QR code
      }
    }
  }

  void _showErrorDialog(BuildContext context, String message, bool canRetry) {
    // Format error message for better readability
    String displayMessage = message;
    if (message.length > 300) {
      // If message is too long, truncate and add details option
      displayMessage = '${message.substring(0, 300)}...\n\nPara más detalles, comuníquese con soporte técnico.';
    }
    
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: displayMessage,
      confirmText: canRetry ? 'Reintentar' : 'OK',
      cancelText: canRetry ? 'Cancelar' : null,
      onConfirm: canRetry ? () {
        final cubit = context.read<StandaloneQRScannerCubit>();
        cubit.resetScanner();
        cubit.startScanning();
      } : null,
      onCancel: canRetry ? () {
        _navigateBack(context);
      } : null,
    );
  }

  void _showSuccessDialog(BuildContext context, dynamic result) {
    IosDialog.showAlertDialog(
      context: context,
      title: '¡Validación Exitosa!',
      message: 'Has validado exitosamente la zona. Tu contribución ayuda a fortalecer la seguridad comunitaria.',
      confirmText: 'OK',
      onConfirm: () {
        _navigateBack(context);
      },
    );
  }
}
