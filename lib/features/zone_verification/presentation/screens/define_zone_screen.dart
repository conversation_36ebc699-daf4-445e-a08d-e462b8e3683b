import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_text_field.dart';

import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_bloc_exports.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/zone_verification_widgets.dart';



class DefineZoneScreen extends StatefulWidget {
  final ZoneEntity? zoneToEdit; // Optional zone for editing mode

  const DefineZoneScreen({super.key, this.zoneToEdit});

  @override
  State<DefineZoneScreen> createState() => _DefineZoneScreenState();
}

class _DefineZoneScreenState extends State<DefineZoneScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _presenceHoursController = TextEditingController();

  LatLng? _selectedLocation;
  ZoneType _selectedZoneType = ZoneType.home;
  ValidationMethod _selectedValidationMethod = ValidationMethod.social;
  String _selectedAddress = '';
  String _locationName = '';
  bool _isMapFullScreen = false; // Track map full-screen state
  bool _hasUserSelectedLocation = false; // Track if user manually selected location

  @override
  void initState() {
    super.initState();
    _initializeForEditMode();
    _loadUserZones();
    // Auto-generate name if starting with empty name and location is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _autoGenerateNameIfEmpty();
    });
  }

  void _initializeForEditMode() {
    if (widget.zoneToEdit != null) {
      final zone = widget.zoneToEdit!;
      _nameController.text = zone.name;
      _presenceHoursController.text = zone.presenceHours ?? '';
      _selectedZoneType = zone.type;
      _selectedValidationMethod = zone.validationMethod;
      _selectedAddress = zone.address;
      _selectedLocation = LatLng(
        zone.centerCoordinates.latitude,
        zone.centerCoordinates.longitude,
      );
      _hasUserSelectedLocation = true;
    }
  }

  void _loadUserZones() {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      context.read<ZoneValidationBloc>().add(LoadUserZones(userId: authState.user.id));
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _presenceHoursController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(widget.zoneToEdit != null ? 'Edit Zone' : 'Define Zone'),
        leading: IconButton(
          onPressed: () => NavigationService.navigateTo(context, 'my-zones'),
          icon: const Icon(CupertinoIcons.back),
        ),

        actions: [
          TextButton(
            onPressed: _isFormValid() ? _saveZone : null,
            child: Text(
              'Save',
              style: TextStyle(
                color: _isFormValid() ? theme.colorScheme.primary : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<ZoneValidationBloc, ZoneValidationState>(
        listener: (context, state) {
          if (state.isCreated || state.status == ZoneValidationStatus.updated) {
            // Add a small delay before showing the dialog to improve UX
            Future.delayed(const Duration(milliseconds: 300), () {
              final isEditing = widget.zoneToEdit != null;
              IosDialog.showAlertDialog(
                context: context,
                title: context.l10n.success,
                message: isEditing
                    ? 'Zone updated successfully!'
                    : context.l10n.zoneCreatedSuccessfully,
                confirmText: context.l10n.ok,
                onConfirm: () {
                  NavigationService.navigateTo(context, 'my-zones');
                },
              );
            });
          } else if (state.requiresIdentityValidation) {
            // Show identity validation required dialog and redirect
            IosDialog.showAlertDialog(
              context: context,
              title: 'Identity Verification Required',
              message: state.identityValidationMessage ?? 'You need to complete identity verification before creating zones.',
              confirmText: 'Verify Identity',
              cancelText: 'Cancel',
              onConfirm: () {
                // Navigate to identity validation screen
                NavigationService.navigateTo(context, 'identity-validation');
              },
              onCancel: () {
                // Stay on current screen
              },
            );
          } else if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Unknown error'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
        // Use conditional rendering based on full-screen state
        child: _isMapFullScreen 
          // Full-screen map view
          ? InteractiveZoneMap(
              height: MediaQuery.of(context).size.height,
              initialLocation: _selectedLocation,
              initialAddress: _selectedAddress,
              onLocationSelected: _onLocationSelected,
              onAddressSelected: _onAddressSelected,
              onCurrentLocationRequested: _requestCurrentLocation,
              showLocationInfo: true,
              showSearchOverlay: false,
              isInteractive: true,
              autoRequestLocation: true,
              margin: EdgeInsets.zero,
              borderRadius: BorderRadius.zero,
              showFullScreenToggle: true,
              key: const ValueKey('interactive_map_fullscreen'),
              onFullScreenChanged: (isFullScreen) {
                setState(() {
                  _isMapFullScreen = isFullScreen;
                });
              },
            )
          // Normal view with form
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  // Interactive Map Section
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 8.h),

                      InteractiveZoneMap(
                        height: 220,
                        initialLocation: _selectedLocation,
                        initialAddress: _selectedAddress,
                        onLocationSelected: _onLocationSelected,
                        onAddressSelected: _onAddressSelected,
                        onCurrentLocationRequested: _requestCurrentLocation,
                        showLocationInfo: true,
                        showSearchOverlay: false,
                        isInteractive: true,
                        autoRequestLocation: true,
                        margin: EdgeInsets.all(16.w),
                        borderRadius: BorderRadius.circular(16.r),
                        showFullScreenToggle: true,
                        key: const ValueKey('interactive_map'),
                        onFullScreenChanged: (isFullScreen) {
                          setState(() {
                            _isMapFullScreen = isFullScreen;
                          });
                        },
                      ),

                      // Location requirement message
                      if (!_hasUserSelectedLocation)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            padding: EdgeInsets.all(12.w),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  FluentIcons.info_24_regular,
                                  color: theme.colorScheme.primary,
                                  size: 16.r,
                                ),
                                SizedBox(width: 8.w),
                                Expanded(
                                  child: Text(
                                    'Tap on the map to select a location for your zone',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.primary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),

                  // Form Section
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      
                      SizedBox(height: 16.h),
                      _buildZoneNameField(),
                      SizedBox(height: 16.h),

                      
                      BlocBuilder<ZoneValidationBloc, ZoneValidationState>(
                        builder: (context, zoneState) {
                          final hasHomeZone = zoneState.homeZones.isNotEmpty;
                          final isCreatingNewZone = widget.zoneToEdit == null;

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ZoneTypeSelector(
                                selectedType: _selectedZoneType,
                                onTypeSelected: (type) {
                                  // Check if user is trying to select a non-Home zone without having a Home zone first
                                  if (isCreatingNewZone && type != ZoneType.home && !hasHomeZone) {
                                    _showHomeZoneRequiredDialog();
                                    return;
                                  }

                                  // Check if user is editing an existing zone and trying to change from Home to another type
                                  // when they only have one Home zone
                                  if (!isCreatingNewZone &&
                                      widget.zoneToEdit?.type == ZoneType.home &&
                                      type != ZoneType.home &&
                                      zoneState.homeZones.length == 1) {
                                    _showLastHomeZoneWarningDialog();
                                    return;
                                  }

                                  setState(() {
                                    _selectedZoneType = type;
                                  });
                                  _updateZoneName();
                                },
                              ),

                              // Show helper text when Home Zone is required
                              if (isCreatingNewZone && !hasHomeZone) ...[
                                SizedBox(height: 8.h),
                                Container(
                                  padding: EdgeInsets.all(12.w),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(
                                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        FluentIcons.info_24_regular,
                                        color: theme.colorScheme.primary,
                                        size: 16.r,
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Text(
                                          'Create a Home Zone first to unlock other zone types',
                                          style: theme.textTheme.bodySmall?.copyWith(
                                            color: theme.colorScheme.primary,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          );
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Validation Method Selector
                      _buildValidationMethodSelector(theme),

                      SizedBox(height: 24.h),

                      // Presence Hours (Optional)
                      PresenceHoursInput(
                        initialValue: _presenceHoursController.text,
                        labelText: 'Presence Hours (Optional)',
                        isRequired: false,
                        onChanged: (value) {
                          _presenceHoursController.text = value;
                          setState(() {});
                        },
                      ),
                      SizedBox(height: 24.h),

                      // Info Card
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                          border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  FluentIcons.info_24_regular,
                                  color: theme.colorScheme.primary,
                                  size: 20.r,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  'Zone Validation Info',
                                  style: theme.textTheme.labelMedium?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Your zone will need to be validated by community members or through automatic validation before it becomes active.',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 32.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      // Only show bottom navigation bar when not in full-screen mode
      bottomNavigationBar: _isMapFullScreen ? null : Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(color: theme.colorScheme.outline, width: 1),
          ),
        ),
        child: BlocBuilder<ZoneValidationBloc, ZoneValidationState>(
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Helper text when form is incomplete
                if (!_isFormValid() && !state.isCreating)
                  Padding(
                    padding: EdgeInsets.only(bottom: 8.h),
                    child: Text(
                      _getFormValidationMessage(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: state.isCreating ? null : (_isFormValid() ? _saveZone : null),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: state.isCreating
                        ? SizedBox(
                            height: 20.h,
                            width: 20.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.onPrimary),
                            ),
                          )
                        : Text(
                            'Create Zone',
                            style: theme.textTheme.labelLarge?.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _onLocationSelected(LatLng location, String address) async {
    setState(() {
      _selectedLocation = location;
      _selectedAddress = address;
      _hasUserSelectedLocation = true; // Mark that user has manually selected location
    });

    // Get better location name for auto-generation
    try {
      final locationService = LocationService.instance;
      final locationName = await locationService.getBetterLocationName(
        location.latitude,
        location.longitude,
      );

      if (mounted) {
        setState(() {
          _locationName = locationName;
        });
        // Auto-generate zone name if it's empty
        _autoGenerateNameIfEmpty();
      }
    } catch (e) {
      // Handle error silently, keep existing address
    }
  }

  void _onAddressSelected(AddressSuggestion address) {
    if (address.latitude != null && address.longitude != null) {
      setState(() {
        _selectedLocation = LatLng(address.latitude!, address.longitude!);
        _selectedAddress = address.address;
        _hasUserSelectedLocation = true; // Mark that user has manually selected location
      });
    }
  }

  void _onCurrentLocationSelected(LatLng location, String address) async {
    setState(() {
      _selectedLocation = location;
      _selectedAddress = address;
      _hasUserSelectedLocation = true; // Mark that user has manually selected location
    });

    // Get better location name for auto-generation
    try {
      final locationService = LocationService.instance;
      final locationName = await locationService.getBetterLocationName(
        location.latitude,
        location.longitude,
      );

      if (mounted) {
        setState(() {
          _locationName = locationName;
        });
        // Auto-generate zone name if it's empty
        _autoGenerateNameIfEmpty();
      }
    } catch (e) {
      // Handle error silently, keep existing address
    }
  }

  void _requestCurrentLocation() async {
    try {
      final locationService = LocationService.instance;
      final position = await locationService.getCurrentPosition();

      if (position != null && mounted) {
        final location = LatLng(position.latitude, position.longitude);
        _onCurrentLocationSelected(location, 'Current Location');
      }
    } catch (e) {
      // Handle location error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unable to get current location: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  bool _isFormValid() {
    return _hasUserSelectedLocation;
  }

  String _getFormValidationMessage() {
    if (!_hasUserSelectedLocation) {
      return 'Please select a location on the map';
    }
    return '';
  }

  Widget _buildValidationMethodSelector(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Row(
          children: [
            Text(
              'Validation Method',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              ' *',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),

        // Validation method cards
        Column(
          children: [
            IntrinsicHeight(
              child: Row(
                children: [
                  // Social Validation Card
                  Expanded(
                    child: _buildValidationMethodCard(
                      theme: theme,
                      method: ValidationMethod.social,
                      title: 'Social Validation',
                      description: 'Community members validate',
                      icon: FluentIcons.people_24_regular,
                      isSelected: _selectedValidationMethod == ValidationMethod.social,
                      onTap: () {
                        setState(() {
                          _selectedValidationMethod = ValidationMethod.social;
                        });
                      },
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // Automatic Validation Card
                  Expanded(
                    child: _buildValidationMethodCard(
                      theme: theme,
                      method: ValidationMethod.automatic,
                      title: 'Automatic Validation',
                      description: 'Location-based validation',
                      icon: FluentIcons.location_24_regular,
                      isSelected: _selectedValidationMethod == ValidationMethod.automatic,
                      onTap: () {
                        setState(() {
                          _selectedValidationMethod = ValidationMethod.automatic;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),


          ],
        ),

        SizedBox(height: 8.h),

        // Description based on selected method
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                FluentIcons.info_24_regular,
                color: theme.colorScheme.primary,
                size: 16.r,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  _getValidationMethodDescription(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildValidationMethodCard({
    required ThemeData theme,
    required ValidationMethod method,
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: 60.h, // Fixed height for equal card heights
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.1)
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 1.5 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 36.w,
              height: 36.h,
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primary.withValues(alpha: 0.1)
                    : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 18.r,
              ),
            ),

            SizedBox(width: 10.w),

            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Title
                  Text(
                    title,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: 2.h),

                  // Description
                  Text(
                    description,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Selection indicator
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primary
                    : Colors.transparent,
                shape: BoxShape.circle,
                border: isSelected ? null : Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: isSelected
                  ? Icon(
                      FluentIcons.checkmark_12_filled,
                      color: theme.colorScheme.onPrimary,
                      size: 12.r,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  String _getValidationMethodDescription() {
    switch (_selectedValidationMethod) {
      case ValidationMethod.social:
        return 'Your zone will be validated through secure QR code scanning that requires physical proximity between you and community validators.';
      case ValidationMethod.automatic:
        return 'Your zone will be validated automatically when you spend time in the location during your specified presence hours.';
    }
  }

  void _saveZone() {
    if (!_formKey.currentState!.validate() || _selectedLocation == null) {
      return;
    }

    final authState = context.read<AuthBloc>().state;
    if (!authState.isAuthenticated) {
      return;
    }

    // Check if user is trying to create a non-Home zone without having a Home zone first
    if (widget.zoneToEdit == null && _selectedZoneType != ZoneType.home) {
      final zoneState = context.read<ZoneValidationBloc>().state;
      final hasHomeZone = zoneState.homeZones.isNotEmpty;

      if (!hasHomeZone) {
        _showHomeZoneRequiredDialog();
        return;
      }
    }

    if (widget.zoneToEdit != null) {
      // Update existing zone
      final zoneName = _nameController.text.trim().isNotEmpty
          ? _nameController.text.trim()
          : _createAutoGeneratedName();

      final updatedZone = widget.zoneToEdit!.copyWith(
        name: zoneName,
        type: _selectedZoneType,
        address: _selectedAddress,
        centerCoordinates: Coordinates(
          latitude: _selectedLocation!.latitude,
          longitude: _selectedLocation!.longitude,
        ),
        presenceHours: _presenceHoursController.text.trim().isNotEmpty
            ? _presenceHoursController.text.trim()
            : null,
        validationMethod: _selectedValidationMethod,
        updatedAt: DateTime.now(),
      );

      context.read<ZoneValidationBloc>().add(UpdateZone(updatedZone));
    } else {
      // Create new zone
      final zoneName = _nameController.text.trim().isNotEmpty
          ? _nameController.text.trim()
          : _createAutoGeneratedName();

      final zone = ZoneEntity(
        id: '', // Will be generated by the backend
        userId: authState.user.id,
        name: zoneName,
        type: _selectedZoneType,
        address: _selectedAddress,
        centerCoordinates: Coordinates(
          latitude: _selectedLocation!.latitude,
          longitude: _selectedLocation!.longitude,
        ),
        presenceHours: _presenceHoursController.text.trim().isNotEmpty
            ? _presenceHoursController.text.trim()
            : null,
        validationMethod: _selectedValidationMethod,
        createdAt: DateTime.now(),
      );

      context.read<ZoneValidationBloc>().add(CreateZone(zone));
    }
  }

  Widget _buildZoneNameField() {
    return CustomTextField(
      controller: _nameController,
      labelText: 'Zone Name (Optional)',
      hintText: 'Updates automatically when zone type changes',
      prefixIcon: Icon(
        FluentIcons.tag_24_regular,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white54
            : Colors.black45,
      ),
      onChanged: (_) => setState(() {}),
    );
  }

  void _autoGenerateNameIfEmpty() {
    // Only auto-generate if the field is empty and we have location data
    if (_nameController.text.trim().isEmpty && _selectedLocation != null) {
      final generatedName = _createAutoGeneratedName();
      _nameController.text = generatedName;
    }
  }

  void _updateZoneName() {
    // Always update the zone name when zone type changes (if we have location data)
    if (_selectedLocation != null) {
      final generatedName = _createAutoGeneratedName();
      _nameController.text = generatedName;
    }
  }

  String _createAutoGeneratedName() {
    final zoneTypePrefix = _getZoneTypePrefix(_selectedZoneType);
    final locationName = _locationName.isNotEmpty ? _locationName : _selectedAddress;

    // Create a more descriptive name
    if (locationName.isNotEmpty) {
      // Extract meaningful parts from location name
      final cleanLocationName = _cleanLocationName(locationName);
      return '$zoneTypePrefix - $cleanLocationName';
    }

    // Fallback to coordinates if no location name
    final lat = _selectedLocation!.latitude.toStringAsFixed(4);
    final lng = _selectedLocation!.longitude.toStringAsFixed(4);
    return '$zoneTypePrefix - $lat, $lng';
  }

  String _getZoneTypePrefix(ZoneType type) {
    switch (type) {
      case ZoneType.home:
        return 'Home Zone';
      case ZoneType.work:
        return 'Work Zone';
      case ZoneType.university:
        return 'University Zone';
      case ZoneType.other:
        return 'Zone';
    }
  }

  String _cleanLocationName(String locationName) {
    // Remove common prefixes and clean up the location name
    String cleaned = locationName;

    // Remove country and common administrative divisions
    final removePatterns = [
      r', Colombia$',
      r', CO$',
      r'^Colombia, ',
      r', Bogotá D\.C\.$',
      r', Bogotá$',
      r', Cundinamarca$',
    ];

    for (final pattern in removePatterns) {
      cleaned = cleaned.replaceAll(RegExp(pattern), '');
    }

    // Take the most specific part (usually the first part)
    final parts = cleaned.split(',');
    if (parts.isNotEmpty) {
      cleaned = parts.first.trim();
    }

    // Limit length
    if (cleaned.length > 30) {
      cleaned = cleaned.substring(0, 30).trim();
      if (cleaned.endsWith(',')) {
        cleaned = cleaned.substring(0, cleaned.length - 1);
      }
    }

    return cleaned.isNotEmpty ? cleaned : 'Location';
  }

  void _showHomeZoneRequiredDialog() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Home Zone Required',
      message: 'You must create a Home Zone first before creating other zone types. This helps establish your primary location for security purposes.',
      confirmText: 'Create Home Zone',
      cancelText: 'Cancel',
      onConfirm: () {
        setState(() {
          _selectedZoneType = ZoneType.home;
        });
        _updateZoneName();
      },
      onCancel: () {
        // User cancelled, do nothing
      },
    );
  }

  void _showLastHomeZoneWarningDialog() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Cannot Change Zone Type',
      message: 'This is your only Home Zone. You must have at least one Home Zone for security purposes. Create another Home Zone first if you want to change this one to a different type.',
      confirmText: 'OK',
      onConfirm: () {
        // User acknowledged, do nothing
      },
    );
  }
}
