import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_bloc_exports.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/zone_verification_widgets.dart';

class MyZonesScreen extends StatefulWidget {
  const MyZonesScreen({super.key});

  @override
  State<MyZonesScreen> createState() => _MyZonesScreenState();
}

class _MyZonesScreenState extends State<MyZonesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ZoneStatus? _statusFilter;
  ZoneType? _typeFilter;
  ValidationMethod? _methodFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadUserZones();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserZones() {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      context.read<ZoneValidationBloc>().add(
            LoadUserZones(
              userId: authState.user.id,
              statusFilter: _statusFilter,
              typeFilter: _typeFilter,
              methodFilter: _methodFilter,
            ),
          );
    }
  }

  void _onTabChanged() {
    switch (_tabController.index) {
      case 0:
        _statusFilter = null;
        break;
      case 1:
        _statusFilter = ZoneStatus.pending;
        break;
      case 2:
        _statusFilter = ZoneStatus.validated;
        break;
      case 3:
        _statusFilter = ZoneStatus.rejected;
        break;
    }
    _loadUserZones();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('My Zones'),
        leading: IconButton(onPressed: () => NavigationService.navigateTo(context, 'more'), icon: Icon(CupertinoIcons.back)),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(FluentIcons.filter_24_regular),
            tooltip: 'Filter',
          ),
          IconButton(
            onPressed: _refreshZones,
            icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          onTap: (_) => _onTabChanged(),
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Pending'),
            Tab(text: 'Validated'),
            Tab(text: 'Rejected'),
          ],
        ),
      ),
      body: BlocConsumer<ZoneValidationBloc, ZoneValidationState>(
        listener: (context, state) {
          
          
          // Handle deletion error
          if (state.hasError && state.status == ZoneValidationStatus.error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(
                      FluentIcons.error_circle_24_filled,
                      color: Colors.white,
                      size: 20.w,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        state.errorMessage ?? 'Failed to delete zone',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: theme.colorScheme.error,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                margin: EdgeInsets.all(16.w),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        builder: (context, state) {
          if (state.isLoading && state.zones.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError && state.zones.isEmpty) {
            return _buildErrorState(state.errorMessage ?? 'Unknown error occurred');
          }

          if (state.zones.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: () async => _refreshZones(),
            child: Column(
              children: [
                if (state.isRefreshing || state.status == ZoneValidationStatus.deleting)
                  LinearProgressIndicator(
                    backgroundColor: theme.colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildZonesList(state.zones),
                      _buildZonesList(state.pendingZones),
                      _buildZonesList(state.validatedZones),
                      _buildZonesList(state.rejectedZones),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToDefineZone,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 6,
        child: const Icon(FluentIcons.add_24_regular),
      ),
    );
  }

  Widget _buildZonesList(List<ZoneEntity> zones) {
    if (zones.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: zones.length,
      itemBuilder: (context, index) {
        final zone = zones[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: ZoneCard(
            zone: zone,
            onTap: () => _navigateToZoneDetails(zone),
            onValidate: zone.validationStatus == ZoneStatus.pending
                ? () => _showValidationOptions(zone)
                : null,
            onDelete: () => _deleteZone(zone),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FluentIcons.location_24_regular,
              size: 64.r,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16.h),
            Text(
              'No zones yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Create your first security zone to start protecting your area.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: _navigateToDefineZone,
              icon: const Icon(FluentIcons.add_24_regular),
              label: const Text('Add First Zone'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FluentIcons.error_circle_24_regular,
              size: 64.r,
              color: theme.colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'Error loading zones',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: _refreshZones,
              icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshZones() {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      context.read<ZoneValidationBloc>().add(RefreshZones(authState.user.id));
    }
  }

  void _navigateToDefineZone() {
    NavigationService.navigateTo(context, 'define-zone');
  }

  void _navigateToZoneDetails(ZoneEntity zone) {
    NavigationService.navigateTo(
      context,
      'zone-details',
      params: {'zoneId': zone.id},
      extra: zone,
    );
  }

  void _showValidationOptions(ZoneEntity zone) {
    NavigationService.navigateTo(
      context,
      'zone-details',
      params: {'zoneId': zone.id},
      extra: zone,
    );
  }

  void _deleteZone(ZoneEntity zone) {
    context.read<ZoneValidationBloc>().add(DeleteZone(zone.id));
  }

  void _showFilterDialog() {
    ZoneFilterDialog.show(
      context: context,
      initialTypeFilter: _typeFilter,
      initialMethodFilter: _methodFilter,
      onApplyFilters: (typeFilter, methodFilter) {
        setState(() {
          _typeFilter = typeFilter;
          _methodFilter = methodFilter;
        });
        _loadUserZones();
      },
    );
  }
}
