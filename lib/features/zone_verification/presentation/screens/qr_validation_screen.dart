import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/qr_validation_bloc.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/cubit/zone_validation_watcher_cubit.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/qr_tab_navigation_widget.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/qr_code_section_widget.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/scanner_controls_widget.dart';

import 'package:respublicaseguridad/core/services/proximity_verification_service.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/core/services/qr_validation_error_handler.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/qr_token_service.dart';


class QRValidationScreen extends StatefulWidget {
  final ZoneEntity? zone; // Made optional for standalone scanning
  final String currentUserId;

  const QRValidationScreen({
    super.key,
    this.zone, // Optional zone for standalone scanning
    required this.currentUserId,
  });

  @override
  State<QRValidationScreen> createState() => _QRValidationScreenState();
}

class _QRValidationScreenState extends State<QRValidationScreen> {
  MobileScannerController? _scannerController;
  Timer? _countdownTimer;
  int _remainingSeconds = 0;
  bool _isScanning = false;
  bool _isProcessing = false; // Prevent multiple scans
  int _selectedTabIndex = 0; // 0 = Show QR, 1 = Scan QR

  @override
  void initState() {
    super.initState();
    _scannerController = MobileScannerController();

    // Generate QR token only if zone is provided (for zone owners)
    if (widget.zone != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<QRValidationBloc>().add(
          GenerateQRTokenEvent(
            zoneId: widget.zone!.id,
            userId: widget.currentUserId,
          ),
        );
      });

      // Start watching zone validation changes using clean architecture
      context.read<ZoneValidationWatcherCubit>().startWatching(widget.zone!.id);
    }
  }

  @override
  void dispose() {
    _scannerController?.dispose();
    _countdownTimer?.cancel();
    context.read<ZoneValidationWatcherCubit>().stopWatching();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<ZoneValidationWatcherCubit, ZoneValidationWatcherState>(
      listener: (context, state) {
        if (state is ZoneValidationWatcherValidated) {
          // Show success message when zone is auto-validated
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is ZoneValidationWatcherError) {
          // Show error message if something goes wrong
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _navigateToZones(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Validación Social',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.zone != null)
              Text(
                widget.zone!.name,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
        actions: [
          
          IconButton(
            onPressed: () => _showHelpDialog(context),
            icon: const Icon(Icons.help_outline),
            tooltip: 'Ayuda',
          ),
        ],

      ),
      body: BlocConsumer<QRValidationBloc, QRValidationState>(
        listener: (context, state) {
          if (state is QRValidationErrorState) {
            _showEnhancedErrorDialog(context, state);
          } else if (state is QRValidationCompleted) {
            _showValidationCompletedDialog(context, state);
          } else if (state is QRTokenGenerated) {
            _startCountdown(state.remainingTime);
          } else if (state is QRTokenScanned) {
            _showQRScannedDialog(context, state);
          }
        },
        builder: (context, state) {
          // For standalone scanning (no zone), show only scanner
          if (widget.zone == null) {
            return _buildScanQRTab(context, state);
          }

          // For zone owners, show both tabs
          return Column(
            children: [
              // Tab Navigation Buttons
              QRTabNavigationWidget(
                selectedTabIndex: _selectedTabIndex,
                onTabSelected: (index) {
                  setState(() {
                    _selectedTabIndex = index;
                  });
                },
              ),

              // Content Area
              Expanded(
                child: _selectedTabIndex == 0
                  ? _buildShowQRTab(context, state)
                  : _buildScanQRTab(context, state),
              ),
            ],
          );
        },
      ),
    ),
    );
  }



  void _navigateToZones(BuildContext context) {
    // For standalone scanning, go back to home
    if (widget.zone == null) {
      Navigator.of(context).pop();
    } else {
      // For zone-based scanning, go to my zones
      NavigationService.navigateTo(context, 'my-zones');
    }
  }



  Widget _buildCenteredLoading(BuildContext context, String message) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 60.r,
            height: 60.r,
            child: CircularProgressIndicator(
              strokeWidth: 4,
              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            message,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
         
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Ayuda - Validación Social',
      message: 'La validación social permite que otros usuarios confirmen tu ubicación:\n\n'
          '• Mostrar QR: Genera un código QR para que otros lo escaneen\n'
          '• Escanear QR: Escanea el código QR de otro usuario\n\n'
          'Ambos usuarios deben estar físicamente cerca (menos de 100 metros) '
          'para completar la validación.',
      cancelText: 'Entendido',
    );
  }

  Widget _buildShowQRTab(BuildContext context, QRValidationState state) {
    // Show centered loading for initial states
    if (state is QRValidationInitial ||
        (state is QRValidationLoading &&
         (state.message?.contains('sesión') == true ||
          state.message?.contains('Inicializando') == true))) {
      return _buildCenteredLoading(context, 'Preparando validación...');
    }

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
      
          Expanded(
            child: Center(
              child: QRCodeSectionWidget(
                remainingSeconds: _remainingSeconds,
              ),
            ),
          ),

          // Action Buttons
          _buildActionButtons(context, state),
        ],
      ),
    );
  }

  Widget _buildScanQRTab(BuildContext context, QRValidationState state) {
    // Show centered loading for initial states
    if (state is QRValidationInitial ||
        (state is QRValidationLoading &&
         (state.message?.contains('sesión') == true ||
          state.message?.contains('Inicializando') == true))) {
      return _buildCenteredLoading(context, 'Preparando escáner...');
    }

    return Column(
      children: [
        // Top instruction bar
        _buildScannerInstructionBar(context),

        // Camera scanner area
        Expanded(
          child: Container(
            width: double.infinity,
            color: Colors.black,
            child: _buildProfessionalScanner(context),
          ),
        ),

        // Bottom controls
        ScannerControlsWidget(
          onCancel: () => _navigateToZones(context),
        ),
      ],
    );
  }





  Widget _buildScannerInstructionBar(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          Container(
            width: 4.w,
            height: 20.h,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'Apunta la cámara al código QR',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalScanner(BuildContext context) {
    // Auto-start scanning when widget builds
    if (!_isScanning) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _scannerController != null) {
          setState(() {
            _isScanning = true;
          });
          try {
            _scannerController!.start();
          } catch (e) {
            // Ignore controller start errors
          }
        }
      });
    }

    return Stack(
      children: [
        // Full-screen camera
        Positioned.fill(
          child: MobileScanner(
            controller: _scannerController,
            onDetect: _onQRDetected,
          ),
        ),

        // Simple scanning overlay
        Center(
          child: Container(
            width: 250.w,
            height: 250.w,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildActionButtons(BuildContext context, QRValidationState state) {
    final isLoading = state is QRValidationLoading;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // Refresh Token Button
          if (state is QRTokenGenerated)
            CustomButton(
              text: 'Actualizar token',
              onPressed: () => _refreshQRToken(context, state),
              prefixIcon: const Icon(Icons.refresh, color: Colors.white),
              isLoading: isLoading,
              buttonType: ButtonType.primary,
              height: 56.h,
            ),

          if (state is QRTokenGenerated)
            SizedBox(height: 12.h),

          // Cancel Button
          SizedBox(height: 8.h),
          CustomButton(
            text: 'Cancelar validación',
            onPressed: () => _cancelValidation(context),
            prefixIcon: Icon(Icons.close, color: Theme.of(context).colorScheme.error),
            buttonType: ButtonType.outlined,
            height: 48.h,
            disabled: isLoading,
          ),

          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  void _onQRDetected(BarcodeCapture capture) {
    // Prevent multiple processing
    if (_isProcessing) return;

    final List<Barcode> barcodes = capture.barcodes;

    for (final barcode in barcodes) {
      if (barcode.rawValue != null) {
        _handleQRScan(barcode.rawValue!);
        break; // Process only the first valid QR code
      }
    }
  }

  void _refreshQRToken(BuildContext context, QRValidationState state) {
    if (state is QRTokenGenerated) {
      context.read<QRValidationBloc>().add(
        RefreshQRTokenEvent(
          zoneId: state.zoneId,
          userId: widget.currentUserId,
        ),
      );
    }
  }

  void _handleQRScan(String qrData) async {
    // Prevent multiple processing
    if (_isProcessing) return;

    // Stop scanning and set processing state
    setState(() {
      _isScanning = false;
      _isProcessing = true;
    });

    try {
      await _scannerController?.stop();
    } catch (e) {
      // Ignore controller stop errors
    }

    try {
      // Decrypt QR data to extract token ID
      final tokenPayload = QRTokenService.instance.validateAndDecryptToken(qrData);

      if (tokenPayload == null) {
        _showErrorDialog(context, 'Código QR inválido o expirado.');
        _resetScanning();
        return;
      }

      final tokenId = tokenPayload['tokenId'] as String?;
      if (tokenId == null) {
        _showErrorDialog(context, 'Código QR no contiene un ID de token válido.');
        _resetScanning();
        return;
      }

      // Get current location for proximity verification
      final proximityData = await ProximityVerificationService.instance.getCurrentLocationForVerification();

      if (proximityData != null) {
        // Validate location quality
        if (!ProximityVerificationService.instance.isLocationDataValid(proximityData)) {
          _showErrorDialog(context, 'La precisión del GPS es insuficiente. Intenta en un área con mejor señal.');
          _resetScanning();
          return;
        }

        context.read<QRValidationBloc>().add(
          ScanQRTokenEvent(
            tokenId: tokenId, // Use extracted token ID
            scannerUserId: widget.currentUserId,
            scannerLocation: proximityData,
          ),
        );
      } else {
        _showErrorDialog(context, 'No se pudo obtener la ubicación. Verifica los permisos de ubicación.');
        _resetScanning();
      }
    } catch (e) {
      _showErrorDialog(context, 'Error al procesar el código QR: ${e.toString()}');
      _resetScanning();
    }
  }

  void _resetScanning() {
    setState(() {
      _isScanning = true;
      _isProcessing = false;
    });

    try {
      _scannerController?.start();
    } catch (e) {
      // Ignore controller start errors
    }
  }

  void _cancelValidation(BuildContext context) {
    // Reset the QR validation state
    context.read<QRValidationBloc>().add(const ResetQRValidationEvent());
    _navigateToZones(context);
  }

  void _startCountdown(Duration duration) {
    _remainingSeconds = duration.inSeconds;
    _countdownTimer?.cancel();
    
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _remainingSeconds--;
        });

        if (_remainingSeconds <= 0) {
          timer.cancel();
        }
      } else {
        timer.cancel();
      }
    });
  }

  void _showErrorDialog(BuildContext context, String message) {
    HapticFeedback.lightImpact();

    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: message,
      confirmText: 'OK',
      onConfirm: () => Navigator.of(context).pop(),
    );
  }

  void _showEnhancedErrorDialog(BuildContext context, QRValidationErrorState errorState) {
    // Get user-friendly error title
    String errorTitle = _getErrorTitle(errorState.errorType ?? QRValidationErrorType.unknown);

    // Build error message with recovery suggestions
    String errorMessage = errorState.message;
    if (errorState.recoverySuggestions.isNotEmpty) {
      errorMessage += '\n\nSugerencias:\n';
      for (int i = 0; i < errorState.recoverySuggestions.length; i++) {
        errorMessage += '• ${errorState.recoverySuggestions[i]}';
        if (i < errorState.recoverySuggestions.length - 1) {
          errorMessage += '\n';
        }
      }
    }

    IosDialog.showAlertDialog(
      context: context,
      title: errorTitle,
      message: errorMessage,
      cancelText: 'Cancelar',
      confirmText: errorState.isRecoverable ? 'Reintentar' : null,
      onCancel: () {
        // Dialog automatically closes
      },
      onConfirm: errorState.isRecoverable && widget.zone != null ? () {
        // Retry generating QR token
        final bloc = context.read<QRValidationBloc>();
        bloc.add(GenerateQRTokenEvent(
          zoneId: widget.zone!.id,
          userId: widget.currentUserId,
        ));
      } : null,
    );
  }

  String _getErrorTitle(QRValidationErrorType errorType) {
    switch (errorType) {
      case QRValidationErrorType.network:
        return 'Error de Conexión';
      case QRValidationErrorType.location:
        return 'Error de Ubicación';
      case QRValidationErrorType.permission:
        return 'Permisos Requeridos';
      case QRValidationErrorType.timeout:
        return 'Tiempo Agotado';
      case QRValidationErrorType.validation:
        return 'Error de Validación';
      case QRValidationErrorType.server:
        return 'Error del Servidor';
      case QRValidationErrorType.security:
        return 'Error de Seguridad';
      case QRValidationErrorType.unknown:
        return 'Error Inesperado';
    }
  }

  void _showQRScannedDialog(BuildContext context, QRTokenScanned state) {
    HapticFeedback.lightImpact();

    IosDialog.showAlertDialog(
      context: context,
      title: '¡Escaneo Exitoso!',
      message: 'Has escaneado el código QR correctamente. '
          'Esperando confirmación de la validación...',
      confirmText: 'Entendido',
    );
  }

  void _showValidationCompletedDialog(BuildContext context, QRValidationCompleted state) {
    HapticFeedback.lightImpact();

    final isSuccessful = state.result.isSuccessful;
    final hasZoneUpdate = state.result.updatedZone != null;

    if (isSuccessful && hasZoneUpdate) {
      // Zone validation count increased - show success message
      IosDialog.showAlertDialog(
        context: context,
        title: '¡Validación Exitosa!',
        message: 'La validación social se completó correctamente. '
            'El contador de validaciones de la zona ha sido actualizado. '
            '¡Felicitaciones por contribuir a la seguridad comunitaria!',
        confirmText: 'Excelente',
        onConfirm: () {
          Navigator.of(context).pop(); // Close dialog
          Navigator.of(context).pop(); // Close screen
        },
      );
    } else if (isSuccessful) {
      // Validation completed but no zone update
      IosDialog.showAlertDialog(
        context: context,
        title: 'Validación Completada',
        message: 'La validación social se completó exitosamente.',
        confirmText: 'OK',
        onConfirm: () {
          Navigator.of(context).pop(); // Close dialog
          Navigator.of(context).pop(); // Close screen
        },
      );
    } else {
      // Validation failed
      IosDialog.showAlertDialog(
        context: context,
        title: 'Validación Fallida',
        message: state.result.errorMessage ?? 'La validación no pudo completarse.',
        confirmText: 'Entendido',
      );
    }
  }


}

/// Custom painter for QR scanner overlay with simple border
class QRScannerOverlayPainter extends CustomPainter {
  final double scanAreaSize;
  final Color borderColor;
  final Color cornerColor;

  QRScannerOverlayPainter({
    required this.scanAreaSize,
    required this.borderColor,
    required this.cornerColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final scanAreaRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: scanAreaSize,
      height: scanAreaSize,
    );

    // Draw simple border around scan area
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    canvas.drawRect(scanAreaRect, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
