import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Base class for all zone validation events
abstract class ZoneValidationEvent extends Equatable {
  const ZoneValidationEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load user zones
class LoadUserZones extends ZoneValidationEvent {
  final String userId;
  final ZoneStatus? statusFilter;
  final ZoneType? typeFilter;
  final ValidationMethod? methodFilter;

  const LoadUserZones({
    required this.userId,
    this.statusFilter,
    this.typeFilter,
    this.methodFilter,
  });

  @override
  List<Object?> get props => [userId, statusFilter, typeFilter, methodFilter];
}

/// Event to create a new zone
class CreateZone extends ZoneValidationEvent {
  final ZoneEntity zone;

  const CreateZone(this.zone);

  @override
  List<Object> get props => [zone];
}

/// Event to update an existing zone
class UpdateZone extends ZoneValidationEvent {
  final ZoneEntity zone;

  const UpdateZone(this.zone);

  @override
  List<Object> get props => [zone];
}

/// Event to delete a zone
class DeleteZone extends ZoneValidationEvent {
  final String zoneId;

  const DeleteZone(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to request social validation for a zone
class RequestSocialValidation extends ZoneValidationEvent {
  final String zoneId;

  const RequestSocialValidation(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to enable automatic validation for a zone
class EnableAutomaticValidation extends ZoneValidationEvent {
  final String zoneId;

  const EnableAutomaticValidation(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to disable automatic validation for a zone
class DisableAutomaticValidation extends ZoneValidationEvent {
  final String zoneId;

  const DisableAutomaticValidation(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to validate a zone (community validation)
class ValidateZone extends ZoneValidationEvent {
  final String zoneId;
  final String validatorUserId;

  const ValidateZone({
    required this.zoneId,
    required this.validatorUserId,
  });

  @override
  List<Object> get props => [zoneId, validatorUserId];
}

/// Event to load zones near a location
class LoadZonesNearLocation extends ZoneValidationEvent {
  final double latitude;
  final double longitude;
  final double radiusInMeters;

  const LoadZonesNearLocation({
    required this.latitude,
    required this.longitude,
    this.radiusInMeters = 1000.0,
  });

  @override
  List<Object> get props => [latitude, longitude, radiusInMeters];
}

/// Event to search zones
class SearchZones extends ZoneValidationEvent {
  final String query;
  final String? userId;

  const SearchZones({
    required this.query,
    this.userId,
  });

  @override
  List<Object?> get props => [query, userId];
}

/// Event to load validation history for a zone
class LoadValidationHistory extends ZoneValidationEvent {
  final String zoneId;

  const LoadValidationHistory(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to load user statistics
class LoadUserStatistics extends ZoneValidationEvent {
  final String userId;

  const LoadUserStatistics(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Event to check if user can validate a zone
class CheckValidationEligibility extends ZoneValidationEvent {
  final String userId;
  final String zoneId;

  const CheckValidationEligibility({
    required this.userId,
    required this.zoneId,
  });

  @override
  List<Object> get props => [userId, zoneId];
}

/// Event to load community validation requests
class LoadCommunityValidationRequests extends ZoneValidationEvent {
  final String userId;

  const LoadCommunityValidationRequests(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Event to process automatic validation
class ProcessAutomaticValidation extends ZoneValidationEvent {
  final String zoneId;
  final String userId;
  final double userLatitude;
  final double userLongitude;

  const ProcessAutomaticValidation({
    required this.zoneId,
    required this.userId,
    required this.userLatitude,
    required this.userLongitude,
  });

  @override
  List<Object> get props => [zoneId, userId, userLatitude, userLongitude];
}

/// Event to refresh zones (pull to refresh)
class RefreshZones extends ZoneValidationEvent {
  final String userId;

  const RefreshZones(this.userId);

  @override
  List<Object> get props => [userId];
}

/// Event to clear error state
class ClearError extends ZoneValidationEvent {
  const ClearError();
}

/// Event to reset state
class ResetState extends ZoneValidationEvent {
  const ResetState();
}

/// Event to start watching zone validation updates
class StartWatchingZoneValidation extends ZoneValidationEvent {
  final String zoneId;

  const StartWatchingZoneValidation(this.zoneId);

  @override
  List<Object> get props => [zoneId];
}

/// Event to stop watching zone validation updates
class StopWatchingZoneValidation extends ZoneValidationEvent {
  const StopWatchingZoneValidation();
}

/// Event when zone validation is updated (from stream)
class ZoneValidationUpdated extends ZoneValidationEvent {
  final ZoneEntity zone;

  const ZoneValidationUpdated(this.zone);

  @override
  List<Object> get props => [zone];
}

/// Event to batch update zones
class BatchUpdateZones extends ZoneValidationEvent {
  final List<ZoneEntity> zones;

  const BatchUpdateZones(this.zones);

  @override
  List<Object> get props => [zones];
}

/// Event to handle identity validation requirement for zone creation
class IdentityValidationRequired extends ZoneValidationEvent {
  final String userId;
  final String validationStatus;
  final String message;

  const IdentityValidationRequired({
    required this.userId,
    required this.validationStatus,
    required this.message,
  });

  @override
  List<Object> get props => [userId, validationStatus, message];
}
