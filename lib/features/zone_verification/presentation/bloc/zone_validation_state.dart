import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';

/// Enum for zone validation status
enum ZoneValidationStatus {
  initial,
  loading,
  loaded,
  creating,
  created,
  updating,
  updated,
  deleting,
  deleted,
  validating,
  validated,
  error,
  identityValidationRequired,
}

/// State class for zone validation
class ZoneValidationState extends Equatable {
  final ZoneValidationStatus status;
  final List<ZoneEntity> zones;
  final List<ZoneEntity> nearbyZones;
  final List<ZoneEntity> searchResults;
  final List<ZoneEntity> communityValidationRequests;
  final List<ValidationEntity> validationHistory;
  final ZoneEntity? selectedZone;
  final Map<String, dynamic>? userStatistics;
  final String? errorMessage;
  final bool isRefreshing;
  final bool canValidateZone;
  final int dailyValidationCount;
  final SocialValidationRequest? socialValidationRequest;
  final AutomaticValidationResult? automaticValidationResult;
  final String? identityValidationUserId;
  final String? identityValidationStatus;
  final String? identityValidationMessage;

  const ZoneValidationState({
    this.status = ZoneValidationStatus.initial,
    this.zones = const [],
    this.nearbyZones = const [],
    this.searchResults = const [],
    this.communityValidationRequests = const [],
    this.validationHistory = const [],
    this.selectedZone,
    this.userStatistics,
    this.errorMessage,
    this.isRefreshing = false,
    this.canValidateZone = false,
    this.dailyValidationCount = 0,
    this.socialValidationRequest,
    this.automaticValidationResult,
    this.identityValidationUserId,
    this.identityValidationStatus,
    this.identityValidationMessage,
  });

  /// Convenience getters
  bool get isInitial => status == ZoneValidationStatus.initial;
  bool get isLoading => status == ZoneValidationStatus.loading;
  bool get isLoaded => status == ZoneValidationStatus.loaded;
  bool get isCreating => status == ZoneValidationStatus.creating;
  bool get isCreated => status == ZoneValidationStatus.created;
  bool get isUpdating => status == ZoneValidationStatus.updating;
  bool get isUpdated => status == ZoneValidationStatus.updated;
  bool get isDeleting => status == ZoneValidationStatus.deleting;
  bool get isDeleted => status == ZoneValidationStatus.deleted;
  bool get isValidating => status == ZoneValidationStatus.validating;
  bool get isValidated => status == ZoneValidationStatus.validated;
  bool get hasError => status == ZoneValidationStatus.error;
  bool get requiresIdentityValidation => status == ZoneValidationStatus.identityValidationRequired;

  /// Get zones by status
  List<ZoneEntity> get pendingZones => zones.where((z) => z.validationStatus == ZoneStatus.pending).toList();
  List<ZoneEntity> get validatedZones => zones.where((z) => z.validationStatus == ZoneStatus.validated).toList();
  List<ZoneEntity> get rejectedZones => zones.where((z) => z.validationStatus == ZoneStatus.rejected).toList();

  /// Get zones by validation method
  List<ZoneEntity> get socialValidationZones => zones.where((z) => z.validationMethod == ValidationMethod.social).toList();
  List<ZoneEntity> get automaticValidationZones => zones.where((z) => z.validationMethod == ValidationMethod.automatic).toList();

  /// Get zones by type
  List<ZoneEntity> get homeZones => zones.where((z) => z.type == ZoneType.home).toList();
  List<ZoneEntity> get workZones => zones.where((z) => z.type == ZoneType.work).toList();
  List<ZoneEntity> get universityZones => zones.where((z) => z.type == ZoneType.university).toList();
  List<ZoneEntity> get otherZones => zones.where((z) => z.type == ZoneType.other).toList();

  /// Check if user has reached daily validation limit
  bool get hasReachedDailyLimit => dailyValidationCount >= 5; // Assuming 5 validations per day limit

  /// Get validation progress for social validation zones
  Map<String, double> get validationProgress {
    final Map<String, double> progress = {};
    for (final zone in socialValidationZones) {
      if (zone.validationStatus == ZoneStatus.pending) {
        progress[zone.id] = zone.communityValidationCount / 3.0; // Assuming 3 validations needed
      }
    }
    return progress;
  }

  /// Copy with method
  ZoneValidationState copyWith({
    ZoneValidationStatus? status,
    List<ZoneEntity>? zones,
    List<ZoneEntity>? nearbyZones,
    List<ZoneEntity>? searchResults,
    List<ZoneEntity>? communityValidationRequests,
    List<ValidationEntity>? validationHistory,
    ZoneEntity? selectedZone,
    Map<String, dynamic>? userStatistics,
    String? errorMessage,
    bool? clearError,
    bool? isRefreshing,
    bool? canValidateZone,
    int? dailyValidationCount,
    SocialValidationRequest? socialValidationRequest,
    AutomaticValidationResult? automaticValidationResult,
    String? identityValidationUserId,
    String? identityValidationStatus,
    String? identityValidationMessage,
  }) {
    return ZoneValidationState(
      status: status ?? this.status,
      zones: zones ?? this.zones,
      nearbyZones: nearbyZones ?? this.nearbyZones,
      searchResults: searchResults ?? this.searchResults,
      communityValidationRequests: communityValidationRequests ?? this.communityValidationRequests,
      validationHistory: validationHistory ?? this.validationHistory,
      selectedZone: selectedZone ?? this.selectedZone,
      userStatistics: userStatistics ?? this.userStatistics,
      errorMessage: clearError == true ? null : (errorMessage ?? this.errorMessage),
      isRefreshing: isRefreshing ?? this.isRefreshing,
      canValidateZone: canValidateZone ?? this.canValidateZone,
      dailyValidationCount: dailyValidationCount ?? this.dailyValidationCount,
      socialValidationRequest: socialValidationRequest ?? this.socialValidationRequest,
      automaticValidationResult: automaticValidationResult ?? this.automaticValidationResult,
      identityValidationUserId: identityValidationUserId ?? this.identityValidationUserId,
      identityValidationStatus: identityValidationStatus ?? this.identityValidationStatus,
      identityValidationMessage: identityValidationMessage ?? this.identityValidationMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        zones,
        nearbyZones,
        searchResults,
        communityValidationRequests,
        validationHistory,
        selectedZone,
        userStatistics,
        errorMessage,
        isRefreshing,
        canValidateZone,
        dailyValidationCount,
        socialValidationRequest,
        automaticValidationResult,
        identityValidationUserId,
        identityValidationStatus,
        identityValidationMessage,
      ];

  /// Factory constructors for common states
  factory ZoneValidationState.initial() => const ZoneValidationState();

  factory ZoneValidationState.loading() => const ZoneValidationState(
        status: ZoneValidationStatus.loading,
      );

  factory ZoneValidationState.loaded(List<ZoneEntity> zones) => ZoneValidationState(
        status: ZoneValidationStatus.loaded,
        zones: zones,
      );

  factory ZoneValidationState.error(String message) => ZoneValidationState(
        status: ZoneValidationStatus.error,
        errorMessage: message,
      );

  factory ZoneValidationState.creating() => const ZoneValidationState(
        status: ZoneValidationStatus.creating,
      );

  factory ZoneValidationState.created(ZoneEntity zone) => ZoneValidationState(
        status: ZoneValidationStatus.created,
        selectedZone: zone,
      );

  factory ZoneValidationState.updating() => const ZoneValidationState(
        status: ZoneValidationStatus.updating,
      );

  factory ZoneValidationState.updated(ZoneEntity zone) => ZoneValidationState(
        status: ZoneValidationStatus.updated,
        selectedZone: zone,
      );

  factory ZoneValidationState.deleting() => const ZoneValidationState(
        status: ZoneValidationStatus.deleting,
      );

  factory ZoneValidationState.deleted() => const ZoneValidationState(
        status: ZoneValidationStatus.deleted,
      );

  factory ZoneValidationState.validating() => const ZoneValidationState(
        status: ZoneValidationStatus.validating,
      );

  factory ZoneValidationState.validated(ZoneEntity zone) => ZoneValidationState(
        status: ZoneValidationStatus.validated,
        selectedZone: zone,
      );

  factory ZoneValidationState.identityValidationRequired({
    required String userId,
    required String validationStatus,
    required String message,
  }) => ZoneValidationState(
        status: ZoneValidationStatus.identityValidationRequired,
        identityValidationUserId: userId,
        identityValidationStatus: validationStatus,
        identityValidationMessage: message,
      );
}

/// Data classes for validation results
class SocialValidationRequest extends Equatable {
  final String zoneId;
  final String qrCode;
  final String shareUrl;
  final DateTime expiresAt;
  final int currentValidations;
  final int requiredValidations;

  const SocialValidationRequest({
    required this.zoneId,
    required this.qrCode,
    required this.shareUrl,
    required this.expiresAt,
    required this.currentValidations,
    required this.requiredValidations,
  });

  double get progress => currentValidations / requiredValidations;
  bool get isComplete => currentValidations >= requiredValidations;
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  @override
  List<Object> get props => [
        zoneId,
        qrCode,
        shareUrl,
        expiresAt,
        currentValidations,
        requiredValidations,
      ];
}

class AutomaticValidationResult extends Equatable {
  final String zoneId;
  final bool isEnabled;
  final bool isMonitoring;
  final DateTime? lastValidationCheck;
  final String? errorMessage;

  const AutomaticValidationResult({
    required this.zoneId,
    required this.isEnabled,
    required this.isMonitoring,
    this.lastValidationCheck,
    this.errorMessage,
  });

  bool get hasError => errorMessage != null;

  @override
  List<Object?> get props => [
        zoneId,
        isEnabled,
        isMonitoring,
        lastValidationCheck,
        errorMessage,
      ];
}
