import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/create_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/get_user_zones_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/request_social_validation_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/enable_automatic_validation_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/delete_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/validate_zone_use_case.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_event.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/bloc/zone_validation_state.dart';
import 'package:respublicaseguridad/core/services/limited_tracking_service.dart';
import 'package:respublicaseguridad/core/error/failures.dart';

@injectable
class ZoneValidationBloc extends Bloc<ZoneValidationEvent, ZoneValidationState> {
  final CreateZoneUseCase _createZoneUseCase;
  final GetUserZonesUseCase _getUserZonesUseCase;
  final RequestSocialValidationUseCase _requestSocialValidationUseCase;
  final EnableAutomaticValidationUseCase _enableAutomaticValidationUseCase;
  final DeleteZoneUseCase _deleteZoneUseCase;
  final ValidateZoneUseCase _validateZoneUseCase;
  final LimitedTrackingService _limitedTrackingService;

  StreamSubscription? _zoneValidationSubscription;

  ZoneValidationBloc({
    required CreateZoneUseCase createZoneUseCase,
    required GetUserZonesUseCase getUserZonesUseCase,
    required RequestSocialValidationUseCase requestSocialValidationUseCase,
    required EnableAutomaticValidationUseCase enableAutomaticValidationUseCase,
    required DeleteZoneUseCase deleteZoneUseCase,
    required ValidateZoneUseCase validateZoneUseCase,
    required LimitedTrackingService limitedTrackingService,
  })  : _createZoneUseCase = createZoneUseCase,
        _getUserZonesUseCase = getUserZonesUseCase,
        _requestSocialValidationUseCase = requestSocialValidationUseCase,
        _enableAutomaticValidationUseCase = enableAutomaticValidationUseCase,
        _deleteZoneUseCase = deleteZoneUseCase,
        _validateZoneUseCase = validateZoneUseCase,
        _limitedTrackingService = limitedTrackingService,
        super(ZoneValidationState.initial()) {
    // Register event handlers
    on<LoadUserZones>(_onLoadUserZones);
    on<CreateZone>(_onCreateZone);
    on<UpdateZone>(_onUpdateZone);
    on<DeleteZone>(_onDeleteZone);
    on<RequestSocialValidation>(_onRequestSocialValidation);
    on<EnableAutomaticValidation>(_onEnableAutomaticValidation);
    on<DisableAutomaticValidation>(_onDisableAutomaticValidation);
    on<ValidateZone>(_onValidateZone);
    on<LoadZonesNearLocation>(_onLoadZonesNearLocation);
    on<SearchZones>(_onSearchZones);
    on<LoadValidationHistory>(_onLoadValidationHistory);
    on<LoadUserStatistics>(_onLoadUserStatistics);
    on<CheckValidationEligibility>(_onCheckValidationEligibility);
    on<LoadCommunityValidationRequests>(_onLoadCommunityValidationRequests);
    on<ProcessAutomaticValidation>(_onProcessAutomaticValidation);
    on<RefreshZones>(_onRefreshZones);
    on<ClearError>(_onClearError);
    on<ResetState>(_onResetState);
    on<StartWatchingZoneValidation>(_onStartWatchingZoneValidation);
    on<StopWatchingZoneValidation>(_onStopWatchingZoneValidation);
    on<ZoneValidationUpdated>(_onZoneValidationUpdated);
    on<BatchUpdateZones>(_onBatchUpdateZones);
    on<IdentityValidationRequired>(_onIdentityValidationRequired);
  }

  @override
  Future<void> close() {
    _zoneValidationSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLoadUserZones(
    LoadUserZones event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.loading));

    final result = await _getUserZonesUseCase(GetUserZonesParams(
      userId: event.userId,
      status: event.statusFilter,
      type: event.typeFilter,
      validationMethod: event.methodFilter,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (zones) => emit(state.copyWith(
        status: ZoneValidationStatus.loaded,
        zones: zones,
        clearError: true,
      )),
    );
  }

  Future<void> _onCreateZone(
    CreateZone event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.creating));

    final result = await _createZoneUseCase(CreateZoneParams(
      userId: event.zone.userId,
      name: event.zone.name,
      type: event.zone.type,
      address: event.zone.address,
      centerCoordinates: event.zone.centerCoordinates,
      presenceHours: event.zone.presenceHours ?? '',
      validationMethod: event.zone.validationMethod,
    ));

    result.fold(
      (failure) {
        if (failure is IdentityValidationRequiredFailure) {
          // Handle identity validation requirement
          add(IdentityValidationRequired(
            userId: failure.userId,
            validationStatus: failure.validationStatus,
            message: failure.message,
          ));
        } else {
          emit(state.copyWith(
            status: ZoneValidationStatus.error,
            errorMessage: failure.message,
          ));
        }
      },
      (zone) async {
        final updatedZones = List<ZoneEntity>.from(state.zones)..add(zone);
        emit(state.copyWith(
          status: ZoneValidationStatus.created,
          zones: updatedZones,
          selectedZone: zone,
          clearError: true,
        ));

        // Start the 3-day validation window when first zone is added
        await _limitedTrackingService.onFirstZoneAdded();
      },
    );
  }

  Future<void> _onUpdateZone(
    UpdateZone event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.updating));

    // For now, we'll use the create use case as update functionality
    // In a real implementation, you'd have an UpdateZoneUseCase
    final result = await _createZoneUseCase(CreateZoneParams(
      userId: event.zone.userId,
      name: event.zone.name,
      type: event.zone.type,
      address: event.zone.address,
      centerCoordinates: event.zone.centerCoordinates,
      presenceHours: event.zone.presenceHours ?? '',
      validationMethod: event.zone.validationMethod,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (zone) {
        final updatedZones = state.zones.map((z) => z.id == zone.id ? zone : z).toList();
        emit(state.copyWith(
          status: ZoneValidationStatus.updated,
          zones: updatedZones,
          selectedZone: zone,
          clearError: true,
        ));
      },
    );
  }

  Future<void> _onDeleteZone(
    DeleteZone event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.deleting));

    // Find the zone to get the user ID
    final zone = state.zones.firstWhere((z) => z.id == event.zoneId);
    final result = await _deleteZoneUseCase(DeleteZoneParams(
      userId: zone.userId,
      zoneId: event.zoneId,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (_) {
        final updatedZones = state.zones.where((z) => z.id != event.zoneId).toList();
        emit(state.copyWith(
          status: ZoneValidationStatus.deleted,
          zones: updatedZones,
          clearError: true,
        ));
      },
    );
  }

  Future<void> _onRequestSocialValidation(
    RequestSocialValidation event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.loading));

    // Find the zone to get the user ID
    final zone = state.zones.firstWhere((z) => z.id == event.zoneId);
    final result = await _requestSocialValidationUseCase(RequestSocialValidationParams(
      userId: zone.userId,
      zoneId: event.zoneId,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (socialValidationRequest) => emit(state.copyWith(
        status: ZoneValidationStatus.loaded,
        socialValidationRequest: SocialValidationRequest(
          zoneId: event.zoneId,
          qrCode: socialValidationRequest.qrCodeData,
          shareUrl: socialValidationRequest.validationUrl,
          expiresAt: socialValidationRequest.expiresAt,
          currentValidations: socialValidationRequest.currentValidationCount,
          requiredValidations: socialValidationRequest.requiredValidationCount,
        ),
        clearError: true,
      )),
    );
  }

  Future<void> _onEnableAutomaticValidation(
    EnableAutomaticValidation event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.loading));

    final zone = state.zones.firstWhere((z) => z.id == event.zoneId);
    final result = await _enableAutomaticValidationUseCase(EnableAutomaticValidationParams(
      userId: zone.userId,
      zoneId: event.zoneId,
      hasLocationPermission: true, 
      isLocationServiceEnabled: true,
      isLocationAccurate: true,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (automaticValidationResult) => emit(state.copyWith(
        status: ZoneValidationStatus.loaded,
        automaticValidationResult: AutomaticValidationResult(
          zoneId: event.zoneId,
          isEnabled: automaticValidationResult.isEnabled,
          isMonitoring: automaticValidationResult.monitoringStarted,
          lastValidationCheck: automaticValidationResult.nextLocationCheck,
          errorMessage: automaticValidationResult.allRequirementsMet ? null : 'Location requirements not met',
        ),
        clearError: true,
      )),
    );
  }

  Future<void> _onDisableAutomaticValidation(
    DisableAutomaticValidation event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.loading));

    // For now, we'll just update the state to indicate automatic validation is disabled
    // In a real implementation, this would call a use case to disable monitoring
    emit(state.copyWith(
      status: ZoneValidationStatus.loaded,
      automaticValidationResult: AutomaticValidationResult(
        zoneId: event.zoneId,
        isEnabled: false,
        isMonitoring: false,
        lastValidationCheck: null,
        errorMessage: null,
      ),
      clearError: true,
    ));
  }

  Future<void> _onValidateZone(
    ValidateZone event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(status: ZoneValidationStatus.validating));

    final result = await _validateZoneUseCase(ValidateZoneParams(
      zoneId: event.zoneId,
      validatorUserId: event.validatorUserId,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ZoneValidationStatus.error,
        errorMessage: failure.message,
      )),
      (validationResult) {
        final updatedZones = state.zones.map((z) => z.id == validationResult.zone.id ? validationResult.zone : z).toList();
        emit(state.copyWith(
          status: ZoneValidationStatus.validated,
          zones: updatedZones,
          selectedZone: validationResult.zone,
          clearError: true,
        ));
      },
    );
  }

  Future<void> _onLoadZonesNearLocation(
    LoadZonesNearLocation event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(
      nearbyZones: [],
      clearError: true,
    ));
  }

  Future<void> _onSearchZones(
    SearchZones event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a SearchZonesUseCase
    // For now, we'll filter existing zones
    final searchResults = state.zones
        .where((zone) => zone.name.toLowerCase().contains(event.query.toLowerCase()) ||
                        zone.address.toLowerCase().contains(event.query.toLowerCase()))
        .toList();

    emit(state.copyWith(
      searchResults: searchResults,
      clearError: true,
    ));
  }

  Future<void> _onLoadValidationHistory(
    LoadValidationHistory event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a GetValidationHistoryUseCase
    emit(state.copyWith(
      validationHistory: [],
      clearError: true,
    ));
  }

  Future<void> _onLoadUserStatistics(
    LoadUserStatistics event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a GetUserStatisticsUseCase
    emit(state.copyWith(
      userStatistics: {},
      clearError: true,
    ));
  }

  Future<void> _onCheckValidationEligibility(
    CheckValidationEligibility event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a CanUserValidateZoneUseCase
    emit(state.copyWith(
      canValidateZone: true,
      clearError: true,
    ));
  }

  Future<void> _onLoadCommunityValidationRequests(
    LoadCommunityValidationRequests event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a GetCommunityValidationRequestsUseCase
    emit(state.copyWith(
      communityValidationRequests: [],
      clearError: true,
    ));
  }

  Future<void> _onProcessAutomaticValidation(
    ProcessAutomaticValidation event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a ProcessAutomaticValidationUseCase
    emit(state.copyWith(clearError: true));
  }

  Future<void> _onRefreshZones(
    RefreshZones event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));
    
    // Reload zones
    add(LoadUserZones(userId: event.userId));
    
    emit(state.copyWith(isRefreshing: false));
  }

  void _onClearError(
    ClearError event,
    Emitter<ZoneValidationState> emit,
  ) {
    emit(state.copyWith(clearError: true));
  }

  void _onResetState(
    ResetState event,
    Emitter<ZoneValidationState> emit,
  ) {
    emit(ZoneValidationState.initial());
  }

  void _onStartWatchingZoneValidation(
    StartWatchingZoneValidation event,
    Emitter<ZoneValidationState> emit,
  ) {
    // This would set up a stream subscription to watch zone validation updates
    // For now, we'll just emit the current state
    emit(state.copyWith(clearError: true));
  }

  void _onStopWatchingZoneValidation(
    StopWatchingZoneValidation event,
    Emitter<ZoneValidationState> emit,
  ) {
    _zoneValidationSubscription?.cancel();
    _zoneValidationSubscription = null;
  }

  void _onZoneValidationUpdated(
    ZoneValidationUpdated event,
    Emitter<ZoneValidationState> emit,
  ) {
    final updatedZones = state.zones.map((z) => z.id == event.zone.id ? event.zone : z).toList();
    emit(state.copyWith(
      zones: updatedZones,
      selectedZone: event.zone,
    ));
  }

  Future<void> _onBatchUpdateZones(
    BatchUpdateZones event,
    Emitter<ZoneValidationState> emit,
  ) async {
    // This would require a BatchUpdateZonesUseCase
    emit(state.copyWith(
      zones: event.zones,
      clearError: true,
    ));
  }

  Future<void> _onIdentityValidationRequired(
    IdentityValidationRequired event,
    Emitter<ZoneValidationState> emit,
  ) async {
    emit(ZoneValidationState.identityValidationRequired(
      userId: event.userId,
      validationStatus: event.validationStatus,
      message: event.message,
    ));
  }
}
