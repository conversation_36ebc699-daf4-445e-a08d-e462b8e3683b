import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/services/qr_validation_error_handler.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/create_qr_validation_session_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/generate_qr_token_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/validate_qr_scan_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/complete_qr_validation_usecase.dart';

// Events
abstract class QRValidationEvent extends Equatable {
  const QRValidationEvent();

  @override
  List<Object?> get props => [];
}

class CreateQRValidationSessionEvent extends QRValidationEvent {
  final String zoneId;
  final String initiatorUserId;

  const CreateQRValidationSessionEvent({
    required this.zoneId,
    required this.initiatorUserId,
  });

  @override
  List<Object?> get props => [zoneId, initiatorUserId];
}

class JoinQRValidationSessionEvent extends QRValidationEvent {
  final String sessionId;
  final String validatorUserId;

  const JoinQRValidationSessionEvent({
    required this.sessionId,
    required this.validatorUserId,
  });

  @override
  List<Object?> get props => [sessionId, validatorUserId];
}

class GenerateQRTokenEvent extends QRValidationEvent {
  final String zoneId;
  final String userId;

  const GenerateQRTokenEvent({
    required this.zoneId,
    required this.userId,
  });

  @override
  List<Object?> get props => [zoneId, userId];
}

class ScanQRTokenEvent extends QRValidationEvent {
  final String tokenId;
  final String scannerUserId;
  final ProximityDataEntity scannerLocation;

  const ScanQRTokenEvent({
    required this.tokenId,
    required this.scannerUserId,
    required this.scannerLocation,
  });

  @override
  List<Object?> get props => [tokenId, scannerUserId, scannerLocation];
}

class CompleteQRValidationEvent extends QRValidationEvent {
  final String sessionId;

  const CompleteQRValidationEvent({required this.sessionId});

  @override
  List<Object?> get props => [sessionId];
}

class CancelQRValidationEvent extends QRValidationEvent {
  final String sessionId;

  const CancelQRValidationEvent({required this.sessionId});

  @override
  List<Object?> get props => [sessionId];
}

class RefreshQRTokenEvent extends QRValidationEvent {
  final String zoneId;
  final String userId;

  const RefreshQRTokenEvent({
    required this.zoneId,
    required this.userId,
  });

  @override
  List<Object?> get props => [zoneId, userId];
}

class ResetQRValidationEvent extends QRValidationEvent {
  const ResetQRValidationEvent();
}

// States
abstract class QRValidationState extends Equatable {
  const QRValidationState();

  @override
  List<Object?> get props => [];
}

class QRValidationInitial extends QRValidationState {
  const QRValidationInitial();
}

class QRValidationLoading extends QRValidationState {
  final String? message;

  const QRValidationLoading({this.message});

  @override
  List<Object?> get props => [message];
}

class QRValidationSessionCreated extends QRValidationState {
  final QRValidationSessionEntity session;
  final ZoneEntity zone;

  const QRValidationSessionCreated({
    required this.session,
    required this.zone,
  });

  @override
  List<Object?> get props => [session, zone];
}

class QRValidationSessionJoined extends QRValidationState {
  final QRValidationSessionEntity session;
  final ZoneEntity zone;

  const QRValidationSessionJoined({
    required this.session,
    required this.zone,
  });

  @override
  List<Object?> get props => [session, zone];
}

class QRTokenGenerated extends QRValidationState {
  final QRTokenEntity token;
  final String zoneId;
  final Duration remainingTime;

  const QRTokenGenerated({
    required this.token,
    required this.zoneId,
    required this.remainingTime,
  });

  @override
  List<Object?> get props => [token, zoneId, remainingTime];
}

class QRTokenScanned extends QRValidationState {
  final QRScanValidationResult result;
  final String zoneId;

  const QRTokenScanned({
    required this.result,
    required this.zoneId,
  });

  @override
  List<Object?> get props => [result, zoneId];
}

class QRValidationCompleted extends QRValidationState {
  final QRValidationCompletionResult result;

  const QRValidationCompleted({required this.result});

  @override
  List<Object?> get props => [result];
}

class QRValidationCancelled extends QRValidationState {
  final QRValidationSessionEntity session;

  const QRValidationCancelled({required this.session});

  @override
  List<Object?> get props => [session];
}

class QRValidationErrorState extends QRValidationState {
  final String message;
  final String? errorCode;
  final QRValidationErrorType? errorType;
  final bool isRecoverable;
  final List<String> recoverySuggestions;

  const QRValidationErrorState({
    required this.message,
    this.errorCode,
    this.errorType,
    this.isRecoverable = true,
    this.recoverySuggestions = const [],
  });

  /// Create error from QRValidationError
  factory QRValidationErrorState.fromQRError(QRValidationError error) {
    return QRValidationErrorState(
      message: error.message,
      errorCode: error.type.name,
      errorType: error.type,
      isRecoverable: QRValidationErrorHandler.instance.isRecoverable(error),
      recoverySuggestions: QRValidationErrorHandler.instance.getRecoverySuggestions(error),
    );
  }

  @override
  List<Object?> get props => [message, errorCode, errorType, isRecoverable, recoverySuggestions];
}

class QRValidationProgress extends QRValidationState {
  final QRValidationSessionEntity session;
  final double progress;
  final String statusMessage;
  final bool canProceed;

  const QRValidationProgress({
    required this.session,
    required this.progress,
    required this.statusMessage,
    required this.canProceed,
  });

  @override
  List<Object?> get props => [session, progress, statusMessage, canProceed];
}

// BLoC
class QRValidationBloc extends Bloc<QRValidationEvent, QRValidationState> {
  final CreateQRValidationSessionUseCase _createSessionUseCase;
  final JoinQRValidationSessionUseCase _joinSessionUseCase;
  final GenerateQRTokenUseCase _generateTokenUseCase;
  final ValidateQRScanUseCase _validateScanUseCase;
  final CompleteQRValidationUseCase _completeValidationUseCase;
  final CancelQRValidationUseCase _cancelValidationUseCase;

  // Current session tracking
  QRValidationSessionEntity? _currentSession;
  Timer? _tokenRefreshTimer;
  Timer? _sessionTimeoutTimer;

  QRValidationBloc({
    required CreateQRValidationSessionUseCase createSessionUseCase,
    required JoinQRValidationSessionUseCase joinSessionUseCase,
    required GenerateQRTokenUseCase generateTokenUseCase,
    required ValidateQRScanUseCase validateScanUseCase,
    required CompleteQRValidationUseCase completeValidationUseCase,
    required CancelQRValidationUseCase cancelValidationUseCase,
  })  : _createSessionUseCase = createSessionUseCase,
        _joinSessionUseCase = joinSessionUseCase,
        _generateTokenUseCase = generateTokenUseCase,
        _validateScanUseCase = validateScanUseCase,
        _completeValidationUseCase = completeValidationUseCase,
        _cancelValidationUseCase = cancelValidationUseCase,
        super(const QRValidationInitial()) {
    on<CreateQRValidationSessionEvent>(_onCreateSession);
    on<JoinQRValidationSessionEvent>(_onJoinSession);
    on<GenerateQRTokenEvent>(_onGenerateToken);
    on<ScanQRTokenEvent>(_onScanToken);
    on<CompleteQRValidationEvent>(_onCompleteValidation);
    on<CancelQRValidationEvent>(_onCancelValidation);
    on<RefreshQRTokenEvent>(_onRefreshToken);
    on<ResetQRValidationEvent>(_onReset);
  }

  @override
  Future<void> close() {
    _tokenRefreshTimer?.cancel();
    _sessionTimeoutTimer?.cancel();
    return super.close();
  }

  Future<void> _onCreateSession(
    CreateQRValidationSessionEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Creating validation session...'));

    final params = CreateQRValidationSessionParams(
      zoneId: event.zoneId,
      initiatorUserId: event.initiatorUserId,
    );

    final result = await _createSessionUseCase(params);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (sessionResult) {
        if (sessionResult.canProceed) {
          _currentSession = sessionResult.session;
          _startSessionTimeoutTimer(sessionResult.session);
          emit(QRValidationSessionCreated(
            session: sessionResult.session,
            zone: sessionResult.zone,
          ));
        } else {
          emit(QRValidationErrorState(
            message: sessionResult.errorMessage ?? 'Cannot create validation session',
          ));
        }
      },
    );
  }

  Future<void> _onJoinSession(
    JoinQRValidationSessionEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Joining validation session...'));

    final params = JoinQRValidationSessionParams(
      sessionId: event.sessionId,
      validatorUserId: event.validatorUserId,
    );

    final result = await _joinSessionUseCase(params);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (sessionResult) {
        if (sessionResult.canProceed) {
          _currentSession = sessionResult.session;
          _startSessionTimeoutTimer(sessionResult.session);
          emit(QRValidationSessionJoined(
            session: sessionResult.session,
            zone: sessionResult.zone,
          ));
        } else {
          emit(QRValidationErrorState(
            message: sessionResult.errorMessage ?? 'Cannot join validation session',
          ));
        }
      },
    );
  }

  Future<void> _onGenerateToken(
    GenerateQRTokenEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Generating QR token...'));

    final params = GenerateQRTokenParams(
      zoneId: event.zoneId,
      userId: event.userId,
    );

    final result = await _generateTokenUseCase(params);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (token) {
        emit(QRTokenGenerated(
          token: token,
          zoneId: event.zoneId,
          remainingTime: token.expiresAt.difference(DateTime.now()),
        ));
        _startTokenRefreshTimer(event.zoneId, event.userId);
      },
    );
  }

  Future<void> _onScanToken(
    ScanQRTokenEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Validating QR scan...'));

    final params = ValidateQRScanParams(
      tokenId: event.tokenId,
      scannerUserId: event.scannerUserId,
      scannerLocation: event.scannerLocation,
    );

    final result = await _validateScanUseCase(params);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (scanResult) {
        if (scanResult.isValidScan) {
          emit(QRTokenScanned(
            result: scanResult,
            zoneId: scanResult.zoneId,
          ));
        } else {
          emit(QRValidationErrorState(
            message: scanResult.errorMessage ?? 'QR scan validation failed',
          ));
        }
      },
    );
  }

  Future<void> _onCompleteValidation(
    CompleteQRValidationEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Completing validation...'));

    final params = CompleteQRValidationParams(sessionId: event.sessionId);
    final result = await _completeValidationUseCase(params);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (completionResult) {
        if (completionResult.isSuccessful) {
          _cleanup();
          emit(QRValidationCompleted(result: completionResult));
        } else {
          emit(QRValidationErrorState(
            message: completionResult.errorMessage ?? 'Validation completion failed',
          ));
        }
      },
    );
  }

  Future<void> _onCancelValidation(
    CancelQRValidationEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    emit(const QRValidationLoading(message: 'Cancelling validation...'));

    final result = await _cancelValidationUseCase(event.sessionId);

    result.fold(
      (failure) => emit(QRValidationErrorState(message: failure.message)),
      (session) {
        _cleanup();
        emit(QRValidationCancelled(session: session));
      },
    );
  }

  Future<void> _onRefreshToken(
    RefreshQRTokenEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    // Generate a new token
    add(GenerateQRTokenEvent(
      zoneId: event.zoneId,
      userId: event.userId,
    ));
  }

  Future<void> _onReset(
    ResetQRValidationEvent event,
    Emitter<QRValidationState> emit,
  ) async {
    _cleanup();
    emit(const QRValidationInitial());
  }

  void _startTokenRefreshTimer(String zoneId, String userId) {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = Timer.periodic(const Duration(seconds: 40), (timer) {
      // Auto-refresh token 5 seconds before expiration
      add(RefreshQRTokenEvent(zoneId: zoneId, userId: userId));
    });
  }

  void _startSessionTimeoutTimer(QRValidationSessionEntity session) {
    _sessionTimeoutTimer?.cancel();
    final timeUntilExpiry = session.expiresAt.difference(DateTime.now());
    
    if (timeUntilExpiry.isNegative) return;

    _sessionTimeoutTimer = Timer(timeUntilExpiry, () {
      add(CancelQRValidationEvent(sessionId: session.id));
    });
  }

  void _cleanup() {
    _tokenRefreshTimer?.cancel();
    _sessionTimeoutTimer?.cancel();
    _currentSession = null;
  }
}
