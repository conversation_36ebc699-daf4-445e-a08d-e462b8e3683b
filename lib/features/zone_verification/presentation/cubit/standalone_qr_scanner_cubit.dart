import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:respublicaseguridad/core/services/proximity_verification_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/validate_qr_scan_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/core/services/qr_token_service.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'dart:convert'; // Added for jsonDecode, base64 and utf8
import 'package:flutter/foundation.dart'; // Added for debugPrint

// States
abstract class StandaloneQRScannerState extends Equatable {
  const StandaloneQRScannerState();

  @override
  List<Object?> get props => [];
}

class QRScannerInitial extends StandaloneQRScannerState {
  const QRScannerInitial();
}

class QRScannerReady extends StandaloneQRScannerState {
  const QRScannerReady();
}

class QRScannerScanning extends StandaloneQRScannerState {
  const QRScannerScanning();
}

class QRScannerProcessing extends StandaloneQRScannerState {
  final String message;
  
  const QRScannerProcessing({this.message = 'Procesando código QR...'});

  @override
  List<Object?> get props => [message];
}

class QRScannerSuccess extends StandaloneQRScannerState {
  final QRScanValidationResult result;
  
  const QRScannerSuccess(this.result);

  @override
  List<Object?> get props => [result];
}

class QRScannerError extends StandaloneQRScannerState {
  final String message;
  final bool canRetry;
  
  const QRScannerError(this.message, {this.canRetry = true});

  @override
  List<Object?> get props => [message, canRetry];
}

class QRScannerPaused extends StandaloneQRScannerState {
  const QRScannerPaused();
}

// Events
abstract class StandaloneQRScannerEvent extends Equatable {
  const StandaloneQRScannerEvent();

  @override
  List<Object?> get props => [];
}

class InitializeScanner extends StandaloneQRScannerEvent {
  const InitializeScanner();
}

class StartScanning extends StandaloneQRScannerEvent {
  const StartScanning();
}

class StopScanning extends StandaloneQRScannerEvent {
  const StopScanning();
}

class PauseScanning extends StandaloneQRScannerEvent {
  const PauseScanning();
}

class ResumeScanning extends StandaloneQRScannerEvent {
  const ResumeScanning();
}

class ProcessQRCode extends StandaloneQRScannerEvent {
  final String qrData;
  final String scannerUserId;
  
  const ProcessQRCode(this.qrData, this.scannerUserId);

  @override
  List<Object?> get props => [qrData, scannerUserId];
}

class ResetScanner extends StandaloneQRScannerEvent {
  const ResetScanner();
}

class DisposeScanner extends StandaloneQRScannerEvent {
  const DisposeScanner();
}

// Cubit
class StandaloneQRScannerCubit extends Cubit<StandaloneQRScannerState> {
  final ValidateQRScanUseCase _validateScanUseCase;
  MobileScannerController? _controller;
  bool _isDisposed = false;

  StandaloneQRScannerCubit({
    required ValidateQRScanUseCase validateScanUseCase,
  }) : _validateScanUseCase = validateScanUseCase,
       super(const QRScannerInitial());

  void initializeScanner() {
    if (_isDisposed) return;
    
    try {
      _controller?.dispose();
      _controller = MobileScannerController(
        detectionSpeed: DetectionSpeed.noDuplicates,
        facing: CameraFacing.back,
        torchEnabled: false,
      );
      
      if (!_isDisposed) {
        emit(const QRScannerReady());
      }
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al inicializar la cámara: ${e.toString()}'));
      }
    }
  }

  void startScanning() {
    if (_isDisposed || state is QRScannerProcessing) return;
    
    try {
      if (_controller != null && !_isDisposed) {
        emit(const QRScannerScanning());
      }
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al iniciar el escaneo: ${e.toString()}'));
      }
    }
  }

  void stopScanning() {
    if (_isDisposed) return;
    
    try {
      if (!_isDisposed) {
        emit(const QRScannerPaused());
      }
    } catch (e) {
      // Ignore stop errors
    }
  }

  void pauseScanning() {
    if (_isDisposed || state is QRScannerProcessing) return;
    
    try {
      if (!_isDisposed) {
        emit(const QRScannerPaused());
      }
    } catch (e) {
      // Ignore pause errors
    }
  }

  void resumeScanning() {
    if (_isDisposed) return;
    
    try {
      if (!_isDisposed && state is QRScannerPaused) {
        emit(const QRScannerScanning());
      }
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al reanudar el escaneo: ${e.toString()}'));
      }
    }
  }

  Future<void> processQRCode(String qrData, String scannerUserId) async {
    if (_isDisposed || state is QRScannerProcessing) return;

    emit(const QRScannerProcessing(message: 'Validando código QR...'));

    try {
      debugPrint('Processing QR data: $qrData');

      // Check if this is a social validation QR code (simple format)
      if (_isSocialValidationQR(qrData)) {
        await _processSocialValidationQR(qrData, scannerUserId);
        return;
      }

      // Otherwise, try to process as a token-based QR code (complex format)
      await _processTokenBasedQR(qrData, scannerUserId);

    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al procesar el código QR: ${e.toString()}'));
      }
    }
  }

  /// Check if QR data is in social validation format
  bool _isSocialValidationQR(String qrData) {
    return qrData.startsWith('ZONE_VALIDATION:') ||
           qrData.contains('respublicaseguridad.app/validate/') ||
           qrData.contains('respublicaseguridad.com/validate/');
  }

  /// Process social validation QR code using the new cloud function
  Future<void> _processSocialValidationQR(String qrData, String scannerUserId) async {
    try {
      if (!_isDisposed) {
        emit(const QRScannerProcessing(message: 'Obteniendo ubicación...'));
      }

      // Get current location for proximity verification
      final proximityData = await ProximityVerificationService.instance
          .getCurrentLocationForVerification();

      if (proximityData == null) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'No se pudo obtener la ubicación. Verifica los permisos de ubicación.',
          ));
        }
        return;
      }

      // Validate location quality
      if (!ProximityVerificationService.instance.isLocationDataValid(proximityData)) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'La precisión del GPS es insuficiente. Intenta en un área con mejor señal.',
          ));
        }
        return;
      }

      // Extract zone ID or request ID from social validation QR
      final extractedId = _extractZoneIdFromSocialQR(qrData);
      if (extractedId == null) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'Código QR de validación social inválido.',
          ));
        }
        return;
      }

      if (!_isDisposed) {
        emit(const QRScannerProcessing(message: 'Validando zona...'));
      }

      // Call the social validation cloud function
      final functions = FirebaseFunctions.instance;
      final callable = functions.httpsCallable('processSocialValidation');

      // Determine if we have a zone ID or request ID
      Map<String, dynamic> requestData = {
        'validatorLocation': {
          'latitude': proximityData.latitude,
          'longitude': proximityData.longitude,
          'accuracy': proximityData.accuracy,
        },
      };

      // If it looks like a UUID (zone ID), use zoneId parameter
      // If it looks like a timestamp (request ID), use requestId parameter
      if (qrData.startsWith('ZONE_VALIDATION:')) {
        requestData['zoneId'] = extractedId;
      } else {
        requestData['requestId'] = extractedId;
      }

      final result = await callable.call(requestData);

      if (_isDisposed) return;

      final data = result.data as Map<String, dynamic>;

      if (data['success'] == true) {
        // Get the actual zone ID from the response or use the extracted ID
        final actualZoneId = data['zoneId'] ?? extractedId;

        // Create a success result compatible with the existing UI
        final successResult = QRScanValidationResult.success(
          token: QRTokenEntity.createForZone(
            id: 'social-validation',
            zoneId: actualZoneId,
            userId: scannerUserId,
            encryptedData: '',
            tokenDuration: const Duration(minutes: 1),
          ),
          zoneId: actualZoneId,
          proximityVerified: true,
          metadata: {
            'validationId': data['validationId'],
            'newValidationCount': data['newValidationCount'],
            'isZoneValidated': data['isZoneValidated'],
            'distance': data['distance'],
            'message': data['message'],
          },
        );
        emit(QRScannerSuccess(successResult));
      } else {
        emit(QRScannerError(data['message'] ?? 'Validación social falló'));
      }

    } on FirebaseFunctionsException catch (e) {
      if (!_isDisposed) {
        String errorMessage = 'Error de validación social';

        switch (e.code) {
          case 'permission-denied':
            errorMessage = e.message ?? 'Solo usuarios verificados pueden validar zonas';
            break;
          case 'invalid-argument':
            errorMessage = e.message ?? 'Datos de validación inválidos';
            break;
          case 'not-found':
            errorMessage = 'Zona no encontrada';
            break;
          default:
            errorMessage = e.message ?? 'Error de validación social';
        }

        emit(QRScannerError(errorMessage));
      }
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al procesar validación social: ${e.toString()}'));
      }
    }
  }

  /// Process token-based QR code using the existing system
  Future<void> _processTokenBasedQR(String qrData, String scannerUserId) async {
    try {
      // Extract token ID from QR data
      final tokenId = _extractTokenId(qrData);
      if (tokenId == null) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'Código QR inválido. Asegúrate de escanear un código QR de validación de zona.',
          ));
        }
        return;
      }

      if (!_isDisposed) {
        emit(const QRScannerProcessing(message: 'Obteniendo ubicación...'));
      }

      // Get current location for proximity verification
      final proximityData = await ProximityVerificationService.instance
          .getCurrentLocationForVerification();

      if (proximityData == null) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'No se pudo obtener la ubicación. Verifica los permisos de ubicación.',
          ));
        }
        return;
      }

      // Validate location quality
      if (!ProximityVerificationService.instance.isLocationDataValid(proximityData)) {
        if (!_isDisposed) {
          emit(const QRScannerError(
            'La precisión del GPS es insuficiente. Intenta en un área con mejor señal.',
          ));
        }
        return;
      }

      if (!_isDisposed) {
        emit(const QRScannerProcessing(message: 'Validando zona...'));
      }

      // Validate QR scan using existing system
      final params = ValidateQRScanParams(
        tokenId: tokenId,
        scannerUserId: scannerUserId,
        scannerLocation: proximityData,
      );

      final result = await _validateScanUseCase(params);

      if (_isDisposed) return;

      result.fold(
        (failure) => emit(QRScannerError(failure.message)),
        (scanResult) {
          if (scanResult.isValidScan) {
            emit(QRScannerSuccess(scanResult));
          } else {
            emit(QRScannerError(
              scanResult.errorMessage ?? 'QR scan validation failed',
            ));
          }
        },
      );
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al procesar código QR: ${e.toString()}'));
      }
    }
  }

  void resetScanner() {
    if (_isDisposed) return;
    
    try {
      if (!_isDisposed) {
        emit(const QRScannerReady());
      }
    } catch (e) {
      if (!_isDisposed) {
        emit(QRScannerError('Error al reiniciar el escáner: ${e.toString()}'));
      }
    }
  }
  
  // Safely dispose the scanner controller
  void disposeScanner() {
    if (_isDisposed) return;
    
    try {
      stopScanning();
      _controller?.dispose();
      _controller = null;
    } catch (e) {
      // Ignore disposal errors
    }
  }

  /// Extract zone ID from social validation QR code
  String? _extractZoneIdFromSocialQR(String qrData) {
    try {
      debugPrint('Extracting zone ID from social QR data: $qrData');

      // Format: 'ZONE_VALIDATION:$requestId:${zone.id}'
      if (qrData.startsWith('ZONE_VALIDATION:')) {
        final parts = qrData.split(':');
        if (parts.length >= 3) {
          final zoneId = parts[2];
          debugPrint('Extracted zone ID from ZONE_VALIDATION format: $zoneId');
          return zoneId;
        }
      }

      // Format: URL like 'https://app.respublicaseguridad.com/validate/$requestId'
      // or 'https://respublicaseguridad.app/validate/$requestId'
      if (qrData.contains('/validate/')) {
        final uri = Uri.tryParse(qrData);
        if (uri != null) {
          final pathSegments = uri.pathSegments;
          if (pathSegments.length >= 2 && pathSegments[pathSegments.length - 2] == 'validate') {
            final requestId = pathSegments.last;
            debugPrint('Extracted request ID from URL: $requestId');
            // For URL format, we need to look up the zone ID by request ID
            // For now, return the request ID and handle lookup in cloud function
            return requestId;
          }
        }
      }

      debugPrint('Could not extract zone ID from social QR data');
      return null;
    } catch (e) {
      debugPrint('Error extracting zone ID from social QR: $e');
      return null;
    }
  }

  String? _extractTokenId(String qrData) {
    try {
      debugPrint('Extracting token ID from QR data: ${qrData.length} characters');

      // Handle empty data
      if (qrData.isEmpty) {
        debugPrint('QR data is empty');
        return null;
      }

      // Check for common QR code patterns

      // If it's a plain token ID (UUID-like)
      if (qrData.contains('-') && qrData.length > 30 && qrData.length < 40) {
        debugPrint('Detected UUID-like token ID');
        return qrData;
      }

      // If it's a JSON string
      if (qrData.startsWith('{') && qrData.endsWith('}')) {
        try {
          final decodedJson = jsonDecode(qrData) as Map<String, dynamic>;
          if (decodedJson.containsKey('tokenId')) {
            debugPrint('Extracted token ID from JSON: ${decodedJson['tokenId']}');
            return decodedJson['tokenId'] as String;
          }
        } catch (e) {
          debugPrint('Failed to parse QR data as JSON: $e');
        }
      }

      // If it's a base64 encoded string (complex token format)
      if (qrData.length > 10 && _isBase64(qrData)) {
        debugPrint('Detected possible base64 token data');
        try {
          // First try to decode as base64 URL
          final decoded = utf8.decode(base64Url.decode(qrData));
          if (decoded.startsWith('{') && decoded.endsWith('}')) {
            final tokenData = jsonDecode(decoded) as Map<String, dynamic>;

            // Check if this is the complex token format with encrypted data
            if (tokenData.containsKey('data') && tokenData.containsKey('signature')) {
              debugPrint('Detected complex encrypted token format');

              // Use QR token service to decrypt and validate the token
              try {
                final payload = QRTokenService.instance.validateAndDecryptToken(qrData);
                if (payload != null && payload.containsKey('tokenId')) {
                  final tokenId = payload['tokenId'] as String;
                  debugPrint('Extracted token ID from encrypted payload: $tokenId');
                  return tokenId;
                }
              } catch (decryptError) {
                debugPrint('Failed to decrypt token using QR token service: $decryptError');
              }
            }

            // Fallback: check if tokenId is directly in the JSON
            if (tokenData.containsKey('tokenId')) {
              debugPrint('Extracted token ID from base64 JSON: ${tokenData['tokenId']}');
              return tokenData['tokenId'] as String;
            }
          }
        } catch (e) {
          debugPrint('Failed to decode base64 data: $e');

          // Try regular base64 decode as fallback
          try {
            final decoded = utf8.decode(base64.decode(qrData));
            if (decoded.startsWith('{') && decoded.endsWith('}')) {
              final decodedJson = jsonDecode(decoded) as Map<String, dynamic>;
              if (decodedJson.containsKey('tokenId')) {
                debugPrint('Extracted token ID from regular base64 JSON: ${decodedJson['tokenId']}');
                return decodedJson['tokenId'] as String;
              }
            }
          } catch (e2) {
            debugPrint('Failed to decode with regular base64: $e2');
          }
        }
      }

      // For direct token ID (when the QR data is just the token ID)
      if (qrData.isNotEmpty && qrData.length > 10) {
        debugPrint('Using QR data as direct token ID');
        return qrData;
      }

      debugPrint('Could not extract valid token ID from QR data');
      return null;
    } catch (e) {
      debugPrint('Error extracting token ID: $e');
      return null;
    }
  }
  
  // Helper to check if a string is base64 encoded
  bool _isBase64(String str) {
    try {
      base64.decode(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  MobileScannerController? get controller => _controller;

  @override
  Future<void> close() {
    _isDisposed = true;
    try {
      _controller?.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    return super.close();
  }
}
