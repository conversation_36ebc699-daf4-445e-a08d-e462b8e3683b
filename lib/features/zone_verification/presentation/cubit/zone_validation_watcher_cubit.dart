import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/watch_zone_validation_usecase.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/update_zone_validation_status_usecase.dart';

/// States for zone validation watcher
abstract class ZoneValidationWatcherState extends Equatable {
  const ZoneValidationWatcherState();

  @override
  List<Object?> get props => [];
}

class ZoneValidationWatcherInitial extends ZoneValidationWatcherState {
  const ZoneValidationWatcherInitial();
}

class ZoneValidationWatcherLoading extends ZoneValidationWatcherState {
  const ZoneValidationWatcherLoading();
}

class ZoneValidationWatcherWatching extends ZoneValidationWatcherState {
  final ZoneEntity zone;

  const ZoneValidationWatcherWatching(this.zone);

  @override
  List<Object?> get props => [zone];
}

class ZoneValidationWatcherValidated extends ZoneValidationWatcherState {
  final ZoneEntity zone;
  final String message;

  const ZoneValidationWatcherValidated(this.zone, this.message);

  @override
  List<Object?> get props => [zone, message];
}

class ZoneValidationWatcherError extends ZoneValidationWatcherState {
  final String message;

  const ZoneValidationWatcherError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Cubit for watching zone validation changes and auto-updating status
class ZoneValidationWatcherCubit extends Cubit<ZoneValidationWatcherState> {
  final WatchZoneValidationUseCase _watchZoneUseCase;
  final UpdateZoneValidationStatusUseCase _updateZoneStatusUseCase;
  
  StreamSubscription? _zoneSubscription;

  ZoneValidationWatcherCubit({
    required WatchZoneValidationUseCase watchZoneUseCase,
    required UpdateZoneValidationStatusUseCase updateZoneStatusUseCase,
  }) : _watchZoneUseCase = watchZoneUseCase,
       _updateZoneStatusUseCase = updateZoneStatusUseCase,
       super(const ZoneValidationWatcherInitial());

  /// Start watching a zone for validation changes
  void startWatching(String zoneId) {
    emit(const ZoneValidationWatcherLoading());
    
    _zoneSubscription?.cancel();
    
    _zoneSubscription = _watchZoneUseCase(
      WatchZoneValidationParams(zoneId: zoneId),
    ).listen(
      (result) {
        result.fold(
          (failure) => emit(ZoneValidationWatcherError(failure.message)),
          (zone) => _handleZoneUpdate(zone),
        );
      },
      onError: (error) {
        emit(ZoneValidationWatcherError('Error watching zone: ${error.toString()}'));
      },
    );
  }

  /// Handle zone updates and check if auto-validation is needed
  Future<void> _handleZoneUpdate(ZoneEntity zone) async {
    emit(ZoneValidationWatcherWatching(zone));
    
    // Check if zone should be auto-validated
    if (zone.communityValidationCount >= 3 && zone.validationStatus == ZoneStatus.pending) {
      await _autoValidateZone(zone.id);
    }
  }

  /// Auto-validate zone when it reaches required validations
  Future<void> _autoValidateZone(String zoneId) async {
    try {
      final result = await _updateZoneStatusUseCase(
        UpdateZoneValidationStatusParams(zoneId: zoneId),
      );
      
      result.fold(
        (failure) => emit(ZoneValidationWatcherError(failure.message)),
        (updatedZone) {
          if (updatedZone.validationStatus == ZoneStatus.validated) {
            emit(ZoneValidationWatcherValidated(
              updatedZone,
              '¡Zona validada exitosamente por la comunidad!',
            ));
          }
        },
      );
    } catch (e) {
      emit(ZoneValidationWatcherError('Error auto-validating zone: ${e.toString()}'));
    }
  }

  /// Stop watching zone changes
  void stopWatching() {
    _zoneSubscription?.cancel();
    _zoneSubscription = null;
    emit(const ZoneValidationWatcherInitial());
  }

  @override
  Future<void> close() {
    _zoneSubscription?.cancel();
    return super.close();
  }
}
