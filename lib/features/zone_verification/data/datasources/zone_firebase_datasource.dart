import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/zone_verification/data/models/zone_verification_models.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/validation_entities.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';

/// Firebase data source for zone operations
abstract class ZoneFirebaseDataSource {
  /// Create a new zone
  Future<ZoneModel> createZone(ZoneModel zone);

  /// Get all zones for a specific user
  Future<List<ZoneModel>> getUserZones(String userId);

  /// Get a specific zone by ID
  Future<ZoneModel> getZoneById(String zoneId);

  /// Watch a specific zone for real-time updates
  Stream<ZoneEntity> watchZone(String zoneId);

  /// Update an existing zone
  Future<ZoneModel> updateZone(ZoneModel zone);

  /// Delete a zone
  Future<void> deleteZone(String zoneId);

  /// Get zones within a specific radius of coordinates
  Future<List<ZoneModel>> getZonesNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  });

  /// Add a community validation to a zone
  Future<ZoneModel> addCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  });

  /// Remove a community validation from a zone
  Future<ZoneModel> removeCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  });

  /// Update zone validation status
  Future<ZoneModel> updateZoneValidationStatus({
    required String zoneId,
    required ZoneStatus status,
    String? rejectionReason,
  });

  /// Get validation history for a zone
  Future<List<ValidationModel>> getZoneValidationHistory(String zoneId);

  /// Check if user can validate a specific zone
  Future<bool> canUserValidateZone({
    required String userId,
    required String zoneId,
  });

  /// Get daily validation count for a user
  Future<int> getUserDailyValidationCount(String userId);

  /// Enable automatic validation for a zone
  Future<ZoneModel> enableAutomaticValidation(String zoneId);

  /// Disable automatic validation for a zone
  Future<ZoneModel> disableAutomaticValidation(String zoneId);

  /// Get zones that need automatic validation check
  Future<List<ZoneModel>> getZonesForAutomaticValidation();

  /// Process automatic validation for a zone
  Future<ZoneModel> processAutomaticValidation({
    required String zoneId,
    required String userId,
    required double userLatitude,
    required double userLongitude,
  });

  /// Get zone statistics for a user
  Future<Map<String, dynamic>> getUserZoneStatistics(String userId);

  /// Search zones by name or address
  Future<List<ZoneModel>> searchZones({
    required String query,
    String? userId,
  });

  /// Get zones by type
  Future<List<ZoneModel>> getZonesByType({
    required ZoneType type,
    String? userId,
  });

  /// Get zones by validation status
  Future<List<ZoneModel>> getZonesByStatus({
    required ZoneStatus status,
    String? userId,
  });

  /// Batch update zones
  Future<List<ZoneModel>> batchUpdateZones(List<ZoneModel> zones);

  /// Get zone validation requirements
  Future<Map<String, dynamic>> getZoneValidationRequirements(String zoneId);

  /// Check if location is within any user zone
  Future<ZoneModel?> getZoneAtLocation({
    required String userId,
    required double latitude,
    required double longitude,
  });

  /// Get zones that are pending validation
  Future<List<ZoneModel>> getPendingValidationZones({String? userId});

  /// Get zones that are validated
  Future<List<ZoneModel>> getValidatedZones({String? userId});

  /// Get community validation requests for a user
  Future<List<ZoneModel>> getCommunityValidationRequests(String userId);

  /// Stream of zone updates for real-time UI updates
  Stream<List<ZoneModel>> watchUserZones(String userId);

  /// Stream of zone validation updates
  Stream<ZoneModel> watchZoneValidation(String zoneId);

  // Enhanced Automatic Validation Methods

  /// Get zones with automatic validation enabled for a user
  Future<List<ZoneModel>> getAutomaticValidationZones(String userId);

  /// Update presence hours configuration for a zone
  Future<ZoneModel> updatePresenceHours({
    required String zoneId,
    required PresenceHoursConfiguration presenceHours,
  });

  /// Record geofence event for a zone
  Future<void> recordGeofenceEvent({
    required String zoneId,
    required String userId,
    required GeofenceEventType eventType,
    required DateTime timestamp,
    required double latitude,
    required double longitude,
    required double accuracy,
  });

  /// Get geofence events for a zone
  Future<List<GeofenceEventRecord>> getGeofenceEvents({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });

  /// Update automatic validation progress
  Future<ZoneModel> updateValidationProgress({
    required String zoneId,
    required Map<String, dynamic> progressData,
  });

  /// Get zones that require presence validation at current time
  Future<List<ZoneModel>> getActivePresenceZones({
    required String userId,
    DateTime? currentTime,
  });

  /// Check if zone validation criteria are met
  Future<ValidationCriteriaResult> checkValidationCriteria({
    required String zoneId,
    required String userId,
  });

  /// Complete automatic validation for a zone
  Future<ZoneModel> completeAutomaticValidation({
    required String zoneId,
    required String userId,
    required Map<String, dynamic> validationData,
  });

  /// Get validation analytics for a zone
  Future<Map<String, dynamic>> getZoneValidationAnalytics({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Batch process automatic validation for multiple zones
  Future<List<ZoneModel>> batchProcessAutomaticValidation({
    required List<String> zoneIds,
    required String userId,
    required double latitude,
    required double longitude,
  });
}

/// Implementation of ZoneFirebaseDataSource
class ZoneFirebaseDataSourceImpl implements ZoneFirebaseDataSource {
  final FirebaseFirestore firestore;

  ZoneFirebaseDataSourceImpl({required this.firestore});

  @override
  Future<ZoneModel> createZone(ZoneModel zone) async {
    try {
      final docRef = await firestore
          .collection(ZoneConstants.zonesCollection)
          .add(zone.toFirestore());

      final doc = await docRef.get();
      return ZoneModel.fromFirestore(doc);
    } catch (e) {
      throw ServerException('Failed to create zone: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getUserZones(String userId) async {
    try {
      final querySnapshot = await firestore
          .collection(ZoneConstants.zonesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get user zones: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> getZoneById(String zoneId) async {
    try {
      final doc = await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .get();

      if (!doc.exists) {
        throw NotFoundException('Zone not found');
      }

      return ZoneModel.fromFirestore(doc);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to get zone: ${e.toString()}');
    }
  }

  @override
  Stream<ZoneEntity> watchZone(String zoneId) {
    try {
      return firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .snapshots()
          .map((doc) {
        if (!doc.exists) {
          throw NotFoundException('Zone not found');
        }
        return ZoneModel.fromFirestore(doc);
      });
    } catch (e) {
      throw ServerException('Failed to watch zone: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> updateZone(ZoneModel zone) async {
    try {
      await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zone.id)
          .update(zone.toFirestore());

      return await getZoneById(zone.id);
    } catch (e) {
      throw ServerException('Failed to update zone: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteZone(String zoneId) async {
    try {
      await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .delete();
    } catch (e) {
      throw ServerException('Failed to delete zone: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getZonesNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  }) async {
    try {
      // This is a simplified implementation
      // In production, you would use GeoFirestore or similar for efficient geo queries
      final querySnapshot = await firestore
          .collection(ZoneConstants.zonesCollection)
          .get();

      final zones = querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .where((zone) {
            return ZoneValidationRules.isLocationInZone(
              userLat: latitude,
              userLng: longitude,
              zoneLat: zone.centerCoordinates.latitude,
              zoneLng: zone.centerCoordinates.longitude,
            );
          })
          .toList();

      return zones;
    } catch (e) {
      throw ServerException('Failed to get zones near location: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> addCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  }) async {
    try {
      final zoneRef = firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId);

      await firestore.runTransaction((transaction) async {
        final zoneDoc = await transaction.get(zoneRef);
        
        if (!zoneDoc.exists) {
          throw NotFoundException('Zone not found');
        }

        final zone = ZoneModel.fromFirestore(zoneDoc);
        
        if (zone.communityValidatedBy.contains(validatorUserId)) {
          throw ValidationException('User has already validated this zone');
        }

        final updatedValidatedBy = List<String>.from(zone.communityValidatedBy)
          ..add(validatorUserId);

        transaction.update(zoneRef, {
          'communityValidatedBy': updatedValidatedBy,
          'communityValidationCount': zone.communityValidationCount + 1,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Create validation record
        await _createValidationRecord(
          zoneId: zoneId,
          validatorUserId: validatorUserId,
          type: ValidationType.community,
        );
      });

      return await getZoneById(zoneId);
    } catch (e) {
      if (e is NotFoundException || e is ValidationException) rethrow;
      throw ServerException('Failed to add community validation: ${e.toString()}');
    }
  }

  /// Create a validation record
  Future<void> _createValidationRecord({
    required String zoneId,
    required String validatorUserId,
    required ValidationType type,
    String? note,
  }) async {
    final validationData = {
      'validatedEntityId': zoneId,
      'entityType': ValidationEntityType.zone.name,
      'type': type.name,
      'validatorUserId': validatorUserId,
      'validatedAt': FieldValue.serverTimestamp(),
      'validationNote': note,
    };

    await firestore
        .collection(ZoneConstants.zoneValidationsCollection)
        .add(validationData);
  }

  @override
  Future<ZoneModel> removeCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  }) async {
    try {
      final zoneRef = firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId);

      await firestore.runTransaction((transaction) async {
        final zoneDoc = await transaction.get(zoneRef);
        
        if (!zoneDoc.exists) {
          throw NotFoundException('Zone not found');
        }

        final zone = ZoneModel.fromFirestore(zoneDoc);
        
        if (!zone.communityValidatedBy.contains(validatorUserId)) {
          throw ValidationException('User has not validated this zone');
        }

        final updatedValidatedBy = List<String>.from(zone.communityValidatedBy)
          ..remove(validatorUserId);

        transaction.update(zoneRef, {
          'communityValidatedBy': updatedValidatedBy,
          'communityValidationCount': zone.communityValidationCount - 1,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      return await getZoneById(zoneId);
    } catch (e) {
      if (e is NotFoundException || e is ValidationException) rethrow;
      throw ServerException('Failed to remove community validation: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> updateZoneValidationStatus({
    required String zoneId,
    required ZoneStatus status,
    String? rejectionReason,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'validationStatus': status.name,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (status == ZoneStatus.validated) {
        updateData['validatedAt'] = FieldValue.serverTimestamp();
      }

      if (status == ZoneStatus.rejected && rejectionReason != null) {
        updateData['rejectionReason'] = rejectionReason;
      }

      await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .update(updateData);

      return await getZoneById(zoneId);
    } catch (e) {
      throw ServerException('Failed to update zone validation status: ${e.toString()}');
    }
  }

  @override
  Future<List<ValidationModel>> getZoneValidationHistory(String zoneId) async {
    try {
      final querySnapshot = await firestore
          .collection(ZoneConstants.zoneValidationsCollection)
          .where('validatedEntityId', isEqualTo: zoneId)
          .orderBy('validatedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ValidationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get zone validation history: ${e.toString()}');
    }
  }

  @override
  Future<bool> canUserValidateZone({
    required String userId,
    required String zoneId,
  }) async {
    try {
      // Get the zone
      final zone = await getZoneById(zoneId);
      
      // Check basic validation rules
      if (zone.userId == userId) return false; // Can't validate own zone
      if (zone.validationStatus == ZoneStatus.validated) return false; // Already validated
      if (zone.validationStatus == ZoneStatus.rejected) return false; // Rejected
      if (zone.communityValidatedBy.contains(userId)) return false; // Already validated by user
      if (zone.validationMethod != ValidationMethod.social) return false; // Not social validation

      // Check daily validation limit
      final dailyCount = await getUserDailyValidationCount(userId);
      if (dailyCount >= ZoneConstants.maxCommunityValidationsPerUser) return false;

      // TODO: Check if user is validated (requires user validation status)
      // This would require integration with the identity verification system

      return true;
    } catch (e) {
      throw ServerException('Failed to check if user can validate zone: ${e.toString()}');
    }
  }

  @override
  Future<int> getUserDailyValidationCount(String userId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final querySnapshot = await firestore
          .collection(ZoneConstants.zoneValidationsCollection)
          .where('validatorUserId', isEqualTo: userId)
          .where('validatedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('validatedAt', isLessThan: Timestamp.fromDate(endOfDay))
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      throw ServerException('Failed to get user daily validation count: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> enableAutomaticValidation(String zoneId) async {
    try {
      await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .update({
        'validationMethod': ValidationMethod.automatic.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return await getZoneById(zoneId);
    } catch (e) {
      throw ServerException('Failed to enable automatic validation: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> disableAutomaticValidation(String zoneId) async {
    try {
      await firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .update({
        'validationMethod': ValidationMethod.social.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return await getZoneById(zoneId);
    } catch (e) {
      throw ServerException('Failed to disable automatic validation: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getZonesForAutomaticValidation() async {
    try {
      final querySnapshot = await firestore
          .collection(ZoneConstants.zonesCollection)
          .where('validationMethod', isEqualTo: ValidationMethod.automatic.name)
          .where('validationStatus', isEqualTo: ZoneStatus.pending.name)
          .get();

      return querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get zones for automatic validation: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> processAutomaticValidation({
    required String zoneId,
    required String userId,
    required double userLatitude,
    required double userLongitude,
  }) async {
    try {
      final zone = await getZoneById(zoneId);
      
      // Check if user is within zone
      final isInZone = ZoneValidationRules.isLocationInZone(
        userLat: userLatitude,
        userLng: userLongitude,
        zoneLat: zone.centerCoordinates.latitude,
        zoneLng: zone.centerCoordinates.longitude,
      );

      if (!isInZone) {
        throw ValidationException('User is not within zone boundaries');
      }

      // TODO: Implement automatic validation logic
      // This would involve checking presence patterns, time spent in zone, etc.
      
      return zone;
    } catch (e) {
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to process automatic validation: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserZoneStatistics(String userId) async {
    try {
      final zones = await getUserZones(userId);
      
      final stats = <String, dynamic>{
        'totalZones': zones.length,
        'validatedZones': zones.where((z) => z.validationStatus == ZoneStatus.validated).length,
        'pendingZones': zones.where((z) => z.validationStatus == ZoneStatus.pending).length,
        'rejectedZones': zones.where((z) => z.validationStatus == ZoneStatus.rejected).length,
        'socialValidationZones': zones.where((z) => z.validationMethod == ValidationMethod.social).length,
        'automaticValidationZones': zones.where((z) => z.validationMethod == ValidationMethod.automatic).length,
        'zonesByType': <String, int>{},
      };

      // Count zones by type
      for (final type in ZoneType.values) {
        stats['zonesByType'][type.name] = zones.where((z) => z.type == type).length;
      }

      return stats;
    } catch (e) {
      throw ServerException('Failed to get user zone statistics: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> searchZones({
    required String query,
    String? userId,
  }) async {
    try {
      Query<Map<String, dynamic>> firestoreQuery = firestore
          .collection(ZoneConstants.zonesCollection);

      if (userId != null) {
        firestoreQuery = firestoreQuery.where('userId', isEqualTo: userId);
      }

      final querySnapshot = await firestoreQuery.get();

      // Filter by name or address (Firestore doesn't support full-text search natively)
      final zones = querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .where((zone) {
            final lowerQuery = query.toLowerCase();
            return zone.name.toLowerCase().contains(lowerQuery) ||
                   zone.address.toLowerCase().contains(lowerQuery);
          })
          .toList();

      return zones;
    } catch (e) {
      throw ServerException('Failed to search zones: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getZonesByType({
    required ZoneType type,
    String? userId,
  }) async {
    try {
      Query<Map<String, dynamic>> query = firestore
          .collection(ZoneConstants.zonesCollection)
          .where('type', isEqualTo: type.name);

      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get zones by type: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getZonesByStatus({
    required ZoneStatus status,
    String? userId,
  }) async {
    try {
      Query<Map<String, dynamic>> query = firestore
          .collection(ZoneConstants.zonesCollection)
          .where('validationStatus', isEqualTo: status.name);

      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get zones by status: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> batchUpdateZones(List<ZoneModel> zones) async {
    try {
      final batch = firestore.batch();

      for (final zone in zones) {
        final docRef = firestore
            .collection(ZoneConstants.zonesCollection)
            .doc(zone.id);
        batch.update(docRef, zone.toFirestore());
      }

      await batch.commit();

      // Return updated zones
      final updatedZones = <ZoneModel>[];
      for (final zone in zones) {
        updatedZones.add(await getZoneById(zone.id));
      }

      return updatedZones;
    } catch (e) {
      throw ServerException('Failed to batch update zones: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getZoneValidationRequirements(String zoneId) async {
    try {
      final zone = await getZoneById(zoneId);
      
      return {
        'requiredValidations': ZoneConstants.requiredCommunityValidations,
        'currentValidations': zone.communityValidationCount,
        'remainingValidations': (ZoneConstants.requiredCommunityValidations - zone.communityValidationCount).clamp(0, ZoneConstants.requiredCommunityValidations),
        'validationMethod': zone.validationMethod.name,
        'validationStatus': zone.validationStatus.name,
        'canRequestSocialValidation': zone.validationMethod == ValidationMethod.social && zone.validationStatus == ZoneStatus.pending,
        'canEnableAutomaticValidation': zone.validationStatus == ZoneStatus.pending,
      };
    } catch (e) {
      throw ServerException('Failed to get zone validation requirements: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel?> getZoneAtLocation({
    required String userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final userZones = await getUserZones(userId);
      
      for (final zone in userZones) {
        final isInZone = ZoneValidationRules.isLocationInZone(
          userLat: latitude,
          userLng: longitude,
          zoneLat: zone.centerCoordinates.latitude,
          zoneLng: zone.centerCoordinates.longitude,
        );
        
        if (isInZone) {
          return zone;
        }
      }
      
      return null;
    } catch (e) {
      throw ServerException('Failed to get zone at location: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getPendingValidationZones({String? userId}) async {
    return await getZonesByStatus(status: ZoneStatus.pending, userId: userId);
  }

  @override
  Future<List<ZoneModel>> getValidatedZones({String? userId}) async {
    return await getZonesByStatus(status: ZoneStatus.validated, userId: userId);
  }

  @override
  Future<List<ZoneModel>> getCommunityValidationRequests(String userId) async {
    try {
      // Get zones that need community validation (excluding user's own zones)
      final querySnapshot = await firestore
          .collection(ZoneConstants.zonesCollection)
          .where('validationMethod', isEqualTo: ValidationMethod.social.name)
          .where('validationStatus', isEqualTo: ZoneStatus.pending.name)
          .get();

      final zones = querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .where((zone) => 
              zone.userId != userId && // Not user's own zone
              !zone.communityValidatedBy.contains(userId) && // User hasn't validated it
              zone.communityValidationCount < ZoneConstants.requiredCommunityValidations // Still needs validations
          )
          .toList();

      return zones;
    } catch (e) {
      throw ServerException('Failed to get community validation requests: ${e.toString()}');
    }
  }

  @override
  Stream<List<ZoneModel>> watchUserZones(String userId) {
    try {
      return firestore
          .collection(ZoneConstants.zonesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => ZoneModel.fromFirestore(doc))
              .toList());
    } catch (e) {
      throw ServerException('Failed to watch user zones: ${e.toString()}');
    }
  }

  @override
  Stream<ZoneModel> watchZoneValidation(String zoneId) {
    try {
      return firestore
          .collection(ZoneConstants.zonesCollection)
          .doc(zoneId)
          .snapshots()
          .map((doc) {
            if (!doc.exists) {
              throw NotFoundException('Zone not found');
            }
            return ZoneModel.fromFirestore(doc);
          });
    } catch (e) {
      throw ServerException('Failed to watch zone validation: ${e.toString()}');
    }
  }

  // Enhanced Automatic Validation Methods Implementation

  @override
  Future<List<ZoneModel>> getAutomaticValidationZones(String userId) async {
    try {
      final querySnapshot = await firestore
          .collection('zones')
          .where('createdBy', isEqualTo: userId)
          .where('automaticValidationEnabled', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get automatic validation zones: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> updatePresenceHours({
    required String zoneId,
    required PresenceHoursConfiguration presenceHours,
  }) async {
    try {
      final docRef = firestore.collection('zones').doc(zoneId);

      await docRef.update({
        'presenceHoursConfig': presenceHours.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await docRef.get();
      if (!updatedDoc.exists) {
        throw NotFoundException('Zone not found');
      }

      return ZoneModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException('Failed to update presence hours: ${e.toString()}');
    }
  }

  @override
  Future<void> recordGeofenceEvent({
    required String zoneId,
    required String userId,
    required GeofenceEventType eventType,
    required DateTime timestamp,
    required double latitude,
    required double longitude,
    required double accuracy,
  }) async {
    try {
      final eventData = {
        'zoneId': zoneId,
        'userId': userId,
        'eventType': eventType.name,
        'timestamp': Timestamp.fromDate(timestamp),
        'latitude': latitude,
        'longitude': longitude,
        'accuracy': accuracy,
        'createdAt': FieldValue.serverTimestamp(),
      };

      await firestore.collection('geofence_events').add(eventData);
    } catch (e) {
      throw ServerException('Failed to record geofence event: ${e.toString()}');
    }
  }

  @override
  Future<List<GeofenceEventRecord>> getGeofenceEvents({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      Query query = firestore
          .collection('geofence_events')
          .where('zoneId', isEqualTo: zoneId)
          .orderBy('timestamp', descending: true);

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return GeofenceEventRecord.fromMap({
          'id': doc.id,
          ...data,
          'timestamp': (data['timestamp'] as Timestamp).toDate().toIso8601String(),
        });
      }).toList();
    } catch (e) {
      throw ServerException('Failed to get geofence events: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> updateValidationProgress({
    required String zoneId,
    required Map<String, dynamic> progressData,
  }) async {
    try {
      final docRef = firestore.collection('zones').doc(zoneId);

      await docRef.update({
        'validationProgress': progressData,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await docRef.get();
      if (!updatedDoc.exists) {
        throw NotFoundException('Zone not found');
      }

      return ZoneModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException('Failed to update validation progress: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> getActivePresenceZones({
    required String userId,
    DateTime? currentTime,
  }) async {
    try {
      final now = currentTime ?? DateTime.now();
      final currentHour = now.hour;
      final currentDay = now.weekday;

      final querySnapshot = await firestore
          .collection('zones')
          .where('createdBy', isEqualTo: userId)
          .where('automaticValidationEnabled', isEqualTo: true)
          .get();

      final zones = querySnapshot.docs
          .map((doc) => ZoneModel.fromFirestore(doc))
          .where((zone) {
            // Check if zone has presence hours configured
            if (zone.presenceHoursConfig == null) return false;

            // Check if current time falls within presence hours
            // This is a simplified check - full implementation would be more complex
            return true; // TODO: Implement proper presence hours checking
          })
          .toList();

      return zones;
    } catch (e) {
      throw ServerException('Failed to get active presence zones: ${e.toString()}');
    }
  }

  @override
  Future<ValidationCriteriaResult> checkValidationCriteria({
    required String zoneId,
    required String userId,
  }) async {
    try {
      // Get zone information
      final zoneDoc = await firestore.collection('zones').doc(zoneId).get();
      if (!zoneDoc.exists) {
        throw NotFoundException('Zone not found');
      }

      // Get geofence events for this user and zone
      final eventsQuery = await firestore
          .collection('geofence_events')
          .where('zoneId', isEqualTo: zoneId)
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(100)
          .get();

      final events = eventsQuery.docs.map((doc) {
        final data = doc.data();
        return GeofenceEventRecord.fromMap({
          'id': doc.id,
          ...data,
          'timestamp': (data['timestamp'] as Timestamp).toDate().toIso8601String(),
        });
      }).toList();

      // Calculate validation criteria
      final criteria = <String, ValidationCriterion>{};

      // Example criteria - presence time
      final totalPresenceTime = _calculatePresenceTime(events);
      criteria['presence_time'] = ValidationCriterion.presenceTime(
        currentTime: totalPresenceTime,
        requiredTime: const Duration(hours: 24), // Example requirement
      );

      // Check if all criteria are met
      final allMet = criteria.values.every((criterion) => criterion.isMet);

      return ValidationCriteriaResult(
        zoneId: zoneId,
        userId: userId,
        criteriaMetOverall: allMet,
        criteria: criteria,
        checkedAt: DateTime.now(),
      );
    } catch (e) {
      throw ServerException('Failed to check validation criteria: ${e.toString()}');
    }
  }

  @override
  Future<ZoneModel> completeAutomaticValidation({
    required String zoneId,
    required String userId,
    required Map<String, dynamic> validationData,
  }) async {
    try {
      final docRef = firestore.collection('zones').doc(zoneId);

      await docRef.update({
        'automaticValidationCompleted': true,
        'automaticValidationCompletedAt': FieldValue.serverTimestamp(),
        'automaticValidationData': validationData,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await docRef.get();
      if (!updatedDoc.exists) {
        throw NotFoundException('Zone not found');
      }

      return ZoneModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException('Failed to complete automatic validation: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getZoneValidationAnalytics({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get geofence events for analytics
      Query query = firestore
          .collection('geofence_events')
          .where('zoneId', isEqualTo: zoneId);

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final eventsSnapshot = await query.get();
      final events = eventsSnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return GeofenceEventRecord.fromMap({
          'id': doc.id,
          ...data,
          'timestamp': (data['timestamp'] as Timestamp).toDate().toIso8601String(),
        });
      }).toList();

      // Calculate analytics
      final analytics = {
        'totalEvents': events.length,
        'entryEvents': events.where((e) => e.isEntry).length,
        'exitEvents': events.where((e) => e.isExit).length,
        'uniqueUsers': events.map((e) => e.userId).toSet().length,
        'averageAccuracy': events.isEmpty ? 0.0 : events.map((e) => e.accuracy).reduce((a, b) => a + b) / events.length,
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
      };

      return analytics;
    } catch (e) {
      throw ServerException('Failed to get zone validation analytics: ${e.toString()}');
    }
  }

  @override
  Future<List<ZoneModel>> batchProcessAutomaticValidation({
    required List<String> zoneIds,
    required String userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final results = <ZoneModel>[];

      for (final zoneId in zoneIds) {
        try {
          final zone = await processAutomaticValidation(
            zoneId: zoneId,
            userId: userId,
            userLatitude: latitude,
            userLongitude: longitude,
          );
          results.add(zone);
        } catch (e) {
          // Continue processing other zones even if one fails
          print('Failed to process zone $zoneId: $e');
        }
      }

      return results;
    } catch (e) {
      throw ServerException('Failed to batch process automatic validation: ${e.toString()}');
    }
  }

  /// Helper method to calculate total presence time from events
  Duration _calculatePresenceTime(List<GeofenceEventRecord> events) {
    Duration total = Duration.zero;
    DateTime? entryTime;

    for (final event in events) {
      if (event.isEntry) {
        entryTime = event.timestamp;
      } else if (event.isExit && entryTime != null) {
        total += event.timestamp.difference(entryTime);
        entryTime = null;
      }
    }

    // If still in zone, add current time
    if (entryTime != null) {
      total += DateTime.now().difference(entryTime);
    }

    return total;
  }
}

/// Custom exceptions
class ServerException implements Exception {
  final String message;
  ServerException(this.message);

  @override
  String toString() => 'ServerException: $message';
}

class ValidationException implements Exception {
  final String message;
  ValidationException(this.message);

  @override
  String toString() => 'ValidationException: $message';
}

class NotFoundException implements Exception {
  final String message;
  NotFoundException(this.message);

  @override
  String toString() => 'NotFoundException: $message';
}
