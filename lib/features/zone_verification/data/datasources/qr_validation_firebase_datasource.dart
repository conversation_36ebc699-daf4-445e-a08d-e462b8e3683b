import 'package:firebase_database/firebase_database.dart';
import 'package:respublicaseguridad/features/zone_verification/data/models/qr_validation_models.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_constants.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:flutter/foundation.dart';

/// Abstract data source for QR validation operations
abstract class QRValidationFirebaseDataSource {
  
  // Session Management
  Future<QRValidationSessionModel> createValidationSession(QRValidationSessionModel session);
  Future<QRValidationSessionModel> getValidationSession(String sessionId);
  Future<QRValidationSessionModel> updateValidationSession(QRValidationSessionModel session);
  Future<List<QRValidationSessionModel>> getActiveSessionsForUser(String userId);
  Future<int> getUserDailyValidationCount(String userId);
  Stream<QRValidationSessionModel> watchValidationSession(String sessionId);

  // Token Management
  Future<QRTokenModel> generateQRToken(QRTokenModel token);
  Future<QRTokenModel> getQRToken(String tokenId);
  Future<QRTokenModel> updateQRToken(QRTokenModel token);
  Future<List<QRTokenModel>> getActiveTokensForSession(String sessionId);
  Stream<QRTokenModel> watchQRToken(String tokenId);

  // Proximity Verification
  Future<void> recordProximityVerification({
    required String sessionId,
    required ProximityDataModel initiatorLocation,
    required ProximityDataModel validatorLocation,
    required bool isVerified,
  });
  Future<bool> getProximityVerificationStatus(String sessionId);

  // Event Logging
  Future<void> logValidationEvent({
    required String sessionId,
    required String eventType,
    required Map<String, dynamic> eventData,
  });
  Future<List<Map<String, dynamic>>> getValidationEvents(String sessionId);

  // Statistics and Cleanup
  Future<void> updateUserValidationStats(String userId, Map<String, dynamic> stats);
  Future<Map<String, dynamic>> getUserValidationStats(String userId);
  Future<void> cleanupExpiredData();
}

/// Implementation of QR validation data source using Firebase Realtime Database
class QRValidationFirebaseDataSourceImpl implements QRValidationFirebaseDataSource {
  final FirebaseDatabase _database;

  QRValidationFirebaseDataSourceImpl(this._database);

  @override
  Future<QRValidationSessionModel> createValidationSession(QRValidationSessionModel session) async {
    try {
      final ref = _database.ref(ZoneConstants.qrSessionsPath).child(session.id);
      await ref.set(session.toRealtimeDB());
      
      // Update user stats
      await _incrementUserActiveSessionsCount(session.initiatorUserId);
      
      return session;
    } catch (e) {
      throw Exception('Failed to create validation session: $e');
    }
  }

  @override
  Future<QRValidationSessionModel> getValidationSession(String sessionId) async {
    try {
      // This method is deprecated - sessions are no longer used
      // Keeping for backward compatibility but will always throw
      throw Exception('Validation sessions are no longer supported - use zone-based validation');
    } catch (e) {
      throw Exception('Failed to get validation session: $e');
    }
  }

  @override
  Future<QRValidationSessionModel> updateValidationSession(QRValidationSessionModel session) async {
    try {
      final ref = _database.ref(ZoneConstants.qrSessionsPath).child(session.id);
      await ref.update(session.toRealtimeDB());
      return session;
    } catch (e) {
      throw Exception('Failed to update validation session: $e');
    }
  }

  @override
  Future<List<QRValidationSessionModel>> getActiveSessionsForUser(String userId) async {
    try {
      final ref = _database.ref(ZoneConstants.qrSessionsPath);
      final query = ref.orderByChild('initiatorUserId').equalTo(userId);
      final snapshot = await query.get();
      
      final sessions = <QRValidationSessionModel>[];
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        for (final entry in data.entries) {
          final sessionData = Map<String, dynamic>.from(entry.value as Map);
          final session = QRValidationSessionModel.fromRealtimeDB(sessionData);
          
          // Only include active sessions
          if (session.status == QRValidationStatus.pending || 
              session.status == QRValidationStatus.active) {
            sessions.add(session);
          }
        }
      }
      
      // Also check sessions where user is validator
      final validatorQuery = ref.orderByChild('validatorUserId').equalTo(userId);
      final validatorSnapshot = await validatorQuery.get();
      
      if (validatorSnapshot.exists) {
        final data = Map<String, dynamic>.from(validatorSnapshot.value as Map);
        for (final entry in data.entries) {
          final sessionData = Map<String, dynamic>.from(entry.value as Map);
          final session = QRValidationSessionModel.fromRealtimeDB(sessionData);
          
          // Only include active sessions and avoid duplicates
          if ((session.status == QRValidationStatus.pending || 
               session.status == QRValidationStatus.active) &&
              !sessions.any((s) => s.id == session.id)) {
            sessions.add(session);
          }
        }
      }
      
      return sessions;
    } catch (e) {
      throw Exception('Failed to get active sessions for user: $e');
    }
  }

  @override
  Future<int> getUserDailyValidationCount(String userId) async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD format
      final ref = _database.ref('user_validation_stats').child(userId);
      final snapshot = await ref.get();
      
      if (!snapshot.exists) {
        return 0;
      }
      
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final lastValidationDate = data['lastValidationDate'] as String?;
      
      if (lastValidationDate == today) {
        return (data['dailyValidationCount'] as num?)?.toInt() ?? 0;
      }
      
      return 0; // Reset count for new day
    } catch (e) {
      throw Exception('Failed to get user daily validation count: $e');
    }
  }

  @override
  Stream<QRValidationSessionModel> watchValidationSession(String sessionId) {
    final ref = _database.ref(ZoneConstants.qrSessionsPath).child(sessionId);
    
    return ref.onValue.map((event) {
      if (!event.snapshot.exists) {
        throw Exception('Validation session not found');
      }
      
      final data = Map<String, dynamic>.from(event.snapshot.value as Map);
      return QRValidationSessionModel.fromRealtimeDB(data);
    });
  }

  @override
  Future<QRTokenModel> generateQRToken(QRTokenModel token) async {
    try {
      final ref = _database.ref(ZoneConstants.qrTokensPath).child(token.id);
      await ref.set(token.toRealtimeDB());
      return token;
    } catch (e) {
      throw Exception('Failed to generate QR token: $e');
    }
  }

  @override
  Future<QRTokenModel> getQRToken(String tokenId) async {
    try {
      debugPrint('QR token Chelsea : ${tokenId}');
      
      // Validate token ID
      if (tokenId.isEmpty || tokenId.length < 10) {
        throw Exception('Invalid QR token ID format');
      }
      
      final ref = _database.ref(ZoneConstants.qrTokensPath).child(tokenId);
      final snapshot = await ref.get();
      
      debugPrint('QR token snapshot: ${snapshot.value}');

      if (!snapshot.exists) {
        throw Exception('QR token not found: $tokenId');
      }
      
      try {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return QRTokenModel.fromRealtimeDB(data);
      } catch (parseError) {
        debugPrint('Error parsing QR token data: $parseError');
        throw Exception('Invalid QR token data format: $parseError');
      }
    } catch (e) {
      debugPrint('Error retrieving QR token: $e');
      throw Exception('Failed to get QR token: $e');
    }
  }

  @override
  Future<QRTokenModel> updateQRToken(QRTokenModel token) async {
    try {
      final ref = _database.ref(ZoneConstants.qrTokensPath).child(token.id);
      await ref.update(token.toRealtimeDB());
      return token;
    } catch (e) {
      throw Exception('Failed to update QR token: $e');
    }
  }

  @override
  Future<List<QRTokenModel>> getActiveTokensForSession(String sessionId) async {
    try {
      final ref = _database.ref(ZoneConstants.qrTokensPath);
      final query = ref.orderByChild('sessionId').equalTo(sessionId);
      final snapshot = await query.get();
      
      final tokens = <QRTokenModel>[];
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        for (final entry in data.entries) {
          final tokenData = Map<String, dynamic>.from(entry.value as Map);
          final token = QRTokenModel.fromRealtimeDB(tokenData);
          tokens.add(token);
        }
      }
      
      return tokens;
    } catch (e) {
      throw Exception('Failed to get active tokens for session: $e');
    }
  }

  @override
  Stream<QRTokenModel> watchQRToken(String tokenId) {
    debugPrint('Watching QR token: $tokenId');
    final ref = _database.ref(ZoneConstants.qrTokensPath).child(tokenId);

    
    
    return ref.onValue.map((event) {
      debugPrint('QR token watch event: ${event.snapshot.value}');
      if (!event.snapshot.exists) {
        throw Exception('QR token not found');
      }
      
      final data = Map<String, dynamic>.from(event.snapshot.value as Map);
      return QRTokenModel.fromRealtimeDB(data);
    });
  }

  @override
  Future<void> recordProximityVerification({
    required String sessionId,
    required ProximityDataModel initiatorLocation,
    required ProximityDataModel validatorLocation,
    required bool isVerified,
  }) async {
    try {
      final ref = _database.ref('proximity_verifications').child(sessionId);
      await ref.set({
        'sessionId': sessionId,
        'isVerified': isVerified,
        'timestamp': DateTime.now().toIso8601String(),
        'initiatorLocation': initiatorLocation.toRealtimeDB(),
        'validatorLocation': validatorLocation.toRealtimeDB(),
      });
    } catch (e) {
      throw Exception('Failed to record proximity verification: $e');
    }
  }

  @override
  Future<bool> getProximityVerificationStatus(String sessionId) async {
    try {
      final ref = _database.ref('proximity_verifications').child(sessionId);
      final snapshot = await ref.get();
      
      if (!snapshot.exists) {
        return false;
      }
      
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      return data['isVerified'] as bool? ?? false;
    } catch (e) {
      throw Exception('Failed to get proximity verification status: $e');
    }
  }

  @override
  Future<void> logValidationEvent({
    required String sessionId,
    required String eventType,
    required Map<String, dynamic> eventData,
  }) async {
    try {
      final ref = _database.ref(ZoneConstants.qrEventsPath).child(sessionId).push();
      await ref.set({
        'eventType': eventType,
        'timestamp': DateTime.now().toIso8601String(),
        'eventData': eventData,
      });
    } catch (e) {
      throw Exception('Failed to log validation event: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getValidationEvents(String sessionId) async {
    try {
      final ref = _database.ref(ZoneConstants.qrEventsPath).child(sessionId);
      final snapshot = await ref.get();
      
      final events = <Map<String, dynamic>>[];
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        for (final entry in data.entries) {
          final eventData = Map<String, dynamic>.from(entry.value as Map);
          events.add(eventData);
        }
      }
      
      // Sort by timestamp
      events.sort((a, b) => (a['timestamp'] as String).compareTo(b['timestamp'] as String));
      
      return events;
    } catch (e) {
      throw Exception('Failed to get validation events: $e');
    }
  }

  @override
  Future<void> updateUserValidationStats(String userId, Map<String, dynamic> stats) async {
    try {
      final ref = _database.ref('user_validation_stats').child(userId);
      await ref.update(stats);
    } catch (e) {
      throw Exception('Failed to update user validation stats: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserValidationStats(String userId) async {
    try {
      final ref = _database.ref('user_validation_stats').child(userId);
      final snapshot = await ref.get();
      
      if (!snapshot.exists) {
        return {
          'dailyValidationCount': 0,
          'totalValidations': 0,
          'activeSessionsCount': 0,
          'lastValidationDate': null,
        };
      }
      
      return Map<String, dynamic>.from(snapshot.value as Map);
    } catch (e) {
      throw Exception('Failed to get user validation stats: $e');
    }
  }

  @override
  Future<void> cleanupExpiredData() async {
    try {
      final now = DateTime.now();
      
      // Clean up expired sessions
      final sessionsRef = _database.ref(ZoneConstants.qrSessionsPath);
      final sessionsSnapshot = await sessionsRef.get();
      
      if (sessionsSnapshot.exists) {
        final sessionsData = Map<String, dynamic>.from(sessionsSnapshot.value as Map);
        for (final entry in sessionsData.entries) {
          final sessionData = Map<String, dynamic>.from(entry.value as Map);
          final expiresAt = DateTime.parse(sessionData['expiresAt'] as String);
          
          if (now.isAfter(expiresAt)) {
            await sessionsRef.child(entry.key).remove();
          }
        }
      }
      
      // Clean up expired tokens
      final tokensRef = _database.ref(ZoneConstants.qrTokensPath);
      final tokensSnapshot = await tokensRef.get();
      
      if (tokensSnapshot.exists) {
        final tokensData = Map<String, dynamic>.from(tokensSnapshot.value as Map);
        for (final entry in tokensData.entries) {
          final tokenData = Map<String, dynamic>.from(entry.value as Map);
          final expiresAt = DateTime.parse(tokenData['expiresAt'] as String);
          
          if (now.isAfter(expiresAt)) {
            await tokensRef.child(entry.key).remove();
          }
        }
      }
    } catch (e) {
      throw Exception('Failed to cleanup expired data: $e');
    }
  }

  // Helper methods
  Future<void> _incrementUserActiveSessionsCount(String userId) async {
    try {
      final ref = _database.ref('user_validation_stats').child(userId);
      final snapshot = await ref.get();
      
      Map<String, dynamic> stats = {};
      if (snapshot.exists) {
        stats = Map<String, dynamic>.from(snapshot.value as Map);
      }
      
      final currentCount = (stats['activeSessionsCount'] as num?)?.toInt() ?? 0;
      stats['activeSessionsCount'] = currentCount + 1;
      
      await ref.set(stats);
    } catch (e) {
      // Log error but don't fail the operation
      print('Failed to increment user active sessions count: $e');
    }
  }
}
