import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';

/// Data model for PresenceTimeBlock
class PresenceTimeBlockModel extends PresenceTimeBlock {
  const PresenceTimeBlockModel({
    required super.id,
    required super.startTime,
    required super.endTime,
    required super.activeDays,
    super.isEnabled,
    super.label,
  });

  /// Create from Firestore document data
  factory PresenceTimeBlockModel.fromMap(Map<String, dynamic> map) {
    return PresenceTimeBlockModel(
      id: map['id'] as String,
      startTime: PresenceTimeOfDay.fromString(map['startTime'] as String),
      endTime: PresenceTimeOfDay.fromString(map['endTime'] as String),
      activeDays: _parseDaysFromList(map['activeDays'] as List<dynamic>),
      isEnabled: map['isEnabled'] as bool? ?? true,
      label: map['label'] as String?,
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'startTime': startTime.format24Hour(),
      'endTime': endTime.format24Hour(),
      'activeDays': activeDays.map((day) => day.index).toList(),
      'isEnabled': isEnabled,
      'label': label,
    };
  }

  /// Create from entity
  factory PresenceTimeBlockModel.fromEntity(PresenceTimeBlock entity) {
    return PresenceTimeBlockModel(
      id: entity.id,
      startTime: entity.startTime,
      endTime: entity.endTime,
      activeDays: entity.activeDays,
      isEnabled: entity.isEnabled,
      label: entity.label,
    );
  }

  static Set<DayOfWeek> _parseDaysFromList(List<dynamic> dayIndices) {
    return dayIndices
        .cast<int>()
        .map((index) => DayOfWeek.values[index])
        .toSet();
  }
}

/// Data model for PresenceHoursConfiguration
class PresenceHoursConfigurationModel extends PresenceHoursConfiguration {
  const PresenceHoursConfigurationModel({
    required super.zoneId,
    super.isAutomaticValidationEnabled,
    super.timeBlocks,
    required super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  /// Create from Firestore document
  factory PresenceHoursConfigurationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return PresenceHoursConfigurationModel(
        zoneId: doc.id,
        createdAt: DateTime.now(),
      );
    }

    return PresenceHoursConfigurationModel(
      zoneId: doc.id,
      isAutomaticValidationEnabled: data['isAutomaticValidationEnabled'] as bool? ?? false,
      timeBlocks: _parseTimeBlocks(data['timeBlocks'] as List<dynamic>? ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Create from map data
  factory PresenceHoursConfigurationModel.fromMap(Map<String, dynamic> map) {
    return PresenceHoursConfigurationModel(
      zoneId: map['zoneId'] as String,
      isAutomaticValidationEnabled: map['isAutomaticValidationEnabled'] as bool? ?? false,
      timeBlocks: _parseTimeBlocks(map['timeBlocks'] as List<dynamic>? ?? []),
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? _parseDateTime(map['updatedAt']) : null,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Parse DateTime from either String or Timestamp
  static DateTime _parseDateTime(dynamic value) {
    if (value is Timestamp) {
      return value.toDate();
    } else if (value is String) {
      return DateTime.parse(value);
    } else {
      throw ArgumentError('Invalid date format: $value');
    }
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'zoneId': zoneId,
      'isAutomaticValidationEnabled': isAutomaticValidationEnabled,
      'timeBlocks': timeBlocks.map((block) => PresenceTimeBlockModel.fromEntity(block).toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'metadata': metadata,
    };
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'zoneId': zoneId,
      'isAutomaticValidationEnabled': isAutomaticValidationEnabled,
      'timeBlocks': timeBlocks.map((block) => PresenceTimeBlockModel.fromEntity(block).toMap()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from entity
  factory PresenceHoursConfigurationModel.fromEntity(PresenceHoursConfiguration entity) {
    return PresenceHoursConfigurationModel(
      zoneId: entity.zoneId,
      isAutomaticValidationEnabled: entity.isAutomaticValidationEnabled,
      timeBlocks: entity.timeBlocks,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      metadata: entity.metadata,
    );
  }

  static List<PresenceTimeBlock> _parseTimeBlocks(List<dynamic> timeBlocksData) {
    return timeBlocksData
        .cast<Map<String, dynamic>>()
        .map((blockData) => PresenceTimeBlockModel.fromMap(blockData))
        .toList();
  }
}

/// Preset configurations for common presence hour patterns
class PresenceHoursPresets {
  static const String fullTimeId = 'full_time';
  static const String partTimeId = 'part_time';
  static const String morningsId = 'mornings';
  static const String afternoonsId = 'afternoons';
  static const String eveningsId = 'evenings';
  static const String weekendsId = 'weekends';

  /// Full-time work schedule (9 AM - 5 PM, weekdays)
  static PresenceTimeBlock get fullTime => PresenceTimeBlock.fromTimeStrings(
    id: fullTimeId,
    startTime: '09:00',
    endTime: '17:00',
    activeDays: DayOfWeek.weekdays,
    label: 'Full-time (9 AM - 5 PM)',
  );

  /// Part-time work schedule (9 AM - 1 PM, weekdays)
  static PresenceTimeBlock get partTime => PresenceTimeBlock.fromTimeStrings(
    id: partTimeId,
    startTime: '09:00',
    endTime: '13:00',
    activeDays: DayOfWeek.weekdays,
    label: 'Part-time (9 AM - 1 PM)',
  );

  /// Morning hours (6 AM - 12 PM, all days)
  static PresenceTimeBlock get mornings => PresenceTimeBlock.fromTimeStrings(
    id: morningsId,
    startTime: '06:00',
    endTime: '12:00',
    activeDays: DayOfWeek.all,
    label: 'Mornings (6 AM - 12 PM)',
  );

  /// Afternoon hours (12 PM - 6 PM, all days)
  static PresenceTimeBlock get afternoons => PresenceTimeBlock.fromTimeStrings(
    id: afternoonsId,
    startTime: '12:00',
    endTime: '18:00',
    activeDays: DayOfWeek.all,
    label: 'Afternoons (12 PM - 6 PM)',
  );

  /// Evening hours (6 PM - 10 PM, all days)
  static PresenceTimeBlock get evenings => PresenceTimeBlock.fromTimeStrings(
    id: eveningsId,
    startTime: '18:00',
    endTime: '22:00',
    activeDays: DayOfWeek.all,
    label: 'Evenings (6 PM - 10 PM)',
  );

  /// Weekend hours (10 AM - 4 PM, weekends only)
  static PresenceTimeBlock get weekends => PresenceTimeBlock.fromTimeStrings(
    id: weekendsId,
    startTime: '10:00',
    endTime: '16:00',
    activeDays: DayOfWeek.weekends,
    label: 'Weekends (10 AM - 4 PM)',
  );

  /// Get all preset time blocks
  static List<PresenceTimeBlock> get allPresets => [
    fullTime,
    partTime,
    mornings,
    afternoons,
    evenings,
    weekends,
  ];

  /// Get preset by ID
  static PresenceTimeBlock? getPresetById(String id) {
    switch (id) {
      case fullTimeId:
        return fullTime;
      case partTimeId:
        return partTime;
      case morningsId:
        return mornings;
      case afternoonsId:
        return afternoons;
      case eveningsId:
        return evenings;
      case weekendsId:
        return weekends;
      default:
        return null;
    }
  }
}

/// Validation rules for presence hours configuration
class PresenceHoursValidation {
  /// Validate a presence hours configuration
  static List<String> validateConfiguration(PresenceHoursConfiguration config) {
    final errors = <String>[];

    if (config.isAutomaticValidationEnabled && config.timeBlocks.isEmpty) {
      errors.add('At least one time block is required when automatic validation is enabled');
    }

    if (config.hasOverlappingBlocks) {
      errors.add('Time blocks cannot overlap on the same days');
    }

    for (final block in config.timeBlocks) {
      final blockErrors = validateTimeBlock(block);
      errors.addAll(blockErrors);
    }

    return errors;
  }

  /// Validate a single time block
  static List<String> validateTimeBlock(PresenceTimeBlock block) {
    final errors = <String>[];

    if (block.activeDays.isEmpty) {
      errors.add('Time block must have at least one active day');
    }

    if (block.startTime.totalMinutes >= block.endTime.totalMinutes) {
      errors.add('Start time must be before end time');
    }

    final duration = block.durationInMinutes;
    if (duration < 30) {
      errors.add('Time block must be at least 30 minutes long');
    }

    if (duration > 18 * 60) { // 18 hours
      errors.add('Time block cannot be longer than 18 hours');
    }

    return errors;
  }
}
