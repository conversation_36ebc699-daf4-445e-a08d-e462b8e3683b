import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/data/models/presence_hours_model.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/coordinates.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

class ZoneModel extends ZoneEntity {
  const ZoneModel({
    required super.id,
    required super.userId,
    required super.name,
    required super.type,
    required super.address,
    required super.centerCoordinates,
    super.radiusInMeters,
    super.presenceHours,
    super.presenceHoursConfig,
    super.validationStatus,
    super.validationMethod,
    super.communityValidationCount,
    super.communityValidatedBy,
    required super.createdAt,
    super.updatedAt,
    super.validatedAt,
    super.rejectionReason,
    super.metadata,
  });

  factory ZoneModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return ZoneModel(
        id: doc.id,
        userId: '',
        name: '',
        type: ZoneType.other,
        address: '',
        centerCoordinates: Coordinates.empty,

        createdAt: DateTime.now(),
      );
    }

    return ZoneModel(
      id: doc.id,
      userId: data['userId'] as String,
      name: data['name'] as String,
      type: _parseZoneType(data['type']),
      address: data['address'] as String,
      centerCoordinates: _parseCoordinates(data['centerCoordinates']),
      radiusInMeters: (data['radiusInMeters'] as num?)?.toDouble() ?? 600.0,
      presenceHours: data['presenceHours'] as String?,
      presenceHoursConfig: _parsePresenceHoursConfig(data['presenceHoursConfig'], doc.id),
      validationStatus: _parseZoneStatus(data['validationStatus']),
      validationMethod: _parseValidationMethod(data['validationMethod']),
      communityValidationCount: data['communityValidationCount'] as int? ?? 0,
      communityValidatedBy: List<String>.from(data['communityValidatedBy'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      validatedAt: (data['validatedAt'] as Timestamp?)?.toDate(),
      rejectionReason: data['rejectionReason'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory ZoneModel.fromEntity(ZoneEntity entity) {
    return ZoneModel(
      id: entity.id,
      userId: entity.userId,
      name: entity.name,
      type: entity.type,
      address: entity.address,
      centerCoordinates: entity.centerCoordinates,
      radiusInMeters: entity.radiusInMeters,
      presenceHours: entity.presenceHours,
      presenceHoursConfig: entity.presenceHoursConfig,
      validationStatus: entity.validationStatus,
      validationMethod: entity.validationMethod,
      communityValidationCount: entity.communityValidationCount,
      communityValidatedBy: entity.communityValidatedBy,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      validatedAt: entity.validatedAt,
      rejectionReason: entity.rejectionReason,
      metadata: entity.metadata,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'name': name,
      'type': type.name,
      'address': address,
      'centerCoordinates': centerCoordinates.toMap(),
      'radiusInMeters': radiusInMeters,
      'presenceHours': presenceHours,
      'presenceHoursConfig': presenceHoursConfig != null
          ? PresenceHoursConfigurationModel.fromEntity(presenceHoursConfig!).toFirestore()
          : null,
      'validationStatus': validationStatus.name,
      'validationMethod': validationMethod.name,
      'communityValidationCount': communityValidationCount,
      'communityValidatedBy': communityValidatedBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : FieldValue.serverTimestamp(),
      'validatedAt': validatedAt != null ? Timestamp.fromDate(validatedAt!) : null,
      'rejectionReason': rejectionReason,
      'metadata': metadata,
    };
  }

  static ZoneType _parseZoneType(dynamic type) {
    switch (type?.toString()) {
      case 'home':
        return ZoneType.home;
      case 'work':
        return ZoneType.work;

      case 'university':
        return ZoneType.university;
      case 'other':
        return ZoneType.other;
      default:
        return ZoneType.other;
    }
  }

  static ZoneStatus _parseZoneStatus(dynamic status) {
    switch (status?.toString()) {
      case 'pending':
        return ZoneStatus.pending;
      case 'validated':
        return ZoneStatus.validated;
      case 'rejected':
        return ZoneStatus.rejected;
      default:
        return ZoneStatus.pending;
    }
  }

  static ValidationMethod _parseValidationMethod(dynamic method) {
    switch (method?.toString()) {
      case 'social':
      case 'qrCommunity': // Legacy support - map old qrCommunity to social
        return ValidationMethod.social;
      case 'automatic':
        return ValidationMethod.automatic;
      default:
        return ValidationMethod.social;
    }
  }

  static Coordinates _parseCoordinates(dynamic coords) {
    if (coords is Map<String, dynamic>) {
      return Coordinates.fromMap(coords);
    }
    return Coordinates.empty;
  }

  static PresenceHoursConfiguration? _parsePresenceHoursConfig(dynamic value, String zoneId) {
    if (value == null) return null;
    if (value is! Map<String, dynamic>) return null;

    try {
      return PresenceHoursConfigurationModel.fromMap(value);
    } catch (e) {
      // If parsing fails, return null to maintain backward compatibility
      return null;
    }
  }
}
