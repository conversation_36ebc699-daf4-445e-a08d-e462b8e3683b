import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

/// Data model for QR validation session
class QRValidationSessionModel extends QRValidationSessionEntity {
  const QRValidationSessionModel({
    required super.id,
    required super.zoneId,
    required super.initiatorUserId,
    super.validatorUserId,
    required super.status,
    required super.createdAt,
    required super.expiresAt,
    super.completedAt,
    super.failureReason,
    super.metadata,
  });

  /// Create model from entity
  factory QRValidationSessionModel.fromEntity(QRValidationSessionEntity entity) {
    return QRValidationSessionModel(
      id: entity.id,
      zoneId: entity.zoneId,
      initiatorUserId: entity.initiatorUserId,
      validatorUserId: entity.validatorUserId,
      status: entity.status,
      createdAt: entity.createdAt,
      expiresAt: entity.expiresAt,
      completedAt: entity.completedAt,
      failureReason: entity.failureReason,
      metadata: entity.metadata,
    );
  }

  /// Create model from Firebase Realtime Database data
  factory QRValidationSessionModel.fromRealtimeDB(Map<String, dynamic> data) {
    return QRValidationSessionModel(
      id: data['id'] as String,
      zoneId: data['zoneId'] as String,
      initiatorUserId: data['initiatorUserId'] as String,
      validatorUserId: data['validatorUserId'] as String?,
      status: QRValidationStatus.values.firstWhere(
        (status) => status.name == data['status'],
        orElse: () => QRValidationStatus.pending,
      ),
      createdAt: DateTime.parse(data['createdAt'] as String),
      expiresAt: DateTime.parse(data['expiresAt'] as String),
      completedAt: data['completedAt'] != null 
          ? DateTime.parse(data['completedAt'] as String)
          : null,
      failureReason: data['failureReason'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert model to Firebase Realtime Database data
  Map<String, dynamic> toRealtimeDB() {
    return {
      'id': id,
      'zoneId': zoneId,
      'initiatorUserId': initiatorUserId,
      'validatorUserId': validatorUserId,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'failureReason': failureReason,
      'metadata': metadata,
    };
  }

  /// Convert to entity
  QRValidationSessionEntity toEntity() {
    return QRValidationSessionEntity(
      id: id,
      zoneId: zoneId,
      initiatorUserId: initiatorUserId,
      validatorUserId: validatorUserId,
      status: status,
      createdAt: createdAt,
      expiresAt: expiresAt,
      completedAt: completedAt,
      failureReason: failureReason,
      metadata: metadata,
    );
  }

  @override
  QRValidationSessionModel copyWith({
    String? id,
    String? zoneId,
    String? initiatorUserId,
    String? validatorUserId,
    QRValidationStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return QRValidationSessionModel(
      id: id ?? this.id,
      zoneId: zoneId ?? this.zoneId,
      initiatorUserId: initiatorUserId ?? this.initiatorUserId,
      validatorUserId: validatorUserId ?? this.validatorUserId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Data model for QR token
class QRTokenModel extends QRTokenEntity {
  const QRTokenModel({
    required super.id,
    required super.sessionId,
    required super.userId,
    required super.encryptedData,
    required super.status,
    required super.createdAt,
    required super.expiresAt,
    super.usedAt,
    super.usedByUserId,
    super.metadata,
  });

  /// Create model from entity
  factory QRTokenModel.fromEntity(QRTokenEntity entity) {
    return QRTokenModel(
      id: entity.id,
      sessionId: entity.sessionId,
      userId: entity.userId,
      encryptedData: entity.encryptedData,
      status: entity.status,
      createdAt: entity.createdAt,
      expiresAt: entity.expiresAt,
      usedAt: entity.usedAt,
      usedByUserId: entity.usedByUserId,
      metadata: entity.metadata,
    );
  }

  /// Create model from Firebase Realtime Database data
  factory QRTokenModel.fromRealtimeDB(Map<String, dynamic> data) {
    return QRTokenModel(
      id: data['id'] as String,
      sessionId: data['sessionId'] as String,
      userId: data['userId'] as String,
      encryptedData: data['encryptedData'] as String,
      status: QRTokenStatus.values.firstWhere(
        (status) => status.name == data['status'],
        orElse: () => QRTokenStatus.active,
      ),
      createdAt: DateTime.parse(data['createdAt'] as String),
      expiresAt: DateTime.parse(data['expiresAt'] as String),
      usedAt: data['usedAt'] != null 
          ? DateTime.parse(data['usedAt'] as String)
          : null,
      usedByUserId: data['usedByUserId'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert model to Firebase Realtime Database data
  Map<String, dynamic> toRealtimeDB() {
    return {
      'id': id,
      'sessionId': sessionId,
      'userId': userId,
      'encryptedData': encryptedData,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'usedAt': usedAt?.toIso8601String(),
      'usedByUserId': usedByUserId,
      'metadata': metadata,
    };
  }

  /// Convert to entity
  QRTokenEntity toEntity() {
    return QRTokenEntity(
      id: id,
      sessionId: sessionId,
      userId: userId,
      encryptedData: encryptedData,
      status: status,
      createdAt: createdAt,
      expiresAt: expiresAt,
      usedAt: usedAt,
      usedByUserId: usedByUserId,
      metadata: metadata,
    );
  }

  @override
  QRTokenModel copyWith({
    String? id,
    String? sessionId,
    String? userId,
    String? encryptedData,
    QRTokenStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? usedAt,
    String? usedByUserId,
    Map<String, dynamic>? metadata,
  }) {
    return QRTokenModel(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      userId: userId ?? this.userId,
      encryptedData: encryptedData ?? this.encryptedData,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      usedAt: usedAt ?? this.usedAt,
      usedByUserId: usedByUserId ?? this.usedByUserId,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Data model for proximity data
class ProximityDataModel extends ProximityDataEntity {
  const ProximityDataModel({
    required super.latitude,
    required super.longitude,
    required super.accuracy,
    required super.timestamp,
  });

  /// Create model from entity
  factory ProximityDataModel.fromEntity(ProximityDataEntity entity) {
    return ProximityDataModel(
      latitude: entity.latitude,
      longitude: entity.longitude,
      accuracy: entity.accuracy,
      timestamp: entity.timestamp,
    );
  }

  /// Create model from Firebase Realtime Database data
  factory ProximityDataModel.fromRealtimeDB(Map<String, dynamic> data) {
    return ProximityDataModel(
      latitude: (data['latitude'] as num).toDouble(),
      longitude: (data['longitude'] as num).toDouble(),
      accuracy: (data['accuracy'] as num).toDouble(),
      timestamp: DateTime.parse(data['timestamp'] as String),
    );
  }

  /// Convert model to Firebase Realtime Database data
  Map<String, dynamic> toRealtimeDB() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Convert to entity
  ProximityDataEntity toEntity() {
    return ProximityDataEntity(
      latitude: latitude,
      longitude: longitude,
      accuracy: accuracy,
      timestamp: timestamp,
    );
  }
}
