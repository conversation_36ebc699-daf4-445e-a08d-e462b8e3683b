import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

class ZoneUserModel extends ZoneUserEntity {
  const ZoneUserModel({
    required super.id,
    super.validationStatus,
    super.definedZoneIds,
    super.currentZoneId,
    super.lastLocationUpdate,
    super.metadata,
  });

  factory ZoneUserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return ZoneUserModel(id: doc.id);
    }

    return ZoneUserModel(
      id: doc.id,
      validationStatus: _parseUserStatus(data['validationStatus']),
      definedZoneIds: List<String>.from(data['definedZoneIds'] ?? []),
      currentZoneId: data['currentZoneId'] as String?,
      lastLocationUpdate: (data['lastLocationUpdate'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory ZoneUserModel.fromEntity(ZoneUserEntity entity) {
    return ZoneUserModel(
      id: entity.id,
      validationStatus: entity.validationStatus,
      definedZoneIds: entity.definedZoneIds,
      currentZoneId: entity.currentZoneId,
      lastLocationUpdate: entity.lastLocationUpdate,
      metadata: entity.metadata,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'validationStatus': validationStatus.name,
      'definedZoneIds': definedZoneIds,
      'currentZoneId': currentZoneId,
      'lastLocationUpdate': lastLocationUpdate != null 
          ? Timestamp.fromDate(lastLocationUpdate!) 
          : null,
      'metadata': metadata,
    };
  }

  static UserStatus _parseUserStatus(dynamic status) {
    switch (status?.toString()) {
      case 'unverified':
        return UserStatus.unverified;
      case 'pendingId':
        return UserStatus.pendingId;
      case 'pendingCommunity':
        return UserStatus.pendingCommunity;
      case 'validated':
        return UserStatus.validated;
      case 'rejected':
        return UserStatus.rejected;
      default:
        return UserStatus.unverified;
    }
  }
}
