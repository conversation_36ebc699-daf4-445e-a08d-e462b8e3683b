import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/validation_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

class ValidationModel extends ValidationEntity {
  const ValidationModel({
    required super.id,
    required super.validatedEntityId,
    required super.entityType,
    required super.type,
    required super.validatorUserId,
    required super.validatedAt,
    super.validationNote,
    super.metadata,
  });

  factory ValidationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ValidationModel(
      id: doc.id,
      validatedEntityId: data['validatedEntityId'] as String,
      entityType: _parseValidationEntityType(data['entityType']),
      type: _parseValidationType(data['type']),
      validatorUserId: data['validatorUserId'] as String,
      validatedAt: (data['validatedAt'] as Timestamp).toDate(),
      validationNote: data['validationNote'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory ValidationModel.fromEntity(ValidationEntity entity) {
    return ValidationModel(
      id: entity.id,
      validatedEntityId: entity.validatedEntityId,
      entityType: entity.entityType,
      type: entity.type,
      validatorUserId: entity.validatorUserId,
      validatedAt: entity.validatedAt,
      validationNote: entity.validationNote,
      metadata: entity.metadata,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'validatedEntityId': validatedEntityId,
      'entityType': entityType.name,
      'type': type.name,
      'validatorUserId': validatorUserId,
      'validatedAt': Timestamp.fromDate(validatedAt),
      'validationNote': validationNote,
      'metadata': metadata,
    };
  }

  static ValidationEntityType _parseValidationEntityType(dynamic type) {
    switch (type?.toString()) {
      case 'zone':
        return ValidationEntityType.zone;
      default:
        return ValidationEntityType.zone;
    }
  }

  static ValidationType _parseValidationType(dynamic type) {
    switch (type?.toString()) {
      case 'community':
        return ValidationType.community;
      case 'automatic':
        return ValidationType.automatic;
      default:
        return ValidationType.community;
    }
  }
}
