import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/zone_verification/data/datasources/zone_firebase_datasource.dart';
import 'package:respublicaseguridad/features/zone_verification/data/models/zone_verification_models.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/presence_hours_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/core/services/geofencing_service.dart';

/// Implementation of ZoneRepository
class ZoneRepositoryImpl implements ZoneRepository {
  final ZoneFirebaseDataSource firebaseDataSource;

  ZoneRepositoryImpl({
    required this.firebaseDataSource,
  });

  @override
  Future<Either<Failure, ZoneEntity>> createZone(ZoneEntity zone) async {
    try {
      final zoneModel = ZoneModel.fromEntity(zone);
      final result = await firebaseDataSource.createZone(zoneModel);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to create zone: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getUserZones(String userId) async {
    try {
      final result = await firebaseDataSource.getUserZones(userId);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> getZoneById(String zoneId) async {
    try {
      final result = await firebaseDataSource.getZoneById(zoneId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zone: ${e.toString()}'));
    }
  }

  @override
  Stream<Either<Failure, ZoneEntity>> watchZone(String zoneId) {
    try {
      return firebaseDataSource.watchZone(zoneId)
          .map<Either<Failure, ZoneEntity>>((zone) => Right(zone))
          .handleError((error) {
        return Left(ServerFailure(message: 'Failed to watch zone: ${error.toString()}'));
      });
    } catch (e) {
      return Stream.value(
        Left(ServerFailure(message: 'Failed to watch zone: ${e.toString()}')),
      );
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> updateZone(ZoneEntity zone) async {
    try {
      final zoneModel = ZoneModel.fromEntity(zone);
      final result = await firebaseDataSource.updateZone(zoneModel);
      return Right(result);
    } catch (e) {
      if (e.toString().contains('ValidationException')) {
        return Left(ValidationFailure(e.toString()));
      }
      return Left(ServerFailure(message: 'Failed to update zone: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteZone(String zoneId) async {
    try {
      await firebaseDataSource.deleteZone(zoneId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete zone: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getZonesNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  }) async {
    try {
      final result = await firebaseDataSource.getZonesNearLocation(
        latitude: latitude,
        longitude: longitude,
        radiusInMeters: radiusInMeters,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zones near location: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> addCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  }) async {
    try {
      final result = await firebaseDataSource.addCommunityValidation(
        zoneId: zoneId,
        validatorUserId: validatorUserId,
      );
      return Right(result);
    } catch (e) {
      if (e.toString().contains('ValidationException')) {
        return Left(ValidationFailure(e.toString()));
      }
      if (e.toString().contains('NotFoundException')) {
        return Left(NotFoundFailure(e.toString()));
      }
      return Left(ServerFailure(message: 'Failed to add community validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> removeCommunityValidation({
    required String zoneId,
    required String validatorUserId,
  }) async {
    try {
      final result = await firebaseDataSource.removeCommunityValidation(
        zoneId: zoneId,
        validatorUserId: validatorUserId,
      );
      return Right(result);
    } catch (e) {
      if (e.toString().contains('ValidationException')) {
        return Left(ValidationFailure(e.toString()));
      }
      if (e.toString().contains('NotFoundException')) {
        return Left(NotFoundFailure(e.toString()));
      }
      return Left(ServerFailure(message: 'Failed to remove community validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> updateZoneValidationStatus({
    required String zoneId,
    required ZoneStatus status,
    String? rejectionReason,
  }) async {
    try {
      final result = await firebaseDataSource.updateZoneValidationStatus(
        zoneId: zoneId,
        status: status,
        rejectionReason: rejectionReason,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update zone validation status: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ValidationEntity>>> getZoneValidationHistory(String zoneId) async {
    try {
      final result = await firebaseDataSource.getZoneValidationHistory(zoneId);
      return Right(result.cast<ValidationEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zone validation history: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> canUserValidateZone({
    required String userId,
    required String zoneId,
  }) async {
    try {
      final result = await firebaseDataSource.canUserValidateZone(
        userId: userId,
        zoneId: zoneId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check if user can validate zone: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, int>> getUserDailyValidationCount(String userId) async {
    try {
      final result = await firebaseDataSource.getUserDailyValidationCount(userId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user daily validation count: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> enableAutomaticValidation(String zoneId) async {
    try {
      final result = await firebaseDataSource.enableAutomaticValidation(zoneId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to enable automatic validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> disableAutomaticValidation(String zoneId) async {
    try {
      final result = await firebaseDataSource.disableAutomaticValidation(zoneId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to disable automatic validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getZonesForAutomaticValidation() async {
    try {
      final result = await firebaseDataSource.getZonesForAutomaticValidation();
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zones for automatic validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> processAutomaticValidation({
    required String zoneId,
    required String userId,
    required double userLatitude,
    required double userLongitude,
  }) async {
    try {
      final result = await firebaseDataSource.processAutomaticValidation(
        zoneId: zoneId,
        userId: userId,
        userLatitude: userLatitude,
        userLongitude: userLongitude,
      );
      return Right(result);
    } catch (e) {
      if (e.toString().contains('ValidationException')) {
        return Left(ValidationFailure(e.toString()));
      }
      return Left(ServerFailure(message: 'Failed to process automatic validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserZoneStatistics(String userId) async {
    try {
      final result = await firebaseDataSource.getUserZoneStatistics(userId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user zone statistics: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> searchZones({
    required String query,
    String? userId,
  }) async {
    try {
      final result = await firebaseDataSource.searchZones(
        query: query,
        userId: userId,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to search zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getZonesByType({
    required ZoneType type,
    String? userId,
  }) async {
    try {
      final result = await firebaseDataSource.getZonesByType(
        type: type,
        userId: userId,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zones by type: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getZonesByStatus({
    required ZoneStatus status,
    String? userId,
  }) async {
    try {
      final result = await firebaseDataSource.getZonesByStatus(
        status: status,
        userId: userId,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zones by status: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> batchUpdateZones(List<ZoneEntity> zones) async {
    try {
      final zoneModels = zones.map((zone) => ZoneModel.fromEntity(zone)).toList();
      final result = await firebaseDataSource.batchUpdateZones(zoneModels);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to batch update zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getZoneValidationRequirements(String zoneId) async {
    try {
      final result = await firebaseDataSource.getZoneValidationRequirements(zoneId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zone validation requirements: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity?>> getZoneAtLocation({
    required String userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final result = await firebaseDataSource.getZoneAtLocation(
        userId: userId,
        latitude: latitude,
        longitude: longitude,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zone at location: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getPendingValidationZones({String? userId}) async {
    try {
      final result = await firebaseDataSource.getPendingValidationZones(userId: userId);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get pending validation zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getValidatedZones({String? userId}) async {
    try {
      final result = await firebaseDataSource.getValidatedZones(userId: userId);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get validated zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getCommunityValidationRequests(String userId) async {
    try {
      final result = await firebaseDataSource.getCommunityValidationRequests(userId);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get community validation requests: ${e.toString()}'));
    }
  }

  @override
  Stream<Either<Failure, List<ZoneEntity>>> watchUserZones(String userId) {
    try {
      return firebaseDataSource.watchUserZones(userId)
          .map<Either<Failure, List<ZoneEntity>>>(
            (zones) => Right(zones.cast<ZoneEntity>()),
          )
          .handleError((error) {
            return Left(ServerFailure(message: 'Failed to watch user zones: ${error.toString()}'));
          });
    } catch (e) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch user zones: ${e.toString()}')));
    }
  }

  @override
  Stream<Either<Failure, ZoneEntity>> watchZoneValidation(String zoneId) {
    try {
      return firebaseDataSource.watchZoneValidation(zoneId)
          .map<Either<Failure, ZoneEntity>>(
            (zone) => Right(zone as ZoneEntity),
          )
          .handleError((error) {
            if (error.toString().contains('NotFoundException')) {
              return Left(NotFoundFailure(error.toString()));
            }
            return Left(ServerFailure(message: 'Failed to watch zone validation: ${error.toString()}'));
          });
    } catch (e) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch zone validation: ${e.toString()}')));
    }
  }

  // Enhanced Automatic Validation Methods Implementation

  @override
  Future<Either<Failure, List<ZoneEntity>>> getAutomaticValidationZones(String userId) async {
    try {
      final result = await firebaseDataSource.getAutomaticValidationZones(userId);
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get automatic validation zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> updatePresenceHours({
    required String zoneId,
    required PresenceHoursConfiguration presenceHours,
  }) async {
    try {
      final result = await firebaseDataSource.updatePresenceHours(
        zoneId: zoneId,
        presenceHours: presenceHours,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update presence hours: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> recordGeofenceEvent({
    required String zoneId,
    required String userId,
    required GeofenceEventType eventType,
    required DateTime timestamp,
    required double latitude,
    required double longitude,
    required double accuracy,
  }) async {
    try {
      await firebaseDataSource.recordGeofenceEvent(
        zoneId: zoneId,
        userId: userId,
        eventType: eventType,
        timestamp: timestamp,
        latitude: latitude,
        longitude: longitude,
        accuracy: accuracy,
      );
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to record geofence event: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<GeofenceEventRecord>>> getGeofenceEvents({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final result = await firebaseDataSource.getGeofenceEvents(
        zoneId: zoneId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get geofence events: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> updateValidationProgress({
    required String zoneId,
    required Map<String, dynamic> progressData,
  }) async {
    try {
      final result = await firebaseDataSource.updateValidationProgress(
        zoneId: zoneId,
        progressData: progressData,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update validation progress: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> getActivePresenceZones({
    required String userId,
    DateTime? currentTime,
  }) async {
    try {
      final result = await firebaseDataSource.getActivePresenceZones(
        userId: userId,
        currentTime: currentTime,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get active presence zones: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ValidationCriteriaResult>> checkValidationCriteria({
    required String zoneId,
    required String userId,
  }) async {
    try {
      final result = await firebaseDataSource.checkValidationCriteria(
        zoneId: zoneId,
        userId: userId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check validation criteria: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ZoneEntity>> completeAutomaticValidation({
    required String zoneId,
    required String userId,
    required Map<String, dynamic> validationData,
  }) async {
    try {
      final result = await firebaseDataSource.completeAutomaticValidation(
        zoneId: zoneId,
        userId: userId,
        validationData: validationData,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to complete automatic validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getZoneValidationAnalytics({
    required String zoneId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final result = await firebaseDataSource.getZoneValidationAnalytics(
        zoneId: zoneId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get zone validation analytics: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ZoneEntity>>> batchProcessAutomaticValidation({
    required List<String> zoneIds,
    required String userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final result = await firebaseDataSource.batchProcessAutomaticValidation(
        zoneIds: zoneIds,
        userId: userId,
        latitude: latitude,
        longitude: longitude,
      );
      return Right(result.cast<ZoneEntity>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to batch process automatic validation: ${e.toString()}'));
    }
  }
}
