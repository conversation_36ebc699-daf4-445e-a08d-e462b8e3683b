import 'package:dartz/dartz.dart';
import 'package:flutter/widgets.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/core/services/qr_token_service.dart';
import 'package:respublicaseguridad/core/services/qr_validation_error_handler.dart';
import 'package:respublicaseguridad/core/services/qr_validation_retry_service.dart';
import 'package:respublicaseguridad/core/services/qr_validation_cloud_security_service.dart';
import 'package:respublicaseguridad/core/services/qr_validation_audit_service.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/zone_verification/data/datasources/qr_validation_firebase_datasource.dart';
import 'package:respublicaseguridad/features/zone_verification/data/models/qr_validation_models.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/qr_validation_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_constants.dart';

import 'package:respublicaseguridad/features/zone_verification/domain/repositories/qr_validation_repository.dart';

/// Implementation of QR validation repository
class QRValidationRepositoryImpl with QRValidationRetryMixin implements QRValidationRepository {
  final QRValidationFirebaseDataSource _dataSource;
  final Uuid _uuid;
  final QRValidationCloudSecurityService _securityService;
  final QRValidationAuditService _auditService;

  QRValidationRepositoryImpl(
    this._dataSource,
    this._uuid, {
    QRValidationCloudSecurityService? securityService,
    QRValidationAuditService? auditService,
  }) : _securityService = securityService ?? QRValidationCloudSecurityService.instance,
       _auditService = auditService ?? QRValidationAuditService.instance;

  @override
  Future<Either<Failure, QRValidationSessionEntity>> createValidationSession({
    required String zoneId,
    required String initiatorUserId,
  }) async {
    try {
      // Validate user permissions
      final permissionResult = await _securityService.validateUserPermissions(
        userId: initiatorUserId,
        zoneId: zoneId,
        sessionId: '', // Will be generated
        isInitiator: true,
      );

      if (!permissionResult.isValid) {
        await _auditService.logUnauthorizedAccess(
          userId: initiatorUserId,
          attemptedAction: 'createValidationSession',
          reason: permissionResult.errorMessage ?? 'Permission denied',
          zoneId: zoneId,
        );
        return Left(QRValidationError.security(
          message: permissionResult.errorMessage ?? 'Permission denied',
        ));
      }

      final result = await retryOperation(
        operation: () async {
          final sessionId = await _securityService.generateSecureSessionId();
          final session = QRValidationSessionEntity.create(
            id: sessionId,
            zoneId: zoneId,
            initiatorUserId: initiatorUserId,
            sessionDuration: ZoneConstants.qrSessionDuration,
          );

          final sessionModel = QRValidationSessionModel.fromEntity(session);
          final createdSession = await _dataSource.createValidationSession(sessionModel);

          // Log session creation
          await _auditService.logSessionEvent(
            eventType: SecurityAuditEventType.sessionCreated,
            sessionId: sessionId,
            userId: initiatorUserId,
            zoneId: zoneId,
            additionalData: {
              'sessionDuration': ZoneConstants.qrSessionDuration.inMinutes,
            },
          );

          return createdSession;
        },
        operationName: 'sessionCreation',
      );

      return Right(result.toEntity());
    } catch (e) {
      final error = QRValidationErrorHandler.instance.handleError(e, context: 'createValidationSession');
      return Left(error);
    }
  }

  @override
  Future<Either<Failure, QRValidationSessionEntity>> getValidationSession(String sessionId) async {
    try {
      final sessionModel = await _dataSource.getValidationSession(sessionId);
      return Right(sessionModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get validation session: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRValidationSessionEntity>> joinValidationSession({
    required String sessionId,
    required String validatorUserId,
  }) async {
    try {
      final sessionModel = await _dataSource.getValidationSession(sessionId);
      final session = sessionModel.toEntity();

      final joinedSession = session.joinAsValidator(validatorUserId);
      final updatedSessionModel = QRValidationSessionModel.fromEntity(joinedSession);

      final result = await _dataSource.updateValidationSession(updatedSessionModel);
      return Right(result.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to join validation session: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRValidationSessionEntity>> updateValidationSession(
    QRValidationSessionEntity session,
  ) async {
    try {
      final sessionModel = QRValidationSessionModel.fromEntity(session);
      final updatedSession = await _dataSource.updateValidationSession(sessionModel);
      return Right(updatedSession.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update validation session: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<QRValidationSessionEntity>>> getActiveSessionsForUser(String userId) async {
    try {
      final sessionModels = await _dataSource.getActiveSessionsForUser(userId);
      final sessions = sessionModels.map((model) => model.toEntity()).toList();
      return Right(sessions);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get active sessions for user: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, int>> getUserDailyValidationCount(String userId) async {
    try {
      final count = await _dataSource.getUserDailyValidationCount(userId);
      return Right(count);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user daily validation count: ${e.toString()}'));
    }
  }

  @override
  Stream<Either<Failure, QRValidationSessionEntity>> watchValidationSession(String sessionId) {
    try {
      return _dataSource.watchValidationSession(sessionId).map(
        (sessionModel) => Right<Failure, QRValidationSessionEntity>(sessionModel.toEntity()),
      ).handleError((error) {
        return Left<Failure, QRValidationSessionEntity>(
          ServerFailure(message: 'Failed to watch validation session: ${error.toString()}'),
        );
      });
    } catch (e) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch validation session: ${e.toString()}')));
    }
  }

  @override
  Future<Either<Failure, QRTokenEntity>> generateQRToken({
    required String sessionId,
    required String userId,
  }) async {
    try {
      final tokenId = _uuid.v4();
      final encryptedData = QRTokenService.instance.generateSecureToken(
        sessionId: sessionId,
        userId: userId,
        tokenId: tokenId,
      );

      final token = QRTokenEntity.create(
        id: tokenId,
        sessionId: sessionId,
        userId: userId,
        encryptedData: encryptedData,
        tokenDuration: ZoneConstants.qrTokenDuration,
      );

      final tokenModel = QRTokenModel.fromEntity(token);
      final createdToken = await _dataSource.generateQRToken(tokenModel);

      return Right(createdToken.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to generate QR token: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRTokenEntity>> generateQRTokenForZone({
    required String zoneId,
    required String userId,
  }) async {
    try {
      final tokenId = _uuid.v4();
      final encryptedData = QRTokenService.instance.generateSecureTokenForZone(
        zoneId: zoneId,
        userId: userId,
        tokenId: tokenId,
      );

      final token = QRTokenEntity.createForZone(
        id: tokenId,
        zoneId: zoneId,
        userId: userId,
        encryptedData: encryptedData,
        tokenDuration: ZoneConstants.qrTokenDuration,
      );

      final tokenModel = QRTokenModel.fromEntity(token);
      final createdToken = await _dataSource.generateQRToken(tokenModel);

      return Right(createdToken.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to generate QR token for zone: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRTokenEntity>> getQRToken(String tokenId) async {
    try {
      debugPrint('Fetching QR token with ID: $tokenId');
      
      if (tokenId.isEmpty) {
        debugPrint('Error: Empty token ID provided');
        return Left(ServerFailure(message: 'Invalid QR code: Empty token ID'));
      }
      
      final tokenModel = await _dataSource.getQRToken(tokenId);
      debugPrint('Successfully retrieved token: ${tokenModel.id}');
      return Right(tokenModel.toEntity());
    } catch (e) {
      debugPrint('Failed to get QR token: $e');
      return Left(ServerFailure(
        message: 'No se pudo validar el código QR. El código puede haber expirado o ser inválido. Por favor intente nuevamente con un código QR válido.',
      ));
    }
  }

  @override
  Future<Either<Failure, QRTokenEntity>> validateQRToken({
    required String tokenId,
    required String scannerUserId,
  }) async {
    try {
      final tokenModel = await _dataSource.getQRToken(tokenId);
      final token = tokenModel.toEntity();

      debugPrint('QR token: ${token.id}');
      debugPrint('Token status: ${token.status.name}');

      final usedToken = token.markAsUsed(scannerUserId);
      final updatedTokenModel = QRTokenModel.fromEntity(usedToken);

      final result = await _dataSource.updateQRToken(updatedTokenModel);

      debugPrint('result for the updating token ${result.metadata}');

      // Update user daily validation count
      await _incrementUserDailyValidationCount(scannerUserId);

      return Right(result.toEntity());
    } catch (e) {
      debugPrint('Failed to validate QR token: ${e.toString()}');
      return Left(ServerFailure(message: 'Failed to validate QR token: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<QRTokenEntity>>> getActiveTokensForSession(String sessionId) async {
    try {
      final tokenModels = await _dataSource.getActiveTokensForSession(sessionId);
      final tokens = tokenModels.map((model) => model.toEntity()).toList();
      return Right(tokens);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get active tokens for session: ${e.toString()}'));
    }
  }

  @override
  Stream<Either<Failure, QRTokenEntity>> watchQRToken(String tokenId) {
    try {
      return _dataSource.watchQRToken(tokenId).map(
        (tokenModel) => Right<Failure, QRTokenEntity>(tokenModel.toEntity()),
      ).handleError((error) {
        return Left<Failure, QRTokenEntity>(
          ServerFailure(message: 'Failed to watch QR token: ${error.toString()}'),
        );
      });
    } catch (e) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch QR token: ${e.toString()}')));
    }
  }

  @override
  Future<Either<Failure, void>> recordProximityVerification({
    required String sessionId,
    required ProximityDataEntity initiatorLocation,
    required ProximityDataEntity validatorLocation,
    required bool isVerified,
  }) async {
    try {
      final initiatorModel = ProximityDataModel.fromEntity(initiatorLocation);
      final validatorModel = ProximityDataModel.fromEntity(validatorLocation);
      
      await _dataSource.recordProximityVerification(
        sessionId: sessionId,
        initiatorLocation: initiatorModel,
        validatorLocation: validatorModel,
        isVerified: isVerified,
      );
      
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to record proximity verification: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> getProximityVerificationStatus(String sessionId) async {
    try {
      final status = await _dataSource.getProximityVerificationStatus(sessionId);
      return Right(status);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get proximity verification status: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRValidationSessionEntity>> completeValidation({
    required String sessionId,
    required String zoneId,
  }) async {
    try {
      final sessionModel = await _dataSource.getValidationSession(sessionId);
      final session = sessionModel.toEntity();

      final completedSession = session.complete();
      final updatedSessionModel = QRValidationSessionModel.fromEntity(completedSession);

      final result = await _dataSource.updateValidationSession(updatedSessionModel);

      // Update user stats
      if (session.validatorUserId != null) {
        await _incrementUserTotalValidations(session.validatorUserId!);
        await _decrementUserActiveSessionsCount(session.initiatorUserId);
      }

      return Right(result.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to complete validation: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, QRValidationSessionEntity>> recordValidationFailure({
    required String sessionId,
    required String reason,
  }) async {
    try {
      final sessionModel = await _dataSource.getValidationSession(sessionId);
      final session = sessionModel.toEntity();

      final failedSession = session.fail(reason);
      final updatedSessionModel = QRValidationSessionModel.fromEntity(failedSession);

      final result = await _dataSource.updateValidationSession(updatedSessionModel);

      // Update user stats
      await _decrementUserActiveSessionsCount(session.initiatorUserId);

      return Right(result.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to record validation failure: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> cleanupExpiredData() async {
    try {
      await _dataSource.cleanupExpiredData();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cleanup expired data: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getValidationStatistics(String userId) async {
    try {
      final stats = await _dataSource.getUserValidationStats(userId);
      return Right(stats);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get validation statistics: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> logValidationEvent({
    required String sessionId,
    required String eventType,
    required Map<String, dynamic> eventData,
  }) async {
    try {
      await _dataSource.logValidationEvent(
        sessionId: sessionId,
        eventType: eventType,
        eventData: eventData,
      );
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to log validation event: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getValidationEvents(String sessionId) async {
    try {
      final events = await _dataSource.getValidationEvents(sessionId);
      return Right(events);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get validation events: ${e.toString()}'));
    }
  }

  // Helper methods

  Future<void> _incrementUserDailyValidationCount(String userId) async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0];
      final stats = await _dataSource.getUserValidationStats(userId);

      debugPrint('Stats chelsea: $stats');
      
      final lastValidationDate = stats['lastValidationDate'] as String?;
      int dailyCount = 0;
      
      if (lastValidationDate == today) {
        dailyCount = (stats['dailyValidationCount'] as num?)?.toInt() ?? 0;
      }
      
      await _dataSource.updateUserValidationStats(userId, {
        'dailyValidationCount': dailyCount + 1,
        'lastValidationDate': today,
        'totalValidations': ((stats['totalValidations'] as num?)?.toInt() ?? 0) + 1,
      });
    } catch (e) {
      // Log error but don't fail the operation
      print('Failed to increment user daily validation count: $e');
    }
  }

  Future<void> _incrementUserTotalValidations(String userId) async {
    try {
      final stats = await _dataSource.getUserValidationStats(userId);
      await _dataSource.updateUserValidationStats(userId, {
        'totalValidations': ((stats['totalValidations'] as num?)?.toInt() ?? 0) + 1,
      });
    } catch (e) {
      // Log error but don't fail the operation
      print('Failed to increment user total validations: $e');
    }
  }

  Future<void> _decrementUserActiveSessionsCount(String userId) async {
    try {
      final stats = await _dataSource.getUserValidationStats(userId);
      final currentCount = (stats['activeSessionsCount'] as num?)?.toInt() ?? 0;
      await _dataSource.updateUserValidationStats(userId, {
        'activeSessionsCount': (currentCount - 1).clamp(0, double.infinity).toInt(),
      });
    } catch (e) {
      // Log error but don't fail the operation
      print('Failed to decrement user active sessions count: $e');
    }
  }
}
