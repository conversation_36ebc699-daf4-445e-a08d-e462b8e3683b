import 'package:cloud_functions/cloud_functions.dart';
import 'package:respublicaseguridad/core/error/exceptions.dart';

/// Data source for notification-related cloud functions
class NotificationCloudFunctionsDataSource {
  final FirebaseFunctions _functions;

  NotificationCloudFunctionsDataSource({required FirebaseFunctions functions})
    : _functions = functions;

  /// Update user location for notifications
  Future<Map<String, dynamic>> updateUserLocation({
    required double latitude,
    required double longitude,
    double? accuracy,
    String source = 'gps',
  }) async {
    try {
      final callable = _functions.httpsCallable('updateUserLocationGen2');

      final result = await callable.call({
        'latitude': latitude,
        'longitude': longitude,
        'accuracy': accuracy,
        'source': source,
      });

      return Map<String, dynamic>.from(result.data);
    } on FirebaseFunctionsException catch (e) {
      throw ServerException(
        'Failed to update user location: ${e.message}',
        code: e.code,
      );
    } catch (e) {
      throw ServerException('Failed to update user location: $e');
    }
  }

  /// Get notification analytics
  Future<Map<String, dynamic>> getNotificationAnalytics({
    int timeRangeHours = 24,
  }) async {
    try {
      final callable = _functions.httpsCallable('getNotificationAnalyticsGen2');

      final result = await callable.call({'timeRangeHours': timeRangeHours});

      final data = Map<String, dynamic>.from(result.data);
      if (data['success'] == true) {
        return Map<String, dynamic>.from(data['data']);
      } else {
        throw ServerException(
          'Failed to get notification analytics: ${data['error'] ?? 'Unknown error'}',
        );
      }
    } on FirebaseFunctionsException catch (e) {
      throw ServerException(
        'Failed to get notification analytics: ${e.message}',
        code: e.code,
      );
    } catch (e) {
      throw ServerException('Failed to get notification analytics: $e');
    }
  }

  /// Send test notification
  Future<Map<String, dynamic>> sendTestNotification({
    required String fcmToken,
  }) async {
    try {
      final callable = _functions.httpsCallable('sendTestNotificationGen2');

      final result = await callable.call({'fcmToken': fcmToken});

      return Map<String, dynamic>.from(result.data);
    } on FirebaseFunctionsException catch (e) {
      throw ServerException(
        'Failed to send test notification: ${e.message}',
        code: e.code,
      );
    } catch (e) {
      throw ServerException('Failed to send test notification: $e');
    }
  }

  /// Get error statistics
  Future<Map<String, dynamic>> getErrorStatistics({
    int timeRangeHours = 24,
  }) async {
    try {
      final callable = _functions.httpsCallable('getErrorStatisticsGen2');

      final result = await callable.call({'timeRangeHours': timeRangeHours});

      final data = Map<String, dynamic>.from(result.data);
      if (data['success'] == true) {
        return Map<String, dynamic>.from(data['data']);
      } else {
        throw ServerException(
          'Failed to get error statistics: ${data['error'] ?? 'Unknown error'}',
        );
      }
    } on FirebaseFunctionsException catch (e) {
      throw ServerException(
        'Failed to get error statistics: ${e.message}',
        code: e.code,
      );
    } catch (e) {
      throw ServerException('Failed to get error statistics: $e');
    }
  }

  /// Get system health
  Future<Map<String, dynamic>> getSystemHealth() async {
    try {
      final callable = _functions.httpsCallable('getSystemHealthGen2');

      final result = await callable.call();

      return Map<String, dynamic>.from(result.data);
    } on FirebaseFunctionsException catch (e) {
      throw ServerException(
        'Failed to get system health: ${e.message}',
        code: e.code,
      );
    } catch (e) {
      throw ServerException('Failed to get system health: $e');
    }
  }
}
