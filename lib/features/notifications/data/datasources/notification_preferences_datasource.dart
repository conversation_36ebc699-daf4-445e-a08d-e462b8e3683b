import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/notifications/data/models/notification_preferences_model.dart';

/// Data source interface for notification preferences
abstract class NotificationPreferencesDataSource {
  /// Get user notification preferences from Firestore
  Future<NotificationPreferencesModel?> getUserPreferences(String userId);

  /// Save user notification preferences to Firestore
  Future<NotificationPreferencesModel> saveUserPreferences(NotificationPreferencesModel preferences);

  /// Update user notification preferences in Firestore
  Future<NotificationPreferencesModel> updateUserPreferences(NotificationPreferencesModel preferences);

  /// Delete user notification preferences from Firestore
  Future<void> deleteUserPreferences(String userId);

  /// Check if user preferences exist
  Future<bool> userPreferencesExist(String userId);

  /// Stream of preference changes for real-time updates
  Stream<NotificationPreferencesModel?> watchUserPreferences(String userId);
}

/// Firebase implementation of notification preferences data source
class NotificationPreferencesFirebaseDataSource implements NotificationPreferencesDataSource {
  final FirebaseFirestore _firestore;
  static const String _collectionName = 'notification_preferences';

  NotificationPreferencesFirebaseDataSource({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<NotificationPreferencesModel?> getUserPreferences(String userId) async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      return NotificationPreferencesModel.fromFirestore(doc);
    } catch (e) {
      throw Exception('Failed to get user preferences: $e');
    }
  }

  @override
  Future<NotificationPreferencesModel> saveUserPreferences(NotificationPreferencesModel preferences) async {
    try {
      final docRef = _firestore
          .collection(_collectionName)
          .doc(preferences.userId);

      await docRef.set(preferences.toFirestore());

      final savedDoc = await docRef.get();
      return NotificationPreferencesModel.fromFirestore(savedDoc);
    } catch (e) {
      throw Exception('Failed to save user preferences: $e');
    }
  }

  @override
  Future<NotificationPreferencesModel> updateUserPreferences(NotificationPreferencesModel preferences) async {
    try {
      final docRef = _firestore
          .collection(_collectionName)
          .doc(preferences.userId);

      final updatedPreferences = preferences.copyWith(
        updatedAt: DateTime.now(),
      );

      await docRef.update(updatedPreferences.toFirestore());

      final updatedDoc = await docRef.get();
      return NotificationPreferencesModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw Exception('Failed to update user preferences: $e');
    }
  }

  @override
  Future<void> deleteUserPreferences(String userId) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(userId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete user preferences: $e');
    }
  }

  @override
  Future<bool> userPreferencesExist(String userId) async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(userId)
          .get();

      return doc.exists;
    } catch (e) {
      throw Exception('Failed to check if user preferences exist: $e');
    }
  }

  @override
  Stream<NotificationPreferencesModel?> watchUserPreferences(String userId) {
    return _firestore
        .collection(_collectionName)
        .doc(userId)
        .snapshots()
        .map((doc) {
          if (!doc.exists) return null;
          return NotificationPreferencesModel.fromFirestore(doc);
        });
  }
}
