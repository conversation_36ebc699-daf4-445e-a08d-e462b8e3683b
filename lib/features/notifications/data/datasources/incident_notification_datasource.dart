import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/notifications/data/models/incident_notification_model.dart';

/// Data source interface for incident notifications
abstract class IncidentNotificationDataSource {
  /// Save incident notification to Firestore
  Future<IncidentNotificationModel> saveNotification(IncidentNotificationModel notification);

  /// Get incident notifications for a user
  Future<List<IncidentNotificationModel>> getUserNotifications(
    String userId, {
    int? limit,
    DateTime? since,
  });

  /// Check if notification exists for user and incident
  Future<bool> notificationExists(String userId, String incidentId);

  /// Update notification status
  Future<IncidentNotificationModel> updateNotificationStatus(
    String notificationId,
    String status,
  );

  /// Get unread notification count
  Future<int> getUnreadNotificationCount(String userId);

  /// Delete notifications older than specified date
  Future<void> deleteOldNotifications(DateTime olderThan, {String? userId});

  /// Get notification statistics
  Future<Map<String, dynamic>> getNotificationStatistics(String userId);

  /// Stream of new notifications
  Stream<IncidentNotificationModel> watchNewNotifications(String userId);

  /// Stream of unread count changes
  Stream<int> watchUnreadNotificationCount(String userId);
}

/// Firebase implementation of incident notification data source
class IncidentNotificationFirebaseDataSource implements IncidentNotificationDataSource {
  final FirebaseFirestore _firestore;
  static const String _collectionName = 'incident_notifications';

  IncidentNotificationFirebaseDataSource({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<IncidentNotificationModel> saveNotification(IncidentNotificationModel notification) async {
    try {
      final docRef = _firestore.collection(_collectionName).doc();
      final notificationWithId = notification.copyWith(id: docRef.id);
      
      await docRef.set(notificationWithId.toFirestore());
      
      return notificationWithId;
    } catch (e) {
      throw Exception('Failed to save incident notification: $e');
    }
  }

  @override
  Future<List<IncidentNotificationModel>> getUserNotifications(
    String userId, {
    int? limit,
    DateTime? since,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .orderBy('notificationSentAt', descending: true);

      if (since != null) {
        query = query.where('notificationSentAt', isGreaterThan: Timestamp.fromDate(since));
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      
      return querySnapshot.docs
          .map((doc) => IncidentNotificationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user notifications: $e');
    }
  }

  @override
  Future<bool> notificationExists(String userId, String incidentId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .where('incidentId', isEqualTo: incidentId)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      throw Exception('Failed to check if notification exists: $e');
    }
  }

  @override
  Future<IncidentNotificationModel> updateNotificationStatus(
    String notificationId,
    String status,
  ) async {
    try {
      final docRef = _firestore.collection(_collectionName).doc(notificationId);
      
      await docRef.update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await docRef.get();
      return IncidentNotificationModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw Exception('Failed to update notification status: $e');
    }
  }

  @override
  Future<int> getUnreadNotificationCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .where('status', whereIn: ['sent', 'delivered'])
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get unread notification count: $e');
    }
  }

  @override
  Future<void> deleteOldNotifications(DateTime olderThan, {String? userId}) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection(_collectionName)
          .where('notificationSentAt', isLessThan: Timestamp.fromDate(olderThan));

      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      final querySnapshot = await query.get();
      
      // Delete in batches to avoid timeout
      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to delete old notifications: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getNotificationStatistics(String userId) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekAgo = now.subtract(const Duration(days: 7));

      // Get all notifications for user
      final allNotifications = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .get();

      // Get today's notifications
      final todayNotifications = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .where('notificationSentAt', isGreaterThanOrEqualTo: Timestamp.fromDate(today))
          .get();

      // Get this week's notifications
      final weekNotifications = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .where('notificationSentAt', isGreaterThanOrEqualTo: Timestamp.fromDate(weekAgo))
          .get();

      // Get unread notifications
      final unreadNotifications = await _firestore
          .collection(_collectionName)
          .where('userId', isEqualTo: userId)
          .where('status', whereIn: ['sent', 'delivered'])
          .get();

      // Count by type
      final notificationsByType = <String, int>{};
      for (final doc in allNotifications.docs) {
        final data = doc.data();
        final type = data['incidentType'] as String? ?? 'unknown';
        notificationsByType[type] = (notificationsByType[type] ?? 0) + 1;
      }

      // Get last notification time
      DateTime? lastNotificationTime;
      if (allNotifications.docs.isNotEmpty) {
        final lastDoc = allNotifications.docs.first;
        final timestamp = lastDoc.data()['notificationSentAt'] as Timestamp?;
        lastNotificationTime = timestamp?.toDate();
      }

      return {
        'totalNotifications': allNotifications.docs.length,
        'unreadNotifications': unreadNotifications.docs.length,
        'notificationsToday': todayNotifications.docs.length,
        'notificationsThisWeek': weekNotifications.docs.length,
        'notificationsByType': notificationsByType,
        'lastNotificationTime': lastNotificationTime?.toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get notification statistics: $e');
    }
  }

  @override
  Stream<IncidentNotificationModel> watchNewNotifications(String userId) {
    return _firestore
        .collection(_collectionName)
        .where('userId', isEqualTo: userId)
        .orderBy('notificationSentAt', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
          if (snapshot.docs.isEmpty) {
            throw Exception('No notifications found');
          }
          return IncidentNotificationModel.fromFirestore(snapshot.docs.first);
        });
  }

  @override
  Stream<int> watchUnreadNotificationCount(String userId) {
    return _firestore
        .collection(_collectionName)
        .where('userId', isEqualTo: userId)
        .where('status', whereIn: ['sent', 'delivered'])
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }
}
