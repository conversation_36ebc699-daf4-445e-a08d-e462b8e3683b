import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/incidents/data/models/location_model.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';

/// Firestore model for incident notifications
class IncidentNotificationModel extends IncidentNotificationEntity {
  const IncidentNotificationModel({
    required super.id,
    required super.userId,
    required super.incidentId,
    required super.incidentType,
    required super.incidentTitle,
    required super.incidentDescription,
    required super.incidentLocation,
    required super.userLocationAtTime,
    required super.distanceFromUserMeters,
    required super.incidentTimestamp,
    required super.notificationSentAt,
    super.status,
    super.priority,
    super.metadata,
  });

  /// Create from Firestore document
  factory IncidentNotificationModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};
    
    return IncidentNotificationModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      incidentId: data['incidentId'] as String? ?? '',
      incidentType: data['incidentType'] as String? ?? '',
      incidentTitle: data['incidentTitle'] as String? ?? '',
      incidentDescription: data['incidentDescription'] as String? ?? '',
      incidentLocation: LocationModel.fromMap(data['incidentLocation'] as Map<String, dynamic>? ?? {}),
      userLocationAtTime: LocationModel.fromMap(data['userLocationAtTime'] as Map<String, dynamic>? ?? {}),
      distanceFromUserMeters: (data['distanceFromUserMeters'] as num?)?.toDouble() ?? 0.0,
      incidentTimestamp: (data['incidentTimestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notificationSentAt: (data['notificationSentAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: IncidentNotificationStatus.values.firstWhere(
        (status) => status.name == (data['status'] as String? ?? 'sent'),
        orElse: () => IncidentNotificationStatus.sent,
      ),
      priority: IncidentNotificationPriority.values.firstWhere(
        (priority) => priority.name == (data['priority'] as String? ?? 'medium'),
        orElse: () => IncidentNotificationPriority.medium,
      ),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Create from Map
  factory IncidentNotificationModel.fromMap(Map<String, dynamic> map) {
    return IncidentNotificationModel(
      id: map['id'] as String? ?? '',
      userId: map['userId'] as String? ?? '',
      incidentId: map['incidentId'] as String? ?? '',
      incidentType: map['incidentType'] as String? ?? '',
      incidentTitle: map['incidentTitle'] as String? ?? '',
      incidentDescription: map['incidentDescription'] as String? ?? '',
      incidentLocation: LocationModel.fromMap(map['incidentLocation'] as Map<String, dynamic>? ?? {}),
      userLocationAtTime: LocationModel.fromMap(map['userLocationAtTime'] as Map<String, dynamic>? ?? {}),
      distanceFromUserMeters: (map['distanceFromUserMeters'] as num?)?.toDouble() ?? 0.0,
      incidentTimestamp: map['incidentTimestamp'] is DateTime
          ? map['incidentTimestamp'] as DateTime
          : DateTime.parse(map['incidentTimestamp'] as String? ?? DateTime.now().toIso8601String()),
      notificationSentAt: map['notificationSentAt'] is DateTime
          ? map['notificationSentAt'] as DateTime
          : DateTime.parse(map['notificationSentAt'] as String? ?? DateTime.now().toIso8601String()),
      status: IncidentNotificationStatus.values.firstWhere(
        (status) => status.name == (map['status'] as String? ?? 'sent'),
        orElse: () => IncidentNotificationStatus.sent,
      ),
      priority: IncidentNotificationPriority.values.firstWhere(
        (priority) => priority.name == (map['priority'] as String? ?? 'medium'),
        orElse: () => IncidentNotificationPriority.medium,
      ),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Create from entity
  factory IncidentNotificationModel.fromEntity(IncidentNotificationEntity entity) {
    return IncidentNotificationModel(
      id: entity.id,
      userId: entity.userId,
      incidentId: entity.incidentId,
      incidentType: entity.incidentType,
      incidentTitle: entity.incidentTitle,
      incidentDescription: entity.incidentDescription,
      incidentLocation: entity.incidentLocation,
      userLocationAtTime: entity.userLocationAtTime,
      distanceFromUserMeters: entity.distanceFromUserMeters,
      incidentTimestamp: entity.incidentTimestamp,
      notificationSentAt: entity.notificationSentAt,
      status: entity.status,
      priority: entity.priority,
      metadata: entity.metadata,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'incidentId': incidentId,
      'incidentType': incidentType,
      'incidentTitle': incidentTitle,
      'incidentDescription': incidentDescription,
      'incidentLocation': LocationModel.fromEntity(incidentLocation).toMap(),
      'userLocationAtTime': LocationModel.fromEntity(userLocationAtTime).toMap(),
      'distanceFromUserMeters': distanceFromUserMeters,
      'incidentTimestamp': Timestamp.fromDate(incidentTimestamp),
      'notificationSentAt': Timestamp.fromDate(notificationSentAt),
      'status': status.name,
      'priority': priority.name,
      'metadata': metadata,
    };
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'incidentId': incidentId,
      'incidentType': incidentType,
      'incidentTitle': incidentTitle,
      'incidentDescription': incidentDescription,
      'incidentLocation': LocationModel.fromEntity(incidentLocation).toMap(),
      'userLocationAtTime': LocationModel.fromEntity(userLocationAtTime).toMap(),
      'distanceFromUserMeters': distanceFromUserMeters,
      'incidentTimestamp': incidentTimestamp.toIso8601String(),
      'notificationSentAt': notificationSentAt.toIso8601String(),
      'status': status.name,
      'priority': priority.name,
      'metadata': metadata,
    };
  }

  IncidentNotificationModel copyWith({
    String? id,
    String? userId,
    String? incidentId,
    String? incidentType,
    String? incidentTitle,
    String? incidentDescription,
    LocationEntity? incidentLocation,
    LocationEntity? userLocationAtTime,
    double? distanceFromUserMeters,
    DateTime? incidentTimestamp,
    DateTime? notificationSentAt,
    IncidentNotificationStatus? status,
    IncidentNotificationPriority? priority,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentNotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      incidentId: incidentId ?? this.incidentId,
      incidentType: incidentType ?? this.incidentType,
      incidentTitle: incidentTitle ?? this.incidentTitle,
      incidentDescription: incidentDescription ?? this.incidentDescription,
      incidentLocation: incidentLocation ?? this.incidentLocation,
      userLocationAtTime: userLocationAtTime ?? this.userLocationAtTime,
      distanceFromUserMeters: distanceFromUserMeters ?? this.distanceFromUserMeters,
      incidentTimestamp: incidentTimestamp ?? this.incidentTimestamp,
      notificationSentAt: notificationSentAt ?? this.notificationSentAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      metadata: metadata ?? this.metadata,
    );
  }
}
