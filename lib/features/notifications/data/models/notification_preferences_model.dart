import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';

/// Firestore model for notification preferences
class NotificationPreferencesModel extends NotificationPreferencesEntity {
  const NotificationPreferencesModel({
    required super.userId,
    super.isLocationNotificationsEnabled,
    super.notificationRadiusKm,
    super.enabledIncidentTypes,
    super.quietHours,
    super.isLocationTrackingConsented,
    super.locationConsentGrantedAt,
    super.locationAccessDurationHours,
    super.priorityLevel,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create from Firestore document
  factory NotificationPreferencesModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    return NotificationPreferencesModel(
      userId: doc.id,
      isLocationNotificationsEnabled:
          data['isLocationNotificationsEnabled'] as bool? ?? true,
      notificationRadiusKm:
          (data['notificationRadiusKm'] as num?)?.toDouble() ?? 2.0,
      enabledIncidentTypes: List<String>.from(
        data['enabledIncidentTypes'] as List? ?? [],
      ),
      quietHours:
          data['quietHours'] != null
              ? QuietHoursModel.fromMap(
                data['quietHours'] as Map<String, dynamic>,
              )
              : null,
      isLocationTrackingConsented:
          data['isLocationTrackingConsented'] as bool? ?? false,
      locationConsentGrantedAt:
          (data['locationConsentGrantedAt'] as Timestamp?)?.toDate(),
      locationAccessDurationHours:
          (data['locationAccessDurationHours'] as num?)?.toDouble() ?? 24.0,
      priorityLevel: NotificationPriorityLevel.values.firstWhere(
        (level) => level.name == (data['priorityLevel'] as String? ?? 'medium'),
        orElse: () => NotificationPriorityLevel.medium,
      ),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Create from Map
  factory NotificationPreferencesModel.fromMap(Map<String, dynamic> map) {
    return NotificationPreferencesModel(
      userId: map['userId'] as String? ?? '',
      isLocationNotificationsEnabled:
          map['isLocationNotificationsEnabled'] as bool? ?? true,
      notificationRadiusKm:
          (map['notificationRadiusKm'] as num?)?.toDouble() ?? 2.0,
      enabledIncidentTypes: List<String>.from(
        map['enabledIncidentTypes'] as List? ?? [],
      ),
      quietHours:
          map['quietHours'] != null
              ? QuietHoursModel.fromMap(
                map['quietHours'] as Map<String, dynamic>,
              )
              : null,
      isLocationTrackingConsented:
          map['isLocationTrackingConsented'] as bool? ?? false,
      locationConsentGrantedAt:
          map['locationConsentGrantedAt'] is DateTime
              ? map['locationConsentGrantedAt'] as DateTime
              : map['locationConsentGrantedAt'] != null
              ? DateTime.parse(map['locationConsentGrantedAt'] as String)
              : null,
      locationAccessDurationHours:
          (map['locationAccessDurationHours'] as num?)?.toDouble() ?? 24.0,
      priorityLevel: NotificationPriorityLevel.values.firstWhere(
        (level) => level.name == (map['priorityLevel'] as String? ?? 'medium'),
        orElse: () => NotificationPriorityLevel.medium,
      ),
      createdAt:
          map['createdAt'] is DateTime
              ? map['createdAt'] as DateTime
              : DateTime.parse(
                map['createdAt'] as String? ?? DateTime.now().toIso8601String(),
              ),
      updatedAt:
          map['updatedAt'] is DateTime
              ? map['updatedAt'] as DateTime
              : DateTime.parse(
                map['updatedAt'] as String? ?? DateTime.now().toIso8601String(),
              ),
    );
  }

  /// Create from entity
  factory NotificationPreferencesModel.fromEntity(
    NotificationPreferencesEntity entity,
  ) {
    return NotificationPreferencesModel(
      userId: entity.userId,
      isLocationNotificationsEnabled: entity.isLocationNotificationsEnabled,
      notificationRadiusKm: entity.notificationRadiusKm,
      enabledIncidentTypes: entity.enabledIncidentTypes,
      quietHours: entity.quietHours,
      isLocationTrackingConsented: entity.isLocationTrackingConsented,
      locationConsentGrantedAt: entity.locationConsentGrantedAt,
      locationAccessDurationHours: entity.locationAccessDurationHours,
      priorityLevel: entity.priorityLevel,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'isLocationNotificationsEnabled': isLocationNotificationsEnabled,
      'notificationRadiusKm': notificationRadiusKm,
      'enabledIncidentTypes': enabledIncidentTypes,
      'quietHours':
          quietHours != null
              ? QuietHoursModel.fromEntity(quietHours!).toMap()
              : null,
      'isLocationTrackingConsented': isLocationTrackingConsented,
      'locationConsentGrantedAt':
          locationConsentGrantedAt != null
              ? Timestamp.fromDate(locationConsentGrantedAt!)
              : null,
      'locationAccessDurationHours': locationAccessDurationHours,
      'priorityLevel': priorityLevel.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'isLocationNotificationsEnabled': isLocationNotificationsEnabled,
      'notificationRadiusKm': notificationRadiusKm,
      'enabledIncidentTypes': enabledIncidentTypes,
      'quietHours':
          quietHours != null
              ? QuietHoursModel.fromEntity(quietHours!).toMap()
              : null,
      'isLocationTrackingConsented': isLocationTrackingConsented,
      'locationConsentGrantedAt': locationConsentGrantedAt?.toIso8601String(),
      'locationAccessDurationHours': locationAccessDurationHours,
      'priorityLevel': priorityLevel.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  NotificationPreferencesModel copyWith({
    String? userId,
    bool? isLocationNotificationsEnabled,
    double? notificationRadiusKm,
    List<String>? enabledIncidentTypes,
    QuietHoursEntity? quietHours,
    bool? isLocationTrackingConsented,
    DateTime? locationConsentGrantedAt,
    double? locationAccessDurationHours,
    NotificationPriorityLevel? priorityLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationPreferencesModel(
      userId: userId ?? this.userId,
      isLocationNotificationsEnabled:
          isLocationNotificationsEnabled ?? this.isLocationNotificationsEnabled,
      notificationRadiusKm: notificationRadiusKm ?? this.notificationRadiusKm,
      enabledIncidentTypes: enabledIncidentTypes ?? this.enabledIncidentTypes,
      quietHours: quietHours ?? this.quietHours,
      isLocationTrackingConsented:
          isLocationTrackingConsented ?? this.isLocationTrackingConsented,
      locationConsentGrantedAt:
          locationConsentGrantedAt ?? this.locationConsentGrantedAt,
      locationAccessDurationHours:
          locationAccessDurationHours ?? this.locationAccessDurationHours,
      priorityLevel: priorityLevel ?? this.priorityLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Model for quiet hours
class QuietHoursModel extends QuietHoursEntity {
  const QuietHoursModel({
    required super.startTime,
    required super.endTime,
    super.isEnabled,
  });

  factory QuietHoursModel.fromEntity(QuietHoursEntity entity) {
    return QuietHoursModel(
      startTime: entity.startTime,
      endTime: entity.endTime,
      isEnabled: entity.isEnabled,
    );
  }

  factory QuietHoursModel.fromMap(Map<String, dynamic> map) {
    return QuietHoursModel(
      startTime: TimeOfDay(
        hour: map['startHour'] as int? ?? 22,
        minute: map['startMinute'] as int? ?? 0,
      ),
      endTime: TimeOfDay(
        hour: map['endHour'] as int? ?? 6,
        minute: map['endMinute'] as int? ?? 0,
      ),
      isEnabled: map['isEnabled'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'startHour': startTime.hour,
      'startMinute': startTime.minute,
      'endHour': endTime.hour,
      'endMinute': endTime.minute,
      'isEnabled': isEnabled,
    };
  }
}
