import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_preferences_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/models/notification_preferences_model.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';

/// Implementation of notification preferences repository
class NotificationPreferencesRepositoryImpl
    implements NotificationPreferencesRepository {
  final NotificationPreferencesDataSource _dataSource;

  NotificationPreferencesRepositoryImpl({
    required NotificationPreferencesDataSource dataSource,
  }) : _dataSource = dataSource;

  @override
  Future<Either<Failure, NotificationPreferencesEntity>> getUserPreferences(
    String userId,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        // Create initial preferences if they don't exist
        return await createInitialPreferences(userId);
      }

      return Right(preferences);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, NotificationPreferencesEntity>> updateUserPreferences(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      final model = NotificationPreferencesModel.fromEntity(preferences);
      final updatedModel = await _dataSource.updateUserPreferences(model);
      return Right(updatedModel);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update user preferences: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, NotificationPreferencesEntity>>
  createInitialPreferences(String userId) async {
    try {
      final initialPreferences = NotificationPreferencesModel(
        userId: userId,
        isLocationNotificationsEnabled: true,
        notificationRadiusKm: 2.0, // Default 2km radius
        enabledIncidentTypes: [], // Empty means all types enabled
        quietHours: const QuietHoursEntity(
          startTime: TimeOfDay(hour: 22, minute: 0), // 10 PM
          endTime: TimeOfDay(hour: 6, minute: 0), // 6 AM
          isEnabled: false, // Disabled by default
        ),
        isLocationTrackingConsented: false, // Requires explicit consent
        priorityLevel: NotificationPriorityLevel.medium,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedPreferences = await _dataSource.saveUserPreferences(
        initialPreferences,
      );
      return Right(savedPreferences);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to create initial preferences: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> hasLocationTrackingConsent(
    String userId,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);
      return Right(preferences?.isLocationTrackingConsented ?? false);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to check location tracking consent: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> updateLocationTrackingConsent(
    String userId,
    bool hasConsent,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final now = DateTime.now();
      final updatedPreferences = preferences.copyWith(
        isLocationTrackingConsented: hasConsent,
        locationConsentGrantedAt:
            hasConsent ? now : null, // Set timestamp when granting consent
        updatedAt: now,
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to update location tracking consent: $e',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, double>> getNotificationRadius(String userId) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);
      return Right(preferences?.notificationRadiusKm ?? 2.0);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to get notification radius: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> updateNotificationRadius(
    String userId,
    double radiusKm,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final updatedPreferences = preferences.copyWith(
        notificationRadiusKm: radiusKm,
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update notification radius: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, List<String>>> getEnabledIncidentTypes(
    String userId,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);
      return Right(preferences?.enabledIncidentTypes ?? []);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to get enabled incident types: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> updateEnabledIncidentTypes(
    String userId,
    List<String> incidentTypes,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final updatedPreferences = preferences.copyWith(
        enabledIncidentTypes: incidentTypes,
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update enabled incident types: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> areNotificationsEnabled(String userId) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);
      return Right(preferences?.isLocationNotificationsEnabled ?? true);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to check if notifications are enabled: $e',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, void>> setNotificationsEnabled(
    String userId,
    bool enabled,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final updatedPreferences = preferences.copyWith(
        isLocationNotificationsEnabled: enabled,
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to set notifications enabled: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, QuietHoursEntity?>> getQuietHours(
    String userId,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);
      return Right(preferences?.quietHours);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get quiet hours: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateQuietHours(
    String userId,
    QuietHoursEntity? quietHours,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final updatedPreferences = preferences.copyWith(
        quietHours: quietHours,
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update quiet hours: $e'));
    }
  }

  @override
  Stream<Either<Failure, NotificationPreferencesEntity>> watchUserPreferences(
    String userId,
  ) {
    return _dataSource
        .watchUserPreferences(userId)
        .map<Either<Failure, NotificationPreferencesEntity>>((preferences) {
          if (preferences == null) {
            return Left(NotFoundFailure('User preferences not found'));
          }
          return Right(preferences as NotificationPreferencesEntity);
        })
        .handleError((error) {
          return Stream.value(
            Left(
              ServerFailure(
                message: 'Failed to watch user preferences: $error',
              ),
            ),
          );
        })
        .cast<Either<Failure, NotificationPreferencesEntity>>();
  }

  /// Grant location tracking consent with proper timestamp
  Future<Either<Failure, void>> grantLocationTrackingConsent(
    String userId, {
    double? locationAccessDurationHours,
  }) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final now = DateTime.now();
      final updatedPreferences = preferences.copyWith(
        isLocationTrackingConsented: true,
        locationConsentGrantedAt: now,
        locationAccessDurationHours:
            locationAccessDurationHours ??
            preferences.locationAccessDurationHours,
        updatedAt: now,
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to grant location tracking consent: $e'),
      );
    }
  }

  /// Revoke location tracking consent
  Future<Either<Failure, void>> revokeLocationTrackingConsent(
    String userId,
  ) async {
    try {
      final preferences = await _dataSource.getUserPreferences(userId);

      if (preferences == null) {
        return Left(NotFoundFailure('User preferences not found'));
      }

      final updatedPreferences = preferences.copyWith(
        isLocationTrackingConsented: false,
        locationConsentGrantedAt: null, // Clear the consent timestamp
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateUserPreferences(updatedPreferences);
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to revoke location tracking consent: $e',
        ),
      );
    }
  }
}
