import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/incident_notification_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/models/incident_notification_model.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';

/// Implementation of incident notification repository
class IncidentNotificationRepositoryImpl implements IncidentNotificationRepository {
  final IncidentNotificationDataSource _dataSource;

  IncidentNotificationRepositoryImpl({
    required IncidentNotificationDataSource dataSource,
  }) : _dataSource = dataSource;

  @override
  Future<Either<Failure, IncidentNotificationEntity>> saveNotification(
    IncidentNotificationEntity notification,
  ) async {
    try {
      final model = IncidentNotificationModel.fromEntity(notification);
      final savedModel = await _dataSource.saveNotification(model);
      return Right(savedModel);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to save notification: $e'));
    }
  }

  @override
  Future<Either<Failure, List<IncidentNotificationEntity>>> getUserNotifications(
    String userId, {
    int? limit,
    DateTime? since,
  }) async {
    try {
      final models = await _dataSource.getUserNotifications(
        userId,
        limit: limit,
        since: since,
      );
      
      final entities = models.map((model) => model as IncidentNotificationEntity).toList();
      return Right(entities);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user notifications: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasUserBeenNotified(String userId, String incidentId) async {
    try {
      final exists = await _dataSource.notificationExists(userId, incidentId);
      return Right(exists);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check notification existence: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> markNotificationAsRead(String notificationId) async {
    try {
      await _dataSource.updateNotificationStatus(notificationId, 'read');
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to mark notification as read: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> markNotificationAsDismissed(String notificationId) async {
    try {
      await _dataSource.updateNotificationStatus(notificationId, 'dismissed');
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to mark notification as dismissed: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getUnreadNotificationCount(String userId) async {
    try {
      final count = await _dataSource.getUnreadNotificationCount(userId);
      return Right(count);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get unread notification count: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteOldNotifications({
    required DateTime olderThan,
    String? userId,
  }) async {
    try {
      await _dataSource.deleteOldNotifications(olderThan, userId: userId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete old notifications: $e'));
    }
  }

  @override
  Future<Either<Failure, NotificationStatistics>> getNotificationStatistics(String userId) async {
    try {
      final statsData = await _dataSource.getNotificationStatistics(userId);
      
      final statistics = NotificationStatistics(
        totalNotifications: statsData['totalNotifications'] as int? ?? 0,
        unreadNotifications: statsData['unreadNotifications'] as int? ?? 0,
        notificationsToday: statsData['notificationsToday'] as int? ?? 0,
        notificationsThisWeek: statsData['notificationsThisWeek'] as int? ?? 0,
        notificationsByType: Map<String, int>.from(
          statsData['notificationsByType'] as Map<String, dynamic>? ?? {}
        ),
        lastNotificationTime: statsData['lastNotificationTime'] != null
            ? DateTime.parse(statsData['lastNotificationTime'] as String)
            : null,
      );
      
      return Right(statistics);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get notification statistics: $e'));
    }
  }

  @override
  Stream<Either<Failure, IncidentNotificationEntity>> watchNewNotifications(String userId) {
    return _dataSource.watchNewNotifications(userId)
        .map<Either<Failure, IncidentNotificationEntity>>((notification) {
      return Right(notification as IncidentNotificationEntity);
    }).handleError((error) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch notifications: $error')));
    }).cast<Either<Failure, IncidentNotificationEntity>>();
  }

  @override
  Stream<Either<Failure, int>> watchUnreadNotificationCount(String userId) {
    return _dataSource.watchUnreadNotificationCount(userId)
        .map<Either<Failure, int>>((count) {
      return Right(count);
    }).handleError((error) {
      return Stream.value(Left(ServerFailure(message: 'Failed to watch unread count: $error')));
    }).cast<Either<Failure, int>>();
  }
}
