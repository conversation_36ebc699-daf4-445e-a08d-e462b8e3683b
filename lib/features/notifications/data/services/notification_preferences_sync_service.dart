import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_cloud_functions_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/services/location_notification_service.dart';

/// Service for syncing notification preferences with cloud functions
class NotificationPreferencesSyncService {
  final NotificationCloudFunctionsDataSource _cloudFunctionsDataSource;
  final LocationNotificationService _locationService;

  NotificationPreferencesSyncService({
    required NotificationCloudFunctionsDataSource cloudFunctionsDataSource,
    required LocationNotificationService locationService,
  })  : _cloudFunctionsDataSource = cloudFunctionsDataSource,
        _locationService = locationService;

  /// Sync user preferences with cloud functions
  Future<Either<Failure, void>> syncPreferencesWithCloud(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      // If location notifications are enabled, ensure location tracking is active
      if (preferences.isLocationNotificationsEnabled && 
          preferences.isLocationTrackingConsented) {
        
        // Update user location for notifications
        final locationResult = await _locationService.getCurrentLocationAndUpdate();
        if (locationResult.isLeft()) {
          return Left(locationResult.fold((l) => l, (r) => throw Exception()));
        }
      }

      // Additional sync operations can be added here
      // For example, updating user preferences in Firestore
      // or subscribing/unsubscribing from FCM topics

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to sync preferences: $e'));
    }
  }

  /// Update location tracking based on preferences
  Future<Either<Failure, void>> updateLocationTracking(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      if (preferences.isLocationNotificationsEnabled && 
          preferences.isLocationTrackingConsented) {
        
        // Start location tracking
        final trackingResult = await _locationService.startLocationTracking(
          interval: const Duration(minutes: 5),
          distanceFilter: 100, // 100 meters
        );

        if (trackingResult.isLeft()) {
          return Left(trackingResult.fold((l) => l, (r) => throw Exception()));
        }

        // Subscribe to location-based topics if needed
        await _subscribeToLocationTopics(preferences);
      } else {
        // Unsubscribe from location-based topics
        await _unsubscribeFromLocationTopics();
      }

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update location tracking: $e'));
    }
  }

  /// Subscribe to FCM topics based on incident types
  Future<Either<Failure, void>> updateIncidentTypeSubscriptions(
    List<String> enabledIncidentTypes,
  ) async {
    try {
      // Define all possible incident types
      const allIncidentTypes = [
        'robbery',
        'assault',
        'theft',
        'suspicious_activity',
        'vandalism',
        'emergency',
        'accident',
        'fire',
        'medical_emergency',
      ];

      // Subscribe to enabled types
      for (final incidentType in enabledIncidentTypes) {
        // Subscribe to topic (this would need FCM integration)
        // await _firebaseMessaging.subscribeToTopic('incident_$incidentType');
      }

      // Unsubscribe from disabled types
      final disabledTypes = allIncidentTypes
          .where((type) => !enabledIncidentTypes.contains(type))
          .toList();

      for (final incidentType in disabledTypes) {
        // Unsubscribe from topic
        // await _firebaseMessaging.unsubscribeFromTopic('incident_$incidentType');
      }

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update incident subscriptions: $e'));
    }
  }

  /// Update notification radius in cloud functions
  Future<Either<Failure, void>> updateNotificationRadius(
    String userId,
    double radiusKm,
  ) async {
    try {
      // This would update the user's notification radius in Firestore
      // The cloud functions would then use this radius when finding nearby users
      
      // For now, we'll just ensure location is updated
      await _locationService.getCurrentLocationAndUpdate();

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update notification radius: $e'));
    }
  }

  /// Update quiet hours configuration
  Future<Either<Failure, void>> updateQuietHours(
    String userId,
    QuietHoursEntity? quietHours,
  ) async {
    try {
      // This would update the user's quiet hours in Firestore
      // The cloud functions would check this before sending notifications
      
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update quiet hours: $e'));
    }
  }

  /// Validate notification setup
  Future<Either<Failure, NotificationSetupStatus>> validateNotificationSetup(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      final issues = <String>[];
      
      // Check location services
      if (preferences.isLocationNotificationsEnabled) {
        final locationAvailable = await _locationService.isLocationServiceAvailable();
        if (locationAvailable.isLeft() || !locationAvailable.getOrElse(() => false)) {
          issues.add('Location services are not available');
        }
      }

      // Check FCM token
      // This would check if FCM token is valid and registered

      // Check network connectivity
      // This would verify cloud functions are reachable

      final status = NotificationSetupStatus(
        isValid: issues.isEmpty,
        issues: issues,
        lastChecked: DateTime.now(),
      );

      return Right(status);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to validate notification setup: $e'));
    }
  }

  /// Subscribe to location-based topics
  Future<void> _subscribeToLocationTopics(NotificationPreferencesEntity preferences) async {
    // Subscribe to general location-based notifications
    // await _firebaseMessaging.subscribeToTopic('location_notifications');
    
    // Subscribe to radius-specific topics if needed
    final radiusGroup = _getRadiusGroup(preferences.notificationRadiusKm);
    // await _firebaseMessaging.subscribeToTopic('radius_$radiusGroup');
  }

  /// Unsubscribe from location-based topics
  Future<void> _unsubscribeFromLocationTopics() async {
    // Unsubscribe from location-based topics
    // await _firebaseMessaging.unsubscribeFromTopic('location_notifications');
    
    // Unsubscribe from all radius groups
    for (final group in ['1km', '2km', '5km', '10km']) {
      // await _firebaseMessaging.unsubscribeFromTopic('radius_$group');
    }
  }

  /// Get radius group for topic subscription
  String _getRadiusGroup(double radiusKm) {
    if (radiusKm <= 1.0) return '1km';
    if (radiusKm <= 2.0) return '2km';
    if (radiusKm <= 5.0) return '5km';
    return '10km';
  }
}

/// Notification setup validation status
class NotificationSetupStatus {
  final bool isValid;
  final List<String> issues;
  final DateTime lastChecked;

  const NotificationSetupStatus({
    required this.isValid,
    required this.issues,
    required this.lastChecked,
  });

  bool get hasLocationIssues => issues.any((issue) => issue.contains('location'));
  bool get hasPermissionIssues => issues.any((issue) => issue.contains('permission'));
  bool get hasConnectivityIssues => issues.any((issue) => issue.contains('network'));
}
