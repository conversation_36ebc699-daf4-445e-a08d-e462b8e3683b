import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_cloud_functions_datasource.dart';

/// Service for handling location updates for notifications
class LocationNotificationService {
  final NotificationCloudFunctionsDataSource _cloudFunctionsDataSource;

  LocationNotificationService({
    required NotificationCloudFunctionsDataSource cloudFunctionsDataSource,
  }) : _cloudFunctionsDataSource = cloudFunctionsDataSource;

  /// Update user location for notification purposes
  Future<Either<Failure, void>> updateUserLocationForNotifications({
    required double latitude,
    required double longitude,
    double? accuracy,
    String source = 'gps',
  }) async {
    try {
      await _cloudFunctionsDataSource.updateUserLocation(
        latitude: latitude,
        longitude: longitude,
        accuracy: accuracy,
        source: source,
      );

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update location: $e'));
    }
  }

  /// Get current location and update it for notifications
  Future<Either<Failure, Position>> getCurrentLocationAndUpdate() async {
    try {
      // Check location permissions
      final permission = await _checkLocationPermission();
      if (permission.isLeft()) {
        return Left(permission.fold((l) => l, (r) => throw Exception()));
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Update location in cloud functions
      final updateResult = await updateUserLocationForNotifications(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        source: 'gps',
      );

      if (updateResult.isLeft()) {
        return Left(updateResult.fold((l) => l, (r) => throw Exception()));
      }

      return Right(position);
    } catch (e) {
      return Left(
        LocationFailure(message: 'Failed to get current location: $e'),
      );
    }
  }

  /// Start location tracking for notifications
  Future<Either<Failure, Stream<Position>>> startLocationTracking({
    Duration interval = const Duration(minutes: 5),
    double distanceFilter = 100, // meters
  }) async {
    try {
      // Check location permissions
      final permission = await _checkLocationPermission();
      if (permission.isLeft()) {
        return Left(permission.fold((l) => l, (r) => throw Exception()));
      }

      // Create location stream
      final locationStream = Geolocator.getPositionStream(
        locationSettings: LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: distanceFilter.round(),
          timeLimit: const Duration(seconds: 30),
        ),
      );

      // Transform stream to update cloud functions
      final transformedStream = locationStream.asyncMap((position) async {
        // Update location in background
        await updateUserLocationForNotifications(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          source: 'background',
        );

        return position;
      });

      return Right(transformedStream);
    } catch (e) {
      return Left(
        LocationFailure(message: 'Failed to start location tracking: $e'),
      );
    }
  }

  /// Check and request location permissions
  Future<Either<Failure, bool>> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return Left(
          LocationFailure(
            message:
                'Location services are disabled. Please enable location services.',
          ),
        );
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return Left(
            LocationFailure(
              message:
                  'Location permissions are denied. Please grant location access.',
            ),
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return Left(
          LocationFailure(
            message:
                'Location permissions are permanently denied. Please enable in settings.',
          ),
        );
      }

      return const Right(true);
    } catch (e) {
      return Left(
        LocationFailure(message: 'Failed to check location permission: $e'),
      );
    }
  }

  /// Check if location services are available
  Future<Either<Failure, bool>> isLocationServiceAvailable() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      final permission = await Geolocator.checkPermission();

      final isAvailable =
          serviceEnabled &&
          permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever;

      return Right(isAvailable);
    } catch (e) {
      return Left(
        LocationFailure(message: 'Failed to check location service: $e'),
      );
    }
  }

  /// Get last known location
  Future<Either<Failure, Position?>> getLastKnownLocation() async {
    try {
      final position = await Geolocator.getLastKnownPosition();
      return Right(position);
    } catch (e) {
      return Left(
        LocationFailure(message: 'Failed to get last known location: $e'),
      );
    }
  }
}

/// Custom failure for location-related errors
class LocationFailure extends Failure {
  const LocationFailure({required String message}) : super(message: message);
}
