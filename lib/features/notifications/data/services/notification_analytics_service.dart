import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/data/datasources/notification_cloud_functions_datasource.dart';

/// Service for notification analytics and statistics
class NotificationAnalyticsService {
  final NotificationCloudFunctionsDataSource _cloudFunctionsDataSource;

  NotificationAnalyticsService({
    required NotificationCloudFunctionsDataSource cloudFunctionsDataSource,
  }) : _cloudFunctionsDataSource = cloudFunctionsDataSource;

  /// Get notification analytics from cloud functions
  Future<Either<Failure, NotificationAnalytics>> getNotificationAnalytics({
    int timeRangeHours = 24,
  }) async {
    try {
      final data = await _cloudFunctionsDataSource.getNotificationAnalytics(
        timeRangeHours: timeRangeHours,
      );

      final analytics = NotificationAnalytics.fromMap(data);
      return Right(analytics);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get notification analytics: $e'));
    }
  }

  /// Get error statistics from cloud functions
  Future<Either<Failure, ErrorStatistics>> getErrorStatistics({
    int timeRangeHours = 24,
  }) async {
    try {
      final data = await _cloudFunctionsDataSource.getErrorStatistics(
        timeRangeHours: timeRangeHours,
      );

      final errorStats = ErrorStatistics.fromMap(data);
      return Right(errorStats);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get error statistics: $e'));
    }
  }

  /// Get system health status
  Future<Either<Failure, SystemHealth>> getSystemHealth() async {
    try {
      final data = await _cloudFunctionsDataSource.getSystemHealth();
      
      final systemHealth = SystemHealth.fromMap(data);
      return Right(systemHealth);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get system health: $e'));
    }
  }

  /// Send test notification
  Future<Either<Failure, TestNotificationResult>> sendTestNotification({
    required String fcmToken,
  }) async {
    try {
      final data = await _cloudFunctionsDataSource.sendTestNotification(
        fcmToken: fcmToken,
      );

      final result = TestNotificationResult.fromMap(data);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to send test notification: $e'));
    }
  }
}

/// Notification analytics data model
class NotificationAnalytics {
  final int totalNotificationsSent;
  final int successfulDeliveries;
  final int failedDeliveries;
  final double deliveryRate;
  final Map<String, int> notificationsByType;
  final Map<String, int> notificationsByPriority;
  final List<NotificationTrend> trends;
  final DateTime generatedAt;

  const NotificationAnalytics({
    required this.totalNotificationsSent,
    required this.successfulDeliveries,
    required this.failedDeliveries,
    required this.deliveryRate,
    required this.notificationsByType,
    required this.notificationsByPriority,
    required this.trends,
    required this.generatedAt,
  });

  factory NotificationAnalytics.fromMap(Map<String, dynamic> map) {
    return NotificationAnalytics(
      totalNotificationsSent: map['totalNotificationsSent'] as int? ?? 0,
      successfulDeliveries: map['successfulDeliveries'] as int? ?? 0,
      failedDeliveries: map['failedDeliveries'] as int? ?? 0,
      deliveryRate: (map['deliveryRate'] as num?)?.toDouble() ?? 0.0,
      notificationsByType: Map<String, int>.from(
        map['notificationsByType'] as Map<String, dynamic>? ?? {}
      ),
      notificationsByPriority: Map<String, int>.from(
        map['notificationsByPriority'] as Map<String, dynamic>? ?? {}
      ),
      trends: (map['trends'] as List<dynamic>?)
          ?.map((e) => NotificationTrend.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      generatedAt: DateTime.tryParse(map['generatedAt'] as String? ?? '') ?? DateTime.now(),
    );
  }
}

/// Notification trend data
class NotificationTrend {
  final DateTime timestamp;
  final int count;
  final String type;

  const NotificationTrend({
    required this.timestamp,
    required this.count,
    required this.type,
  });

  factory NotificationTrend.fromMap(Map<String, dynamic> map) {
    return NotificationTrend(
      timestamp: DateTime.tryParse(map['timestamp'] as String? ?? '') ?? DateTime.now(),
      count: map['count'] as int? ?? 0,
      type: map['type'] as String? ?? '',
    );
  }
}

/// Error statistics data model
class ErrorStatistics {
  final int totalErrors;
  final Map<String, int> errorsByType;
  final Map<String, int> errorsByFunction;
  final List<ErrorTrend> trends;
  final DateTime generatedAt;

  const ErrorStatistics({
    required this.totalErrors,
    required this.errorsByType,
    required this.errorsByFunction,
    required this.trends,
    required this.generatedAt,
  });

  factory ErrorStatistics.fromMap(Map<String, dynamic> map) {
    return ErrorStatistics(
      totalErrors: map['totalErrors'] as int? ?? 0,
      errorsByType: Map<String, int>.from(
        map['errorsByType'] as Map<String, dynamic>? ?? {}
      ),
      errorsByFunction: Map<String, int>.from(
        map['errorsByFunction'] as Map<String, dynamic>? ?? {}
      ),
      trends: (map['trends'] as List<dynamic>?)
          ?.map((e) => ErrorTrend.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      generatedAt: DateTime.tryParse(map['generatedAt'] as String? ?? '') ?? DateTime.now(),
    );
  }
}

/// Error trend data
class ErrorTrend {
  final DateTime timestamp;
  final int count;
  final String errorType;

  const ErrorTrend({
    required this.timestamp,
    required this.count,
    required this.errorType,
  });

  factory ErrorTrend.fromMap(Map<String, dynamic> map) {
    return ErrorTrend(
      timestamp: DateTime.tryParse(map['timestamp'] as String? ?? '') ?? DateTime.now(),
      count: map['count'] as int? ?? 0,
      errorType: map['errorType'] as String? ?? '',
    );
  }
}

/// System health data model
class SystemHealth {
  final String status;
  final DateTime timestamp;
  final Map<String, String> services;
  final String version;

  const SystemHealth({
    required this.status,
    required this.timestamp,
    required this.services,
    required this.version,
  });

  factory SystemHealth.fromMap(Map<String, dynamic> map) {
    return SystemHealth(
      status: map['status'] as String? ?? 'unknown',
      timestamp: DateTime.tryParse(map['timestamp'] as String? ?? '') ?? DateTime.now(),
      services: Map<String, String>.from(
        map['services'] as Map<String, dynamic>? ?? {}
      ),
      version: map['version'] as String? ?? '1.0.0',
    );
  }

  bool get isHealthy => status == 'healthy';
  bool get isDegraded => status == 'degraded';
  bool get isUnhealthy => status == 'unhealthy';
}

/// Test notification result
class TestNotificationResult {
  final bool success;
  final String? messageId;
  final String? error;

  const TestNotificationResult({
    required this.success,
    this.messageId,
    this.error,
  });

  factory TestNotificationResult.fromMap(Map<String, dynamic> map) {
    return TestNotificationResult(
      success: map['success'] as bool? ?? false,
      messageId: map['messageId'] as String?,
      error: map['error'] as String?,
    );
  }
}
