import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/data/models/incident_notification_model.dart';
import 'package:respublicaseguridad/features/incidents/data/models/location_model.dart';

/// Service for handling real-time notifications from Firebase Cloud Messaging
class RealTimeNotificationService {
  final FirebaseMessaging _firebaseMessaging;
  final FlutterLocalNotificationsPlugin _localNotifications;

  final StreamController<IncidentNotificationEntity> _notificationController =
      StreamController<IncidentNotificationEntity>.broadcast();

  RealTimeNotificationService({
    required FirebaseMessaging firebaseMessaging,
    required FlutterLocalNotificationsPlugin localNotifications,
  }) : _firebaseMessaging = firebaseMessaging,
       _localNotifications = localNotifications;

  /// Stream of incoming incident notifications
  Stream<IncidentNotificationEntity> get notificationStream =>
      _notificationController.stream;

  /// Initialize the real-time notification service
  Future<void> initialize() async {
    // Initialize local notifications
    await _initializeLocalNotifications();

    // Request notification permissions
    await _requestPermissions();

    // Configure FCM
    await _configureFCM();

    // Set up message handlers
    _setupMessageHandlers();
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(initSettings);

    // Create notification channel for Android
    if (Platform.isAndroid) {
      const channel = AndroidNotificationChannel(
        'incident_alerts',
        'Security Incident Alerts',
        description: 'Notifications for security incidents in your area',
        importance: Importance.high,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: true,
      provisional: false,
      sound: true,
    );

    print('Notification permission status: ${settings.authorizationStatus}');
  }

  /// Configure Firebase Cloud Messaging
  Future<void> _configureFCM() async {
    // Configure foreground notification presentation options
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Get FCM token
    final token = await _firebaseMessaging.getToken();
    print('FCM Token: $token');
  }

  /// Set up message handlers for different states
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle messages when app is in background but not terminated
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // Handle messages when app is terminated
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
  }

  /// Handle messages when app is in foreground
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Received foreground message: ${message.messageId}');

    final notification = _parseNotificationFromMessage(message);
    if (notification != null) {
      // Add to stream for real-time updates
      _notificationController.add(notification);

      // Show local notification
      await _showLocalNotification(message);
    }
  }

  /// Handle messages when app is opened from background
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background message: ${message.messageId}');

    final notification = _parseNotificationFromMessage(message);
    if (notification != null) {
      // Add to stream for real-time updates
      _notificationController.add(notification);
    }
  }

  /// Parse incident notification from FCM message
  IncidentNotificationEntity? _parseNotificationFromMessage(
    RemoteMessage message,
  ) {
    try {
      final data = message.data;

      // Check if this is an incident notification
      if (data['type'] != 'incident_alert') {
        return null;
      }

      // Parse the notification data
      final incidentNotification = IncidentNotificationModel(
        id: data['notificationId'] ?? message.messageId ?? '',
        userId: data['userId'] ?? '',
        incidentId: data['incidentId'] ?? '',
        incidentType: data['incidentType'] ?? '',
        incidentTitle: message.notification?.title ?? data['title'] ?? '',
        incidentDescription:
            message.notification?.body ?? data['description'] ?? '',
        incidentLocation: LocationModel(
          latitude: double.tryParse(data['incidentLatitude'] ?? '0') ?? 0.0,
          longitude: double.tryParse(data['incidentLongitude'] ?? '0') ?? 0.0,
          address: data['incidentAddress'] ?? '',
        ),
        userLocationAtTime: LocationModel(
          latitude: double.tryParse(data['userLatitude'] ?? '0') ?? 0.0,
          longitude: double.tryParse(data['userLongitude'] ?? '0') ?? 0.0,
          address: '',
        ),
        distanceFromUserMeters: double.tryParse(data['distance'] ?? '0') ?? 0.0,
        incidentTimestamp:
            DateTime.tryParse(data['incidentTimestamp'] ?? '') ??
            DateTime.now(),
        notificationSentAt:
            DateTime.tryParse(data['timestamp'] ?? '') ?? DateTime.now(),
        status: IncidentNotificationStatus.delivered,
        priority: _parsePriority(data['priority'] ?? 'medium'),
        metadata: data,
      );

      return incidentNotification;
    } catch (e) {
      print('Error parsing notification from message: $e');
      return null;
    }
  }

  /// Parse priority from string
  IncidentNotificationPriority _parsePriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'critical':
        return IncidentNotificationPriority.critical;
      case 'high':
        return IncidentNotificationPriority.high;
      case 'medium':
        return IncidentNotificationPriority.medium;
      case 'low':
        return IncidentNotificationPriority.low;
      default:
        return IncidentNotificationPriority.medium;
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'incident_alerts',
      'Security Incident Alerts',
      channelDescription: 'Notifications for security incidents in your area',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'default',
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Security Alert',
      message.notification?.body ?? 'New incident reported in your area',
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  /// Get FCM token
  Future<String?> getFCMToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _notificationController.close();
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
  // Handle background message processing here
}
