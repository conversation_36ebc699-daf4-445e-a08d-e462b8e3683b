import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/data/services/real_time_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/location_notification_service.dart'
    as location_service;
import 'package:respublicaseguridad/features/notifications/data/services/notification_analytics_service.dart';
import 'package:respublicaseguridad/features/notifications/data/services/notification_preferences_sync_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';

// Re-export commonly used classes for convenience
export 'package:respublicaseguridad/features/notifications/data/services/notification_analytics_service.dart'
    show NotificationAnalytics, SystemHealth, TestNotificationResult;
export 'package:respublicaseguridad/features/notifications/data/services/notification_preferences_sync_service.dart'
    show NotificationSetupStatus;
export 'package:geolocator/geolocator.dart' show Position;

/// Main integration service that coordinates all notification functionality
class NotificationIntegrationService {
  final RealTimeNotificationService _realTimeService;
  final location_service.LocationNotificationService _locationService;
  final NotificationAnalyticsService _analyticsService;
  final NotificationPreferencesSyncService _preferencesSync;
  final IncidentNotificationRepository _notificationRepository;

  StreamSubscription<IncidentNotificationEntity>? _notificationSubscription;
  StreamSubscription<Position>? _locationSubscription;

  NotificationIntegrationService({
    required RealTimeNotificationService realTimeService,
    required location_service.LocationNotificationService locationService,
    required NotificationAnalyticsService analyticsService,
    required NotificationPreferencesSyncService preferencesSync,
    required IncidentNotificationRepository notificationRepository,
  }) : _realTimeService = realTimeService,
       _locationService = locationService,
       _analyticsService = analyticsService,
       _preferencesSync = preferencesSync,
       _notificationRepository = notificationRepository;

  /// Initialize the complete notification system
  Future<Either<Failure, void>> initialize(String userId) async {
    try {
      // Initialize real-time notifications
      await _realTimeService.initialize();

      // Set up notification stream listener
      _setupNotificationListener();

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to initialize notification system: $e'),
      );
    }
  }

  /// Start comprehensive notification system with user preferences
  Future<Either<Failure, void>> startNotificationSystem(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      // Sync preferences with cloud functions
      final syncResult = await _preferencesSync.syncPreferencesWithCloud(
        preferences,
      );
      if (syncResult.isLeft()) {
        return Left(syncResult.fold((l) => l, (r) => throw Exception()));
      }

      // Start location tracking if enabled
      if (preferences.isLocationNotificationsEnabled &&
          preferences.isLocationTrackingConsented) {
        final locationResult = await _startLocationTracking(preferences);
        if (locationResult.isLeft()) {
          return Left(locationResult.fold((l) => l, (r) => throw Exception()));
        }
      }

      // Update incident type subscriptions
      await _preferencesSync.updateIncidentTypeSubscriptions(
        preferences.enabledIncidentTypes,
      );

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to start notification system: $e'),
      );
    }
  }

  /// Stop notification system
  Future<Either<Failure, void>> stopNotificationSystem() async {
    try {
      // Cancel subscriptions
      await _notificationSubscription?.cancel();
      await _locationSubscription?.cancel();

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to stop notification system: $e'),
      );
    }
  }

  /// Update notification preferences and sync with cloud
  Future<Either<Failure, void>> updatePreferences(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      // Sync with cloud functions
      final syncResult = await _preferencesSync.syncPreferencesWithCloud(
        preferences,
      );
      if (syncResult.isLeft()) {
        return Left(syncResult.fold((l) => l, (r) => throw Exception()));
      }

      // Update location tracking
      final locationResult = await _preferencesSync.updateLocationTracking(
        preferences,
      );
      if (locationResult.isLeft()) {
        return Left(locationResult.fold((l) => l, (r) => throw Exception()));
      }

      // Update incident subscriptions
      await _preferencesSync.updateIncidentTypeSubscriptions(
        preferences.enabledIncidentTypes,
      );

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update preferences: $e'));
    }
  }

  /// Send test notification
  Future<Either<Failure, TestNotificationResult>> sendTestNotification() async {
    try {
      // Get FCM token
      final token = await _realTimeService.getFCMToken();
      if (token == null) {
        return Left(ServerFailure(message: 'FCM token not available'));
      }

      // Send test notification via cloud function
      final result = await _analyticsService.sendTestNotification(
        fcmToken: token,
      );
      return result;
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to send test notification: $e'),
      );
    }
  }

  /// Get notification analytics
  Future<Either<Failure, NotificationAnalytics>> getAnalytics({
    int timeRangeHours = 24,
  }) async {
    return await _analyticsService.getNotificationAnalytics(
      timeRangeHours: timeRangeHours,
    );
  }

  /// Get system health status
  Future<Either<Failure, SystemHealth>> getSystemHealth() async {
    return await _analyticsService.getSystemHealth();
  }

  /// Validate notification setup
  Future<Either<Failure, NotificationSetupStatus>> validateSetup(
    NotificationPreferencesEntity preferences,
  ) async {
    return await _preferencesSync.validateNotificationSetup(preferences);
  }

  /// Get real-time notification stream
  Stream<IncidentNotificationEntity> get notificationStream =>
      _realTimeService.notificationStream;

  /// Set up notification listener to save incoming notifications
  void _setupNotificationListener() {
    _notificationSubscription = _realTimeService.notificationStream.listen(
      (notification) async {
        // Save notification to local database
        await _notificationRepository.saveNotification(notification);
      },
      onError: (error) {
        print('Error in notification stream: $error');
      },
    );
  }

  /// Start location tracking
  Future<Either<Failure, void>> _startLocationTracking(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      final trackingResult = await _locationService.startLocationTracking(
        interval: const Duration(minutes: 5),
        distanceFilter: 100, // 100 meters
      );

      if (trackingResult.isLeft()) {
        return Left(trackingResult.fold((l) => l, (r) => throw Exception()));
      }

      // Set up location stream listener
      final locationStream = trackingResult.getOrElse(() => throw Exception());
      _locationSubscription = locationStream.listen(
        (position) {
          print(
            'Location updated: ${position.latitude}, ${position.longitude}',
          );
        },
        onError: (error) {
          print('Error in location stream: $error');
        },
      );

      return const Right(null);
    } catch (e) {
      return Left(
        location_service.LocationFailure(
          message: 'Failed to start location tracking: $e',
        ),
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _notificationSubscription?.cancel();
    _locationSubscription?.cancel();
    _realTimeService.dispose();
  }
}

/// Integration status
class NotificationIntegrationStatus {
  final bool isInitialized;
  final bool isLocationTrackingActive;
  final bool isRealTimeActive;
  final DateTime? lastLocationUpdate;
  final DateTime? lastNotificationReceived;
  final List<String> activeSubscriptions;

  const NotificationIntegrationStatus({
    required this.isInitialized,
    required this.isLocationTrackingActive,
    required this.isRealTimeActive,
    this.lastLocationUpdate,
    this.lastNotificationReceived,
    this.activeSubscriptions = const [],
  });

  bool get isFullyActive =>
      isInitialized && isLocationTrackingActive && isRealTimeActive;
}
