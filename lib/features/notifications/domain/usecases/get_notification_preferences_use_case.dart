import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';

/// Use case for getting user notification preferences
class GetNotificationPreferencesUseCase implements UseCase<NotificationPreferencesEntity, String> {
  final NotificationPreferencesRepository _repository;

  GetNotificationPreferencesUseCase(this._repository);

  @override
  Future<Either<Failure, NotificationPreferencesEntity>> call(String userId) async {
    return await _repository.getUserPreferences(userId);
  }
}

/// Use case for updating user notification preferences
class UpdateNotificationPreferencesUseCase implements UseCase<NotificationPreferencesEntity, UpdateNotificationPreferencesParams> {
  final NotificationPreferencesRepository _repository;

  UpdateNotificationPreferencesUseCase(this._repository);

  @override
  Future<Either<Failure, NotificationPreferencesEntity>> call(UpdateNotificationPreferencesParams params) async {
    return await _repository.updateUserPreferences(params.preferences);
  }
}

/// Use case for checking location tracking consent
class CheckLocationTrackingConsentUseCase implements UseCase<bool, String> {
  final NotificationPreferencesRepository _repository;

  CheckLocationTrackingConsentUseCase(this._repository);

  @override
  Future<Either<Failure, bool>> call(String userId) async {
    return await _repository.hasLocationTrackingConsent(userId);
  }
}

/// Use case for updating location tracking consent
class UpdateLocationTrackingConsentUseCase implements UseCase<void, UpdateLocationTrackingConsentParams> {
  final NotificationPreferencesRepository _repository;

  UpdateLocationTrackingConsentUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(UpdateLocationTrackingConsentParams params) async {
    return await _repository.updateLocationTrackingConsent(params.userId, params.hasConsent);
  }
}

/// Use case for updating notification radius
class UpdateNotificationRadiusUseCase implements UseCase<void, UpdateNotificationRadiusParams> {
  final NotificationPreferencesRepository _repository;

  UpdateNotificationRadiusUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(UpdateNotificationRadiusParams params) async {
    // Validate radius (between 0.5km and 10km)
    if (params.radiusKm < 0.5 || params.radiusKm > 10.0) {
      return Left(ValidationFailure('Notification radius must be between 0.5km and 10km'));
    }

    return await _repository.updateNotificationRadius(params.userId, params.radiusKm);
  }
}

/// Use case for updating enabled incident types
class UpdateEnabledIncidentTypesUseCase implements UseCase<void, UpdateEnabledIncidentTypesParams> {
  final NotificationPreferencesRepository _repository;

  UpdateEnabledIncidentTypesUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(UpdateEnabledIncidentTypesParams params) async {
    return await _repository.updateEnabledIncidentTypes(params.userId, params.incidentTypes);
  }
}

/// Use case for updating quiet hours
class UpdateQuietHoursUseCase implements UseCase<void, UpdateQuietHoursParams> {
  final NotificationPreferencesRepository _repository;

  UpdateQuietHoursUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(UpdateQuietHoursParams params) async {
    return await _repository.updateQuietHours(params.userId, params.quietHours);
  }
}

/// Use case for enabling/disabling notifications
class SetNotificationsEnabledUseCase implements UseCase<void, SetNotificationsEnabledParams> {
  final NotificationPreferencesRepository _repository;

  SetNotificationsEnabledUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(SetNotificationsEnabledParams params) async {
    return await _repository.setNotificationsEnabled(params.userId, params.enabled);
  }
}

// Parameter classes
class UpdateNotificationPreferencesParams {
  final NotificationPreferencesEntity preferences;

  UpdateNotificationPreferencesParams({required this.preferences});
}

class UpdateLocationTrackingConsentParams {
  final String userId;
  final bool hasConsent;

  UpdateLocationTrackingConsentParams({
    required this.userId,
    required this.hasConsent,
  });
}

class UpdateNotificationRadiusParams {
  final String userId;
  final double radiusKm;

  UpdateNotificationRadiusParams({
    required this.userId,
    required this.radiusKm,
  });
}

class UpdateEnabledIncidentTypesParams {
  final String userId;
  final List<String> incidentTypes;

  UpdateEnabledIncidentTypesParams({
    required this.userId,
    required this.incidentTypes,
  });
}

class UpdateQuietHoursParams {
  final String userId;
  final QuietHoursEntity? quietHours;

  UpdateQuietHoursParams({
    required this.userId,
    this.quietHours,
  });
}

class SetNotificationsEnabledParams {
  final String userId;
  final bool enabled;

  SetNotificationsEnabledParams({
    required this.userId,
    required this.enabled,
  });
}
