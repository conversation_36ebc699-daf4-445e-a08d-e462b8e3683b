import 'package:equatable/equatable.dart';

/// Entity representing user notification preferences for incident alerts
class NotificationPreferencesEntity extends Equatable {
  final String userId;
  final bool isLocationNotificationsEnabled;
  final double notificationRadiusKm;
  final List<String> enabledIncidentTypes;
  final QuietHoursEntity? quietHours;
  final bool isLocationTrackingConsented;
  final DateTime? locationConsentGrantedAt;
  final double locationAccessDurationHours; // Duration in hours (max 24)
  final NotificationPriorityLevel priorityLevel;
  final DateTime createdAt;
  final DateTime updatedAt;

  const NotificationPreferencesEntity({
    required this.userId,
    this.isLocationNotificationsEnabled = true,
    this.notificationRadiusKm = 2.0, // Default 2km radius
    this.enabledIncidentTypes = const [],
    this.quietHours,
    this.isLocationTrackingConsented = false,
    this.locationConsentGrantedAt,
    this.locationAccessDurationHours = 24.0, // Default 24 hours
    this.priorityLevel = NotificationPriorityLevel.medium,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if notifications should be sent based on current time and quiet hours
  bool shouldSendNotificationNow() {
    if (quietHours == null) return true;

    final now = DateTime.now();
    final currentTime = TimeOfDay(hour: now.hour, minute: now.minute);

    return !quietHours!.isInQuietPeriod(currentTime);
  }

  /// Check if a specific incident type should trigger notifications
  bool shouldNotifyForIncidentType(String incidentType) {
    if (!isLocationNotificationsEnabled) return false;
    if (enabledIncidentTypes.isEmpty)
      return true; // If no filter, notify for all
    return enabledIncidentTypes.contains(incidentType);
  }

  /// Get notification radius in meters
  double get notificationRadiusMeters => notificationRadiusKm * 1000;

  NotificationPreferencesEntity copyWith({
    String? userId,
    bool? isLocationNotificationsEnabled,
    double? notificationRadiusKm,
    List<String>? enabledIncidentTypes,
    QuietHoursEntity? quietHours,
    bool? isLocationTrackingConsented,
    DateTime? locationConsentGrantedAt,
    double? locationAccessDurationHours,
    NotificationPriorityLevel? priorityLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationPreferencesEntity(
      userId: userId ?? this.userId,
      isLocationNotificationsEnabled:
          isLocationNotificationsEnabled ?? this.isLocationNotificationsEnabled,
      notificationRadiusKm: notificationRadiusKm ?? this.notificationRadiusKm,
      enabledIncidentTypes: enabledIncidentTypes ?? this.enabledIncidentTypes,
      quietHours: quietHours ?? this.quietHours,
      isLocationTrackingConsented:
          isLocationTrackingConsented ?? this.isLocationTrackingConsented,
      locationConsentGrantedAt:
          locationConsentGrantedAt ?? this.locationConsentGrantedAt,
      locationAccessDurationHours:
          locationAccessDurationHours ?? this.locationAccessDurationHours,
      priorityLevel: priorityLevel ?? this.priorityLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    userId,
    isLocationNotificationsEnabled,
    notificationRadiusKm,
    enabledIncidentTypes,
    quietHours,
    isLocationTrackingConsented,
    locationConsentGrantedAt,
    locationAccessDurationHours,
    priorityLevel,
    createdAt,
    updatedAt,
  ];

  static final empty = NotificationPreferencesEntity(
    userId: '',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  bool get isEmpty => this == NotificationPreferencesEntity.empty;
  bool get isNotEmpty => this != NotificationPreferencesEntity.empty;
}

/// Entity representing quiet hours configuration
class QuietHoursEntity extends Equatable {
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final bool isEnabled;

  const QuietHoursEntity({
    required this.startTime,
    required this.endTime,
    this.isEnabled = true,
  });

  /// Check if the given time falls within quiet hours
  bool isInQuietPeriod(TimeOfDay currentTime) {
    if (!isEnabled) return false;

    final currentMinutes = currentTime.hour * 60 + currentTime.minute;
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;

    // Handle overnight quiet hours (e.g., 22:00 to 06:00)
    if (startMinutes > endMinutes) {
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }

    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }

  @override
  List<Object?> get props => [startTime, endTime, isEnabled];
}

/// Time of day representation for quiet hours
class TimeOfDay extends Equatable {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  @override
  List<Object?> get props => [hour, minute];

  @override
  String toString() =>
      '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
}

/// Notification priority levels
enum NotificationPriorityLevel {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case NotificationPriorityLevel.low:
        return 'Low';
      case NotificationPriorityLevel.medium:
        return 'Medium';
      case NotificationPriorityLevel.high:
        return 'High';
      case NotificationPriorityLevel.critical:
        return 'Critical';
    }
  }
}
