import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';

/// Entity representing an incident notification sent to a user
class IncidentNotificationEntity extends Equatable {
  final String id;
  final String userId;
  final String incidentId;
  final String incidentType;
  final String incidentTitle;
  final String incidentDescription;
  final LocationEntity incidentLocation;
  final LocationEntity userLocationAtTime;
  final double distanceFromUserMeters;
  final DateTime incidentTimestamp;
  final DateTime notificationSentAt;
  final IncidentNotificationStatus status;
  final IncidentNotificationPriority priority;
  final Map<String, dynamic>? metadata;

  const IncidentNotificationEntity({
    required this.id,
    required this.userId,
    required this.incidentId,
    required this.incidentType,
    required this.incidentTitle,
    required this.incidentDescription,
    required this.incidentLocation,
    required this.userLocationAtTime,
    required this.distanceFromUserMeters,
    required this.incidentTimestamp,
    required this.notificationSentAt,
    this.status = IncidentNotificationStatus.sent,
    this.priority = IncidentNotificationPriority.medium,
    this.metadata,
  });

  /// Get formatted distance string for display
  String get formattedDistance {
    if (distanceFromUserMeters < 1000) {
      return '${distanceFromUserMeters.round()}m away';
    } else {
      final km = distanceFromUserMeters / 1000;
      return '${km.toStringAsFixed(1)}km away';
    }
  }

  /// Get notification title for display
  String get notificationTitle {
    return '🚨 ${incidentType.toUpperCase()} Alert';
  }

  /// Get notification body for display
  String get notificationBody {
    return '$incidentTitle - $formattedDistance';
  }

  /// Get detailed notification content
  String get detailedContent {
    return '''
$incidentTitle

Type: $incidentType
Distance: $formattedDistance
Time: ${_formatTimestamp(incidentTimestamp)}

$incidentDescription
''';
  }

  /// Check if notification is recent (within last 24 hours)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(notificationSentAt);
    return difference.inHours < 24;
  }

  /// Check if incident is still active (within last 6 hours)
  bool get isIncidentActive {
    final now = DateTime.now();
    final difference = now.difference(incidentTimestamp);
    return difference.inHours < 6;
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  IncidentNotificationEntity copyWith({
    String? id,
    String? userId,
    String? incidentId,
    String? incidentType,
    String? incidentTitle,
    String? incidentDescription,
    LocationEntity? incidentLocation,
    LocationEntity? userLocationAtTime,
    double? distanceFromUserMeters,
    DateTime? incidentTimestamp,
    DateTime? notificationSentAt,
    IncidentNotificationStatus? status,
    IncidentNotificationPriority? priority,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentNotificationEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      incidentId: incidentId ?? this.incidentId,
      incidentType: incidentType ?? this.incidentType,
      incidentTitle: incidentTitle ?? this.incidentTitle,
      incidentDescription: incidentDescription ?? this.incidentDescription,
      incidentLocation: incidentLocation ?? this.incidentLocation,
      userLocationAtTime: userLocationAtTime ?? this.userLocationAtTime,
      distanceFromUserMeters: distanceFromUserMeters ?? this.distanceFromUserMeters,
      incidentTimestamp: incidentTimestamp ?? this.incidentTimestamp,
      notificationSentAt: notificationSentAt ?? this.notificationSentAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        incidentId,
        incidentType,
        incidentTitle,
        incidentDescription,
        incidentLocation,
        userLocationAtTime,
        distanceFromUserMeters,
        incidentTimestamp,
        notificationSentAt,
        status,
        priority,
        metadata,
      ];

  static final empty = IncidentNotificationEntity(
    id: '',
    userId: '',
    incidentId: '',
    incidentType: '',
    incidentTitle: '',
    incidentDescription: '',
    incidentLocation: LocationEntity.empty,
    userLocationAtTime: LocationEntity.empty,
    distanceFromUserMeters: 0.0,
    incidentTimestamp: DateTime.now(),
    notificationSentAt: DateTime.now(),
  );

  bool get isEmpty => this == IncidentNotificationEntity.empty;
  bool get isNotEmpty => this != IncidentNotificationEntity.empty;
}

/// Status of an incident notification
enum IncidentNotificationStatus {
  sent,
  delivered,
  read,
  dismissed,
  failed;

  String get displayName {
    switch (this) {
      case IncidentNotificationStatus.sent:
        return 'Sent';
      case IncidentNotificationStatus.delivered:
        return 'Delivered';
      case IncidentNotificationStatus.read:
        return 'Read';
      case IncidentNotificationStatus.dismissed:
        return 'Dismissed';
      case IncidentNotificationStatus.failed:
        return 'Failed';
    }
  }
}

/// Priority levels for incident notifications
enum IncidentNotificationPriority {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case IncidentNotificationPriority.low:
        return 'Low';
      case IncidentNotificationPriority.medium:
        return 'Medium';
      case IncidentNotificationPriority.high:
        return 'High';
      case IncidentNotificationPriority.critical:
        return 'Critical';
    }
  }

  /// Get notification importance level for Android
  int get androidImportance {
    switch (this) {
      case IncidentNotificationPriority.low:
        return 2; // IMPORTANCE_LOW
      case IncidentNotificationPriority.medium:
        return 3; // IMPORTANCE_DEFAULT
      case IncidentNotificationPriority.high:
        return 4; // IMPORTANCE_HIGH
      case IncidentNotificationPriority.critical:
        return 5; // IMPORTANCE_MAX
    }
  }
}
