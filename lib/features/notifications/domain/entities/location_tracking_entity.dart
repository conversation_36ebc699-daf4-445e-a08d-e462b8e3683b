import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';

/// Entity representing user location tracking for incident notifications
class LocationTrackingEntity extends Equatable {
  final String userId;
  final LocationEntity currentLocation;
  final DateTime lastLocationUpdate;
  final bool isTrackingEnabled;
  final LocationTrackingAccuracy accuracy;
  final double batteryOptimizationLevel;
  final List<LocationHistoryEntry> recentLocations;
  final Map<String, dynamic>? metadata;

  const LocationTrackingEntity({
    required this.userId,
    required this.currentLocation,
    required this.lastLocationUpdate,
    this.isTrackingEnabled = true,
    this.accuracy = LocationTrackingAccuracy.balanced,
    this.batteryOptimizationLevel = 0.5, // 0.0 = max accuracy, 1.0 = max battery saving
    this.recentLocations = const [],
    this.metadata,
  });

  /// Check if location data is fresh enough for notifications
  bool get isLocationFresh {
    final now = DateTime.now();
    final difference = now.difference(lastLocationUpdate);
    
    // Location should be updated within the last 10 minutes for notifications
    return difference.inMinutes <= 10;
  }

  /// Check if user has moved significantly since last update
  bool hasMovedSignificantly(LocationEntity newLocation, {double thresholdMeters = 100.0}) {
    return currentLocation.distanceTo(newLocation) > thresholdMeters;
  }

  /// Get recommended update interval based on battery optimization level
  Duration get recommendedUpdateInterval {
    if (batteryOptimizationLevel <= 0.3) {
      return const Duration(minutes: 2); // High accuracy
    } else if (batteryOptimizationLevel <= 0.7) {
      return const Duration(minutes: 5); // Balanced
    } else {
      return const Duration(minutes: 10); // Battery saving
    }
  }

  /// Get location accuracy threshold in meters
  double get accuracyThresholdMeters {
    switch (accuracy) {
      case LocationTrackingAccuracy.high:
        return 10.0;
      case LocationTrackingAccuracy.balanced:
        return 25.0;
      case LocationTrackingAccuracy.low:
        return 50.0;
    }
  }

  /// Add a new location to history
  LocationTrackingEntity addLocationToHistory(LocationEntity location) {
    final newEntry = LocationHistoryEntry(
      location: location,
      timestamp: DateTime.now(),
    );

    // Keep only last 10 locations to manage memory
    final updatedHistory = [newEntry, ...recentLocations].take(10).toList();

    return copyWith(
      currentLocation: location,
      lastLocationUpdate: DateTime.now(),
      recentLocations: updatedHistory,
    );
  }

  /// Get average location from recent history for better accuracy
  LocationEntity? get averageRecentLocation {
    if (recentLocations.isEmpty) return null;

    final recentEntries = recentLocations
        .where((entry) => DateTime.now().difference(entry.timestamp).inMinutes <= 5)
        .toList();

    if (recentEntries.isEmpty) return currentLocation;

    final avgLat = recentEntries.map((e) => e.location.latitude).reduce((a, b) => a + b) / recentEntries.length;
    final avgLng = recentEntries.map((e) => e.location.longitude).reduce((a, b) => a + b) / recentEntries.length;

    return LocationEntity(
      latitude: avgLat,
      longitude: avgLng,
      address: currentLocation.address, // Use current address
    );
  }

  LocationTrackingEntity copyWith({
    String? userId,
    LocationEntity? currentLocation,
    DateTime? lastLocationUpdate,
    bool? isTrackingEnabled,
    LocationTrackingAccuracy? accuracy,
    double? batteryOptimizationLevel,
    List<LocationHistoryEntry>? recentLocations,
    Map<String, dynamic>? metadata,
  }) {
    return LocationTrackingEntity(
      userId: userId ?? this.userId,
      currentLocation: currentLocation ?? this.currentLocation,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
      isTrackingEnabled: isTrackingEnabled ?? this.isTrackingEnabled,
      accuracy: accuracy ?? this.accuracy,
      batteryOptimizationLevel: batteryOptimizationLevel ?? this.batteryOptimizationLevel,
      recentLocations: recentLocations ?? this.recentLocations,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        currentLocation,
        lastLocationUpdate,
        isTrackingEnabled,
        accuracy,
        batteryOptimizationLevel,
        recentLocations,
        metadata,
      ];

  static final empty = LocationTrackingEntity(
    userId: '',
    currentLocation: LocationEntity.empty,
    lastLocationUpdate: DateTime.now(),
  );

  bool get isEmpty => this == LocationTrackingEntity.empty;
  bool get isNotEmpty => this != LocationTrackingEntity.empty;
}

/// Entry in location history
class LocationHistoryEntry extends Equatable {
  final LocationEntity location;
  final DateTime timestamp;

  const LocationHistoryEntry({
    required this.location,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [location, timestamp];
}

/// Location tracking accuracy levels
enum LocationTrackingAccuracy {
  high,    // GPS + Network, frequent updates, higher battery usage
  balanced, // GPS + Network, moderate updates, balanced battery usage
  low;     // Network only, infrequent updates, lower battery usage

  String get displayName {
    switch (this) {
      case LocationTrackingAccuracy.high:
        return 'High Accuracy';
      case LocationTrackingAccuracy.balanced:
        return 'Balanced';
      case LocationTrackingAccuracy.low:
        return 'Battery Saving';
    }
  }

  String get description {
    switch (this) {
      case LocationTrackingAccuracy.high:
        return 'Most accurate location tracking with higher battery usage';
      case LocationTrackingAccuracy.balanced:
        return 'Good accuracy with moderate battery usage';
      case LocationTrackingAccuracy.low:
        return 'Basic location tracking with minimal battery usage';
    }
  }
}
