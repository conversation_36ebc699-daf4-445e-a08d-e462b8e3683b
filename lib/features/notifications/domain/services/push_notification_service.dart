import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';

import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:dartz/dartz.dart';

/// Service for handling push notifications with user preferences integration
class PushNotificationService {
  final NotificationService _notificationService;
  final NotificationPreferencesService _preferencesService;
  final FirebaseMessaging _firebaseMessaging;
  final FirebaseFirestore _firestore;

  PushNotificationService({
    required NotificationService notificationService,
    required NotificationPreferencesService preferencesService,
    required FirebaseMessaging firebaseMessaging,
    FirebaseFirestore? firestore,
  }) : _notificationService = notificationService,
       _preferencesService = preferencesService,
       _firebaseMessaging = firebaseMessaging,
       _firestore = firestore ?? FirebaseFirestore.instance;

  /// Initialize push notification service
  Future<Either<Failure, void>> initialize() async {
    try {
      // Initialize the base notification service
      final initialized = await _notificationService.initialize();
      if (!initialized) {
        return Left(
          ServerFailure(message: 'Failed to initialize notification service'),
        );
      }

      // Set up message handlers
      _setupMessageHandlers();

      // Request notification permissions
      await _requestNotificationPermissions();

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to initialize push notifications: $e'),
      );
    }
  }

  /// Request notification permissions from user
  Future<Either<Failure, bool>> _requestNotificationPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: false,
        announcement: false,
      );

      final isAuthorized =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;

      if (kDebugMode) {
        print(
          '🔔 Push notification permission: ${settings.authorizationStatus}',
        );
      }

      return Right(isAuthorized);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to request notification permissions: $e',
        ),
      );
    }
  }

  /// Set up Firebase message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessageTap);

    // Handle app launch from terminated state
    _firebaseMessaging.getInitialMessage().then((message) {
      if (message != null) {
        _handleBackgroundMessageTap(message);
      }
    });
  }

  /// Handle foreground Firebase messages
  void _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('🔔 Received foreground message: ${message.messageId}');
      print('🔔 Message data: ${message.data}');
    }

    // Check if this is an incident notification
    if (message.data['type'] == 'incident_alert') {
      await _processIncidentNotification(message);
    } else {
      // Handle other notification types
      await _processGeneralNotification(message);
    }
  }

  /// Handle background message tap
  void _handleBackgroundMessageTap(RemoteMessage message) {
    if (kDebugMode) {
      print('🔔 Background message tapped: ${message.messageId}');
    }

    // Navigate to appropriate screen based on notification type
    _navigateToNotificationContent(message);
  }

  /// Process incident notification with user preferences validation
  Future<void> _processIncidentNotification(RemoteMessage message) async {
    try {
      final data = message.data;
      final incidentType = data['incidentType'] ?? '';
      final incidentId = data['incidentId'] ?? '';
      final distance = double.tryParse(data['distance'] ?? '0') ?? 0.0;

      // Get current user ID (you'll need to implement this based on your auth system)
      final userId = await _getCurrentUserId();
      if (userId == null) return;

      // Check user preferences
      final shouldNotifyResult = await _preferencesService
          .shouldReceiveNotifications(userId, incidentType);

      final shouldNotify = shouldNotifyResult.getOrElse(() => false);
      if (!shouldNotify) {
        if (kDebugMode) {
          print('🔔 Notification blocked by user preferences');
        }
        return;
      }

      // Get user preferences for priority determination
      final preferencesResult = await _preferencesService.getUserPreferences(
        userId,
      );
      final preferences = preferencesResult.fold(
        (failure) => null,
        (prefs) => prefs,
      );

      // Determine notification priority based on user settings and distance
      final priority = _determinePriority(incidentType, distance, preferences);

      // Show local notification
      await _notificationService.showIncidentNotification(
        incidentId: incidentId,
        incidentType: incidentType,
        title: message.notification?.title ?? '🚨 Security Alert',
        description:
            message.notification?.body ?? 'Security incident in your area',
        distanceMeters: distance,
        priority: priority,
        metadata: data,
      );
    } catch (e) {
      if (kDebugMode) {
        print('🔔 Error processing incident notification: $e');
      }
    }
  }

  /// Process general notification
  Future<void> _processGeneralNotification(RemoteMessage message) async {
    await _notificationService.showGeneralNotification(
      title: message.notification?.title ?? 'Notification',
      body: message.notification?.body ?? '',
      payload: jsonEncode(message.data),
    );
  }

  /// Navigate to appropriate content based on notification
  void _navigateToNotificationContent(RemoteMessage message) {
    // TODO: Implement navigation logic based on your app's routing system
    // This would typically use GoRouter or your navigation service

    final notificationType = message.data['type'];
    switch (notificationType) {
      case 'incident_alert':
        // Navigate to incident details or map view
        break;
      case 'zone_validation':
        // Navigate to zone validation screen
        break;
      default:
        // Navigate to notifications list or home
        break;
    }
  }

  /// Determine notification priority based on incident type, distance, and user preferences
  IncidentNotificationPriority _determinePriority(
    String incidentType,
    double distanceMeters,
    NotificationPreferencesEntity? preferences,
  ) {
    // Base priority from user preferences
    final userPriorityLevel =
        preferences?.priorityLevel ?? NotificationPriorityLevel.medium;

    // Critical incident types always get high priority
    final criticalTypes = ['emergency', 'fire', 'medical_emergency', 'assault'];
    if (criticalTypes.contains(incidentType.toLowerCase())) {
      return IncidentNotificationPriority.critical;
    }

    // High priority for close incidents
    if (distanceMeters < 500) {
      // Within 500m
      return IncidentNotificationPriority.high;
    }

    // Map user priority level to incident priority
    switch (userPriorityLevel) {
      case NotificationPriorityLevel.low:
        return IncidentNotificationPriority.low;
      case NotificationPriorityLevel.medium:
        return distanceMeters < 1000
            ? IncidentNotificationPriority.medium
            : IncidentNotificationPriority.low;
      case NotificationPriorityLevel.high:
        return distanceMeters < 2000
            ? IncidentNotificationPriority.high
            : IncidentNotificationPriority.medium;
      case NotificationPriorityLevel.critical:
        return IncidentNotificationPriority.high;
    }
  }

  /// Get current user ID from Firebase Auth
  Future<String?> _getCurrentUserId() async {
    try {
      final user = firebase_auth.FirebaseAuth.instance.currentUser;
      return user?.uid;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current user ID: $e');
      }
      return null;
    }
  }

  /// Get FCM token for current user
  Future<Either<Failure, String?>> getFCMToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      return Right(token);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get FCM token: $e'));
    }
  }

  /// Subscribe to topic for incident notifications
  Future<Either<Failure, void>> subscribeToIncidentNotifications() async {
    try {
      await _firebaseMessaging.subscribeToTopic('incident_alerts');
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to subscribe to incident notifications: $e',
        ),
      );
    }
  }

  /// Unsubscribe from incident notifications
  Future<Either<Failure, void>> unsubscribeFromIncidentNotifications() async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic('incident_alerts');
      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to unsubscribe from incident notifications: $e',
        ),
      );
    }
  }

  /// Update FCM token in user preferences
  Future<Either<Failure, void>> updateFCMToken(String userId) async {
    try {
      final tokenResult = await getFCMToken();
      return tokenResult.fold((failure) => Left(failure), (token) async {
        if (token != null) {
          // Store FCM token in notification_preferences collection
          await _firestore
              .collection('notification_preferences')
              .doc(userId)
              .set({
                'fcmToken': token,
                'fcmTokenUpdatedAt': FieldValue.serverTimestamp(),
              }, SetOptions(merge: true));

          if (kDebugMode) {
            print(
              '✅ FCM token updated for user $userId: ${token.substring(0, 20)}...',
            );
          }

          return const Right(null);
        } else {
          return Left(ServerFailure(message: 'FCM token is null'));
        }
      });
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update FCM token: $e'));
    }
  }

  /// Check if notifications are enabled at system level
  Future<Either<Failure, bool>> areNotificationsEnabled() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();
      final isEnabled =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
      return Right(isEnabled);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to check notification settings: $e'),
      );
    }
  }

  /// Dispose resources
  void dispose() {
    // Clean up any subscriptions or resources
  }
}
