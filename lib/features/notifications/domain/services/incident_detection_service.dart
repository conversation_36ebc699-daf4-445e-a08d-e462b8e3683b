import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';

/// Service for detecting incidents near user's location and triggering notifications
class IncidentDetectionService {
  final LocationService _locationService;
  final IncidentRepository _incidentRepository;
  final NotificationService _notificationService;
  final NotificationPreferencesService _preferencesService;

  StreamSubscription<Position>? _locationSubscription;
  Timer? _detectionTimer;
  bool _isMonitoring = false;
  String? _currentUserId;

  IncidentDetectionService({
    required LocationService locationService,
    required IncidentRepository incidentRepository,
    required NotificationService notificationService,
    required NotificationPreferencesService preferencesService,
  })  : _locationService = locationService,
        _incidentRepository = incidentRepository,
        _notificationService = notificationService,
        _preferencesService = preferencesService;

  /// Start monitoring for nearby incidents
  Future<Either<Failure, void>> startMonitoring(String userId) async {
    if (_isMonitoring) {
      return const Right(null);
    }

    _currentUserId = userId;

    final consentResult = await _preferencesService.requestLocationTrackingConsent(userId);
    final hasConsent = consentResult.fold((failure) => false, (consent) => consent);

    if (!hasConsent) {
      return Left(ValidationFailure('Location tracking consent required'));
    }

    try {
      // Start location monitoring
      final locationStarted = await _locationService.startLocationMonitoring(
        interval: const Duration(minutes: 2), // Check every 2 minutes
        distanceFilter: 100.0, // Update when user moves 100m
      );

      if (!locationStarted) {
        return Left(ServerFailure(message: 'Failed to start location monitoring'));
      }

      // Listen to location updates
      _locationSubscription = _locationService.locationStream?.listen(
        (position) => _handleLocationUpdate(userId, position),
        onError: (error) => _handleLocationError(error),
      );

      // Set up periodic incident detection
      _detectionTimer = Timer.periodic(
        const Duration(minutes: 5), // Check for new incidents every 5 minutes
        (_) => _performIncidentDetection(userId),
      );

      _isMonitoring = true;
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to start incident monitoring: $e'));
    }
  }

  /// Stop monitoring for nearby incidents
  Future<void> stopMonitoring() async {
    _locationSubscription?.cancel();
    _detectionTimer?.cancel();
    await _locationService.stopLocationMonitoring();
    
    _isMonitoring = false;
    _currentUserId = null;
  }

  /// Check for incidents near a specific location
  Future<Either<Failure, List<IncidentEntity>>> checkIncidentsNearLocation({
    required String userId,
    required LocationEntity location,
    double? customRadiusKm,
  }) async {
    try {
      // Get user's notification radius preference
      final preferencesResult = await _preferencesService.getUserPreferences(userId);
      
      double radiusKm = customRadiusKm ?? 2.0;

      if (customRadiusKm == null) {
        radiusKm = preferencesResult.fold(
          (failure) => 2.0, // Default 2km
          (preferences) => preferences.notificationRadiusKm,
        );
      }

      final radiusMeters = radiusKm * 1000;

      // Get incidents near location
      final incidentsResult = await _incidentRepository.getIncidentsNearLocation(
        latitude: location.latitude,
        longitude: location.longitude,
        radiusInMeters: radiusMeters,
        userId: userId,
      );

      return incidentsResult.fold(
        (failure) => Left(failure),
        (incidents) {
          // Filter incidents based on user preferences
          return Right(_filterIncidentsByPreferences(incidents, userId));
        },
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check incidents near location: $e'));
    }
  }

  /// Manually trigger incident detection for current location
  Future<Either<Failure, List<IncidentEntity>>> triggerIncidentDetection(String userId) async {
    if (!_isMonitoring) {
      return Left(ValidationFailure('Incident monitoring is not active'));
    }

    return await _performIncidentDetection(userId);
  }

  /// Check if monitoring is active
  bool get isMonitoring => _isMonitoring;

  /// Get current monitoring status
  IncidentMonitoringStatus get monitoringStatus {
    if (!_isMonitoring) {
      return IncidentMonitoringStatus.stopped;
    }

    final lastPosition = _locationService.lastKnownPosition;
    if (lastPosition == null) {
      return IncidentMonitoringStatus.waitingForLocation;
    }

    final locationAge = DateTime.now().difference(lastPosition.timestamp);
    if (locationAge.inMinutes > 10) {
      return IncidentMonitoringStatus.staleLocation;
    }

    return IncidentMonitoringStatus.active;
  }

  /// Handle location updates
  void _handleLocationUpdate(String userId, Position position) async {
    // Convert position to LocationEntity
    final location = LocationEntity(
      latitude: position.latitude,
      longitude: position.longitude,
      address: '', // Address will be resolved if needed
    );

    // Check for nearby incidents
    await _performIncidentDetectionForLocation(userId, location);
  }

  /// Handle location errors
  void _handleLocationError(dynamic error) {
    // Log error and potentially notify user
    print('Location error in incident detection: $error');
  }

  /// Perform incident detection for current location
  Future<Either<Failure, List<IncidentEntity>>> _performIncidentDetection(String userId) async {
    final currentPosition = _locationService.lastKnownPosition;
    if (currentPosition == null) {
      return Left(ValidationFailure('No current location available'));
    }

    final location = LocationEntity(
      latitude: currentPosition.latitude,
      longitude: currentPosition.longitude,
      address: '',
    );

    return await _performIncidentDetectionForLocation(userId, location);
  }

  /// Perform incident detection for specific location
  Future<Either<Failure, List<IncidentEntity>>> _performIncidentDetectionForLocation(
    String userId,
    LocationEntity location,
  ) async {
    final incidentsResult = await checkIncidentsNearLocation(
      userId: userId,
      location: location,
    );

    return incidentsResult.fold(
      (failure) => Left(failure),
      (incidents) async {
        // Filter for recent incidents that haven't been notified about
        final newIncidents = _filterNewIncidents(incidents);

        // Send notifications for new incidents that haven't been notified about
        for (final incident in newIncidents) {
          final alreadyNotified = await _hasUserBeenNotified(userId, incident.incidentId);
          if (!alreadyNotified) {
            await _sendIncidentNotification(userId, incident, location);
          }
        }

        return Right(newIncidents);
      },
    );
  }

  /// Filter incidents based on user preferences
  List<IncidentEntity> _filterIncidentsByPreferences(List<IncidentEntity> incidents, String userId) {
    // This would integrate with user preferences
    // For now, return all incidents
    return incidents;
  }

  /// Filter for new incidents that haven't been notified about
  List<IncidentEntity> _filterNewIncidents(List<IncidentEntity> incidents) {
    final now = DateTime.now();

    return incidents.where((incident) {
      // Only consider incidents from the last 6 hours
      final incidentAge = now.difference(incident.postedAt);
      return incidentAge.inHours <= 6;
    }).toList();
  }

  /// Check if user has already been notified about an incident
  Future<bool> _hasUserBeenNotified(String userId, String incidentId) async {
    // This would check the incident notification repository
    // For now, return false to allow notifications
    // In a complete implementation, this would check the database
    return false;
  }

  /// Send notification for an incident
  Future<void> _sendIncidentNotification(
    String userId,
    IncidentEntity incident,
    LocationEntity userLocation,
  ) async {
    try {
      // Calculate distance
      final distance = userLocation.distanceTo(incident.location);

      // Determine priority based on incident type and distance
      final priority = _determinePriority(incident, distance);

      // Send notification
      await _notificationService.showIncidentNotification(
        incidentId: incident.incidentId,
        incidentType: incident.categoryKey,
        title: incident.title,
        description: incident.description,
        distanceMeters: distance,
        priority: priority,
        metadata: {
          'userId': userId,
          'incidentTimestamp': incident.postedAt.toIso8601String(),
          'userLocation': userLocation.toMap(),
        },
      );
    } catch (e) {
      print('Failed to send incident notification: $e');
    }
  }

  /// Determine notification priority based on incident and distance
  IncidentNotificationPriority _determinePriority(IncidentEntity incident, double distanceMeters) {
    // Critical incidents within 500m
    if (distanceMeters <= 500 && _isCriticalIncident(incident.categoryKey)) {
      return IncidentNotificationPriority.critical;
    }

    // High priority incidents within 1km
    if (distanceMeters <= 1000 && _isHighPriorityIncident(incident.categoryKey)) {
      return IncidentNotificationPriority.high;
    }

    // Medium priority for most incidents within 2km
    if (distanceMeters <= 2000) {
      return IncidentNotificationPriority.medium;
    }

    // Low priority for distant incidents
    return IncidentNotificationPriority.low;
  }

  /// Check if incident type is critical
  bool _isCriticalIncident(String incidentType) {
    const criticalTypes = ['emergency', 'assault', 'fire', 'medical_emergency'];
    return criticalTypes.contains(incidentType.toLowerCase());
  }

  /// Check if incident type is high priority
  bool _isHighPriorityIncident(String incidentType) {
    const highPriorityTypes = ['robbery', 'theft', 'suspicious_activity'];
    return highPriorityTypes.contains(incidentType.toLowerCase());
  }
}

/// Status of incident monitoring
enum IncidentMonitoringStatus {
  stopped,
  waitingForLocation,
  staleLocation,
  active;

  String get displayName {
    switch (this) {
      case IncidentMonitoringStatus.stopped:
        return 'Stopped';
      case IncidentMonitoringStatus.waitingForLocation:
        return 'Waiting for Location';
      case IncidentMonitoringStatus.staleLocation:
        return 'Stale Location';
      case IncidentMonitoringStatus.active:
        return 'Active';
    }
  }
}
