import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';

/// Service for handling privacy and security features for location-based notifications
class PrivacySecurityService {
  static const String _encryptionKey = 'notification_location_key';
  static const double _locationFuzzingRadius = 50.0; // meters
  
  /// Encrypt location data before storage
  Either<Failure, String> encryptLocationData(LocationEntity location) {
    try {
      final locationJson = {
        'latitude': location.latitude,
        'longitude': location.longitude,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      final jsonString = jsonEncode(locationJson);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);
      
      // Simple encryption using base64 encoding with salt
      final salt = _generateSalt();
      final encryptedData = base64Encode(bytes);
      final secureData = '$salt:$encryptedData';
      
      return Right(secureData);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to encrypt location data: $e'));
    }
  }

  /// Decrypt location data after retrieval
  Either<Failure, LocationEntity> decryptLocationData(String encryptedData) {
    try {
      final parts = encryptedData.split(':');
      if (parts.length != 2) {
        return Left(ValidationFailure('Invalid encrypted data format'));
      }
      
      final salt = parts[0];
      final encryptedContent = parts[1];
      
      final decodedBytes = base64Decode(encryptedContent);
      final jsonString = utf8.decode(decodedBytes);
      final locationData = jsonDecode(jsonString) as Map<String, dynamic>;
      
      return Right(LocationEntity(
        latitude: locationData['latitude'] as double,
        longitude: locationData['longitude'] as double,
        address: '', // Address not stored for privacy
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to decrypt location data: $e'));
    }
  }

  /// Apply location fuzzing for privacy protection
  LocationEntity applyLocationFuzzing(LocationEntity location) {
    final random = Random.secure();
    
    // Add random offset within fuzzing radius
    final offsetDistance = random.nextDouble() * _locationFuzzingRadius;
    final offsetAngle = random.nextDouble() * 2 * pi;
    
    // Convert to lat/lng offset
    final latOffset = offsetDistance * cos(offsetAngle) / 111320; // meters to degrees
    final lngOffset = offsetDistance * sin(offsetAngle) / (111320 * cos(location.latitude * pi / 180));
    
    return LocationEntity(
      latitude: location.latitude + latOffset,
      longitude: location.longitude + lngOffset,
      address: location.address,
    );
  }

  /// Validate user consent for location tracking
  /// Consent expires after 24 hours and must be explicitly granted
  Either<Failure, bool> validateLocationConsent(String userId, Map<String, dynamic> consentData) {
    try {
      final hasConsent = consentData['hasConsent'] as bool? ?? false;
      final consentTimestamp = consentData['timestamp'] as String?;

      if (!hasConsent) {
        return const Right(false);
      }

      if (consentTimestamp == null) {
        return Left(ValidationFailure('Consent timestamp missing'));
      }

      final consentDate = DateTime.parse(consentTimestamp);
      final now = DateTime.now();
      final hoursSinceConsent = now.difference(consentDate).inHours;

      if (hoursSinceConsent >= 24) {
        return Left(ValidationFailure('Location consent expired after 24 hours'));
      }

      return const Right(true);
    } catch (e) {
      return Left(ValidationFailure('Invalid consent data: $e'));
    }
  }

  /// Create consent record with proper metadata
  Map<String, dynamic> createConsentRecord(String userId, bool hasConsent) {
    final now = DateTime.now();
    return {
      'userId': userId,
      'hasConsent': hasConsent,
      'timestamp': now.toIso8601String(),
      'expiresAt': now.add(const Duration(hours: 24)).toIso8601String(),
      'version': _getCurrentConsentVersion(),
    };
  }

  /// Check if consent has expired
  bool isConsentExpired(Map<String, dynamic> consentData) {
    final hasConsent = consentData['hasConsent'] as bool? ?? false;
    if (!hasConsent) return true;

    final consentTimestamp = consentData['timestamp'] as String?;
    if (consentTimestamp == null) return true;

    final consentDate = DateTime.parse(consentTimestamp);
    final hoursSinceConsent = DateTime.now().difference(consentDate).inHours;

    return hoursSinceConsent >= 24;
  }

  /// Get remaining consent hours
  int getRemainingConsentHours(Map<String, dynamic> consentData) {
    final hasConsent = consentData['hasConsent'] as bool? ?? false;
    if (!hasConsent) return 0;

    final consentTimestamp = consentData['timestamp'] as String?;
    if (consentTimestamp == null) return 0;

    final consentDate = DateTime.parse(consentTimestamp);
    final hoursSinceConsent = DateTime.now().difference(consentDate).inHours;
    final remainingHours = 24 - hoursSinceConsent;

    return remainingHours > 0 ? remainingHours : 0;
  }

  /// Anonymize user data for analytics
  Map<String, dynamic> anonymizeUserData(String userId, Map<String, dynamic> userData) {
    final hashedUserId = _hashUserId(userId);
    
    return {
      'anonymousId': hashedUserId,
      'notificationCount': userData['notificationCount'],
      'averageRadius': userData['averageRadius'],
      'enabledTypesCount': userData['enabledTypesCount'],
      'hasQuietHours': userData['hasQuietHours'],
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Validate data retention compliance
  Either<Failure, bool> validateDataRetention(DateTime dataTimestamp) {
    final now = DateTime.now();
    final hoursOld = now.difference(dataTimestamp).inHours;

    if (hoursOld >= 24) {
      return Left(ValidationFailure('Location data exceeds 24-hour retention policy'));
    }

    return const Right(true);
  }

  /// Generate audit log entry
  Map<String, dynamic> createAuditLogEntry({
    required String userId,
    required String action,
    required String resource,
    Map<String, dynamic>? metadata,
  }) {
    return {
      'userId': _hashUserId(userId),
      'action': action,
      'resource': resource,
      'timestamp': DateTime.now().toIso8601String(),
      'metadata': metadata ?? {},
      'sessionId': _generateSessionId(),
    };
  }

  /// Validate notification frequency to prevent spam
  Either<Failure, bool> validateNotificationFrequency(
    String userId,
    List<DateTime> recentNotifications,
  ) {
    try {
      final now = DateTime.now();
      final oneHourAgo = now.subtract(const Duration(hours: 1));
      
      // Count notifications in the last hour
      final recentCount = recentNotifications
          .where((timestamp) => timestamp.isAfter(oneHourAgo))
          .length;
      
      const maxNotificationsPerHour = 10;
      
      if (recentCount >= maxNotificationsPerHour) {
        return Left(ValidationFailure('Notification frequency limit exceeded'));
      }
      
      return const Right(true);
    } catch (e) {
      return Left(ValidationFailure('Failed to validate notification frequency: $e'));
    }
  }

  /// Sanitize location data for storage
  LocationEntity sanitizeLocationData(LocationEntity location) {
    // Round coordinates to reduce precision for privacy
    final sanitizedLat = double.parse(location.latitude.toStringAsFixed(6));
    final sanitizedLng = double.parse(location.longitude.toStringAsFixed(6));
    
    return LocationEntity(
      latitude: sanitizedLat,
      longitude: sanitizedLng,
      address: '', // Remove address for privacy
    );
  }

  /// Generate secure random salt
  String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Hash user ID for anonymization
  String _hashUserId(String userId) {
    final bytes = utf8.encode(userId + _encryptionKey);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16); // Use first 16 chars
  }

  /// Hash IP address for audit trail
  String _hashIpAddress(String ipAddress) {
    final bytes = utf8.encode(ipAddress + _encryptionKey);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 12); // Use first 12 chars
  }

  /// Generate session ID
  String _generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(8, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Get current consent version
  String _getCurrentConsentVersion() {
    return '1.0'; // Update when privacy policy changes
  }

  /// Check if location is in sensitive area (e.g., government buildings, hospitals)
  bool isLocationSensitive(LocationEntity location) {
    // This would integrate with a database of sensitive locations
    // For now, return false
    return false;
  }

  /// Apply differential privacy to location data
  LocationEntity applyDifferentialPrivacy(LocationEntity location, double epsilon) {
    final random = Random.secure();
    
    // Add Laplace noise for differential privacy
    final scale = 1.0 / epsilon;
    final latNoise = _generateLaplaceNoise(scale);
    final lngNoise = _generateLaplaceNoise(scale);
    
    // Convert noise to coordinate offset (very small for privacy)
    final latOffset = latNoise / 111320; // meters to degrees
    final lngOffset = lngNoise / (111320 * cos(location.latitude * pi / 180));
    
    return LocationEntity(
      latitude: location.latitude + latOffset,
      longitude: location.longitude + lngOffset,
      address: location.address,
    );
  }

  /// Generate Laplace noise for differential privacy
  double _generateLaplaceNoise(double scale) {
    final random = Random.secure();
    final u = random.nextDouble() - 0.5;
    return -scale * (u >= 0 ? 1 : -1) * log(1 - 2 * (u >= 0 ? u : -u));
  }
}
