import 'dart:async';
import 'dart:math';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';

/// Service for optimizing notification system performance
class PerformanceOptimizationService {
  final Map<String, LocationCache> _locationCache = {};
  final Map<String, NotificationBatch> _notificationBatches = {};
  final List<PerformanceMetric> _metrics = [];
  Timer? _batchProcessingTimer;
  Timer? _cacheCleanupTimer;

  static const Duration _batchInterval = Duration(seconds: 30);
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static const int _maxCacheSize = 1000;
  static const int _maxBatchSize = 50;

  PerformanceOptimizationService() {
    _startBatchProcessing();
    _startCacheCleanup();
  }

  /// Cache location data for efficient access
  void cacheLocation(String userId, LocationEntity location) {
    // Implement LRU cache eviction if needed
    if (_locationCache.length >= _maxCacheSize) {
      _evictOldestCacheEntry();
    }

    _locationCache[userId] = LocationCache(
      location: location,
      timestamp: DateTime.now(),
    );
  }

  /// Get cached location if available and fresh
  LocationEntity? getCachedLocation(String userId) {
    final cached = _locationCache[userId];
    if (cached == null) return null;

    final age = DateTime.now().difference(cached.timestamp);
    if (age > _cacheExpiry) {
      _locationCache.remove(userId);
      return null;
    }

    return cached.location;
  }

  /// Add notification to batch for efficient processing
  void addToBatch(String userId, NotificationData notification) {
    final batch = _notificationBatches[userId] ?? NotificationBatch(userId: userId);
    
    if (batch.notifications.length >= _maxBatchSize) {
      // Process immediately if batch is full
      _processBatch(batch);
      _notificationBatches[userId] = NotificationBatch(userId: userId);
    } else {
      batch.notifications.add(notification);
      _notificationBatches[userId] = batch;
    }
  }

  /// Optimize location update frequency based on user activity
  Duration getOptimalLocationUpdateInterval({
    required String userId,
    required double batteryLevel,
    required bool isMoving,
    required DateTime lastIncidentTime,
  }) {
    // Base interval
    Duration interval = const Duration(minutes: 5);

    // Adjust for battery level
    if (batteryLevel < 0.2) {
      interval = Duration(minutes: (interval.inMinutes * 2).round());
    } else if (batteryLevel > 0.8) {
      interval = Duration(minutes: (interval.inMinutes * 0.7).round());
    }

    // Adjust for movement
    if (isMoving) {
      interval = Duration(minutes: (interval.inMinutes * 0.5).round());
    } else {
      interval = Duration(minutes: (interval.inMinutes * 1.5).round());
    }

    // Adjust for recent incidents
    final timeSinceLastIncident = DateTime.now().difference(lastIncidentTime);
    if (timeSinceLastIncident.inHours < 2) {
      interval = Duration(minutes: (interval.inMinutes * 0.6).round());
    }

    // Ensure minimum and maximum bounds
    interval = Duration(
      minutes: max(1, min(30, interval.inMinutes)),
    );

    return interval;
  }

  /// Calculate optimal notification radius based on area density
  double getOptimalNotificationRadius({
    required LocationEntity location,
    required double userPreferredRadius,
    required int incidentDensity,
    required int userDensity,
  }) {
    double radius = userPreferredRadius;

    // Adjust for incident density
    if (incidentDensity > 10) {
      radius = min(radius, userPreferredRadius * 0.7); // Reduce in high-incident areas
    } else if (incidentDensity < 2) {
      radius = min(10.0, userPreferredRadius * 1.3); // Increase in low-incident areas
    }

    // Adjust for user density
    if (userDensity > 100) {
      radius = max(0.5, radius * 0.8); // Reduce in crowded areas
    } else if (userDensity < 10) {
      radius = min(10.0, radius * 1.2); // Increase in sparse areas
    }

    return radius;
  }

  /// Compress location data for efficient transmission
  Map<String, dynamic> compressLocationData(LocationEntity location) {
    return {
      'lat': _roundToDecimalPlaces(location.latitude, 6),
      'lng': _roundToDecimalPlaces(location.longitude, 6),
      'ts': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Decompress location data
  LocationEntity decompressLocationData(Map<String, dynamic> compressed) {
    return LocationEntity(
      latitude: compressed['lat'] as double,
      longitude: compressed['lng'] as double,
      address: '', // Address not included in compressed data
    );
  }

  /// Record performance metric
  void recordMetric(PerformanceMetricType type, double value, {Map<String, dynamic>? metadata}) {
    final metric = PerformanceMetric(
      type: type,
      value: value,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );

    _metrics.add(metric);

    // Keep only last 1000 metrics
    if (_metrics.length > 1000) {
      _metrics.removeAt(0);
    }
  }

  /// Get performance statistics
  PerformanceStatistics getPerformanceStatistics() {
    final now = DateTime.now();
    final lastHour = now.subtract(const Duration(hours: 1));
    
    final recentMetrics = _metrics.where((m) => m.timestamp.isAfter(lastHour)).toList();
    
    return PerformanceStatistics(
      totalMetrics: _metrics.length,
      recentMetrics: recentMetrics.length,
      averageResponseTime: _calculateAverageMetric(recentMetrics, PerformanceMetricType.responseTime),
      averageBatteryUsage: _calculateAverageMetric(recentMetrics, PerformanceMetricType.batteryUsage),
      averageMemoryUsage: _calculateAverageMetric(recentMetrics, PerformanceMetricType.memoryUsage),
      cacheHitRate: _calculateCacheHitRate(),
      batchProcessingEfficiency: _calculateBatchEfficiency(),
    );
  }

  /// Optimize database queries for geospatial operations
  Map<String, dynamic> optimizeGeospatialQuery({
    required LocationEntity center,
    required double radiusKm,
    required int limit,
  }) {
    // Calculate bounding box for efficient querying
    final radiusInDegrees = radiusKm / 111.32; // Approximate conversion
    
    return {
      'center': {
        'latitude': center.latitude,
        'longitude': center.longitude,
      },
      'bounds': {
        'north': center.latitude + radiusInDegrees,
        'south': center.latitude - radiusInDegrees,
        'east': center.longitude + radiusInDegrees,
        'west': center.longitude - radiusInDegrees,
      },
      'radius': radiusKm,
      'limit': limit,
      'useIndex': true, // Hint to use geospatial index
    };
  }

  /// Predict optimal notification timing
  DateTime predictOptimalNotificationTime({
    required String userId,
    required DateTime incidentTime,
    required List<DateTime> userActivityPattern,
  }) {
    final now = DateTime.now();
    
    // If incident is critical, send immediately
    if (now.difference(incidentTime).inMinutes < 5) {
      return now;
    }

    // Find next active period based on user pattern
    final currentHour = now.hour;
    final activeHours = _analyzeActivityPattern(userActivityPattern);
    
    // If user is typically active now, send immediately
    if (activeHours.contains(currentHour)) {
      return now;
    }

    // Find next active hour
    for (int i = 1; i <= 24; i++) {
      final nextHour = (currentHour + i) % 24;
      if (activeHours.contains(nextHour)) {
        return DateTime(now.year, now.month, now.day, nextHour);
      }
    }

    // Fallback to immediate delivery
    return now;
  }

  /// Clean up resources
  void dispose() {
    _batchProcessingTimer?.cancel();
    _cacheCleanupTimer?.cancel();
    _locationCache.clear();
    _notificationBatches.clear();
    _metrics.clear();
  }

  // Private helper methods

  void _startBatchProcessing() {
    _batchProcessingTimer = Timer.periodic(_batchInterval, (_) {
      _processAllBatches();
    });
  }

  void _startCacheCleanup() {
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 10), (_) {
      _cleanupExpiredCache();
    });
  }

  void _processAllBatches() {
    final batches = List<NotificationBatch>.from(_notificationBatches.values);
    _notificationBatches.clear();

    for (final batch in batches) {
      if (batch.notifications.isNotEmpty) {
        _processBatch(batch);
      }
    }
  }

  void _processBatch(NotificationBatch batch) {
    // Record batch processing metric
    recordMetric(
      PerformanceMetricType.batchSize,
      batch.notifications.length.toDouble(),
      metadata: {'userId': batch.userId},
    );

    // Process notifications in batch
    // This would integrate with the actual notification service
  }

  void _evictOldestCacheEntry() {
    if (_locationCache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _locationCache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestTime = entry.value.timestamp;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _locationCache.remove(oldestKey);
    }
  }

  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _locationCache.entries) {
      final age = now.difference(entry.value.timestamp);
      if (age > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _locationCache.remove(key);
    }
  }

  double _roundToDecimalPlaces(double value, int places) {
    final factor = pow(10, places);
    return (value * factor).round() / factor;
  }

  double _calculateAverageMetric(List<PerformanceMetric> metrics, PerformanceMetricType type) {
    final filteredMetrics = metrics.where((m) => m.type == type).toList();
    if (filteredMetrics.isEmpty) return 0.0;

    final sum = filteredMetrics.map((m) => m.value).reduce((a, b) => a + b);
    return sum / filteredMetrics.length;
  }

  double _calculateCacheHitRate() {
    // This would be calculated based on actual cache hits/misses
    return 0.85; // Placeholder
  }

  double _calculateBatchEfficiency() {
    // This would be calculated based on batch processing metrics
    return 0.92; // Placeholder
  }

  Set<int> _analyzeActivityPattern(List<DateTime> activityTimes) {
    final hourCounts = <int, int>{};
    
    for (final time in activityTimes) {
      final hour = time.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    // Return hours with above-average activity
    final averageActivity = hourCounts.values.isEmpty 
        ? 0 
        : hourCounts.values.reduce((a, b) => a + b) / hourCounts.length;

    return hourCounts.entries
        .where((entry) => entry.value > averageActivity)
        .map((entry) => entry.key)
        .toSet();
  }
}

// Data classes for performance optimization

class LocationCache {
  final LocationEntity location;
  final DateTime timestamp;

  LocationCache({
    required this.location,
    required this.timestamp,
  });
}

class NotificationBatch {
  final String userId;
  final List<NotificationData> notifications;
  final DateTime createdAt;

  NotificationBatch({
    required this.userId,
    List<NotificationData>? notifications,
  })  : notifications = notifications ?? [],
        createdAt = DateTime.now();
}

class NotificationData {
  final String incidentId;
  final String type;
  final String title;
  final String body;
  final Map<String, dynamic> metadata;

  NotificationData({
    required this.incidentId,
    required this.type,
    required this.title,
    required this.body,
    this.metadata = const {},
  });
}

class PerformanceMetric {
  final PerformanceMetricType type;
  final double value;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  PerformanceMetric({
    required this.type,
    required this.value,
    required this.timestamp,
    this.metadata = const {},
  });
}

class PerformanceStatistics {
  final int totalMetrics;
  final int recentMetrics;
  final double averageResponseTime;
  final double averageBatteryUsage;
  final double averageMemoryUsage;
  final double cacheHitRate;
  final double batchProcessingEfficiency;

  PerformanceStatistics({
    required this.totalMetrics,
    required this.recentMetrics,
    required this.averageResponseTime,
    required this.averageBatteryUsage,
    required this.averageMemoryUsage,
    required this.cacheHitRate,
    required this.batchProcessingEfficiency,
  });
}

enum PerformanceMetricType {
  responseTime,
  batteryUsage,
  memoryUsage,
  networkLatency,
  cacheHit,
  cacheMiss,
  batchSize,
  locationAccuracy,
}
