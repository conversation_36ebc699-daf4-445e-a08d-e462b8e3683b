import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';

/// Service for security auditing and monitoring of notification system
class SecurityAuditService {
  final List<AuditEvent> _auditLog = [];
  static const int _maxAuditLogSize = 1000;

  /// Log security event
  void logSecurityEvent({
    required String userId,
    required SecurityEventType eventType,
    required String description,
    Map<String, dynamic>? metadata,
    SecurityRiskLevel riskLevel = SecurityRiskLevel.low,
  }) {
    final event = AuditEvent(
      userId: _hashUserId(userId),
      eventType: eventType,
      description: description,
      metadata: metadata ?? {},
      riskLevel: riskLevel,
      timestamp: DateTime.now(),
    );

    _auditLog.add(event);

    // Keep audit log size manageable
    if (_auditLog.length > _maxAuditLogSize) {
      _auditLog.removeAt(0);
    }

    // Handle high-risk events immediately
    if (riskLevel == SecurityRiskLevel.critical || riskLevel == SecurityRiskLevel.high) {
      _handleHighRiskEvent(event);
    }
  }

  /// Detect suspicious activity patterns
  Either<Failure, List<SecurityAlert>> detectSuspiciousActivity(String userId) {
    try {
      final userEvents = _auditLog
          .where((event) => event.userId == _hashUserId(userId))
          .toList();

      final alerts = <SecurityAlert>[];

      // Check for rapid consent changes
      final consentChanges = userEvents
          .where((event) => event.eventType == SecurityEventType.consentChanged)
          .toList();

      if (consentChanges.length > 5) {
        alerts.add(SecurityAlert(
          type: SecurityAlertType.suspiciousConsentChanges,
          description: 'Multiple consent changes detected',
          riskLevel: SecurityRiskLevel.medium,
          userId: userId,
          timestamp: DateTime.now(),
        ));
      }

      // Check for excessive location updates
      final locationUpdates = userEvents
          .where((event) => event.eventType == SecurityEventType.locationAccessed)
          .where((event) => event.timestamp.isAfter(
            DateTime.now().subtract(const Duration(hours: 1))
          ))
          .toList();

      if (locationUpdates.length > 100) {
        alerts.add(SecurityAlert(
          type: SecurityAlertType.excessiveLocationAccess,
          description: 'Excessive location access detected',
          riskLevel: SecurityRiskLevel.high,
          userId: userId,
          timestamp: DateTime.now(),
        ));
      }

      // Check for failed authentication attempts
      final failedAttempts = userEvents
          .where((event) => event.eventType == SecurityEventType.authenticationFailed)
          .where((event) => event.timestamp.isAfter(
            DateTime.now().subtract(const Duration(minutes: 15))
          ))
          .toList();

      if (failedAttempts.length > 5) {
        alerts.add(SecurityAlert(
          type: SecurityAlertType.multipleFailedAuthentications,
          description: 'Multiple failed authentication attempts',
          riskLevel: SecurityRiskLevel.critical,
          userId: userId,
          timestamp: DateTime.now(),
        ));
      }

      return Right(alerts);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to detect suspicious activity: $e'));
    }
  }

  /// Validate data access permissions
  Either<Failure, bool> validateDataAccess({
    required String userId,
    required String requestedResource,
    required String accessType,
  }) {
    try {
      // Log access attempt
      logSecurityEvent(
        userId: userId,
        eventType: SecurityEventType.dataAccessAttempt,
        description: 'Data access attempt: $requestedResource ($accessType)',
        metadata: {
          'resource': requestedResource,
          'accessType': accessType,
        },
      );

      // Validate access permissions
      final hasPermission = _checkResourcePermission(userId, requestedResource, accessType);

      if (!hasPermission) {
        logSecurityEvent(
          userId: userId,
          eventType: SecurityEventType.unauthorizedAccess,
          description: 'Unauthorized access attempt: $requestedResource',
          riskLevel: SecurityRiskLevel.high,
          metadata: {
            'resource': requestedResource,
            'accessType': accessType,
          },
        );
      }

      return Right(hasPermission);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to validate data access: $e'));
    }
  }

  /// Generate security report
  Either<Failure, SecurityReport> generateSecurityReport({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    try {
      final start = startDate ?? DateTime.now().subtract(const Duration(days: 7));
      final end = endDate ?? DateTime.now();

      var events = _auditLog
          .where((event) => event.timestamp.isAfter(start) && event.timestamp.isBefore(end))
          .toList();

      if (userId != null) {
        events = events.where((event) => event.userId == _hashUserId(userId)).toList();
      }

      final report = SecurityReport(
        startDate: start,
        endDate: end,
        totalEvents: events.length,
        eventsByType: _groupEventsByType(events),
        eventsByRiskLevel: _groupEventsByRiskLevel(events),
        topRiskyUsers: _getTopRiskyUsers(events),
        securityTrends: _calculateSecurityTrends(events),
      );

      return Right(report);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to generate security report: $e'));
    }
  }

  /// Check for data breaches or anomalies
  Either<Failure, List<SecurityAnomaly>> detectSecurityAnomalies() {
    try {
      final anomalies = <SecurityAnomaly>[];
      final recentEvents = _auditLog
          .where((event) => event.timestamp.isAfter(
            DateTime.now().subtract(const Duration(hours: 24))
          ))
          .toList();

      // Check for unusual data access patterns
      final dataAccessEvents = recentEvents
          .where((event) => event.eventType == SecurityEventType.dataAccessAttempt)
          .toList();

      if (dataAccessEvents.length > 1000) {
        anomalies.add(SecurityAnomaly(
          type: SecurityAnomalyType.unusualDataAccess,
          description: 'Unusual spike in data access attempts',
          severity: SecurityRiskLevel.high,
          timestamp: DateTime.now(),
          affectedUsers: dataAccessEvents.map((e) => e.userId).toSet().length,
        ));
      }

      // Check for mass consent revocations
      final consentRevocations = recentEvents
          .where((event) => 
            event.eventType == SecurityEventType.consentChanged &&
            event.metadata['hasConsent'] == false
          )
          .toList();

      if (consentRevocations.length > 50) {
        anomalies.add(SecurityAnomaly(
          type: SecurityAnomalyType.massConsentRevocation,
          description: 'Unusual number of consent revocations',
          severity: SecurityRiskLevel.medium,
          timestamp: DateTime.now(),
          affectedUsers: consentRevocations.length,
        ));
      }

      return Right(anomalies);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to detect security anomalies: $e'));
    }
  }

  /// Validate notification integrity
  Either<Failure, bool> validateNotificationIntegrity(Map<String, dynamic> notificationData) {
    try {
      // Check required fields
      final requiredFields = ['userId', 'incidentId', 'timestamp', 'location'];
      for (final field in requiredFields) {
        if (!notificationData.containsKey(field)) {
          return Left(ValidationFailure('Missing required field: $field'));
        }
      }

      // Validate timestamp is recent
      final timestamp = DateTime.parse(notificationData['timestamp'] as String);
      final maxAge = const Duration(hours: 24);
      if (DateTime.now().difference(timestamp) > maxAge) {
        return Left(ValidationFailure('Notification timestamp too old'));
      }

      // Validate location coordinates
      final location = notificationData['location'] as Map<String, dynamic>;
      final lat = location['latitude'] as double?;
      final lng = location['longitude'] as double?;

      if (lat == null || lng == null || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        return Left(ValidationFailure('Invalid location coordinates'));
      }

      return const Right(true);
    } catch (e) {
      return Left(ValidationFailure('Failed to validate notification integrity: $e'));
    }
  }

  // Private helper methods

  void _handleHighRiskEvent(AuditEvent event) {
    // In a real implementation, this would:
    // 1. Send alerts to security team
    // 2. Trigger automated responses
    // 3. Log to external security systems
    print('HIGH RISK EVENT: ${event.description}');
  }

  bool _checkResourcePermission(String userId, String resource, String accessType) {
    // Simplified permission check
    // In a real implementation, this would check against a permission matrix
    return true;
  }

  String _hashUserId(String userId) {
    final bytes = utf8.encode(userId + 'security_salt');
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16);
  }

  Map<SecurityEventType, int> _groupEventsByType(List<AuditEvent> events) {
    final grouped = <SecurityEventType, int>{};
    for (final event in events) {
      grouped[event.eventType] = (grouped[event.eventType] ?? 0) + 1;
    }
    return grouped;
  }

  Map<SecurityRiskLevel, int> _groupEventsByRiskLevel(List<AuditEvent> events) {
    final grouped = <SecurityRiskLevel, int>{};
    for (final event in events) {
      grouped[event.riskLevel] = (grouped[event.riskLevel] ?? 0) + 1;
    }
    return grouped;
  }

  List<String> _getTopRiskyUsers(List<AuditEvent> events) {
    final userRiskScores = <String, int>{};
    
    for (final event in events) {
      final riskScore = event.riskLevel.score;
      userRiskScores[event.userId] = (userRiskScores[event.userId] ?? 0) + riskScore;
    }

    final sortedUsers = userRiskScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedUsers.take(10).map((e) => e.key).toList();
  }

  Map<String, double> _calculateSecurityTrends(List<AuditEvent> events) {
    // Calculate security trends over time
    return {
      'averageRiskScore': events.isEmpty ? 0.0 : 
        events.map((e) => e.riskLevel.score).reduce((a, b) => a + b) / events.length,
      'eventsPerDay': events.length / 7.0, // Assuming 7-day period
      'highRiskEventRatio': events.where((e) => 
        e.riskLevel == SecurityRiskLevel.high || e.riskLevel == SecurityRiskLevel.critical
      ).length / events.length,
    };
  }
}

// Data classes for security auditing

class AuditEvent {
  final String userId;
  final SecurityEventType eventType;
  final String description;
  final Map<String, dynamic> metadata;
  final SecurityRiskLevel riskLevel;
  final DateTime timestamp;

  AuditEvent({
    required this.userId,
    required this.eventType,
    required this.description,
    required this.metadata,
    required this.riskLevel,
    required this.timestamp,
  });
}

class SecurityAlert {
  final SecurityAlertType type;
  final String description;
  final SecurityRiskLevel riskLevel;
  final String userId;
  final DateTime timestamp;

  SecurityAlert({
    required this.type,
    required this.description,
    required this.riskLevel,
    required this.userId,
    required this.timestamp,
  });
}

class SecurityAnomaly {
  final SecurityAnomalyType type;
  final String description;
  final SecurityRiskLevel severity;
  final DateTime timestamp;
  final int affectedUsers;

  SecurityAnomaly({
    required this.type,
    required this.description,
    required this.severity,
    required this.timestamp,
    required this.affectedUsers,
  });
}

class SecurityReport {
  final DateTime startDate;
  final DateTime endDate;
  final int totalEvents;
  final Map<SecurityEventType, int> eventsByType;
  final Map<SecurityRiskLevel, int> eventsByRiskLevel;
  final List<String> topRiskyUsers;
  final Map<String, double> securityTrends;

  SecurityReport({
    required this.startDate,
    required this.endDate,
    required this.totalEvents,
    required this.eventsByType,
    required this.eventsByRiskLevel,
    required this.topRiskyUsers,
    required this.securityTrends,
  });
}

// Enums for security events

enum SecurityEventType {
  consentChanged,
  locationAccessed,
  dataAccessAttempt,
  unauthorizedAccess,
  authenticationFailed,
  notificationSent,
  dataExported,
  dataDeleted,
}

enum SecurityRiskLevel {
  low(1),
  medium(3),
  high(7),
  critical(10);

  const SecurityRiskLevel(this.score);
  final int score;
}

enum SecurityAlertType {
  suspiciousConsentChanges,
  excessiveLocationAccess,
  multipleFailedAuthentications,
  unauthorizedDataAccess,
}

enum SecurityAnomalyType {
  unusualDataAccess,
  massConsentRevocation,
  suspiciousLocationPatterns,
  dataIntegrityIssue,
}
