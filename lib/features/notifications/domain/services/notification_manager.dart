import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/push_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/location_consent_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';

/// Central manager for all notification-related functionality
class NotificationManager {
  final PushNotificationService _pushNotificationService;
  final LocationConsentService _locationConsentService;
  final NotificationPreferencesService _preferencesService;

  String? _currentUserId;
  StreamSubscription<LocationConsentStatus>? _consentStatusSubscription;
  StreamController<NotificationManagerStatus>? _statusController;

  NotificationManager({
    required PushNotificationService pushNotificationService,
    required LocationConsentService locationConsentService,
    required NotificationPreferencesService preferencesService,
  }) : _pushNotificationService = pushNotificationService,
       _locationConsentService = locationConsentService,
       _preferencesService = preferencesService;

  /// Stream of notification manager status changes
  Stream<NotificationManagerStatus>? get statusStream =>
      _statusController?.stream;

  /// Initialize the notification manager for a specific user
  Future<Either<Failure, void>> initialize(String userId) async {
    try {
      _currentUserId = userId;
      _statusController =
          StreamController<NotificationManagerStatus>.broadcast();

      // Initialize all services
      final pushResult = await _pushNotificationService.initialize();
      if (pushResult.isLeft()) {
        return pushResult;
      }

      final consentResult = await _locationConsentService.initialize(userId);
      if (consentResult.isLeft()) {
        return consentResult;
      }

      // Set up consent status monitoring
      _setupConsentStatusMonitoring();

      // Update FCM token
      await _updateFCMToken(userId);

      // Check initial status
      await _checkAndUpdateStatus();

      if (kDebugMode) {
        print('🔔 NotificationManager initialized for user: $userId');
      }

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to initialize notification manager: $e'),
      );
    }
  }

  /// Grant location consent with specified duration and incident types
  Future<Either<Failure, void>> grantLocationConsent({
    required double durationHours,
    required List<String> enabledIncidentTypes,
  }) async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    try {
      // Update enabled incident types first
      final typesResult = await _preferencesService.updateEnabledIncidentTypes(
        _currentUserId!,
        enabledIncidentTypes,
      );

      if (typesResult.isLeft()) {
        return typesResult;
      }

      // Grant location consent
      final consentResult = await _locationConsentService.grantLocationConsent(
        _currentUserId!,
        durationHours,
      );

      if (consentResult.isLeft()) {
        return consentResult;
      }

      // Update status
      await _checkAndUpdateStatus();

      if (kDebugMode) {
        print(
          '🔔 Location consent granted with ${enabledIncidentTypes.length} incident types',
        );
      }

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to grant location consent: $e'),
      );
    }
  }

  /// Revoke location consent
  Future<Either<Failure, void>> revokeLocationConsent() async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    final result = await _locationConsentService.revokeLocationConsent(
      _currentUserId!,
    );

    if (result.isRight()) {
      await _checkAndUpdateStatus();
    }

    return result;
  }

  /// Update location consent duration
  Future<Either<Failure, void>> updateConsentDuration(
    double newDurationHours,
  ) async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    final result = await _locationConsentService.updateConsentDuration(
      _currentUserId!,
      newDurationHours,
    );

    if (result.isRight()) {
      await _checkAndUpdateStatus();
    }

    return result;
  }

  /// Update notification preferences
  Future<Either<Failure, void>> updateNotificationPreferences({
    bool? isEnabled,
    double? radiusKm,
    List<String>? enabledIncidentTypes,
    NotificationPriorityLevel? priorityLevel,
  }) async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    try {
      // Get current preferences first
      final currentPrefsResult = await _preferencesService.getUserPreferences(
        _currentUserId!,
      );
      if (currentPrefsResult.isLeft()) {
        return currentPrefsResult.fold(
          (failure) => Left(failure),
          (_) => const Right(null),
        );
      }

      final currentPrefs = currentPrefsResult.fold(
        (failure) => null,
        (prefs) => prefs,
      );
      if (currentPrefs == null) {
        return Left(
          ServerFailure(message: 'Failed to get current preferences'),
        );
      }

      // Update individual preferences by creating a new entity with changes
      final updatedPrefs = currentPrefs.copyWith(
        isLocationNotificationsEnabled: isEnabled,
        notificationRadiusKm: radiusKm,
        enabledIncidentTypes: enabledIncidentTypes,
        priorityLevel: priorityLevel,
        updatedAt: DateTime.now(),
      );

      final result = await _preferencesService.updateUserPreferences(
        updatedPrefs,
      );
      if (result.isLeft()) {
        return result.fold(
          (failure) => Left(failure),
          (_) => const Right(null),
        );
      }

      // Update status
      await _checkAndUpdateStatus();

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update notification preferences: $e'),
      );
    }
  }

  /// Get current notification status
  Future<Either<Failure, NotificationManagerStatus>> getStatus() async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    try {
      // Get notification permissions
      final permissionsResult =
          await _pushNotificationService.areNotificationsEnabled();
      final hasPermissions = permissionsResult.getOrElse(() => false);

      // Get user preferences
      final preferencesResult = await _preferencesService.getUserPreferences(
        _currentUserId!,
      );
      final preferences = preferencesResult.fold(
        (failure) => null,
        (prefs) => prefs,
      );

      // Get location consent status
      final consentResult = await _locationConsentService.getConsentStatus(
        _currentUserId!,
      );
      final consentStatus = consentResult.getOrElse(
        () => LocationConsentStatus(
          isGranted: false,
          grantedAt: null,
          durationHours: 0,
          expiresAt: null,
        ),
      );

      final status = NotificationManagerStatus(
        hasSystemPermissions: hasPermissions,
        isNotificationsEnabled:
            preferences?.isLocationNotificationsEnabled ?? false,
        hasLocationConsent: consentStatus.isGranted,
        locationConsentStatus: consentStatus,
        preferences: preferences,
        isFullyConfigured:
            hasPermissions &&
            (preferences?.isLocationNotificationsEnabled ?? false) &&
            consentStatus.isGranted,
      );

      return Right(status);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to get notification status: $e'),
      );
    }
  }

  /// Request system notification permissions
  Future<Either<Failure, bool>> requestSystemPermissions() async {
    // This is handled internally by the push notification service
    // We just need to check the current status
    return await _pushNotificationService.areNotificationsEnabled();
  }

  /// Get remaining consent time
  Future<Either<Failure, Duration?>> getRemainingConsentTime() async {
    if (_currentUserId == null) {
      return Left(ValidationFailure('User not initialized'));
    }

    return await _locationConsentService.getRemainingConsentTime(
      _currentUserId!,
    );
  }

  /// Set up consent status monitoring
  void _setupConsentStatusMonitoring() {
    _consentStatusSubscription = _locationConsentService.statusStream?.listen((
      consentStatus,
    ) async {
      if (kDebugMode) {
        print('🔔 Location consent status changed: ${consentStatus.isGranted}');
      }

      await _checkAndUpdateStatus();
    });
  }

  /// Update FCM token for the user
  Future<void> _updateFCMToken(String userId) async {
    try {
      await _pushNotificationService.updateFCMToken(userId);
    } catch (e) {
      if (kDebugMode) {
        print('🔔 Failed to update FCM token: $e');
      }
    }
  }

  /// Check and update current status
  Future<void> _checkAndUpdateStatus() async {
    final statusResult = await getStatus();
    statusResult.fold(
      (failure) {
        if (kDebugMode) {
          print('🔔 Failed to get status: ${failure.message}');
        }
      },
      (status) {
        _statusController?.add(status);
      },
    );
  }

  /// Dispose resources
  void dispose() {
    _consentStatusSubscription?.cancel();
    _statusController?.close();
    _locationConsentService.dispose();
    _pushNotificationService.dispose();
  }
}

/// Overall status of the notification manager
class NotificationManagerStatus {
  final bool hasSystemPermissions;
  final bool isNotificationsEnabled;
  final bool hasLocationConsent;
  final LocationConsentStatus locationConsentStatus;
  final NotificationPreferencesEntity? preferences;
  final bool isFullyConfigured;

  NotificationManagerStatus({
    required this.hasSystemPermissions,
    required this.isNotificationsEnabled,
    required this.hasLocationConsent,
    required this.locationConsentStatus,
    required this.preferences,
    required this.isFullyConfigured,
  });

  /// Get user-friendly status message
  String get statusMessage {
    if (!hasSystemPermissions) {
      return 'System notification permissions required';
    }

    if (!isNotificationsEnabled) {
      return 'Location notifications disabled';
    }

    if (!hasLocationConsent) {
      return 'Location access required';
    }

    if (isFullyConfigured) {
      return 'Active - ${locationConsentStatus.remainingTimeFormatted} remaining';
    }

    return 'Configuration incomplete';
  }

  /// Get status color indicator
  NotificationStatusColor get statusColor {
    if (isFullyConfigured) {
      return locationConsentStatus.isAboutToExpire
          ? NotificationStatusColor.warning
          : NotificationStatusColor.active;
    } else if (isNotificationsEnabled) {
      return NotificationStatusColor.warning;
    } else {
      return NotificationStatusColor.inactive;
    }
  }
}

/// Status color indicators
enum NotificationStatusColor {
  active, // Green - fully active
  warning, // Orange - partially configured or expiring soon
  inactive, // Red - not configured or disabled
}
