import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';

/// Service for managing notification preferences with business logic
class NotificationPreferencesService {
  final NotificationPreferencesRepository _repository;

  NotificationPreferencesService({
    required NotificationPreferencesRepository repository,
  }) : _repository = repository;

  /// Get user preferences with fallback to defaults
  Future<Either<Failure, NotificationPreferencesEntity>> getUserPreferences(String userId) async {
    return await _repository.getUserPreferences(userId);
  }

  /// Update user preferences with validation
  Future<Either<Failure, NotificationPreferencesEntity>> updateUserPreferences(
    NotificationPreferencesEntity preferences,
  ) async {
    // Validate preferences
    final validationResult = _validatePreferences(preferences);
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    return await _repository.updateUserPreferences(preferences);
  }

  /// Request location tracking consent
  Future<Either<Failure, bool>> requestLocationTrackingConsent(String userId) async {
    final currentConsentResult = await _repository.hasLocationTrackingConsent(userId);

    return currentConsentResult.fold(
      (failure) => Left(failure),
      (hasConsent) => Right(hasConsent),
    );
  }

  /// Grant location tracking consent
  Future<Either<Failure, void>> grantLocationTrackingConsent(String userId) async {
    return await _repository.updateLocationTrackingConsent(userId, true);
  }

  /// Revoke location tracking consent
  Future<Either<Failure, void>> revokeLocationTrackingConsent(String userId) async {
    return await _repository.updateLocationTrackingConsent(userId, false);
  }

  /// Update notification radius with validation
  Future<Either<Failure, void>> updateNotificationRadius(String userId, double radiusKm) async {
    if (radiusKm < 0.5 || radiusKm > 10.0) {
      return Left(ValidationFailure('Notification radius must be between 0.5km and 10km'));
    }

    return await _repository.updateNotificationRadius(userId, radiusKm);
  }

  /// Update enabled incident types with validation
  Future<Either<Failure, void>> updateEnabledIncidentTypes(String userId, List<String> incidentTypes) async {
    // Validate incident types against known types
    final validIncidentTypes = _getValidIncidentTypes();
    final invalidTypes = incidentTypes.where((type) => !validIncidentTypes.contains(type)).toList();
    
    if (invalidTypes.isNotEmpty) {
      return Left(ValidationFailure('Invalid incident types: ${invalidTypes.join(', ')}'));
    }

    return await _repository.updateEnabledIncidentTypes(userId, incidentTypes);
  }

  /// Update quiet hours with validation
  Future<Either<Failure, void>> updateQuietHours(String userId, QuietHoursEntity? quietHours) async {
    if (quietHours != null) {
      // Validate quiet hours
      final validationResult = _validateQuietHours(quietHours);
      if (validationResult != null) {
        return Left(ValidationFailure(validationResult));
      }
    }

    return await _repository.updateQuietHours(userId, quietHours);
  }

  /// Check if user should receive notifications based on preferences
  Future<Either<Failure, bool>> shouldReceiveNotifications(
    String userId,
    String incidentType,
  ) async {
    final preferencesResult = await _repository.getUserPreferences(userId);

    return preferencesResult.fold(
      (failure) => Left(failure),
      (preferences) {
        if (!preferences.isLocationNotificationsEnabled) {
          return const Right(false);
        }

        if (!preferences.isLocationTrackingConsented) {
          return const Right(false);
        }

        if (!preferences.shouldNotifyForIncidentType(incidentType)) {
          return const Right(false);
        }

        if (!preferences.shouldSendNotificationNow()) {
          return const Right(false);
        }

        return const Right(true);
      },
    );
  }

  /// Get notification settings summary for display
  Future<Either<Failure, NotificationSettingsSummary>> getNotificationSettingsSummary(String userId) async {
    final preferencesResult = await _repository.getUserPreferences(userId);
    
    return preferencesResult.fold(
      (failure) => Left(failure),
      (preferences) {
        return Right(NotificationSettingsSummary(
          isEnabled: preferences.isLocationNotificationsEnabled,
          hasLocationConsent: preferences.isLocationTrackingConsented,
          radiusKm: preferences.notificationRadiusKm,
          enabledIncidentTypesCount: preferences.enabledIncidentTypes.isEmpty 
              ? _getValidIncidentTypes().length 
              : preferences.enabledIncidentTypes.length,
          hasQuietHours: preferences.quietHours?.isEnabled ?? false,
          priorityLevel: preferences.priorityLevel,
        ));
      },
    );
  }

  /// Validate notification preferences
  String? _validatePreferences(NotificationPreferencesEntity preferences) {
    if (preferences.userId.isEmpty) {
      return 'User ID cannot be empty';
    }

    if (preferences.notificationRadiusKm < 0.5 || preferences.notificationRadiusKm > 10.0) {
      return 'Notification radius must be between 0.5km and 10km';
    }

    final validIncidentTypes = _getValidIncidentTypes();
    final invalidTypes = preferences.enabledIncidentTypes
        .where((type) => !validIncidentTypes.contains(type))
        .toList();
    
    if (invalidTypes.isNotEmpty) {
      return 'Invalid incident types: ${invalidTypes.join(', ')}';
    }

    if (preferences.quietHours != null) {
      final quietHoursValidation = _validateQuietHours(preferences.quietHours!);
      if (quietHoursValidation != null) {
        return quietHoursValidation;
      }
    }

    return null;
  }

  /// Validate quiet hours configuration
  String? _validateQuietHours(QuietHoursEntity quietHours) {
    if (quietHours.startTime.hour < 0 || quietHours.startTime.hour > 23) {
      return 'Invalid start hour for quiet hours';
    }

    if (quietHours.endTime.hour < 0 || quietHours.endTime.hour > 23) {
      return 'Invalid end hour for quiet hours';
    }

    if (quietHours.startTime.minute < 0 || quietHours.startTime.minute > 59) {
      return 'Invalid start minute for quiet hours';
    }

    if (quietHours.endTime.minute < 0 || quietHours.endTime.minute > 59) {
      return 'Invalid end minute for quiet hours';
    }

    return null;
  }

  /// Get list of valid incident types
  List<String> _getValidIncidentTypes() {
    return [
      'robbery',
      'assault',
      'vandalism',
      'theft',
      'suspicious_activity',
      'emergency',
      'accident',
      'fire',
      'medical_emergency',
      'other',
    ];
  }
}

/// Summary of notification settings for display
class NotificationSettingsSummary {
  final bool isEnabled;
  final bool hasLocationConsent;
  final double radiusKm;
  final int enabledIncidentTypesCount;
  final bool hasQuietHours;
  final NotificationPriorityLevel priorityLevel;

  NotificationSettingsSummary({
    required this.isEnabled,
    required this.hasLocationConsent,
    required this.radiusKm,
    required this.enabledIncidentTypesCount,
    required this.hasQuietHours,
    required this.priorityLevel,
  });
}
