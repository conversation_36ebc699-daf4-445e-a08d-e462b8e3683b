import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';

/// Service for handling user data deletion and GDPR compliance
class DataDeletionService {
  final NotificationPreferencesRepository _preferencesRepository;
  final IncidentNotificationRepository _notificationRepository;

  DataDeletionService({
    required NotificationPreferencesRepository preferencesRepository,
    required IncidentNotificationRepository notificationRepository,
  }) : _preferencesRepository = preferencesRepository,
       _notificationRepository = notificationRepository;

  /// Delete all user data (GDPR Right to be Forgotten)
  Future<Either<Failure, DataDeletionReport>> deleteAllUserData(
    String userId,
  ) async {
    final report = DataDeletionReport(userId: userId);

    try {
      // Delete notification preferences
      final preferencesResult = await _deleteNotificationPreferences(userId);
      report.addResult('notification_preferences', preferencesResult);

      // Delete notification history
      final notificationsResult = await _deleteNotificationHistory(userId);
      report.addResult('notification_history', notificationsResult);

      // Delete location tracking data
      final locationResult = await _deleteLocationData(userId);
      report.addResult('location_data', locationResult);

      // Delete audit logs (anonymized)
      final auditResult = await _deleteAuditLogs(userId);
      report.addResult('audit_logs', auditResult);

      // Delete cached data
      final cacheResult = await _deleteCachedData(userId);
      report.addResult('cached_data', cacheResult);

      report.completedAt = DateTime.now();
      return Right(report);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete user data: $e'));
    }
  }

  /// Delete only notification-related data
  Future<Either<Failure, DataDeletionReport>> deleteNotificationData(
    String userId,
  ) async {
    final report = DataDeletionReport(userId: userId);

    try {
      // Delete notification preferences
      final preferencesResult = await _deleteNotificationPreferences(userId);
      report.addResult('notification_preferences', preferencesResult);

      // Delete notification history
      final notificationsResult = await _deleteNotificationHistory(userId);
      report.addResult('notification_history', notificationsResult);

      report.completedAt = DateTime.now();
      return Right(report);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to delete notification data: $e'),
      );
    }
  }

  /// Delete only location tracking data
  Future<Either<Failure, DataDeletionReport>> deleteLocationData(
    String userId,
  ) async {
    final report = DataDeletionReport(userId: userId);

    try {
      // Delete location tracking data
      final locationResult = await _deleteLocationData(userId);
      report.addResult('location_data', locationResult);

      // Update preferences to revoke location consent
      final consentResult = await _revokeLocationConsent(userId);
      report.addResult('location_consent', consentResult);

      report.completedAt = DateTime.now();
      return Right(report);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete location data: $e'));
    }
  }

  /// Export user data for GDPR compliance
  Future<Either<Failure, UserDataExport>> exportUserData(String userId) async {
    try {
      final export = UserDataExport(userId: userId);

      // Export notification preferences
      final preferencesResult = await _preferencesRepository.getUserPreferences(
        userId,
      );
      preferencesResult.fold(
        (failure) =>
            export.addError('notification_preferences', failure.message),
        (preferences) => export.addData(
          'notification_preferences',
          _serializePreferences(preferences),
        ),
      );

      // Export notification history
      final notificationsResult = await _notificationRepository
          .getUserNotifications(userId);
      notificationsResult.fold(
        (failure) => export.addError('notification_history', failure.message),
        (notifications) => export.addData(
          'notification_history',
          notifications.map((n) => _serializeNotification(n)).toList(),
        ),
      );

      // Export notification statistics
      final statsResult = await _notificationRepository
          .getNotificationStatistics(userId);
      statsResult.fold(
        (failure) =>
            export.addError('notification_statistics', failure.message),
        (stats) => export.addData('notification_statistics', {
          'total_notifications': stats.totalNotifications,
          'unread_notifications': stats.unreadNotifications,
          'notifications_today': stats.notificationsToday,
          'notifications_this_week': stats.notificationsThisWeek,
          'notifications_by_type': stats.notificationsByType,
          'last_notification_time':
              stats.lastNotificationTime?.toIso8601String(),
        }),
      );

      export.completedAt = DateTime.now();
      return Right(export);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to export user data: $e'));
    }
  }

  /// Schedule automatic data deletion based on retention policy
  Future<Either<Failure, void>> scheduleAutomaticDeletion(String userId) async {
    try {
      // This would integrate with a job scheduler
      // For now, we'll just validate the request

      final retentionPeriod = const Duration(days: 90); // 3 months
      final deletionDate = DateTime.now().add(retentionPeriod);

      // In a real implementation, this would:
      // 1. Schedule a background job
      // 2. Store the deletion schedule
      // 3. Send confirmation to user

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to schedule automatic deletion: $e'),
      );
    }
  }

  /// Clean up expired location data (24-hour policy)
  Future<Either<Failure, int>> cleanupExpiredLocationData() async {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
    int deletedCount = 0;

    // This would delete location data older than 24 hours
    // Implementation would depend on where location data is stored

    return Right(deletedCount);
  }

  /// Verify data deletion completion
  Future<Either<Failure, bool>> verifyDataDeletion(String userId) async {
    // Check if notification preferences exist
    final preferencesResult = await _preferencesRepository.getUserPreferences(
      userId,
    );
    final hasPreferences = preferencesResult.fold(
      (failure) => false,
      (preferences) => preferences.isNotEmpty,
    );

    // Check if notification history exists
    final notificationsResult = await _notificationRepository
        .getUserNotifications(userId, limit: 1);
    final hasNotifications = notificationsResult.fold(
      (failure) => false,
      (notifications) => notifications.isNotEmpty,
    );

    final isDeleted = !hasPreferences && !hasNotifications;
    return Right(isDeleted);
  }

  // Private helper methods

  Future<DeletionResult> _deleteNotificationPreferences(String userId) async {
    try {
      // This would call the repository to delete preferences
      // For now, we'll simulate the operation
      return DeletionResult(
        success: true,
        itemsDeleted: 1,
        message: 'Notification preferences deleted',
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to delete notification preferences: $e',
      );
    }
  }

  Future<DeletionResult> _deleteNotificationHistory(String userId) async {
    try {
      // Get count of notifications to delete
      final notificationsResult = await _notificationRepository
          .getUserNotifications(userId);
      final count = notificationsResult.fold(
        (failure) => 0,
        (notifications) => notifications.length,
      );

      // Delete old notifications
      final deleteResult = await _notificationRepository.deleteOldNotifications(
        olderThan: DateTime.now(),
        userId: userId,
      );

      return deleteResult.fold(
        (failure) => DeletionResult(
          success: false,
          itemsDeleted: 0,
          message: 'Failed to delete notification history: ${failure.message}',
        ),
        (_) => DeletionResult(
          success: true,
          itemsDeleted: count,
          message: 'Notification history deleted',
        ),
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to delete notification history: $e',
      );
    }
  }

  Future<DeletionResult> _deleteLocationData(String userId) async {
    try {
      // This would delete location tracking data from Firebase
      // For now, we'll simulate the operation
      return DeletionResult(
        success: true,
        itemsDeleted: 1,
        message: 'Location data deleted',
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to delete location data: $e',
      );
    }
  }

  Future<DeletionResult> _deleteAuditLogs(String userId) async {
    try {
      // This would delete audit logs (keeping anonymized versions)
      return DeletionResult(
        success: true,
        itemsDeleted: 0, // Audit logs are anonymized, not deleted
        message: 'Audit logs anonymized',
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to anonymize audit logs: $e',
      );
    }
  }

  Future<DeletionResult> _deleteCachedData(String userId) async {
    try {
      // This would clear cached data from local storage
      return DeletionResult(
        success: true,
        itemsDeleted: 1,
        message: 'Cached data cleared',
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to clear cached data: $e',
      );
    }
  }

  Future<DeletionResult> _revokeLocationConsent(String userId) async {
    try {
      final result = await _preferencesRepository.updateLocationTrackingConsent(
        userId,
        false,
      );

      return result.fold(
        (failure) => DeletionResult(
          success: false,
          itemsDeleted: 0,
          message: 'Failed to revoke location consent: ${failure.message}',
        ),
        (_) => DeletionResult(
          success: true,
          itemsDeleted: 1,
          message: 'Location consent revoked',
        ),
      );
    } catch (e) {
      return DeletionResult(
        success: false,
        itemsDeleted: 0,
        message: 'Failed to revoke location consent: $e',
      );
    }
  }

  /// Serialize notification preferences entity to map
  Map<String, dynamic> _serializePreferences(
    NotificationPreferencesEntity preferences,
  ) {
    return {
      'userId': preferences.userId,
      'isLocationNotificationsEnabled':
          preferences.isLocationNotificationsEnabled,
      'notificationRadiusKm': preferences.notificationRadiusKm,
      'enabledIncidentTypes': preferences.enabledIncidentTypes,
      'quietHours':
          preferences.quietHours != null
              ? {
                'startTime':
                    '${preferences.quietHours!.startTime.hour}:${preferences.quietHours!.startTime.minute}',
                'endTime':
                    '${preferences.quietHours!.endTime.hour}:${preferences.quietHours!.endTime.minute}',
                'isEnabled': preferences.quietHours!.isEnabled,
              }
              : null,
      'isLocationTrackingConsented': preferences.isLocationTrackingConsented,
      'priorityLevel': preferences.priorityLevel.name,
      'createdAt': preferences.createdAt.toIso8601String(),
      'updatedAt': preferences.updatedAt.toIso8601String(),
    };
  }

  /// Serialize incident notification entity to map
  Map<String, dynamic> _serializeNotification(
    IncidentNotificationEntity notification,
  ) {
    return {
      'id': notification.id,
      'userId': notification.userId,
      'incidentId': notification.incidentId,
      'incidentType': notification.incidentType,
      'incidentTitle': notification.incidentTitle,
      'incidentDescription': notification.incidentDescription,
      'incidentLocation': {
        'latitude': notification.incidentLocation.latitude,
        'longitude': notification.incidentLocation.longitude,
        'address': notification.incidentLocation.address,
      },
      'userLocationAtTime': {
        'latitude': notification.userLocationAtTime.latitude,
        'longitude': notification.userLocationAtTime.longitude,
        'address': notification.userLocationAtTime.address,
      },
      'distanceFromUserMeters': notification.distanceFromUserMeters,
      'incidentTimestamp': notification.incidentTimestamp.toIso8601String(),
      'notificationSentAt': notification.notificationSentAt.toIso8601String(),
      'status': notification.status.name,
      'priority': notification.priority.name,
      'metadata': notification.metadata,
    };
  }
}

/// Report of data deletion operations
class DataDeletionReport {
  final String userId;
  final DateTime startedAt;
  DateTime? completedAt;
  final Map<String, DeletionResult> results = {};

  DataDeletionReport({required this.userId}) : startedAt = DateTime.now();

  void addResult(String category, DeletionResult result) {
    results[category] = result;
  }

  bool get isSuccessful => results.values.every((result) => result.success);
  int get totalItemsDeleted =>
      results.values.fold(0, (sum, result) => sum + result.itemsDeleted);

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'startedAt': startedAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'isSuccessful': isSuccessful,
      'totalItemsDeleted': totalItemsDeleted,
      'results': results.map((key, value) => MapEntry(key, value.toMap())),
    };
  }
}

/// Result of a specific deletion operation
class DeletionResult {
  final bool success;
  final int itemsDeleted;
  final String message;

  DeletionResult({
    required this.success,
    required this.itemsDeleted,
    required this.message,
  });

  Map<String, dynamic> toMap() {
    return {
      'success': success,
      'itemsDeleted': itemsDeleted,
      'message': message,
    };
  }
}

/// Export of user data for GDPR compliance
class UserDataExport {
  final String userId;
  final DateTime startedAt;
  DateTime? completedAt;
  final Map<String, dynamic> data = {};
  final Map<String, String> errors = {};

  UserDataExport({required this.userId}) : startedAt = DateTime.now();

  void addData(String category, dynamic categoryData) {
    data[category] = categoryData;
  }

  void addError(String category, String error) {
    errors[category] = error;
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'exportedAt':
          completedAt?.toIso8601String() ?? startedAt.toIso8601String(),
      'data': data,
      'errors': errors,
    };
  }
}
