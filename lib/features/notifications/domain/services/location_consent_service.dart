import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:respublicaseguridad/core/error/failures.dart';

import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/push_notification_service.dart';

/// Service for managing location consent and its integration with notifications
class LocationConsentService {
  final NotificationPreferencesService _preferencesService;
  final PushNotificationService _pushNotificationService;

  Timer? _consentExpirationTimer;
  StreamController<LocationConsentStatus>? _statusController;

  LocationConsentService({
    required NotificationPreferencesService preferencesService,
    required PushNotificationService pushNotificationService,
  }) : _preferencesService = preferencesService,
       _pushNotificationService = pushNotificationService;

  /// Stream of location consent status changes
  Stream<LocationConsentStatus>? get statusStream => _statusController?.stream;

  /// Initialize the location consent service
  Future<Either<Failure, void>> initialize(String userId) async {
    try {
      _statusController = StreamController<LocationConsentStatus>.broadcast();

      // Check current consent status and set up timer if needed
      await _checkAndSetupConsentTimer(userId);

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to initialize location consent service: $e',
        ),
      );
    }
  }

  /// Grant location consent with specified duration
  Future<Either<Failure, void>> grantLocationConsent(
    String userId,
    double durationHours,
  ) async {
    try {
      // Validate duration (0.5 to 24 hours)
      final clampedDuration = durationHours.clamp(0.5, 24.0);

      // Update preferences with consent and timestamp
      final result = await _preferencesService.grantLocationTrackingConsent(
        userId,
      );

      return result.fold((failure) => Left(failure), (_) async {
        // Set up expiration timer
        await _setupConsentExpirationTimer(userId, clampedDuration);

        // Subscribe to incident notifications
        await _pushNotificationService.subscribeToIncidentNotifications();

        // Notify status change
        _statusController?.add(
          LocationConsentStatus(
            isGranted: true,
            grantedAt: DateTime.now(),
            durationHours: clampedDuration,
            expiresAt: DateTime.now().add(
              Duration(
                hours: clampedDuration.floor(),
                minutes: ((clampedDuration % 1) * 60).round(),
              ),
            ),
          ),
        );

        if (kDebugMode) {
          print('📍 Location consent granted for ${clampedDuration}h');
        }

        return const Right(null);
      });
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to grant location consent: $e'),
      );
    }
  }

  /// Revoke location consent
  Future<Either<Failure, void>> revokeLocationConsent(String userId) async {
    try {
      // Cancel expiration timer
      _consentExpirationTimer?.cancel();
      _consentExpirationTimer = null;

      // Update preferences
      final result = await _preferencesService.revokeLocationTrackingConsent(
        userId,
      );

      return result.fold((failure) => Left(failure), (_) async {
        // Unsubscribe from incident notifications
        await _pushNotificationService.unsubscribeFromIncidentNotifications();

        // Notify status change
        _statusController?.add(
          LocationConsentStatus(
            isGranted: false,
            grantedAt: null,
            durationHours: 0,
            expiresAt: null,
          ),
        );

        if (kDebugMode) {
          print('📍 Location consent revoked');
        }

        return const Right(null);
      });
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to revoke location consent: $e'),
      );
    }
  }

  /// Update location consent duration
  Future<Either<Failure, void>> updateConsentDuration(
    String userId,
    double newDurationHours,
  ) async {
    try {
      // Validate duration
      final clampedDuration = newDurationHours.clamp(0.5, 24.0);

      // Get current preferences to check if consent is active
      final preferencesResult = await _preferencesService.getUserPreferences(
        userId,
      );

      return preferencesResult.fold((failure) => Left(failure), (
        preferences,
      ) async {
        if (!preferences.isLocationTrackingConsented) {
          return Left(
            ValidationFailure('Location consent is not currently granted'),
          );
        }

        // Cancel existing timer
        _consentExpirationTimer?.cancel();

        // Set up new timer with updated duration
        await _setupConsentExpirationTimer(userId, clampedDuration);

        // Calculate new expiration time
        final now = DateTime.now();
        final expiresAt = now.add(
          Duration(
            hours: clampedDuration.floor(),
            minutes: ((clampedDuration % 1) * 60).round(),
          ),
        );

        // Notify status change
        _statusController?.add(
          LocationConsentStatus(
            isGranted: true,
            grantedAt: now, // Reset to current time for new duration
            durationHours: clampedDuration,
            expiresAt: expiresAt,
          ),
        );

        if (kDebugMode) {
          print('📍 Location consent duration updated to ${clampedDuration}h');
        }

        return const Right(null);
      });
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update consent duration: $e'),
      );
    }
  }

  /// Get current location consent status
  Future<Either<Failure, LocationConsentStatus>> getConsentStatus(
    String userId,
  ) async {
    try {
      final preferencesResult = await _preferencesService.getUserPreferences(
        userId,
      );

      return preferencesResult.fold((failure) => Left(failure), (preferences) {
        if (!preferences.isLocationTrackingConsented ||
            preferences.locationConsentGrantedAt == null) {
          return Right(
            LocationConsentStatus(
              isGranted: false,
              grantedAt: null,
              durationHours: 0,
              expiresAt: null,
            ),
          );
        }

        final grantedAt = preferences.locationConsentGrantedAt!;
        final durationHours = preferences.locationAccessDurationHours;
        final expiresAt = grantedAt.add(
          Duration(
            hours: durationHours.floor(),
            minutes: ((durationHours % 1) * 60).round(),
          ),
        );

        // Check if consent has expired
        final now = DateTime.now();
        final isExpired = now.isAfter(expiresAt);

        if (isExpired) {
          // Auto-revoke expired consent
          revokeLocationConsent(userId);
          return Right(
            LocationConsentStatus(
              isGranted: false,
              grantedAt: null,
              durationHours: 0,
              expiresAt: null,
            ),
          );
        }

        return Right(
          LocationConsentStatus(
            isGranted: true,
            grantedAt: grantedAt,
            durationHours: durationHours,
            expiresAt: expiresAt,
          ),
        );
      });
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get consent status: $e'));
    }
  }

  /// Check current consent and set up timer if needed
  Future<void> _checkAndSetupConsentTimer(String userId) async {
    final statusResult = await getConsentStatus(userId);
    statusResult.fold(
      (failure) {
        if (kDebugMode) {
          print('📍 Failed to check consent status: ${failure.message}');
        }
      },
      (status) async {
        if (status.isGranted && status.expiresAt != null) {
          final now = DateTime.now();
          final timeUntilExpiration = status.expiresAt!.difference(now);

          if (timeUntilExpiration.isNegative) {
            // Already expired, revoke consent
            await revokeLocationConsent(userId);
          } else {
            // Set up timer for remaining time
            _consentExpirationTimer = Timer(timeUntilExpiration, () {
              revokeLocationConsent(userId);
            });

            if (kDebugMode) {
              print(
                '📍 Consent timer set for ${timeUntilExpiration.inMinutes} minutes',
              );
            }
          }
        }
      },
    );
  }

  /// Set up consent expiration timer
  Future<void> _setupConsentExpirationTimer(
    String userId,
    double durationHours,
  ) async {
    // Cancel existing timer
    _consentExpirationTimer?.cancel();

    // Calculate expiration duration
    final expirationDuration = Duration(
      hours: durationHours.floor(),
      minutes: ((durationHours % 1) * 60).round(),
    );

    // Set up new timer
    _consentExpirationTimer = Timer(expirationDuration, () async {
      if (kDebugMode) {
        print('📍 Location consent expired, auto-revoking');
      }

      await revokeLocationConsent(userId);
    });

    if (kDebugMode) {
      print(
        '📍 Consent expiration timer set for ${expirationDuration.inMinutes} minutes',
      );
    }
  }

  /// Get remaining consent time
  Future<Either<Failure, Duration?>> getRemainingConsentTime(
    String userId,
  ) async {
    final statusResult = await getConsentStatus(userId);

    return statusResult.fold((failure) => Left(failure), (status) {
      if (!status.isGranted || status.expiresAt == null) {
        return const Right(null);
      }

      final now = DateTime.now();
      final remaining = status.expiresAt!.difference(now);

      return Right(remaining.isNegative ? Duration.zero : remaining);
    });
  }

  /// Dispose resources
  void dispose() {
    _consentExpirationTimer?.cancel();
    _statusController?.close();
  }
}

/// Location consent status information
class LocationConsentStatus {
  final bool isGranted;
  final DateTime? grantedAt;
  final double durationHours;
  final DateTime? expiresAt;

  LocationConsentStatus({
    required this.isGranted,
    required this.grantedAt,
    required this.durationHours,
    required this.expiresAt,
  });

  /// Get remaining time as a formatted string
  String get remainingTimeFormatted {
    if (!isGranted || expiresAt == null) return 'Not active';

    final now = DateTime.now();
    final remaining = expiresAt!.difference(now);

    if (remaining.isNegative) return 'Expired';

    final hours = remaining.inHours;
    final minutes = remaining.inMinutes % 60;
    final seconds = remaining.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Check if consent is about to expire (within 5 minutes)
  bool get isAboutToExpire {
    if (!isGranted || expiresAt == null) return false;

    final now = DateTime.now();
    final remaining = expiresAt!.difference(now);

    return remaining.inMinutes <= 5 && remaining.inMinutes > 0;
  }
}
