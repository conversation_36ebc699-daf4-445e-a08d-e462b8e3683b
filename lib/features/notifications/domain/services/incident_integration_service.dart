import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';

/// Service for integrating notification system with existing incident management
class IncidentIntegrationService {
  final IncidentRepository _incidentRepository;
  final IncidentNotificationRepository _notificationRepository;
  final NotificationPreferencesService _preferencesService;
  final NotificationService _notificationService;

  IncidentIntegrationService({
    required IncidentRepository incidentRepository,
    required IncidentNotificationRepository notificationRepository,
    required NotificationPreferencesService preferencesService,
    required NotificationService notificationService,
  })  : _incidentRepository = incidentRepository,
        _notificationRepository = notificationRepository,
        _preferencesService = preferencesService,
        _notificationService = notificationService;

  /// Process new incident and trigger notifications
  Future<Either<Failure, IncidentProcessingResult>> processNewIncident(
    IncidentEntity incident,
  ) async {
    try {
      final result = IncidentProcessingResult(incidentId: incident.incidentId);

      // Validate incident data
      final validationResult = _validateIncidentForNotifications(incident);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected validation result'),
        );
      }

      // Find users within notification radius
      final nearbyUsersResult = await _findNearbyUsers(incident);
      if (nearbyUsersResult.isLeft()) {
        return nearbyUsersResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected nearby users result'),
        );
      }

      final nearbyUsers = nearbyUsersResult.getOrElse(() => []);
      result.eligibleUsers = nearbyUsers.length;

      // Send notifications to eligible users
      for (final userLocation in nearbyUsers) {
        final notificationResult = await _sendIncidentNotificationToUser(
          incident,
          userLocation,
        );

        notificationResult.fold(
          (failure) => result.addFailure(userLocation.userId, failure.message),
          (notification) => result.addSuccess(userLocation.userId, notification),
        );
      }

      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to process incident: $e'));
    }
  }

  /// Update incident status and send follow-up notifications
  Future<Either<Failure, void>> processIncidentUpdate(
    IncidentEntity oldIncident,
    IncidentEntity updatedIncident,
  ) async {
    try {
      // Check if status changed to resolved
      if (oldIncident.status != 'resolved' && updatedIncident.status == 'resolved') {
        await _sendIncidentResolutionNotifications(updatedIncident);
      }

      // Check if incident was escalated to critical severity
      if (oldIncident.severity != 'Grave' && updatedIncident.severity == 'Grave') {
        await _sendCriticalIncidentNotifications(updatedIncident);
      }

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to process incident update: $e'));
    }
  }

  /// Get notification history for an incident
  Future<Either<Failure, List<IncidentNotificationEntity>>> getIncidentNotificationHistory(
    String incidentId,
  ) async {
    try {
      // This would query the notification repository for incident-specific notifications
      // For now, we'll return an empty list
      return const Right([]);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get notification history: $e'));
    }
  }

  /// Get notification statistics for incident reporting
  Future<Either<Failure, IncidentNotificationStats>> getNotificationStats(
    String incidentId,
  ) async {
    try {
      // Get all notifications for this incident
      final notificationsResult = await getIncidentNotificationHistory(incidentId);
      
      return notificationsResult.fold(
        (failure) => Left(failure),
        (notifications) {
          final stats = IncidentNotificationStats(
            incidentId: incidentId,
            totalNotificationsSent: notifications.length,
            notificationsDelivered: notifications
                .where((n) => n.status == IncidentNotificationStatus.delivered)
                .length,
            notificationsRead: notifications
                .where((n) => n.status == IncidentNotificationStatus.read)
                .length,
            averageDistance: notifications.isEmpty
                ? 0.0
                : notifications
                    .map((n) => n.distanceFromUserMeters)
                    .reduce((a, b) => a + b) / notifications.length,
            notificationsByPriority: _groupNotificationsByPriority(notifications),
          );
          
          return Right(stats);
        },
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get notification stats: $e'));
    }
  }

  /// Link incident with existing zone validation system
  Future<Either<Failure, void>> linkIncidentWithZoneValidation(
    IncidentEntity incident,
    String zoneId,
  ) async {
    try {
      // This would integrate with the zone validation system
      // to enhance incident notifications with zone context
      
      // For now, we'll just validate the integration
      if (incident.location.isEmpty || zoneId.isEmpty) {
        return Left(ValidationFailure('Invalid incident or zone data'));
      }

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to link incident with zone: $e'));
    }
  }

  /// Enhance incident with notification metadata
  Future<Either<Failure, IncidentEntity>> enhanceIncidentWithNotificationData(
    IncidentEntity incident,
  ) async {
    try {
      // Get notification stats for this incident
      final statsResult = await getNotificationStats(incident.incidentId);
      
      return statsResult.fold(
        (failure) => Right(incident), // Return original incident if stats fail
        (stats) {
          // Add notification metadata to incident
          final enhancedMetadata = Map<String, dynamic>.from(incident.metadata ?? {});
          enhancedMetadata['notificationStats'] = {
            'totalSent': stats.totalNotificationsSent,
            'delivered': stats.notificationsDelivered,
            'read': stats.notificationsRead,
            'averageDistance': stats.averageDistance,
          };

          return Right(incident.copyWith(metadata: enhancedMetadata));
        },
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to enhance incident: $e'));
    }
  }

  // Private helper methods

  Either<Failure, void> _validateIncidentForNotifications(IncidentEntity incident) {
    // Validate incident has required data for notifications
    if (incident.incidentId.isEmpty) {
      return Left(ValidationFailure('Incident ID is required'));
    }

    if (incident.location.isEmpty) {
      return Left(ValidationFailure('Incident location is required'));
    }

    if (incident.title.isEmpty) {
      return Left(ValidationFailure('Incident title is required'));
    }

    // Check if incident is visible to community
    if (incident.visibilityStatus != 'visibleToCommunity') {
      return Left(ValidationFailure('Incident is not visible to community'));
    }

    // Check if incident is not blocked
    if (incident.isBlocked) {
      return Left(ValidationFailure('Incident is blocked'));
    }

    return const Right(null);
  }

  Future<Either<Failure, List<UserLocationData>>> _findNearbyUsers(
    IncidentEntity incident,
  ) async {
    try {
      // This would integrate with the user location tracking system
      // to find users within notification radius of the incident
      
      // For now, we'll return an empty list
      // In a real implementation, this would:
      // 1. Query user locations from Firebase
      // 2. Calculate distances from incident location
      // 3. Filter by user notification preferences
      // 4. Return eligible users
      
      return const Right([]);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find nearby users: $e'));
    }
  }

  Future<Either<Failure, IncidentNotificationEntity>> _sendIncidentNotificationToUser(
    IncidentEntity incident,
    UserLocationData userLocation,
  ) async {
    try {
      // Check user preferences
      final shouldNotifyResult = await _preferencesService.shouldReceiveNotifications(
        userLocation.userId,
        incident.categoryKey,
      );

      final shouldNotify = shouldNotifyResult.getOrElse(() => false);
      if (!shouldNotify) {
        return Left(ValidationFailure('User preferences prevent notification'));
      }

      // Calculate distance
      final distance = incident.location.distanceTo(userLocation.location);

      // Determine priority
      final priority = _determinePriority(incident, distance);

      // Send notification
      await _notificationService.showIncidentNotification(
        incidentId: incident.incidentId,
        incidentType: incident.categoryKey,
        title: incident.title,
        description: incident.description,
        distanceMeters: distance,
        priority: priority,
        metadata: {
          'userId': userLocation.userId,
          'incidentTimestamp': incident.postedAt.toIso8601String(),
        },
      );

      // Create notification record
      final notification = IncidentNotificationEntity(
        id: '', // Will be set by repository
        userId: userLocation.userId,
        incidentId: incident.incidentId,
        incidentType: incident.categoryKey,
        incidentTitle: incident.title,
        incidentDescription: incident.description,
        incidentLocation: incident.location,
        userLocationAtTime: userLocation.location,
        distanceFromUserMeters: distance,
        incidentTimestamp: incident.postedAt,
        notificationSentAt: DateTime.now(),
        priority: priority,
      );

      // Save notification record
      final saveResult = await _notificationRepository.saveNotification(notification);
      
      return saveResult;
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to send notification: $e'));
    }
  }

  Future<void> _sendIncidentResolutionNotifications(IncidentEntity incident) async {
    // Send resolution notifications to users who were notified about the incident
    // This would query notification history and send follow-up notifications
  }

  Future<void> _sendCriticalIncidentNotifications(IncidentEntity incident) async {
    // Send critical incident notifications with higher priority
    // This would override user quiet hours and use critical notification channels
  }

  IncidentNotificationPriority _determinePriority(IncidentEntity incident, double distance) {
    // Determine priority based on incident type and distance
    const criticalTypes = ['emergency', 'assault', 'fire', 'medical_emergency'];
    const highPriorityTypes = ['robbery', 'theft', 'suspicious_activity'];

    if (distance <= 500 && criticalTypes.contains(incident.categoryKey.toLowerCase())) {
      return IncidentNotificationPriority.critical;
    }

    if (distance <= 1000 && highPriorityTypes.contains(incident.categoryKey.toLowerCase())) {
      return IncidentNotificationPriority.high;
    }

    if (distance <= 2000) {
      return IncidentNotificationPriority.medium;
    }

    return IncidentNotificationPriority.low;
  }

  Map<IncidentNotificationPriority, int> _groupNotificationsByPriority(
    List<IncidentNotificationEntity> notifications,
  ) {
    final grouped = <IncidentNotificationPriority, int>{};
    for (final notification in notifications) {
      grouped[notification.priority] = (grouped[notification.priority] ?? 0) + 1;
    }
    return grouped;
  }
}

// Data classes for integration

class UserLocationData {
  final String userId;
  final LocationEntity location;
  final DateTime lastUpdate;

  UserLocationData({
    required this.userId,
    required this.location,
    required this.lastUpdate,
  });
}

class IncidentProcessingResult {
  final String incidentId;
  int eligibleUsers = 0;
  final List<String> successfulNotifications = [];
  final Map<String, String> failedNotifications = {};

  IncidentProcessingResult({required this.incidentId});

  void addSuccess(String userId, IncidentNotificationEntity notification) {
    successfulNotifications.add(userId);
  }

  void addFailure(String userId, String error) {
    failedNotifications[userId] = error;
  }

  bool get isSuccessful => failedNotifications.isEmpty && successfulNotifications.isNotEmpty;
  int get totalNotificationsSent => successfulNotifications.length;
  int get totalNotificationsFailed => failedNotifications.length;
}

class IncidentNotificationStats {
  final String incidentId;
  final int totalNotificationsSent;
  final int notificationsDelivered;
  final int notificationsRead;
  final double averageDistance;
  final Map<IncidentNotificationPriority, int> notificationsByPriority;

  IncidentNotificationStats({
    required this.incidentId,
    required this.totalNotificationsSent,
    required this.notificationsDelivered,
    required this.notificationsRead,
    required this.averageDistance,
    required this.notificationsByPriority,
  });

  double get deliveryRate => totalNotificationsSent == 0 
      ? 0.0 
      : notificationsDelivered / totalNotificationsSent;

  double get readRate => totalNotificationsSent == 0 
      ? 0.0 
      : notificationsRead / totalNotificationsSent;
}
