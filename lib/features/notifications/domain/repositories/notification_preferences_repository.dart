import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';

/// Repository interface for notification preferences management
abstract class NotificationPreferencesRepository {
  /// Get user notification preferences
  Future<Either<Failure, NotificationPreferencesEntity>> getUserPreferences(
    String userId,
  );

  /// Update user notification preferences
  Future<Either<Failure, NotificationPreferencesEntity>> updateUserPreferences(
    NotificationPreferencesEntity preferences,
  );

  /// Create initial notification preferences for new user
  Future<Either<Failure, NotificationPreferencesEntity>>
  createInitialPreferences(String userId);

  /// Check if user has consented to location tracking
  Future<Either<Failure, bool>> hasLocationTrackingConsent(String userId);

  /// Update location tracking consent
  Future<Either<Failure, void>> updateLocationTrackingConsent(
    String userId,
    bool hasConsent,
  );

  /// Get notification radius for user
  Future<Either<Failure, double>> getNotificationRadius(String userId);

  /// Update notification radius
  Future<Either<Failure, void>> updateNotificationRadius(
    String userId,
    double radiusKm,
  );

  /// Get enabled incident types for user
  Future<Either<Failure, List<String>>> getEnabledIncidentTypes(String userId);

  /// Update enabled incident types
  Future<Either<Failure, void>> updateEnabledIncidentTypes(
    String userId,
    List<String> incidentTypes,
  );

  /// Check if notifications are enabled for user
  Future<Either<Failure, bool>> areNotificationsEnabled(String userId);

  /// Enable/disable notifications for user
  Future<Either<Failure, void>> setNotificationsEnabled(
    String userId,
    bool enabled,
  );

  /// Get quiet hours configuration
  Future<Either<Failure, QuietHoursEntity?>> getQuietHours(String userId);

  /// Update quiet hours configuration
  Future<Either<Failure, void>> updateQuietHours(
    String userId,
    QuietHoursEntity? quietHours,
  );

  /// Stream of preference changes for real-time updates
  Stream<Either<Failure, NotificationPreferencesEntity>> watchUserPreferences(
    String userId,
  );

  /// Grant location tracking consent with proper timestamp
  Future<Either<Failure, void>> grantLocationTrackingConsent(
    String userId, {
    double? locationAccessDurationHours,
  });

  /// Revoke location tracking consent
  Future<Either<Failure, void>> revokeLocationTrackingConsent(String userId);
}
