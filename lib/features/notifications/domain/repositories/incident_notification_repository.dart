import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';

/// Repository interface for incident notification management
abstract class IncidentNotificationRepository {
  /// Save an incident notification record
  Future<Either<Failure, IncidentNotificationEntity>> saveNotification(
    IncidentNotificationEntity notification,
  );

  /// Get incident notifications for a user
  Future<Either<Failure, List<IncidentNotificationEntity>>> getUserNotifications(
    String userId, {
    int? limit,
    DateTime? since,
  });

  /// Check if user has already been notified about an incident
  Future<Either<Failure, bool>> hasUserBeenNotified(String userId, String incidentId);

  /// Mark notification as read
  Future<Either<Failure, void>> markNotificationAsRead(String notificationId);

  /// Mark notification as dismissed
  Future<Either<Failure, void>> markNotificationAsDismissed(String notificationId);

  /// Get unread notification count for user
  Future<Either<Failure, int>> getUnreadNotificationCount(String userId);

  /// Delete old notifications (cleanup)
  Future<Either<Failure, void>> deleteOldNotifications({
    required DateTime olderThan,
    String? userId,
  });

  /// Get notification statistics for user
  Future<Either<Failure, NotificationStatistics>> getNotificationStatistics(String userId);

  /// Stream of new notifications for real-time updates
  Stream<Either<Failure, IncidentNotificationEntity>> watchNewNotifications(String userId);

  /// Stream of notification count changes
  Stream<Either<Failure, int>> watchUnreadNotificationCount(String userId);
}

/// Statistics about user notifications
class NotificationStatistics {
  final int totalNotifications;
  final int unreadNotifications;
  final int notificationsToday;
  final int notificationsThisWeek;
  final Map<String, int> notificationsByType;
  final DateTime? lastNotificationTime;

  NotificationStatistics({
    required this.totalNotifications,
    required this.unreadNotifications,
    required this.notificationsToday,
    required this.notificationsThisWeek,
    required this.notificationsByType,
    this.lastNotificationTime,
  });
}
