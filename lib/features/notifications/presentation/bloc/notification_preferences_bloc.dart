import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_preferences_service.dart';

part 'notification_preferences_event.dart';
part 'notification_preferences_state.dart';

class NotificationPreferencesBloc
    extends Bloc<NotificationPreferencesEvent, NotificationPreferencesState> {
  final NotificationPreferencesService _preferencesService;
  final String _userId;

  NotificationPreferencesBloc({
    required NotificationPreferencesService preferencesService,
    required String userId,
  }) : _preferencesService = preferencesService,
       _userId = userId,
       super(const NotificationPreferencesState()) {
    on<LoadNotificationPreferences>(_onLoadNotificationPreferences);
    on<UpdateLocationTrackingConsent>(_onUpdateLocationTrackingConsent);
    on<SetNotificationsEnabled>(_onSetNotificationsEnabled);
    on<UpdateNotificationRadius>(_onUpdateNotificationRadius);
    on<UpdateEnabledIncidentTypes>(_onUpdateEnabledIncidentTypes);
    on<UpdateQuietHours>(_onUpdateQuietHours);
    on<UpdateNotificationPriority>(_onUpdateNotificationPriority);

    on<TestNotification>(_onTestNotification);
  }

  Future<void> _onLoadNotificationPreferences(
    LoadNotificationPreferences event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    final result = await _preferencesService.getUserPreferences(_userId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (preferences) => emit(
        state.copyWith(
          isLoading: false,
          preferences: preferences,
          errorMessage: null,
        ),
      ),
    );
  }

  Future<void> _onUpdateLocationTrackingConsent(
    UpdateLocationTrackingConsent event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isLoading: true));

    final result =
        event.hasConsent
            ? await _preferencesService.grantLocationTrackingConsent(_userId)
            : await _preferencesService.revokeLocationTrackingConsent(_userId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (_) {
        final updatedPreferences = state.preferences!.copyWith(
          isLocationTrackingConsented: event.hasConsent,
          updatedAt: DateTime.now(),
        );
        emit(
          state.copyWith(
            isLoading: false,
            preferences: updatedPreferences,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> _onSetNotificationsEnabled(
    SetNotificationsEnabled event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isLoading: true));

    final updatedPreferences = state.preferences!.copyWith(
      isLocationNotificationsEnabled: event.enabled,
      updatedAt: DateTime.now(),
    );

    final result = await _preferencesService.updateUserPreferences(
      updatedPreferences,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (preferences) => emit(
        state.copyWith(
          isLoading: false,
          preferences: preferences,
          errorMessage: null,
        ),
      ),
    );
  }

  Future<void> _onUpdateNotificationRadius(
    UpdateNotificationRadius event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isLoading: true));

    final result = await _preferencesService.updateNotificationRadius(
      _userId,
      event.radiusKm,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (_) {
        final updatedPreferences = state.preferences!.copyWith(
          notificationRadiusKm: event.radiusKm,
          updatedAt: DateTime.now(),
        );
        emit(
          state.copyWith(
            isLoading: false,
            preferences: updatedPreferences,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> _onUpdateEnabledIncidentTypes(
    UpdateEnabledIncidentTypes event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isLoading: true));

    final result = await _preferencesService.updateEnabledIncidentTypes(
      _userId,
      event.incidentTypes,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (_) {
        final updatedPreferences = state.preferences!.copyWith(
          enabledIncidentTypes: event.incidentTypes,
          updatedAt: DateTime.now(),
        );
        emit(
          state.copyWith(
            isLoading: false,
            preferences: updatedPreferences,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> _onUpdateQuietHours(
    UpdateQuietHours event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isLoading: true));

    final result = await _preferencesService.updateQuietHours(
      _userId,
      event.quietHours,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, errorMessage: failure.message)),
      (_) {
        final updatedPreferences = state.preferences!.copyWith(
          quietHours: event.quietHours,
          updatedAt: DateTime.now(),
        );
        emit(
          state.copyWith(
            isLoading: false,
            preferences: updatedPreferences,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> _onUpdateNotificationPriority(
    UpdateNotificationPriority event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    if (state.preferences == null) return;

    final updatedPreferences = state.preferences!.copyWith(
      priorityLevel: event.priority,
      updatedAt: DateTime.now(),
    );

    final result = await _preferencesService.updateUserPreferences(
      updatedPreferences,
    );

    result.fold(
      (failure) => emit(state.copyWith(errorMessage: failure.message)),
      (preferences) =>
          emit(state.copyWith(preferences: preferences, errorMessage: null)),
    );
  }

  Future<void> _onTestNotification(
    TestNotification event,
    Emitter<NotificationPreferencesState> emit,
  ) async {
    // This would trigger a test notification
    // Implementation would depend on the notification service
    emit(state.copyWith(successMessage: 'Test notification sent!'));

    // Clear success message after 3 seconds
    await Future.delayed(const Duration(seconds: 3));
    emit(state.copyWith(successMessage: null));
  }
}
