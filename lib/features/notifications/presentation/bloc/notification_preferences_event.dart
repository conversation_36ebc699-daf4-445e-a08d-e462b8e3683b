part of 'notification_preferences_bloc.dart';

abstract class NotificationPreferencesEvent extends Equatable {
  const NotificationPreferencesEvent();

  @override
  List<Object?> get props => [];
}

class LoadNotificationPreferences extends NotificationPreferencesEvent {
  const LoadNotificationPreferences();
}

class UpdateLocationTrackingConsent extends NotificationPreferencesEvent {
  final bool hasConsent;

  const UpdateLocationTrackingConsent({required this.hasConsent});

  @override
  List<Object?> get props => [hasConsent];
}

class SetNotificationsEnabled extends NotificationPreferencesEvent {
  final bool enabled;

  const SetNotificationsEnabled({required this.enabled});

  @override
  List<Object?> get props => [enabled];
}

class UpdateNotificationRadius extends NotificationPreferencesEvent {
  final double radiusKm;

  const UpdateNotificationRadius({required this.radiusKm});

  @override
  List<Object?> get props => [radiusKm];
}

class UpdateEnabledIncidentTypes extends NotificationPreferencesEvent {
  final List<String> incidentTypes;

  const UpdateEnabledIncidentTypes({required this.incidentTypes});

  @override
  List<Object?> get props => [incidentTypes];
}

class UpdateQuietHours extends NotificationPreferencesEvent {
  final QuietHoursEntity? quietHours;

  const UpdateQuietHours({this.quietHours});

  @override
  List<Object?> get props => [quietHours];
}

class UpdateNotificationPriority extends NotificationPreferencesEvent {
  final NotificationPriorityLevel priority;

  const UpdateNotificationPriority({required this.priority});

  @override
  List<Object?> get props => [priority];
}

class TestNotification extends NotificationPreferencesEvent {
  const TestNotification();
}
