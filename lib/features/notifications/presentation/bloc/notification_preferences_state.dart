part of 'notification_preferences_bloc.dart';

class NotificationPreferencesState extends Equatable {
  final bool isLoading;
  final NotificationPreferencesEntity? preferences;
  final String? errorMessage;
  final String? successMessage;

  const NotificationPreferencesState({
    this.isLoading = false,
    this.preferences,
    this.errorMessage,
    this.successMessage,
  });

  NotificationPreferencesState copyWith({
    bool? isLoading,
    NotificationPreferencesEntity? preferences,
    String? errorMessage,
    String? successMessage,
  }) {
    return NotificationPreferencesState(
      isLoading: isLoading ?? this.isLoading,
      preferences: preferences ?? this.preferences,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        preferences,
        errorMessage,
        successMessage,
      ];
}
