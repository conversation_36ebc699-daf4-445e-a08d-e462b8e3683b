import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/notifications/presentation/cubit/notification_history_cubit.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/core/di/injection.dart';

class NotificationHistoryScreen extends StatelessWidget {
  const NotificationHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    final userId = authState.isAuthenticated ? authState.user.id : '';

    return BlocProvider(
      create: (context) => NotificationHistoryCubit(
        notificationRepository: getIt(),
        userId: userId,
      ),
      child: const _NotificationHistoryView(),
    );
  }
}

class _NotificationHistoryView extends StatefulWidget {
  const _NotificationHistoryView({super.key});

  @override
  State<_NotificationHistoryView> createState() =>
      _NotificationHistoryViewState();
}

class _NotificationHistoryViewState extends State<_NotificationHistoryView> {
  String _selectedFilter = 'All';
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // Load notifications when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationHistoryCubit>().loadNotifications();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<NotificationHistoryCubit>().loadMoreNotifications();
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification History'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.pushNamed('notification-preferences');
            },
          ),
        ],
      ),
      body: BlocBuilder<NotificationHistoryCubit, NotificationHistoryState>(
        builder: (context, state) {
          return Column(
            children: [
              // Button tab navigation
              _buildTabNavigation(context, state),

              // Notification list
              Expanded(child: _buildNotificationList(context, state)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTabNavigation(
    BuildContext context,
    NotificationHistoryState state,
  ) {
    final theme = Theme.of(context);
    final filters = [
      {'name': 'All', 'icon': Icons.notifications_outlined},
      {'name': 'Unread', 'icon': Icons.mark_email_unread_outlined},
      {'name': 'Critical', 'icon': Icons.priority_high_outlined},
      {'name': 'Recent', 'icon': Icons.schedule_outlined},
    ];

    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Row(
        children:
            filters.map((filter) {
              final isSelected = _selectedFilter == filter['name'];
              return Expanded(
                child: _buildTabButton(
                  context: context,
                  icon: filter['icon'] as IconData,
                  label: filter['name'] as String,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedFilter = filter['name'] as String;
                    });
                  },
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTabButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: EdgeInsets.all(4.w),
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18.w,
              color:
                  isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(height: 4.h),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color:
                    isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 11.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationList(
    BuildContext context,
    NotificationHistoryState state,
  ) {
    if (state.isLoading && state.notifications.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return _buildErrorState(context, state.error!);
    }

    final filteredNotifications = _getFilteredNotifications(state);

    if (filteredNotifications.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await context.read<NotificationHistoryCubit>().refresh();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.fromLTRB(20.w, 8.h, 20.w, 20.h),
        itemCount: filteredNotifications.length + (state.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= filteredNotifications.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final notification = filteredNotifications[index];
          return _buildNotificationItem(context, notification);
        },
      ),
    );
  }

  List<IncidentNotificationEntity> _getFilteredNotifications(
    NotificationHistoryState state,
  ) {
    final notifications = state.notifications;

    switch (_selectedFilter) {
      case 'Unread':
        return notifications
            .where(
              (n) =>
                  n.status == IncidentNotificationStatus.sent ||
                  n.status == IncidentNotificationStatus.delivered,
            )
            .toList();
      case 'Critical':
        return notifications
            .where((n) => n.priority == IncidentNotificationPriority.critical)
            .toList();
      case 'Recent':
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        return notifications
            .where((n) => n.notificationSentAt.isAfter(yesterday))
            .toList();
      default:
        return notifications;
    }
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 60.w, color: Colors.red[400]),
            SizedBox(height: 24.h),
            Text(
              'Error loading notifications',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              error,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[500],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: () {
                context.read<NotificationHistoryCubit>().refresh();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120.w,
              height: 120.h,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.notifications_none_outlined,
                size: 60.w,
                color: Colors.grey[400],
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              'No notifications yet',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'You\'ll see incident notifications here when they\'re sent to your area',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[500],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            ElevatedButton.icon(
              onPressed: () {
                context.pushNamed('notification-preferences');
              },
              icon: Icon(Icons.settings, size: 18.w),
              label: const Text('Configure Notifications'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    IncidentNotificationEntity notification,
  ) {
    final priorityColor = _getPriorityColor(notification.priority);
    final isRead =
        notification.status == IncidentNotificationStatus.read ||
        notification.status == IncidentNotificationStatus.dismissed;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              isRead
                  ? Colors.grey.shade200
                  : priorityColor.withValues(alpha: 0.3),
          width: isRead ? 1 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: () {
            if (!isRead) {
              context.read<NotificationHistoryCubit>().markAsRead(
                notification.id,
              );
            }
          },
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 48.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    color: priorityColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    _getIncidentIcon(notification.incidentType),
                    color: priorityColor,
                    size: 24.w,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.incidentTitle,
                              style: TextStyle(
                                fontSize: 15.sp,
                                fontWeight:
                                    isRead ? FontWeight.w500 : FontWeight.w700,
                                color: Colors.grey[900],
                              ),
                            ),
                          ),
                          if (!isRead)
                            Container(
                              width: 8.w,
                              height: 8.h,
                              decoration: BoxDecoration(
                                color: priorityColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 6.h),
                      Text(
                        notification.incidentDescription,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.grey[600],
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          _buildInfoChip(
                            Icons.location_on_outlined,
                            _formatDistance(
                              notification.distanceFromUserMeters,
                            ),
                            Colors.blue,
                          ),
                          SizedBox(width: 8.w),
                          _buildInfoChip(
                            Icons.access_time_outlined,
                            _formatTime(notification.notificationSentAt),
                            Colors.grey,
                          ),
                          SizedBox(width: 8.w),
                          _buildPriorityChip(notification.priority),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12.w, color: color),
          SizedBox(width: 4.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 11.sp,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(IncidentNotificationPriority priority) {
    final color = _getPriorityColor(priority);
    final priorityText = priority.name.toUpperCase();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Text(
        priorityText,
        style: TextStyle(
          fontSize: 10.sp,
          color: color,
          fontWeight: FontWeight.w700,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Color _getPriorityColor(IncidentNotificationPriority priority) {
    switch (priority) {
      case IncidentNotificationPriority.critical:
        return Colors.red;
      case IncidentNotificationPriority.high:
        return Colors.orange;
      case IncidentNotificationPriority.medium:
        return Colors.blue;
      case IncidentNotificationPriority.low:
        return Colors.grey;
    }
  }

  String _formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      return '${(distanceMeters / 1000).toStringAsFixed(1)}km away';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  IconData _getIncidentIcon(String type) {
    switch (type) {
      case 'robbery':
        return Icons.person_remove;
      case 'assault':
        return Icons.warning;
      case 'theft':
        return Icons.shopping_bag;
      case 'suspicious_activity':
        return Icons.visibility;
      case 'emergency':
        return Icons.emergency;
      default:
        return Icons.notification_important;
    }
  }
}
