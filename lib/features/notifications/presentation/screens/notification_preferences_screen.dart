import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/core/services/toast_service.dart';
import 'package:respublicaseguridad/features/notifications/presentation/cubit/notification_preferences_cubit.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:google_fonts/google_fonts.dart';

class NotificationPreferencesScreen extends StatelessWidget {
  const NotificationPreferencesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    final userId = authState.isAuthenticated ? authState.user.id : '';

    return BlocProvider(
      create: (context) {
        final cubit = NotificationPreferencesCubit(
          preferencesRepository: getIt(),
          integrationService: getIt(),
          categoryService: getIt(),
          userId: userId,
        );

        // Initialize cubit and sync permission state
        _initializeCubitWithPermissionCheck(cubit);

        return cubit;
      },
      child: const _NotificationPreferencesView(),
    );
  }

  /// Initialize cubit and check system permissions
  Future<void> _initializeCubitWithPermissionCheck(
    NotificationPreferencesCubit cubit,
  ) async {
    // First initialize the cubit to load database preferences
    await cubit.initialize();

    // Then check if system permissions are granted but database shows not consented
    await _syncPermissionState(cubit);
  }

  /// Sync system permission state with database state
  Future<void> _syncPermissionState(NotificationPreferencesCubit cubit) async {
    try {
      // Check current system location permission
      final locationStatus = await Permission.location.status;

      // If system permission is granted but database shows not consented, sync them
      if (locationStatus.isGranted && !cubit.state.isLocationConsentGranted) {
        // Update database to reflect that user has already granted permission
        await cubit.grantLocationConsent(true);
      }
    } catch (e) {
      // Silently handle errors - don't disrupt the user experience
      debugPrint('Error syncing permission state: $e');
    }
  }
}

class _NotificationPreferencesView extends StatelessWidget {
  const _NotificationPreferencesView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            if (context.canPop()) {
              context.pop();
            } else {
              context.go('/more'); // Fallback to more screen
            }
          },
          icon: Icon(FluentIcons.arrow_left_24_regular),
          tooltip: 'Back',
        ),
        title: const Text('Notification Preferences'),
        actions: [
          IconButton(
            onPressed: () => _showPrivacyInfoDialog(context),
            icon: Icon(FluentIcons.info_24_regular),
            tooltip: 'Privacy Information',
          ),
        ],
      ),
      body: BlocBuilder<
        NotificationPreferencesCubit,
        NotificationPreferencesState
      >(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48.w, color: Colors.red),
                  SizedBox(height: 16.h),
                  Text(
                    'Error loading preferences',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    state.error!,
                    style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed:
                        () =>
                            context
                                .read<NotificationPreferencesCubit>()
                                .initialize(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            children: [
              _buildMainToggleCard(context, state),

              if (state.isLocationNotificationsEnabled) ...[
                SizedBox(height: 16.h),
                _buildLocationConsentCard(context, state),

                if (state.isLocationConsentGranted) ...[
                  SizedBox(height: 16.h),
                  _buildRadiusCard(context, state),
                ],
              ],

              SizedBox(height: 100.h), // Extra space for bottom navigation
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainToggleCard(
    BuildContext context,
    NotificationPreferencesState state,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final cubit = context.read<NotificationPreferencesCubit>();

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: isDarkMode ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow:
            isDarkMode
                ? []
                : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    offset: const Offset(0, 2),
                    blurRadius: 8.r,
                  ),
                ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            cubit.updateLocationNotificationsEnabled(
              !state.locationNotificationsEnabled,
            );
          },
          borderRadius: BorderRadius.circular(4.r),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color:
                        state.isLocationNotificationsEnabled
                            ? theme.colorScheme.primary.withValues(alpha: 0.15)
                            : theme.colorScheme.surfaceContainerHighest
                                .withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Icon(
                    state.isLocationNotificationsEnabled
                        ? FluentIcons.location_24_filled
                        : FluentIcons.location_24_regular,
                    color:
                        state.isLocationNotificationsEnabled
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurfaceVariant,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Location Notifications',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Get alerts about incidents near you',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch.adaptive(
                  value: state.isLocationNotificationsEnabled,
                  onChanged:
                      state.isAnyOperationInProgress
                          ? null
                          : (value) {
                            cubit.toggleLocationNotifications(value);
                          },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationConsentCard(
    BuildContext context,
    NotificationPreferencesState state,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final cubit = context.read<NotificationPreferencesCubit>();
    final statusColor =
        state.isLocationConsentGranted
            ? Colors.green
            : theme.colorScheme.primary;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: isDarkMode ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow:
            isDarkMode
                ? []
                : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.06),
                    offset: const Offset(0, 1),
                    blurRadius: 3.r,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    offset: const Offset(0, 4),
                    blurRadius: 12.r,
                  ),
                ],
      ),
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.r)),
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Icon(
                    state.locationConsentGranted
                        ? FluentIcons.shield_checkmark_24_filled
                        : FluentIcons.location_24_regular,
                    color: Colors.white,
                    size: 22.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Location Access',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child:
                            state.locationConsentGranted &&
                                    state.locationConsentGrantedAt != null
                                ? GestureDetector(
                                  onTap:
                                      () => _showDurationSelectionDialog(
                                        context,
                                        cubit,
                                        state,
                                      ),
                                  child: _buildCountdownTimer(
                                    state.locationConsentGrantedAt!,
                                    theme,
                                  ),
                                )
                                : Text(
                                  state.locationConsentGranted
                                      ? 'Active (24h)'
                                      : 'Required',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color:
                                        state.locationConsentGranted
                                            ? Colors.green.shade700
                                            : theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12.sp,
                                  ),
                                ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              state.locationConsentGranted
                  ? 'Location tracking is active and will automatically expire in 24 hours for your privacy. You can revoke access at any time.'
                  : 'Grant location access to receive incident notifications near you. Access automatically expires every 24 hours to protect your privacy.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 13.sp,
                height: 1.4,
              ),
            ),
            // Show selected incident types as chips when location consent is granted
            if (state.locationConsentGranted &&
                state.enabledIncidentTypes.isNotEmpty) ...[
              SizedBox(height: 16.h),
              Text(
                'Monitoring ${state.enabledIncidentTypes.length} incident types:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              Wrap(
                spacing: 6.w,
                runSpacing: 6.h,
                children:
                    state.availableCategories
                        .where(
                          (category) =>
                              state.enabledIncidentTypes.contains(category.key),
                        )
                        .map((category) {
                          final categoryColor = Color(
                            int.parse(category.color.replaceFirst('#', '0xFF')),
                          );

                          return Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: categoryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4.r),
                              border: Border.all(
                                color: categoryColor.withValues(alpha: 0.3),
                                width: 0.5,
                              ),
                            ),
                            child: Text(
                              category.title,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: categoryColor,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        })
                        .toList(),
              ),
            ],
            SizedBox(height: 20.h),
            if (state.locationConsentGranted) ...[
              CustomButton(
                text: 'Revoke Access',
                onPressed: () {
                  cubit.updateLocationConsentGranted(false);
                },
                buttonType: ButtonType.outlined,
                prefixIcon: Icon(
                  FluentIcons.shield_prohibited_24_regular,
                  size: 18.sp,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.red,
                ),
                height: 48.h,
              ),
            ] else ...[
              CustomButton(
                text: 'Grant Access',
                onPressed:
                    () => _showLocationPermissionChoiceDialog(
                      context,
                      cubit,
                      state,
                    ),
                buttonType: ButtonType.primary,
                prefixIcon: Icon(
                  FluentIcons.location_24_filled,
                  size: 18.sp,
                  color: Colors.white,
                ),
                height: 48.h,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRadiusCard(
    BuildContext context,
    NotificationPreferencesState state,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final cubit = context.read<NotificationPreferencesCubit>();

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: isDarkMode ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow:
            isDarkMode
                ? []
                : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    offset: const Offset(0, 2),
                    blurRadius: 8.r,
                  ),
                ],
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Icon(
                    FluentIcons.target_24_regular,
                    color: theme.colorScheme.primary,
                    size: 22.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification Radius',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          '${state.notificationRadius.toStringAsFixed(1)} km',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Slider.adaptive(
              value: state.notificationRadius,
              min: 0.5,
              max: 10.0,
              divisions: 19,
              onChanged: (value) {
                cubit.updateNotificationRadius(value);
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '0.5 km',
                  style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                ),
                Text(
                  '10 km',
                  style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showPrivacyInfoDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FluentIcons.shield_keyhole_24_regular,
                  color: CupertinoColors.systemBlue,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                const Text('Privacy & Data'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.h),
                _buildPrivacyPoint('24-hour consent expiration'),
                _buildPrivacyPoint('Encrypted location data'),
                _buildPrivacyPoint('Automatic data deletion'),
                _buildPrivacyPoint('No tracking without consent'),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.pushNamed('notification-history');
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      FluentIcons.history_24_regular,
                      size: 16.sp,
                      color: CupertinoColors.systemBlue,
                    ),
                    SizedBox(width: 6.w),
                    const Text('History'),
                  ],
                ),
              ),
              CupertinoDialogAction(
                isDestructiveAction: true,
                onPressed: () {
                  Navigator.of(context).pop();
                  _showDataDeletionDialog(context);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      FluentIcons.delete_24_regular,
                      size: 16.sp,
                      color: CupertinoColors.destructiveRed,
                    ),
                    SizedBox(width: 6.w),
                    const Text('Delete Data'),
                  ],
                ),
              ),
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildPrivacyPoint(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            FluentIcons.checkmark_circle_24_filled,
            size: 14.sp,
            color: CupertinoColors.systemGreen,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 13.sp,
                color: CupertinoColors.secondaryLabel,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDataDeletionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Delete All Data'),
            content: Text(
              'This will permanently delete all your notification preferences, history, and location data. This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ToastService.showError('All notification data deleted');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text('Delete'),
              ),
            ],
          ),
    );
  }

  /// Show location permission choice dialog
  void _showLocationPermissionChoiceDialog(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    NotificationPreferencesState state,
  ) {
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(
              'Location Permission',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.h),
                Text(
                  'Choose your location access preference:',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: CupertinoColors.secondaryLabel,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),

                // Foreground Only Option
                _buildCompactPermissionOption(
                  title: 'Foreground Only',
                  description: 'When app is open',
                  icon: FluentIcons.location_24_regular,
                  isRecommended: false,
                  onTap: () {
                    Navigator.of(context).pop();
                    _requestForegroundLocationPermission(context, cubit, state);
                  },
                ),

                SizedBox(height: 8.h),

                // Both Permissions Option
                _buildCompactPermissionOption(
                  title: 'Both ',
                  description: 'Even when app is closed',
                  icon: FluentIcons.location_24_filled,
                  isRecommended: true,
                  onTap: () {
                    Navigator.of(context).pop();
                    _requestBothLocationPermissions(context, cubit, state);
                  },
                ),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                isDestructiveAction: true,
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel', style: GoogleFonts.outfit()),
              ),
            ],
          ),
    );
  }

  /// Build compact permission option widget for dialog
  Widget _buildCompactPermissionOption({
    required String title,
    required String description,
    required IconData icon,
    required bool isRecommended,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: CupertinoColors.systemGrey6,
          borderRadius: BorderRadius.circular(8.r),
          border:
              isRecommended
                  ? Border.all(
                    color: CupertinoColors.activeBlue.withOpacity(0.3),
                    width: 1.5,
                  )
                  : null,
        ),
        child: Row(
          children: [
            Icon(icon, color: CupertinoColors.activeBlue, size: 18.sp),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title with wrapping
                  Text(
                    title,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: CupertinoColors.label,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // Recommended badge on separate line if needed
                  if (isRecommended) ...[
                    SizedBox(height: 4.h),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 6.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: CupertinoColors.activeBlue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        'Recommended',
                        style: GoogleFonts.outfit(
                          fontSize: 9.sp,
                          fontWeight: FontWeight.w600,
                          color: CupertinoColors.activeBlue,
                        ),
                      ),
                    ),
                  ],

                  SizedBox(height: 4.h),
                  Text(
                    description,
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      color: CupertinoColors.secondaryLabel,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              CupertinoIcons.chevron_right,
              color: CupertinoColors.tertiaryLabel,
              size: 14.sp,
            ),
          ],
        ),
      ),
    );
  }

  /// Show incident type selection dialog
  Future<void> _showIncidentTypeSelectionDialog(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    NotificationPreferencesState state,
    bool requestBackgroundLocation,
  ) async {
    // Create a temporary list to track selections in the dialog
    // Default to existing enabled types, or all types if none are enabled
    List<String> tempSelectedTypes =
        state.enabledIncidentTypes.isNotEmpty
            ? List.from(state.enabledIncidentTypes)
            : state.availableCategories
                .map((category) => category.key)
                .toList();

    await showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => CupertinoAlertDialog(
                  title: Text(
                    'Select Incident Types',
                    style: GoogleFonts.outfit(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 16.h),
                      Text(
                        'Choose which types of security incidents you want to be notified about:',
                        style: GoogleFonts.outfit(fontSize: 14.sp),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16.h),
                      // Incident type checkboxes
                      Wrap(
                        spacing: 10.w,
                        runSpacing: 12.h,
                        children:
                            state.availableCategories.map((category) {
                              final isSelected = tempSelectedTypes.contains(
                                category.key,
                              );
                              final categoryColor = Color(
                                int.parse(
                                  category.color.replaceFirst('#', '0xFF'),
                                ),
                              );

                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    if (isSelected) {
                                      tempSelectedTypes.remove(category.key);
                                    } else {
                                      tempSelectedTypes.add(category.key);
                                    }
                                  });
                                },
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 12.w,
                                    vertical: 8.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected
                                            ? categoryColor.withValues(
                                              alpha: 0.1,
                                            )
                                            : Colors.transparent,
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(
                                      color:
                                          isSelected
                                              ? categoryColor
                                              : CupertinoColors.systemGrey4,
                                      width: 1.5,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        isSelected
                                            ? CupertinoIcons
                                                .check_mark_circled_solid
                                            : CupertinoIcons.circle,
                                        color:
                                            isSelected
                                                ? categoryColor
                                                : CupertinoColors.systemGrey,
                                        size: 16.sp,
                                      ),
                                      SizedBox(width: 6.w),
                                      Text(
                                        category.title,
                                        style: GoogleFonts.outfit(
                                          fontSize: 12.sp,
                                          color:
                                              isSelected
                                                  ? categoryColor
                                                  : CupertinoColors.label,
                                          fontWeight:
                                              isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                    ],
                  ),
                  actions: [
                    CupertinoDialogAction(
                      isDestructiveAction: true,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('Cancel', style: GoogleFonts.outfit()),
                    ),
                    CupertinoDialogAction(
                      isDefaultAction: true,
                      onPressed: () async {
                        Navigator.of(context).pop();

                        // Grant location consent with selected types
                        await _grantLocationConsentWithPermissions(
                          context,
                          cubit,
                          tempSelectedTypes,
                          requestBackgroundLocation,
                        );
                      },
                      child: Text('Grant Access', style: GoogleFonts.outfit()),
                    ),
                  ],
                ),
          ),
    );
  }

  /// Request foreground location permission only
  Future<void> _requestForegroundLocationPermission(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    NotificationPreferencesState state,
  ) async {
    await _showIncidentTypeSelectionDialog(context, cubit, state, false);
  }

  /// Request both foreground and background location permissions
  Future<void> _requestBothLocationPermissions(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    NotificationPreferencesState state,
  ) async {
    // Show incident type selection dialog first
    await _showIncidentTypeSelectionDialog(context, cubit, state, true);
  }

  /// Grant location consent and request appropriate permissions
  Future<void> _grantLocationConsentWithPermissions(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    List<String> selectedIncidentTypes,
    bool requestBackgroundLocation,
  ) async {
    try {
      // Grant location consent
      await cubit.grantLocationConsent(true);

      // Update incident types to match selection
      // First, get current state to know what needs to be changed
      final currentEnabledTypes = cubit.state.enabledIncidentTypes;

      // Disable types that are currently enabled but not selected
      for (final type in currentEnabledTypes) {
        if (!selectedIncidentTypes.contains(type)) {
          cubit.toggleIncidentType(type); // This will disable it
        }
      }

      // Enable types that are selected but not currently enabled
      for (final type in selectedIncidentTypes) {
        if (!currentEnabledTypes.contains(type)) {
          cubit.toggleIncidentType(type); // This will enable it
        }
      }

      // Show appropriate permission explanation
      if (requestBackgroundLocation) {
        await _showBackgroundLocationExplanation(context);
      }

      // Request actual location permissions
      await _requestLocationPermissions(
        context,
        cubit,
        requestBackgroundLocation,
      );
    } catch (e) {
      // Handle errors
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to grant location access: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show explanation for background location permission
  Future<void> _showBackgroundLocationExplanation(BuildContext context) async {
    await showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(
              'Background Location',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 16.h),
                Text(
                  'To receive security notifications even when the app is closed, we need background location access.\n\nThis allows us to alert you about incidents near your location 24/7.',
                  style: GoogleFonts.outfit(fontSize: 14.sp),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.of(context).pop(),
                child: Text('I Understand', style: GoogleFonts.outfit()),
              ),
            ],
          ),
    );
  }

  /// Request location permissions from the system
  Future<void> _requestLocationPermissions(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    bool requestBackgroundLocation,
  ) async {
    try {
      // Step 1: Request foreground location permission
      PermissionStatus locationStatus = await Permission.location.request();

      if (locationStatus.isDenied) {
        await _showPermissionDeniedDialog(context, 'Location', false);
        return;
      }

      if (locationStatus.isPermanentlyDenied) {
        await _showPermissionPermanentlyDeniedDialog(context, 'Location');
        return;
      }

      // Step 2: If foreground granted and background requested, request background
      if (locationStatus.isGranted && requestBackgroundLocation) {
        if (Platform.isAndroid) {
          // On Android, request background location separately
          PermissionStatus backgroundStatus =
              await Permission.locationAlways.request();

          if (backgroundStatus.isDenied) {
            await _showPermissionDeniedDialog(
              context,
              'Background Location',
              true,
            );
            // Still proceed with foreground-only
          } else if (backgroundStatus.isPermanentlyDenied) {
            await _showPermissionPermanentlyDeniedDialog(
              context,
              'Background Location',
            );
            // Still proceed with foreground-only
          }
        }
        // On iOS, background location is handled automatically with proper Info.plist
      }

      // Step 3: Update database state to reflect granted permissions
      if (locationStatus.isGranted) {
        cubit.grantLocationConsent(true);
      }

      // Step 4: Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              requestBackgroundLocation
                  ? 'Location permissions granted! You\'ll receive notifications even when the app is closed.'
                  : 'Location permission granted! You\'ll receive notifications when the app is open.',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show dialog when permission is denied
  Future<void> _showPermissionDeniedDialog(
    BuildContext context,
    String permissionName,
    bool isBackgroundLocation,
  ) async {
    await showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(
              '$permissionName Permission Denied',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 16.h),
                Text(
                  isBackgroundLocation
                      ? 'Background location access was denied. You\'ll only receive notifications when the app is open.\n\nYou can enable it later in Settings if needed.'
                      : 'Location access is required for security notifications. Please grant permission to continue.',
                  style: GoogleFonts.outfit(fontSize: 14.sp),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK', style: GoogleFonts.outfit()),
              ),
            ],
          ),
    );
  }

  /// Show dialog when permission is permanently denied
  Future<void> _showPermissionPermanentlyDeniedDialog(
    BuildContext context,
    String permissionName,
  ) async {
    await showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(
              '$permissionName Permission Required',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 16.h),
                Text(
                  'Location permission has been permanently denied. Please enable it in Settings to receive security notifications.',
                  style: GoogleFonts.outfit(fontSize: 14.sp),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel', style: GoogleFonts.outfit()),
              ),
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: Text('Open Settings', style: GoogleFonts.outfit()),
              ),
            ],
          ),
    );
  }

  /// Build countdown timer widget for location access
  Widget _buildCountdownTimer(DateTime grantedAt, ThemeData theme) {
    return BlocBuilder<
      NotificationPreferencesCubit,
      NotificationPreferencesState
    >(
      builder: (context, state) {
        return StreamBuilder<DateTime>(
          stream: Stream.periodic(
            const Duration(seconds: 1),
            (_) => DateTime.now(),
          ),
          builder: (context, snapshot) {
            final now = snapshot.data ?? DateTime.now();
            final durationHours =
                state.preferences?.locationAccessDurationHours ?? 24.0;
            final expiresAt = grantedAt.add(
              Duration(
                hours: durationHours.floor(),
                minutes: ((durationHours % 1) * 60).round(),
              ),
            );
            final remaining = expiresAt.difference(now);

            if (remaining.isNegative) {
              // Access has expired
              return Text(
                'Expired',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.w600,
                  fontSize: 12.sp,
                ),
              );
            }

            // Format remaining time
            final hours = remaining.inHours;
            final minutes = remaining.inMinutes % 60;
            final seconds = remaining.inSeconds % 60;

            String timeText;
            if (hours > 0) {
              timeText = '${hours}h ${minutes}m';
            } else if (minutes > 0) {
              timeText = '${minutes}m ${seconds}s';
            } else {
              timeText = '${seconds}s';
            }

            return Text(
              timeText,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
                fontSize: 12.sp,
              ),
            );
          },
        );
      },
    );
  }

  /// Show duration selection dialog for location access
  void _showDurationSelectionDialog(
    BuildContext context,
    NotificationPreferencesCubit cubit,
    NotificationPreferencesState state,
  ) {
    final durations = [
      {'label': '30 minutes', 'hours': 0.5},
      {'label': '1 hour', 'hours': 1.0},
      {'label': '2 hours', 'hours': 2.0},
      {'label': '4 hours', 'hours': 4.0},
      {'label': '8 hours', 'hours': 8.0},
      {'label': '12 hours', 'hours': 12.0},
      {'label': '24 hours', 'hours': 24.0},
    ];

    showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(
              'Select Access Duration',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 16.h),
                Text(
                  'Choose how long location access should remain active:',
                  style: GoogleFonts.outfit(fontSize: 14.sp),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                // Duration options
                ...durations.map((duration) {
                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 4.h),
                    child: CupertinoButton(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      onPressed: () {
                        final hours = duration['hours'] as double;
                        cubit.updateLocationAccessDuration(hours);
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        duration['label'] as String,
                        style: GoogleFonts.outfit(
                          fontSize: 16.sp,
                          color: CupertinoColors.systemBlue,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel', style: GoogleFonts.outfit()),
              ),
            ],
          ),
    );
  }
}
