part of 'notification_preferences_cubit.dart';

class NotificationPreferencesState extends Equatable {
  final NotificationPreferencesEntity? preferences;
  final List<IncidentCategoryEntity> availableCategories;
  final bool isLoading;
  final bool isUpdating;
  final String? error;

  const NotificationPreferencesState({
    this.preferences,
    this.availableCategories = const [],
    this.isLoading = false,
    this.isUpdating = false,
    this.error,
  });

  NotificationPreferencesState copyWith({
    NotificationPreferencesEntity? preferences,
    List<IncidentCategoryEntity>? availableCategories,
    bool? isLoading,
    bool? isUpdating,
    String? error,
  }) {
    return NotificationPreferencesState(
      preferences: preferences ?? this.preferences,
      availableCategories: availableCategories ?? this.availableCategories,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      error: error,
    );
  }

  @override
  List<Object?> get props => [
    preferences,
    availableCategories,
    isLoading,
    isUpdating,
    error,
  ];

  // Convenience getters
  bool get hasPreferences => preferences != null;
  bool get isLocationNotificationsEnabled =>
      preferences?.isLocationNotificationsEnabled ?? false;
  bool get isLocationConsentGranted =>
      preferences?.isLocationTrackingConsented ?? false;
  double get notificationRadius => preferences?.notificationRadiusKm ?? 2.0;
  List<String> get enabledIncidentTypes =>
      preferences?.enabledIncidentTypes ?? [];
  QuietHoursEntity? get quietHours => preferences?.quietHours;

  // Legacy getters for backward compatibility
  bool get locationNotificationsEnabled => isLocationNotificationsEnabled;
  bool get locationConsentGranted => isLocationConsentGranted;
  DateTime? get locationConsentGrantedAt =>
      preferences?.locationConsentGrantedAt;

  bool get hasError => error != null;

  bool get canGrantLocationConsent =>
      isLocationNotificationsEnabled && !isLocationConsentGranted;
  bool get canConfigureRadius =>
      isLocationNotificationsEnabled && isLocationConsentGranted;

  bool get isAnyOperationInProgress => isLoading || isUpdating;
}
