import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/incident_notification_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/incident_notification_repository.dart';

part 'notification_history_state.dart';

class NotificationHistoryCubit extends Cubit<NotificationHistoryState> {
  final IncidentNotificationRepository _notificationRepository;
  final String _userId;

  NotificationHistoryCubit({
    required IncidentNotificationRepository notificationRepository,
    required String userId,
  }) : _notificationRepository = notificationRepository,
       _userId = userId,
       super(const NotificationHistoryState());

  /// Load notification history for the current user
  Future<void> loadNotifications({
    int? limit = 50,
    DateTime? since,
    bool refresh = false,
  }) async {
    if (!refresh && state.isLoading) return;

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _notificationRepository.getUserNotifications(
      _userId,
      limit: limit,
      since: since,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (notifications) => emit(
        state.copyWith(
          isLoading: false,
          notifications: notifications,
          error: null,
          lastUpdated: DateTime.now(),
        ),
      ),
    );
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (state.isLoadingMore || state.notifications.isEmpty) return;

    emit(state.copyWith(isLoadingMore: true));

    final oldestNotification = state.notifications.last;
    final result = await _notificationRepository.getUserNotifications(
      _userId,
      limit: 20,
      since: oldestNotification.notificationSentAt,
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoadingMore: false, error: failure.message)),
      (newNotifications) {
        // Filter out duplicates and add new notifications
        final existingIds = state.notifications.map((n) => n.id).toSet();
        final filteredNew =
            newNotifications.where((n) => !existingIds.contains(n.id)).toList();

        emit(
          state.copyWith(
            isLoadingMore: false,
            notifications: [...state.notifications, ...filteredNew],
            hasMoreData: filteredNew.isNotEmpty,
          ),
        );
      },
    );
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    final result = await _notificationRepository.markNotificationAsRead(
      notificationId,
    );

    result.fold((failure) => emit(state.copyWith(error: failure.message)), (_) {
      // Update local state
      final updatedNotifications =
          state.notifications.map((notification) {
            if (notification.id == notificationId) {
              return notification.copyWith(
                status: IncidentNotificationStatus.read,
              );
            }
            return notification;
          }).toList();

      emit(state.copyWith(notifications: updatedNotifications));
    });
  }

  /// Mark notification as dismissed
  Future<void> markAsDismissed(String notificationId) async {
    final result = await _notificationRepository.markNotificationAsDismissed(
      notificationId,
    );

    result.fold((failure) => emit(state.copyWith(error: failure.message)), (_) {
      // Update local state
      final updatedNotifications =
          state.notifications.map((notification) {
            if (notification.id == notificationId) {
              return notification.copyWith(
                status: IncidentNotificationStatus.dismissed,
              );
            }
            return notification;
          }).toList();

      emit(state.copyWith(notifications: updatedNotifications));
    });
  }

  /// Filter notifications by type
  void filterByType(String? incidentType) {
    emit(state.copyWith(selectedFilter: incidentType));
  }

  /// Get filtered notifications based on current filter
  List<IncidentNotificationEntity> get filteredNotifications {
    if (state.selectedFilter == null || state.selectedFilter == 'All') {
      return state.notifications;
    }

    return state.notifications
        .where(
          (notification) => notification.incidentType == state.selectedFilter,
        )
        .toList();
  }

  /// Get unread notification count
  Future<void> loadUnreadCount() async {
    final result = await _notificationRepository.getUnreadNotificationCount(
      _userId,
    );

    result.fold(
      (failure) => emit(state.copyWith(error: failure.message)),
      (count) => emit(state.copyWith(unreadCount: count)),
    );
  }

  /// Refresh notifications
  Future<void> refresh() async {
    await loadNotifications(refresh: true);
    await loadUnreadCount();
  }

  /// Clear error state
  void clearError() {
    emit(state.copyWith(error: null));
  }
}
