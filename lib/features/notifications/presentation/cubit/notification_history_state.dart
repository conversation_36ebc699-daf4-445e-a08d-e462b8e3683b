part of 'notification_history_cubit.dart';

class NotificationHistoryState extends Equatable {
  final List<IncidentNotificationEntity> notifications;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final String? selectedFilter;
  final int unreadCount;
  final bool hasMoreData;
  final DateTime? lastUpdated;

  const NotificationHistoryState({
    this.notifications = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.selectedFilter,
    this.unreadCount = 0,
    this.hasMoreData = true,
    this.lastUpdated,
  });

  NotificationHistoryState copyWith({
    List<IncidentNotificationEntity>? notifications,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    String? selectedFilter,
    int? unreadCount,
    bool? hasMoreData,
    DateTime? lastUpdated,
  }) {
    return NotificationHistoryState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      unreadCount: unreadCount ?? this.unreadCount,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  List<Object?> get props => [
        notifications,
        isLoading,
        isLoadingMore,
        error,
        selectedFilter,
        unreadCount,
        hasMoreData,
        lastUpdated,
      ];
}
