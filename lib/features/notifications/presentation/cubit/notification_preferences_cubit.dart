import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/notifications/domain/entities/notification_preferences_entity.dart';
import 'package:respublicaseguridad/features/notifications/domain/repositories/notification_preferences_repository.dart';
import 'package:respublicaseguridad/features/notifications/data/services/notification_integration_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_category_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/services/incident_category_service.dart';

part 'notification_preferences_state.dart';

/// Cubit for managing notification preferences with cloud function integration
class NotificationPreferencesCubit extends Cubit<NotificationPreferencesState> {
  final NotificationPreferencesRepository _preferencesRepository;
  final NotificationIntegrationService _integrationService;
  final IncidentCategoryService _categoryService;
  final String _userId;

  NotificationPreferencesCubit({
    required NotificationPreferencesRepository preferencesRepository,
    required NotificationIntegrationService integrationService,
    required IncidentCategoryService categoryService,
    required String userId,
  }) : _preferencesRepository = preferencesRepository,
       _integrationService = integrationService,
       _categoryService = categoryService,
       _userId = userId,
       super(const NotificationPreferencesState());

  /// Initialize and load user preferences
  Future<void> initialize() async {
    emit(state.copyWith(isLoading: true));

    try {
      // Load user preferences
      final preferencesResult = await _preferencesRepository.getUserPreferences(
        _userId,
      );

      // Load incident categories
      final categoriesResult = await _categoryService.getCategories();

      preferencesResult.fold(
        (failure) =>
            emit(state.copyWith(isLoading: false, error: failure.message)),
        (preferences) {
          categoriesResult.fold(
            (failure) => emit(
              state.copyWith(
                isLoading: false,
                preferences: preferences,
                error: 'Failed to load incident categories: ${failure.message}',
              ),
            ),
            (categories) {
              final activeCategories =
                  categories.where((category) => category.isActive).toList()
                    ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

              emit(
                state.copyWith(
                  isLoading: false,
                  preferences: preferences,
                  availableCategories: activeCategories,
                  error: null,
                ),
              );
            },
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to initialize preferences: $e',
        ),
      );
    }
  }

  /// Toggle location notifications
  Future<void> toggleLocationNotifications(bool enabled) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      final result = await _preferencesRepository.setNotificationsEnabled(
        _userId,
        enabled,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (_) async {
          // Update local state
          final updatedPreferences = state.preferences!.copyWith(
            isLocationNotificationsEnabled: enabled,
            updatedAt: DateTime.now(),
          );

          // Sync with cloud functions
          await _integrationService.updatePreferences(updatedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: updatedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update location notifications: $e',
        ),
      );
    }
  }

  /// Grant location consent
  Future<void> grantLocationConsent(bool granted) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      final result = await _preferencesRepository.updateLocationTrackingConsent(
        _userId,
        granted,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (_) async {
          // Update local state
          final updatedPreferences = state.preferences!.copyWith(
            isLocationTrackingConsented: granted,
            locationConsentGrantedAt: granted ? DateTime.now() : null,
            updatedAt: DateTime.now(),
          );

          // Sync with cloud functions
          await _integrationService.updatePreferences(updatedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: updatedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update location consent: $e',
        ),
      );
    }
  }

  /// Update notification radius
  Future<void> updateNotificationRadius(double radiusKm) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      final result = await _preferencesRepository.updateNotificationRadius(
        _userId,
        radiusKm,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (_) async {
          // Update local state
          final updatedPreferences = state.preferences!.copyWith(
            notificationRadiusKm: radiusKm,
            updatedAt: DateTime.now(),
          );

          // Sync with cloud functions
          await _integrationService.updatePreferences(updatedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: updatedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update notification radius: $e',
        ),
      );
    }
  }

  /// Toggle incident type
  Future<void> toggleIncidentType(String categoryKey) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      final currentTypes = List<String>.from(
        state.preferences!.enabledIncidentTypes,
      );

      if (currentTypes.contains(categoryKey)) {
        currentTypes.remove(categoryKey);
      } else {
        currentTypes.add(categoryKey);
      }

      final result = await _preferencesRepository.updateEnabledIncidentTypes(
        _userId,
        currentTypes,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (_) async {
          // Update local state
          final updatedPreferences = state.preferences!.copyWith(
            enabledIncidentTypes: currentTypes,
            updatedAt: DateTime.now(),
          );

          // Sync with cloud functions
          await _integrationService.updatePreferences(updatedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: updatedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update incident types: $e',
        ),
      );
    }
  }

  /// Update quiet hours
  Future<void> updateQuietHours(QuietHoursEntity? quietHours) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      final result = await _preferencesRepository.updateQuietHours(
        _userId,
        quietHours,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (_) async {
          // Update local state
          final updatedPreferences = state.preferences!.copyWith(
            quietHours: quietHours,
            updatedAt: DateTime.now(),
          );

          // Sync with cloud functions
          await _integrationService.updatePreferences(updatedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: updatedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update quiet hours: $e',
        ),
      );
    }
  }

  /// Clear error
  void clearError() {
    emit(state.copyWith(error: null));
  }

  /// Legacy method for backward compatibility
  Future<void> updateLocationNotificationsEnabled(bool enabled) async {
    await toggleLocationNotifications(enabled);
  }

  /// Legacy method for backward compatibility
  Future<void> updateLocationConsentGranted(bool granted) async {
    await grantLocationConsent(granted);
  }

  /// Update location access duration
  Future<void> updateLocationAccessDuration(double hours) async {
    if (state.preferences == null) return;

    emit(state.copyWith(isUpdating: true));

    try {
      // Update local state
      final updatedPreferences = state.preferences!.copyWith(
        locationAccessDurationHours: hours,
        updatedAt: DateTime.now(),
      );

      // Save to repository
      final result = await _preferencesRepository.updateUserPreferences(
        updatedPreferences,
      );

      result.fold(
        (failure) =>
            emit(state.copyWith(isUpdating: false, error: failure.message)),
        (savedPreferences) async {
          // Sync with cloud functions
          await _integrationService.updatePreferences(savedPreferences);

          emit(
            state.copyWith(
              isUpdating: false,
              preferences: savedPreferences,
              error: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUpdating: false,
          error: 'Failed to update location access duration: $e',
        ),
      );
    }
  }
}
