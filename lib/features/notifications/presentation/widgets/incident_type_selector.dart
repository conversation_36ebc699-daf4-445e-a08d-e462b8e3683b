import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class IncidentTypeSelector extends StatelessWidget {
  final List<String> enabledTypes;
  final ValueChanged<List<String>> onTypesChanged;

  const IncidentTypeSelector({
    super.key,
    required this.enabledTypes,
    required this.onTypesChanged,
  });

  static const List<IncidentTypeData> _incidentTypes = [
    IncidentTypeData(
      key: 'robbery',
      title: 'Robbery',
      description: 'Theft with force or threat',
      icon: Icons.money_off,
      color: Colors.red,
    ),
    IncidentTypeData(
      key: 'assault',
      title: 'Assault',
      description: 'Physical attacks or violence',
      icon: Icons.warning,
      color: Colors.red,
    ),
    IncidentTypeData(
      key: 'theft',
      title: 'Theft',
      description: 'Stealing without force',
      icon: Icons.shopping_bag_outlined,
      color: Colors.orange,
    ),
    IncidentTypeData(
      key: 'vandalism',
      title: 'Vandalism',
      description: 'Property damage or graffiti',
      icon: Icons.broken_image,
      color: Colors.orange,
    ),
    IncidentTypeData(
      key: 'suspicious_activity',
      title: 'Suspicious Activity',
      description: 'Unusual or concerning behavior',
      icon: Icons.visibility,
      color: Colors.amber,
    ),
    IncidentTypeData(
      key: 'emergency',
      title: 'Emergency',
      description: 'Urgent situations requiring help',
      icon: Icons.emergency,
      color: Colors.red,
    ),
    IncidentTypeData(
      key: 'accident',
      title: 'Accident',
      description: 'Traffic or other accidents',
      icon: Icons.car_crash,
      color: Colors.blue,
    ),
    IncidentTypeData(
      key: 'fire',
      title: 'Fire',
      description: 'Fire incidents or smoke',
      icon: Icons.local_fire_department,
      color: Colors.red,
    ),
    IncidentTypeData(
      key: 'medical_emergency',
      title: 'Medical Emergency',
      description: 'Health-related emergencies',
      icon: Icons.local_hospital,
      color: Colors.red,
    ),
    IncidentTypeData(
      key: 'other',
      title: 'Other',
      description: 'Other security incidents',
      icon: Icons.more_horiz,
      color: Colors.grey,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.category,
                  size: 20.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Incident Types',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              enabledTypes.isEmpty 
                  ? 'All incident types enabled'
                  : '${enabledTypes.length} of ${_incidentTypes.length} types enabled',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Select All / Deselect All buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      onTypesChanged(_incidentTypes.map((type) => type.key).toList());
                    },
                    child: Text('Select All'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      onTypesChanged([]);
                    },
                    child: Text('Clear All'),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Incident type chips
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: _incidentTypes.map((incidentType) {
                final isSelected = enabledTypes.isEmpty || enabledTypes.contains(incidentType.key);
                
                return FilterChip(
                  selected: isSelected,
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        incidentType.icon,
                        size: 16.w,
                        color: isSelected 
                            ? Colors.white 
                            : incidentType.color,
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        incidentType.title,
                        style: GoogleFonts.outfit(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? Colors.white : null,
                        ),
                      ),
                    ],
                  ),
                  selectedColor: incidentType.color,
                  checkmarkColor: Colors.white,
                  onSelected: (selected) {
                    List<String> newTypes = List.from(enabledTypes);
                    
                    if (selected) {
                      if (!newTypes.contains(incidentType.key)) {
                        newTypes.add(incidentType.key);
                      }
                    } else {
                      newTypes.remove(incidentType.key);
                    }
                    
                    onTypesChanged(newTypes);
                  },
                );
              }).toList(),
            ),
            
            SizedBox(height: 16.h),
            
            // Info box
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16.w,
                    color: Colors.blue,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'If no types are selected, you\'ll receive notifications for all incident types.',
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class IncidentTypeData {
  final String key;
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const IncidentTypeData({
    required this.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
