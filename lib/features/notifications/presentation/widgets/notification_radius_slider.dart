import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class NotificationRadiusSlider extends StatelessWidget {
  final double currentRadius;
  final ValueChanged<double> onRadiusChanged;

  const NotificationRadiusSlider({
    super.key,
    required this.currentRadius,
    required this.onRadiusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 20.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Notification Radius',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    '${currentRadius.toStringAsFixed(1)} km',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            Text(
              'Get notified about incidents within this radius of your location',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            
            SizedBox(height: 16.h),
            
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                valueIndicatorColor: Theme.of(context).colorScheme.primary,
                valueIndicatorTextStyle: GoogleFonts.outfit(
                  color: Colors.white,
                  fontSize: 12.sp,
                ),
              ),
              child: Slider(
                value: currentRadius,
                min: 0.5,
                max: 10.0,
                divisions: 19, // 0.5 km increments
                label: '${currentRadius.toStringAsFixed(1)} km',
                onChanged: onRadiusChanged,
              ),
            ),
            
            SizedBox(height: 8.h),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '0.5 km',
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    color: Colors.grey[500],
                  ),
                ),
                Text(
                  '10 km',
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Radius description
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: _getRadiusColor(currentRadius).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: _getRadiusColor(currentRadius).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getRadiusIcon(currentRadius),
                    size: 16.w,
                    color: _getRadiusColor(currentRadius),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _getRadiusDescription(currentRadius),
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: _getRadiusColor(currentRadius),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRadiusColor(double radius) {
    if (radius <= 1.0) {
      return Colors.green;
    } else if (radius <= 3.0) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  IconData _getRadiusIcon(double radius) {
    if (radius <= 1.0) {
      return Icons.location_city;
    } else if (radius <= 3.0) {
      return Icons.location_on;
    } else {
      return Icons.public;
    }
  }

  String _getRadiusDescription(double radius) {
    if (radius <= 1.0) {
      return 'Immediate vicinity - Very close incidents only';
    } else if (radius <= 3.0) {
      return 'Local area - Nearby incidents in your neighborhood';
    } else if (radius <= 5.0) {
      return 'Extended area - Incidents in surrounding areas';
    } else {
      return 'Wide area - Incidents across a large region';
    }
  }
}
