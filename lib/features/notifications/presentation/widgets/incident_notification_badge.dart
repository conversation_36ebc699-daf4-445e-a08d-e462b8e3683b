import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/incident_integration_service.dart';

/// Widget to display notification statistics on incident cards
class IncidentNotificationBadge extends StatelessWidget {
  final String incidentId;
  final IncidentNotificationStats? stats;
  final VoidCallback? onTap;

  const IncidentNotificationBadge({
    super.key,
    required this.incidentId,
    this.stats,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (stats == null || stats!.totalNotificationsSent == 0) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: _getBadgeColor().withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _getBadgeColor().withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.notifications_active,
              size: 14.w,
              color: _getBadgeColor(),
            ),
            SizedBox(width: 4.w),
            Text(
              '${stats!.totalNotificationsSent}',
              style: GoogleFonts.outfit(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: _getBadgeColor(),
              ),
            ),
            if (stats!.deliveryRate < 0.8) ...[
              SizedBox(width: 4.w),
              Icon(
                Icons.warning,
                size: 12.w,
                color: Colors.orange,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getBadgeColor() {
    if (stats!.totalNotificationsSent == 0) {
      return Colors.grey;
    } else if (stats!.totalNotificationsSent < 10) {
      return Colors.blue;
    } else if (stats!.totalNotificationsSent < 50) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Widget to display detailed notification information
class IncidentNotificationDetails extends StatelessWidget {
  final IncidentNotificationStats stats;

  const IncidentNotificationDetails({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications_active,
                  size: 20.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Notification Statistics',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Statistics grid
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Sent',
                    stats.totalNotificationsSent.toString(),
                    Icons.send,
                    Colors.blue,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildStatItem(
                    'Delivered',
                    stats.notificationsDelivered.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Read',
                    stats.notificationsRead.toString(),
                    Icons.visibility,
                    Colors.purple,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildStatItem(
                    'Avg Distance',
                    '${(stats.averageDistance / 1000).toStringAsFixed(1)}km',
                    Icons.location_on,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Delivery and read rates
            _buildRateIndicator(
              'Delivery Rate',
              stats.deliveryRate,
              Colors.green,
            ),
            
            SizedBox(height: 8.h),
            
            _buildRateIndicator(
              'Read Rate',
              stats.readRate,
              Colors.purple,
            ),
            
            if (stats.notificationsByPriority.isNotEmpty) ...[
              SizedBox(height: 16.h),
              
              Text(
                'By Priority',
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              SizedBox(height: 8.h),
              
              ...stats.notificationsByPriority.entries.map((entry) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: Row(
                    children: [
                      Container(
                        width: 12.w,
                        height: 12.h,
                        decoration: BoxDecoration(
                          color: _getPriorityColor(entry.key),
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        entry.key.displayName,
                        style: GoogleFonts.outfit(fontSize: 12.sp),
                      ),
                      const Spacer(),
                      Text(
                        entry.value.toString(),
                        style: GoogleFonts.outfit(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 20.w,
            color: color,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.outfit(
              fontSize: 10.sp,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRateIndicator(String label, double rate, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              '${(rate * 100).toStringAsFixed(1)}%',
              style: GoogleFonts.outfit(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        LinearProgressIndicator(
          value: rate,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Color _getPriorityColor(dynamic priority) {
    // This would map priority enum to colors
    switch (priority.toString().toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
