import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class LocationConsentCard extends StatelessWidget {
  final bool hasConsent;
  final ValueChanged<bool> onConsentChanged;

  const LocationConsentCard({
    super.key,
    required this.hasConsent,
    required this.onConsentChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: hasConsent ? 2 : 4,
      color: hasConsent ? null : Colors.amber[50],
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasConsent ? Icons.location_on : Icons.location_off,
                  size: 24.w,
                  color: hasConsent 
                      ? Colors.green 
                      : Colors.amber[700],
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Location Tracking',
                    style: GoogleFonts.outfit(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: hasConsent ? null : Colors.amber[800],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: hasConsent 
                        ? Colors.green.withOpacity(0.1)
                        : Colors.amber.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    hasConsent ? 'Enabled' : 'Required',
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: hasConsent ? Colors.green[700] : Colors.amber[800],
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            Text(
              hasConsent
                  ? 'Location tracking is enabled. You\'ll receive notifications about incidents near your location.'
                  : 'Location tracking is required to receive incident notifications. Your location data is encrypted and used only for safety notifications.',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: hasConsent ? Colors.grey[600] : Colors.amber[800],
                height: 1.4,
              ),
            ),
            
            if (!hasConsent) ...[
              SizedBox(height: 16.h),
              
              // Privacy features
              _buildPrivacyFeature(
                icon: Icons.security,
                title: 'Encrypted Storage',
                description: 'Your location data is encrypted and secure',
              ),
              
              SizedBox(height: 8.h),
              
              _buildPrivacyFeature(
                icon: Icons.visibility_off,
                title: 'Privacy First',
                description: 'Location used only for safety notifications',
              ),
              
              SizedBox(height: 8.h),
              
              _buildPrivacyFeature(
                icon: Icons.delete_forever,
                title: 'Data Control',
                description: 'You can revoke consent and delete data anytime',
              ),
            ],
            
            SizedBox(height: 16.h),
            
            // Action button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  if (hasConsent) {
                    _showRevokeConsentDialog(context);
                  } else {
                    _showGrantConsentDialog(context);
                  }
                },
                icon: Icon(
                  hasConsent ? Icons.location_off : Icons.location_on,
                  size: 20.w,
                ),
                label: Text(
                  hasConsent ? 'Revoke Consent' : 'Grant Location Access',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: hasConsent 
                      ? Colors.red[400]
                      : Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyFeature({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: Colors.green[600],
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
              Text(
                description,
                style: GoogleFonts.outfit(
                  fontSize: 11.sp,
                  color: Colors.green[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showGrantConsentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Location Access',
          style: GoogleFonts.outfit(fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'To receive incident notifications, we need access to your location. This allows us to:',
              style: GoogleFonts.outfit(fontSize: 14.sp),
            ),
            SizedBox(height: 12.h),
            _buildConsentPoint('Alert you about nearby incidents'),
            _buildConsentPoint('Calculate accurate distances'),
            _buildConsentPoint('Provide location-based safety features'),
            SizedBox(height: 12.h),
            Text(
              'Your privacy is protected:',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            _buildConsentPoint('Data is encrypted and secure'),
            _buildConsentPoint('Used only for safety notifications'),
            _buildConsentPoint('You can revoke consent anytime'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConsentChanged(true);
            },
            child: Text('Grant Access'),
          ),
        ],
      ),
    );
  }

  void _showRevokeConsentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Revoke Location Access',
          style: GoogleFonts.outfit(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Are you sure you want to revoke location access? You will no longer receive incident notifications and your location data will be deleted.',
          style: GoogleFonts.outfit(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConsentChanged(false);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[400],
              foregroundColor: Colors.white,
            ),
            child: Text('Revoke Access'),
          ),
        ],
      ),
    );
  }

  Widget _buildConsentPoint(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16.w,
            color: Colors.green,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.outfit(fontSize: 13.sp),
            ),
          ),
        ],
      ),
    );
  }
}
