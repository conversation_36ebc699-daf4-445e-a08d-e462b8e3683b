import 'dart:io';
import 'package:equatable/equatable.dart';

enum ImageType {
  idFront,
  idBack,
  profilePhoto,
}

enum ImageQualityIssue {
  blur,
  glare,
  tooSmall,
  tooLarge,
  poorLighting,
  notCentered,
  expired,
}

class CapturedImage extends Equatable {
  final File imageFile;
  final ImageType type;
  final List<ImageQualityIssue> qualityIssues;
  final bool isValid;
  final DateTime? expiryDate;
  final String? extractedText;
  
  const CapturedImage({
    required this.imageFile,
    required this.type,
    this.qualityIssues = const [],
    this.isValid = false,
    this.expiryDate,
    this.extractedText,
  });

  CapturedImage copyWith({
    File? imageFile,
    ImageType? type,
    List<ImageQualityIssue>? qualityIssues,
    bool? isValid,
    DateTime? expiryDate,
    String? extractedText,
  }) {
    return CapturedImage(
      imageFile: imageFile ?? this.imageFile,
      type: type ?? this.type,
      qualityIssues: qualityIssues ?? this.qualityIssues,
      isValid: isValid ?? this.isValid,
      expiryDate: expiryDate ?? this.expiryDate,
      extractedText: extractedText ?? this.extractedText,
    );
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return expiryDate!.isBefore(DateTime.now());
  }

  @override
  List<Object?> get props => [imageFile.path, type, qualityIssues, isValid, expiryDate, extractedText];
} 