import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/user_report_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/user_report_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/user_report_state.dart';

class ReportUserScreen extends StatefulWidget {
  final String reportedUserId;
  final String reportedUserName;

  const ReportUserScreen({
    super.key,
    required this.reportedUserId,
    required this.reportedUserName,
  });

  @override
  State<ReportUserScreen> createState() => _ReportUserScreenState();
}

class _ReportUserScreenState extends State<ReportUserScreen> {
  final _formKey = GlobalKey<FormState>();
  final _additionalInfoController = TextEditingController();
  UserReportReason? _selectedReason;

  @override
  void dispose() {
    _additionalInfoController.dispose();
    super.dispose();
  }

  void _submitReport() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<UserReportBloc>().add(
        SubmitUserReportEvent(
          reportedUserId: widget.reportedUserId,
          reason: _selectedReason!,
          additionalInfo: _additionalInfoController.text.trim().isEmpty 
              ? null 
              : _additionalInfoController.text.trim(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Report User'),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: BlocListener<UserReportBloc, UserReportState>(
        listener: (context, state) {
          if (state is UserReportSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Report submitted successfully'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.of(context).pop();
          } else if (state is UserReportError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: theme.colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning,
                        color: theme.colorScheme.error,
                        size: 24.sp,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Reporting ${widget.reportedUserName}',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.error,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              'Please provide details about the identity issue',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: 24.h),
                
                // Reason selection
                Text(
                  'Reason for Report',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 12.h),
                
                ...UserReportReason.values.map((reason) => 
                  _buildReasonTile(context, reason),
                ),
                
                SizedBox(height: 24.h),
                
                // Additional information
                Text(
                  'Additional Information (Optional)',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 12.h),
                
                TextFormField(
                  controller: _additionalInfoController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: 'Provide any additional details that might help with the review...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                ),
                
                SizedBox(height: 32.h),
                
                // Submit button
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<UserReportBloc, UserReportState>(
                    builder: (context, state) {
                      final isLoading = state is UserReportLoading;
                      
                      return ElevatedButton(
                        onPressed: isLoading || _selectedReason == null ? null : _submitReport,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.error,
                          foregroundColor: theme.colorScheme.onError,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        child: isLoading
                            ? SizedBox(
                                height: 20.h,
                                width: 20.w,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    theme.colorScheme.onError,
                                  ),
                                ),
                              )
                            : Text(
                                'Submit Report',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReasonTile(BuildContext context, UserReportReason reason) {
    final theme = Theme.of(context);
    final isSelected = _selectedReason == reason;
    
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: InkWell(
        onTap: () => setState(() => _selectedReason = reason),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isSelected 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected
                    ? Icons.radio_button_checked
                    : Icons.radio_button_unchecked,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getReasonTitle(reason),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected 
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      _getReasonDescription(reason),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getReasonTitle(UserReportReason reason) {
    switch (reason) {
      case UserReportReason.suspiciousIdentity:
        return 'Suspicious Identity';
      case UserReportReason.fakeProfile:
        return 'Fake Profile';
      case UserReportReason.documentForgery:
        return 'Document Forgery';
      case UserReportReason.impersonation:
        return 'Impersonation';
      case UserReportReason.multipleAccounts:
        return 'Multiple Accounts';
      case UserReportReason.other:
        return 'Other';
    }
  }

  String _getReasonDescription(UserReportReason reason) {
    switch (reason) {
      case UserReportReason.suspiciousIdentity:
        return 'The user\'s identity seems suspicious or inconsistent';
      case UserReportReason.fakeProfile:
        return 'This appears to be a fake or fabricated profile';
      case UserReportReason.documentForgery:
        return 'The identity documents appear to be forged or altered';
      case UserReportReason.impersonation:
        return 'This user is impersonating someone else';
      case UserReportReason.multipleAccounts:
        return 'This user appears to have multiple accounts';
      case UserReportReason.other:
        return 'Other identity-related concerns';
    }
  }
}
