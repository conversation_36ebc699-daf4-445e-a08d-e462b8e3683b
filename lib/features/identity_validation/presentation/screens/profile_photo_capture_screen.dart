import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/widgets/camera_capture_widget.dart';

class ProfilePhotoCaptureScreen extends StatelessWidget {
  final CapturedImage? profileImage;
  final bool isLoading;

  const ProfilePhotoCaptureScreen({
    Key? key,
    this.profileImage,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Expanded(
            child: _buildContent(context),
          ),
          Si<PERSON><PERSON><PERSON>(height: 16.h),
          _buildBottomButtons(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return _buildCameraCapture(context);
  }

  Widget _buildCameraCapture(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Take a selfie for identity verification',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Position your face clearly in the frame and ensure good lighting',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 16.h),
        Expanded(
          child: Center(
            child: AspectRatio(
              aspectRatio: 3 / 5, // Increased height for better selfie capture
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Camera widget
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CameraCaptureWidget(
                      imageType: ImageType.profilePhoto,
                      instructionsText: 'Take a selfie for identity verification',
                      onImageCaptured: (File imageFile) {
                        context.read<IdValidationBloc>().add(ProfileImageCaptured(imageFile));
                      },
                      forceFrontCamera: true
                    ),
                  ),

                  // Selfie guide overlay
                  IgnorePointer(
                    child: Center(
                      child: Container(
                        width: 200.w,
                        height: 200.w,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 2.w),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(height: 16.h),
        Center(
          child: Text(
            'Align your face within the circle',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    final bloc = context.read<IdValidationBloc>();

    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: () => bloc.add(GoToPreviousStep()),
            style: TextButton.styleFrom(
              minimumSize: Size(0, 50.h),
            ),
            child: Text(context.l10n.back),
          ),
        ),
      ],
    );
  }
} 