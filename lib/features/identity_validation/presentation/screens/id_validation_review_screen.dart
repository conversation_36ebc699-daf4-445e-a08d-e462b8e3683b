import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_state.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';

class IdValidationReviewScreen extends StatelessWidget {
  final CapturedImage idFrontImage;
  final CapturedImage idBackImage;
  final CapturedImage profileImage;

  const IdValidationReviewScreen({
    Key? key,
    required this.idFrontImage,
    required this.idBackImage,
    required this.profileImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              
              Text(
                context.l10n.reviewInstructions,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),

        // Document review list
        Expanded(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8.h),
                _buildReviewItem(
                  context,
                  title: context.l10n.idFrontSide,
                  image: idFrontImage,
                  onEdit: () => _onEditImage(context, ImageType.idFront),
                ),
                SizedBox(height: 20.h),
                _buildReviewItem(
                  context,
                  title: context.l10n.idBackSide,
                  image: idBackImage,
                  onEdit: () => _onEditImage(context, ImageType.idBack),
                ),
                SizedBox(height: 20.h),
                _buildReviewItem(
                  context,
                  title: context.l10n.profilePhoto,
                  image: profileImage,
                  onEdit: () => _onEditImage(context, ImageType.profilePhoto),
                  isProfile: true,
                ),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),

        _buildBottomButtons(context),
      ],
    );
  }

  Widget _buildReviewItem(
    BuildContext context, {
    required String title,
    required CapturedImage image,
    required VoidCallback onEdit,
    bool isProfile = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: 4.h),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.01),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
       
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title, status and edit button
          Padding(
            padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 12.h),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(0.r),
                  ),
                  child: Icon(
                    isProfile ? Icons.person_outline : Icons.credit_card_outlined,
                    size: 20.sp,
                    color: theme.primaryColor,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : Colors.black87,
                          letterSpacing: -0.2,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Row(
                        children: [
                          Container(
                            width: 6.w,
                            height: 6.w,
                            decoration: BoxDecoration(
                              color: image.isValid ? Colors.green : Colors.orange,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 6.w),
                          Text(
                            image.isValid ? 'Verified' : 'Needs Review',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: image.isValid
                                  ? Colors.green
                                  : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                _buildEditButton(context, onEdit),
              ],
            ),
          ),

          // Image preview with modern design
          Container(
            height: 140.h,
            margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[50],
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: isDark
                    ? Colors.grey[700]!.withValues(alpha: 0.3)
                    : Colors.grey[200]!.withValues(alpha: 0.8),
                width: 1,
              ),
            ),
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Image with rounded corners
                ClipRRect(
                  borderRadius: BorderRadius.circular(7.r),
                  child: isProfile
                    ? _buildProfilePreview(image)
                    : Image.file(
                        image.imageFile,
                        fit: BoxFit.cover,
                      ),
                ),

                // Subtle overlay for better contrast
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(7.r),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.1),
                      ],
                    ),
                  ),
                ),

                // Quality indicator - top right
                Positioned(
                  top: 12.h,
                  right: 12.w,
                  child: _buildQualityIndicator(context, image),
                ),

                // Expiry date indicator - top left
                if (!isProfile)
                  Positioned(
                    top: 12.h,
                    left: 12.w,
                    child: _buildExpiryDateIndicator(context, image),
                  ),

                // Document type indicator - bottom left
                Positioned(
                  bottom: 12.h,
                  left: 12.w,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      isProfile ? 'Profile Photo' : 'ID Document',
                      style: TextStyle(
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildEditButton(BuildContext context, VoidCallback onEdit) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onEdit,
        borderRadius: BorderRadius.circular(0.r),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: isDark
                ? theme.primaryColor.withValues(alpha: 0.15)
                : theme.primaryColor.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(0.r),
            border: Border.all(
              color: theme.primaryColor.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.edit_outlined,
                size: 16.sp,
                color: theme.primaryColor,
              ),
              SizedBox(width: 6.w),
              Text(
                context.l10n.edit,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                  color: theme.primaryColor,
                  letterSpacing: -0.1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  
  Widget _buildProfilePreview(CapturedImage image) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[700]!,
            Colors.grey[900]!,
          ],
        ),
      ),
      child: Center(
        child: Container(
          width: 140.w,
          height: 160.w,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.all(Radius.elliptical(140.w, 160.w)),
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
            image: DecorationImage(
              fit: BoxFit.cover,
              image: FileImage(image.imageFile),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildQualityIndicator(BuildContext context, CapturedImage image) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: image.isValid
            ? Colors.green.withValues(alpha: 0.9)
            : Colors.orange.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            image.isValid ? Icons.check_circle : Icons.warning_amber_rounded,
            color: Colors.white,
            size: 14.sp,
          ),
          SizedBox(width: 4.w),
          Text(
            image.isValid ? 'Verified' : 'Review',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildExpiryDateIndicator(BuildContext context, CapturedImage image) {
    if (image.expiryDate == null) return const SizedBox.shrink();
    
    final isExpired = image.isExpired;
    final formattedDate = DateFormat('MM/dd/yyyy').format(image.expiryDate!);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isExpired
            ? Colors.red.withOpacity(0.9)
            : Colors.blue.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isExpired ? Icons.warning_amber_rounded : Icons.event_available,
            color: Colors.white,
            size: 14.sp,
          ),
          SizedBox(width: 4.w),
          Text(
            isExpired ? 'Expired $formattedDate' : 'Expires $formattedDate',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return BlocBuilder<IdValidationBloc, IdValidationState>(
      builder: (context, state) {
        final bloc = context.read<IdValidationBloc>();

        return Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.white,       
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
              child: Row(
                children: [
                  // Back button
                  Expanded(
                    flex: 2,
                    child: OutlinedButton(
                      onPressed: () => bloc.add(GoToPreviousStep()),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: isDark ? Colors.grey[300] : Colors.grey[700],
                        backgroundColor: Colors.transparent,
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        side: BorderSide(
                          color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                          width: .7,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        elevation: 0,
                      ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: 18.sp,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          context.l10n.back,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                  SizedBox(width: 16.w),
                  // Submit button
                  Expanded(
                    flex: 4,
                    child: ElevatedButton(
                      onPressed: state.canSubmit ? () => bloc.add(SubmitIdValidation()) : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: state.canSubmit
                            ? theme.primaryColor
                            : (isDark ? Colors.grey[700] : Colors.grey[300]),
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: isDark ? Colors.grey[700] : Colors.grey[300],
                        disabledForegroundColor: isDark ? Colors.grey[500] : Colors.grey[500],
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        elevation: state.canSubmit ? 3 : 0,
                        shadowColor: theme.primaryColor.withValues(alpha: 0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (state.isSubmitting) ...[
                          SizedBox(
                            width: 16.sp,
                            height: 16.sp,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8.w),
                        ] else ...[
                          Icon(
                            Icons.verified_rounded,
                            size: 20.sp,
                          ),
                          SizedBox(width: 8.w),
                        ],
                        Flexible(
                          child: Text(
                            state.isSubmitting
                                ? (state.uploadProgress > 0
                                    ? 'Enviando ${(state.uploadProgress * 100).toInt()}%'
                                    : 'Enviando...')
                                : context.l10n.submit,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.2,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _onEditImage(BuildContext context, ImageType type) {
    IdValidationStep step;
    
    switch (type) {
      case ImageType.idFront:
        step = IdValidationStep.idFront;
        break;
      case ImageType.idBack:
        step = IdValidationStep.idBack;
        break;
      case ImageType.profilePhoto:
        step = IdValidationStep.profilePhoto;
        break;
    }
    
    context.read<IdValidationBloc>().add(GoToSpecificStep(step));
  }

} 