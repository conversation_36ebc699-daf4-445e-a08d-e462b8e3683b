import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_state.dart';

import 'package:respublicaseguridad/features/identity_validation/presentation/screens/id_front_capture_screen.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/screens/id_back_capture_screen.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/screens/profile_photo_capture_screen.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/screens/id_validation_review_screen.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';

class IdValidationScreen extends StatelessWidget {
  const IdValidationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print('🆔 IdValidationScreen: Screen is being built/loaded');
    return BlocProvider(
      create: (_) {
        print('🆔 IdValidationScreen: Creating IdValidationBloc');
        return GetIt.instance<IdValidationBloc>();
      },
      child: const _IdValidationScreenContent(),
    );
  }
}

class _IdValidationScreenContent extends StatelessWidget {
  const _IdValidationScreenContent();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Handle system back button
          if (context.canPop()) {
            context.pop();
          } else {
            context.go('/home');
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
        title: Text(context.l10n.identityVerification),
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            // Use GoRouter's pop if possible, otherwise go back to home
            if (context.canPop()) {
              context.pop();
            } else {
              context.go('/home');
            }
          },
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: BlocConsumer<IdValidationBloc, IdValidationState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }

          // Handle existing validation status
          if (state.hasExistingValidation) {
            print('🆔 IdValidationScreen: Showing existing validation dialog');
            print('🆔 IdValidationScreen: Error message: ${state.errorMessage}');
            IosDialog.showAlertDialog(
              context: context,
              title: 'Verificación de Identidad',
              message: state.errorMessage ?? 'Ya tienes una verificación en proceso.',
              confirmText: 'Entendido',
              onConfirm: () {
                print('🆔 IdValidationScreen: User confirmed existing validation dialog');
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/home');
                }
              },
            );
          }

          if (state.isValidationComplete) {
            // Show iOS success dialog
            IosDialog.showAlertDialog(
              context: context,
              title: '¡Verificación Exitosa!',
              message: 'Tu identidad ha sido verificada correctamente. Los documentos han sido enviados para revisión.',
              confirmText: 'Continuar',
              onConfirm: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/home');
                }
              },
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              _buildProgressBar(context, state),
              Expanded(
                child: _buildCurrentStepContent(context, state),
              ),
            ],
          );
        },
      ),
    ));
  }

  Widget _buildProgressBar(BuildContext context, IdValidationState state) {
    final currentStepIndex = state.currentStep.index;
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getStepTitle(context, state.currentStep),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${currentStepIndex + 1}/4',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          LinearProgressIndicator(
            value: (currentStepIndex + 1) / 4,
            backgroundColor: Colors.grey[300],
            borderRadius: BorderRadius.circular(8.r),
            minHeight: 8.h,
          ),
        ],
      ),
    );
  }

  String _getStepTitle(BuildContext context, IdValidationStep step) {
    switch (step) {
      case IdValidationStep.idFront:
        return context.l10n.idFrontSide;
      case IdValidationStep.idBack:
        return context.l10n.idBackSide;
      case IdValidationStep.profilePhoto:
        return context.l10n.profilePhoto;
      case IdValidationStep.review:
        return context.l10n.review;
    }
  }

  Widget _buildCurrentStepContent(BuildContext context, IdValidationState state) {
    switch (state.currentStep) {
      case IdValidationStep.idFront:
        return IdFrontCaptureScreen(
          idFrontImage: state.idFrontImage,
          isLoading: state.isLoading,
        );
      case IdValidationStep.idBack:
        return IdBackCaptureScreen(
          idBackImage: state.idBackImage,
          isLoading: state.isLoading,
        );
      case IdValidationStep.profilePhoto:
        return ProfilePhotoCaptureScreen(
          profileImage: state.profileImage,
          isLoading: state.isLoading,
        );
      case IdValidationStep.review:
        return IdValidationReviewScreen(
          idFrontImage: state.idFrontImage!,
          idBackImage: state.idBackImage!,
          profileImage: state.profileImage!,
        );
    }
  }


} 