import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/widgets/camera_capture_widget.dart';

class IdBackCaptureScreen extends StatelessWidget {
  final CapturedImage? idBackImage;
  final bool isLoading;

  const IdBackCaptureScreen({
    Key? key,
    this.idBackImage,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Expanded(
            child: _buildContent(context),
          ),
          SizedBox(height: 16.h),
          _buildBottomButtons(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return _buildCameraCapture(context);
  }

  Widget _buildCameraCapture(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.idBackInstructions,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 16.h),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: CameraCaptureWidget(
              imageType: ImageType.idBack,
              instructionsText: context.l10n.idBackInstructions,
              onImageCaptured: (File imageFile) {
                context.read<IdValidationBloc>().add(IdBackImageCaptured(imageFile));
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    final bloc = context.read<IdValidationBloc>();

    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: () => bloc.add(GoToPreviousStep()),
            style: TextButton.styleFrom(
              minimumSize: Size(0, 50.h),
            ),
            child: Text(context.l10n.back),
          ),
        ),
      ],
    );
  }
} 