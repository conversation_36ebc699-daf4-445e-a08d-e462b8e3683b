import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:intl/intl.dart';

/// Service for performing OCR on ID documents and extracting relevant information
class OcrService {
  final TextRecognizer _textRecognizer;
  
  OcrService() : _textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
  
  void dispose() {
    _textRecognizer.close();
  }
  
  /// Process an ID card image and extract text
  Future<String> extractText(File imageFile) async {
    try {
      final inputImage = InputImage.fromFilePath(imageFile.path);
      final recognizedText = await _textRecognizer.processImage(inputImage);
      return recognizedText.text;
    } catch (e) {
      debugPrint('Error extracting text from image: $e');
      return '';
    }
  }
  
  /// Try to extract the expiry date from the text
  DateTime? extractExpiryDate(String text) {
    try {
      // Standardize text for pattern matching by converting to lowercase
      // and normalizing whitespace
      final standardizedText = text.toLowerCase().replaceAll(RegExp(r'\s+'), ' ');
      
      // Common date formats in IDs across different languages
      final dateFormats = [
        'dd/MM/yyyy',
        'MM/dd/yyyy',
        'yyyy/MM/dd',
        'dd-MM-yyyy',
        'MM-dd-yyyy',
        'yyyy-MM-dd',
        'dd.MM.yyyy',
        'MM.dd.yyyy',
        'yyyy.MM.dd',
        // Two-digit years
        'dd/MM/yy',
        'MM/dd/yy',
        'yy/MM/dd',
        'dd-MM-yy',
        'MM-dd-yy',
        'yy-MM-dd',
        'dd.MM.yy',
        'MM.dd.yy',
        'yy.MM.dd',
      ];
      
      // Multi-language expiry keywords
      final expiryKeywords = [
        // English
        'exp', 'expiry', 'expiration', 'expires', 'valid until', 'valid thru',
        // Spanish
        'fecha de vencimiento', 'vence', 'caduca', 'válido hasta', 
        // French
        'expire', 'date d\'expiration', 'valable jusqu\'au',
        // German
        'gültig bis', 'ablaufdatum', 
        // Portuguese
        'válido até', 'data de validade',
        // Italian
        'scadenza', 'valido fino a',
        // Generic
        'end', 'expire', 'expira', 'end date', 'valid to', 'until'
      ];
      
      // Check for expiry patterns with keywords from various languages
      for (final keyword in expiryKeywords) {
        final pattern = RegExp('$keyword[\\s.:]*([0-9]{1,2}[/.\\-][0-9]{1,2}[/.\\-][0-9]{2,4})', caseSensitive: false);
        final match = pattern.firstMatch(standardizedText);
        
        if (match != null && match.groupCount >= 1) {
          final dateStr = match.group(1);
          if (dateStr != null) {
            for (final format in dateFormats) {
              try {
                final date = DateFormat(format).parse(dateStr);
                // Validate the date is reasonable (not too far in the past/future)
                if (isReasonableDate(date)) {
                  return date;
                }
              } catch (e) {
                // Try next format
              }
            }
          }
        }
      }
      
      // If no match found with patterns, try to find dates directly
      final dateRegexes = [
        RegExp(r'\b(\d{1,2}[/.\\-]\d{1,2}[/.\\-]\d{2,4})\b'),  // DD/MM/YYYY, MM/DD/YYYY
        RegExp(r'\b(\d{2,4}[/.\\-]\d{1,2}[/.\\-]\d{1,2})\b'),  // YYYY/MM/DD
      ];
      
      List<DateTime> foundDates = [];
      
      for (final regex in dateRegexes) {
        final matches = regex.allMatches(standardizedText);
        
        for (var match in matches) {
          final dateStr = match.group(1);
          if (dateStr != null) {
            for (final format in dateFormats) {
              try {
                final date = DateFormat(format).parse(dateStr);
                // Only consider future or relatively recent dates (within 10 years)
                if (isReasonableDate(date)) {
                  foundDates.add(date);
                }
              } catch (e) {
                // Try next format
              }
            }
          }
        }
      }
      
      // Sort dates and take the most future one (likely the expiry date)
      if (foundDates.isNotEmpty) {
        foundDates.sort();
        // Find the latest date that's not more than 20 years in the future
        final now = DateTime.now();
        final maxFutureDate = now.add(const Duration(days: 365 * 20));
        
        for (var i = foundDates.length - 1; i >= 0; i--) {
          if (foundDates[i].isBefore(maxFutureDate)) {
            return foundDates[i];
          }
        }
        
        return foundDates.last;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error extracting expiry date: $e');
      return null;
    }
  }
  
  /// Check if an ID is expired based on extracted date
  bool isExpired(DateTime? expiryDate) {
    if (expiryDate == null) return false;
    return expiryDate.isBefore(DateTime.now());
  }
  
  /// Determine if a date is reasonable (not too far in past/future)
  bool isReasonableDate(DateTime date) {
    final now = DateTime.now();
    final tenYearsAgo = now.subtract(const Duration(days: 365 * 10));
    final twentyYearsAhead = now.add(const Duration(days: 365 * 20));
    
    // Date should be between 10 years ago and 20 years in the future
    return date.isAfter(tenYearsAgo) && date.isBefore(twentyYearsAhead);
  }
} 