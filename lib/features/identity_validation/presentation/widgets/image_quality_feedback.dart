import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';

class ImageQualityFeedback extends StatelessWidget {
  final CapturedImage image;
  final VoidCallback? onRetake;

  const ImageQualityFeedback({
    Key? key,
    required this.image,
    this.onRetake,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (image.isValid) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSuccessFeedback(context),
          if (image.type != ImageType.profilePhoto && image.expiryDate != null)
            _buildExpiryDateInfo(context),
        ],
      );
    }
    
    if (image.qualityIssues.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return _buildIssuesFeedback(context);
  }
  
  Widget _buildSuccessFeedback(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 14.h),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline_rounded,
            color: Colors.green[700],
            size: 22.sp,
          ),
          SizedBox(width: 14.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Image Quality Verified',
                  style: TextStyle(
                    color: Colors.grey[800],
                    fontWeight: FontWeight.bold,
                    fontSize: 15.sp,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  'The image meets all quality requirements',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
          if (onRetake != null)
            _buildRetakeButton(context, Colors.grey[700]!),
        ],
      ),
    );
  }
  
  Widget _buildRetakeButton(BuildContext context, Color color) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('Retake button pressed for ${image.type}');
          onRetake?.call();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.refresh_rounded, size: 16.sp, color: color),
              SizedBox(width: 4.w),
              Text(
                'Retake',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: 14.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildExpiryDateInfo(BuildContext context) {
    final isExpired = image.isExpired;
    final color = isExpired ? Colors.grey[800]! : Colors.grey[700]!;
    final iconColor = isExpired ? Colors.red[700]! : Colors.blue[700]!;
    final formattedDate = image.expiryDate != null
        ? DateFormat('MMM dd, yyyy').format(image.expiryDate!)
        : 'Unknown';
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 14.h),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            isExpired ? Icons.event_busy_rounded : Icons.event_available_rounded,
            color: iconColor,
            size: 22.sp,
          ),
          SizedBox(width: 14.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isExpired ? 'ID Expired' : 'ID Valid',
                  style: TextStyle(
                    color: isExpired ? Colors.red[700] : Colors.grey[800],
                    fontWeight: FontWeight.bold,
                    fontSize: 15.sp,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  isExpired ? 'Expired on $formattedDate' : 'Valid until $formattedDate',
                  style: TextStyle(
                    color: color,
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildIssuesFeedback(BuildContext context) {
    final isExpired = image.qualityIssues.contains(ImageQualityIssue.expired);
    final textColor = isExpired ? Colors.red[700]! : Colors.grey[800]!;
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      padding: EdgeInsets.all(18.r),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isExpired ? Icons.error_outline_rounded : Icons.warning_amber_rounded,
                color: isExpired ? Colors.red[700] : Colors.amber[700],
                size: 22.sp,
              ),
              SizedBox(width: 14.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isExpired ? 'ID Expired' : 'Image Quality Issues',
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 15.sp,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      isExpired
                          ? 'This document has expired and cannot be used'
                          : 'Please resolve the issues below',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
              if (onRetake != null && !isExpired)
                _buildRetakeButton(context, Colors.grey[700]!),
            ],
          ),
          Divider(color: Colors.grey[300], height: 24.h),
          ...image.qualityIssues.map((issue) => _buildIssueItem(issue)),
          if (onRetake != null)
            Padding(
              padding: EdgeInsets.only(top: 20.h),
              child: SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () {
                    debugPrint('Retake button pressed for ${image.type}');
                    onRetake?.call();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt_rounded,
                        size: 18.sp,
                        color: isExpired ? Colors.red[700] : Colors.grey[700],
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Retake Photo',
                        style: TextStyle(
                          color: isExpired ? Colors.red[700] : Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: isExpired ? Colors.red[700] : Colors.grey[700],
                    padding: EdgeInsets.symmetric(vertical: 14.h),
                    backgroundColor: Colors.grey[100],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildIssueItem(ImageQualityIssue issue) {
    IconData icon;
    String message;
    String suggestion;
    Color iconColor;
    
    switch (issue) {
      case ImageQualityIssue.blur:
        icon = Icons.blur_on_rounded;
        message = 'Image is blurry';
        suggestion = 'Hold the camera steady when capturing';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.glare:
        icon = Icons.wb_sunny_rounded;
        message = 'Excessive glare detected';
        suggestion = 'Avoid direct light on the document';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.tooSmall:
        icon = Icons.photo_size_select_small_rounded;
        message = 'Image resolution is too low';
        suggestion = 'Move closer to the document';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.tooLarge:
        icon = Icons.photo_size_select_large_rounded;
        message = 'Image file size is too large';
        suggestion = 'Try recapturing with default settings';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.poorLighting:
        icon = Icons.brightness_low_rounded;
        message = 'Poor lighting conditions';
        suggestion = 'Move to a well-lit area';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.notCentered:
        icon = Icons.center_focus_weak_rounded;
        message = 'Document not centered';
        suggestion = 'Align document within the frame';
        iconColor = Colors.amber[700]!;
        break;
      case ImageQualityIssue.expired:
        icon = Icons.event_busy_rounded;
        message = 'ID document is expired';
        suggestion = 'Provide an updated ID document';
        iconColor = Colors.red[700]!;
        break;
    }
    
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.all(12.r),
      color: Colors.grey[100],
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: iconColor, size: 20.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: TextStyle(
                    color: Colors.grey[800],
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  suggestion,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 