import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';

class DocumentFrameGuide extends StatelessWidget {
  final ImageType imageType;

  const DocumentFrameGuide({
    Key? key,
    required this.imageType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DocumentFramePainter(imageType),
      child: Container(),
    );
  }
}

class _DocumentFramePainter extends CustomPainter {
  final ImageType imageType;

  _DocumentFramePainter(this.imageType);

  @override
  void paint(Canvas canvas, Size size) {
    // Create path for the entire screen
    final Path backgroundPath = Path()..addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    
    // Create path for the cutout (ID card or profile photo)
    final Path cutoutPath = Path();
    
    // White border paint
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;
    
    if (imageType == ImageType.profilePhoto) {
      // Profile photo (oval for selfie)
      final centerX = size.width / 2;
      final centerY = size.height / 2;
      
      // Make the oval wider and taller for selfie
      final ovalWidth = size.width * 0.65; // Wider
      final ovalHeight = size.height * 0.55; // Taller
      
      final rect = Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: ovalWidth,
        height: ovalHeight,
      );
      
      // Add oval to cutout path
      cutoutPath.addOval(rect);
      
      // Draw white border for the oval
      canvas.drawOval(rect, borderPaint);
    } else {
      // ID card (rectangle with rounded corners)
      final cardWidth = size.width * 0.85;
      final cardHeight = cardWidth / 1.6;
      final cardLeft = (size.width - cardWidth) / 2;
      final cardTop = (size.height - cardHeight) / 2;
      
      final rrect = RRect.fromRectAndRadius(
        Rect.fromLTWH(cardLeft, cardTop, cardWidth, cardHeight),
        const Radius.circular(8),
      );
      
      // Add rounded rectangle to cutout path
      cutoutPath.addRRect(rrect);
      
      // Draw white border for the rounded rectangle
      canvas.drawRRect(rrect, borderPaint);
      
      // Draw guide lines if needed
      if (imageType == ImageType.idFront) {
        final photoLineX = cardLeft + cardWidth * 0.35;
        canvas.drawLine(
          Offset(photoLineX, cardTop),
          Offset(photoLineX, cardTop + cardHeight),
          Paint()
            ..color = Colors.white.withOpacity(0.5)
            ..strokeWidth = 1,
        );
      }
    }
    
    // Create a path representing the area outside the cutout
    // by subtracting the cutout path from the background path
    final Path backgroundMinusCutout = Path.combine(
      PathOperation.difference,
      backgroundPath,
      cutoutPath,
    );
    
    // Fill the area outside the cutout with semi-transparent black
    canvas.drawPath(
      backgroundMinusCutout,
      Paint()
        ..color = Colors.black.withOpacity(0.5)
        ..style = PaintingStyle.fill,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
} 