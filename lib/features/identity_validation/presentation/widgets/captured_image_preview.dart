import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/widgets/image_quality_feedback.dart';

class CapturedImagePreview extends StatelessWidget {
  final CapturedImage image;
  final VoidCallback onRetake;

  const CapturedImagePreview({
    Key? key,
    required this.image,
    required this.onRetake,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildImagePreview(context),
        SizedBox(height: 16.h),
        ImageQualityFeedback(
          image: image,
          onRetake: onRetake,
        ),
      ],
    );
  }

  Widget _buildImagePreview(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: 240.h,
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.grey[100],
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Image container with proper clipping
          ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: image.type == ImageType.profilePhoto
                ? _buildProfilePhotoPreview()
                : _buildIdCardPreview(),
          ),
          
          // Gradient overlay at bottom for better text visibility
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 80.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.5),
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
              ),
            ),
          ),
          
          // Quality indicator badge
          Positioned(
            top: 16.h,
            right: 16.w,
            child: _buildQualityBadge(context),
          ),
          
          // Image type label
          Positioned(
            bottom: 16.h,
            left: 16.w,
            child: _buildImageTypeLabel(context),
          ),
          
          // Retake button overlay
          Positioned(
            bottom: 16.h,
            right: 16.w,
            child: _buildOverlayRetakeButton(context),
          ),
        ],
      ),
    );
  }
  
  Widget _buildOverlayRetakeButton(BuildContext context) {
    return GestureDetector(
      onTap: onRetake,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.refresh,
              size: 16.sp,
              color: Colors.white,
            ),
            SizedBox(width: 6.w),
            Text(
              'Retake',
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildQualityBadge(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: image.isValid 
            ? Colors.green.withOpacity(0.9) 
            : Colors.orange.withOpacity(0.9),
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            image.isValid ? Icons.check_circle : Icons.warning_amber_rounded,
            color: Colors.white,
            size: 16.sp,
          ),
          SizedBox(width: 6.w),
          Text(
            image.isValid ? 'Verified' : 'Review',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildImageTypeLabel(BuildContext context) {
    String label;
    
    switch (image.type) {
      case ImageType.idFront:
        label = 'ID Front';
        break;
      case ImageType.idBack:
        label = 'ID Back';
        break;
      case ImageType.profilePhoto:
        label = 'Profile Photo';
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  Widget _buildIdCardPreview() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
      ),
      child: Image.file(
        image.imageFile,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildProfilePhotoPreview() {
    // For profile photo, use a container with a gradient background
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[800]!,
            Colors.grey[900]!,
          ],
        ),
      ),
      child: Center(
        child: Container(
          width: 180.w,
          height: 220.w, // Taller to make it oval
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.all(Radius.elliptical(180.w, 220.w)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 15,
                spreadRadius: 0,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(color: Colors.white, width: 4),
            image: DecorationImage(
              fit: BoxFit.cover,
              image: FileImage(image.imageFile),
            ),
          ),
        ),
      ),
    );
  }
} 