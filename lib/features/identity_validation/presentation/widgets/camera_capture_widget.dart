import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_mlkit_image_labeling/google_mlkit_image_labeling.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/widgets/document_frame_guide.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';

class CameraCaptureWidget extends StatefulWidget {
  final ImageType imageType;
  final String instructionsText;
  final Function(File) onImageCaptured;
  final bool forceFrontCamera;

  const CameraCaptureWidget({
    Key? key,
    required this.imageType,
    required this.instructionsText,
    required this.onImageCaptured,
    this.forceFrontCamera = false,
  }) : super(key: key);

  @override
  State<CameraCaptureWidget> createState() => _CameraCaptureWidgetState();
}

class _CameraCaptureWidgetState extends State<CameraCaptureWidget> {
  CameraController? _cameraController;
  Future<void>? _cameraInitFuture;
  bool _isCameraReady = false;
  bool _isTakingPicture = false;
  late final ImageLabeler _imageLabeler;
  late final TextRecognizer _textRecognizer;

  @override
  void initState() {
    super.initState();
    _initializeLabeler();
    _initializeCamera();
  }

  Future<void> _initializeLabeler() async {
    final options = ImageLabelerOptions(confidenceThreshold: 0.5);
    _imageLabeler = ImageLabeler(options: options);
    _textRecognizer = TextRecognizer();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _imageLabeler.close();
    _textRecognizer.close();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        debugPrint('No cameras available');
        return;
      }

      // Select camera based on forceFrontCamera parameter
      CameraDescription selectedCamera;

      if (widget.forceFrontCamera) {
        // Try to find front camera
        final frontCameras = cameras.where((camera) =>
          camera.lensDirection == CameraLensDirection.front).toList();

        if (frontCameras.isNotEmpty) {
          selectedCamera = frontCameras.first;
          debugPrint('Using front camera for selfie');
        } else {
          selectedCamera = cameras.first;
          debugPrint('No front camera found, using default camera');
        }
      } else {
        // Default to first camera (usually back camera)
        selectedCamera = cameras.first;
      }

      _cameraController = CameraController(
        selectedCamera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      _cameraInitFuture = _cameraController!.initialize();
      await _cameraInitFuture;

      if (mounted) {
        setState(() {
          _isCameraReady = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing camera: $e');
    }
  }

  Future<void> _takePicture() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (_isTakingPicture) {
      return;
    }

    setState(() {
      _isTakingPicture = true;
    });

    try {
      final XFile picture = await _cameraController!.takePicture();
      final File imageFile = File(picture.path);
      
      // Process image with ML Kit
      bool isValidDocument = await _validateImageWithMlKit(imageFile);

      // Both ID front and back use less strict validation - only ML Kit validation
      // No text validation required for either side
      bool shouldAccept = false;
      if (widget.imageType == ImageType.idBack || widget.imageType == ImageType.idFront) {
        shouldAccept = isValidDocument; // Less strict - only ML Kit validation for both sides
      } else if (widget.imageType == ImageType.profilePhoto) {
        shouldAccept = isValidDocument; // Profile photos don't need text
      }

      if (shouldAccept) {
        // Only pass the image to the bloc if it's valid
        widget.onImageCaptured(imageFile);
      } else {
        // Show error dialog
        if (mounted) {
          _showInvalidDocumentDialog(noText: false);
        }
      }
    } catch (e) {
      debugPrint('Error taking picture: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isTakingPicture = false;
        });
      }
    }
  }



  Future<bool> _validateImageWithMlKit(File imageFile) async {
    final inputImage = InputImage.fromFilePath(imageFile.path);
    final List<ImageLabel> labels = await _imageLabeler.processImage(inputImage);
    
    // Look for labels that suggest this is an ID card - expanded to be more inclusive
    final relevantLabels = [
      'card', 'id', 'document', 'license', 'passport', 'paper', 
      'text', 'certificate', 'identity', 'official', 'credential',
      'identification'
    ];
    
    // For ID documents, check for relevant labels with same confidence thresholds
    if (widget.imageType == ImageType.idFront || widget.imageType == ImageType.idBack) {
      // Both ID front and back use less strict validation with lower confidence thresholds
      final confidenceThreshold = 0.3; // Less strict for both sides
      final lowConfidenceThreshold = 0.2; // Less strict for both sides

      // Try first with appropriate confidence threshold
      for (ImageLabel label in labels) {
        if (relevantLabels.contains(label.label.toLowerCase()) && label.confidence > confidenceThreshold) {
          debugPrint('Found ID document: ${label.label} with confidence ${label.confidence} (${widget.imageType})');
          return true;
        }
      }

      // If no match, try with lower confidence for certain key labels
      final highPriorityLabels = ['passport', 'card', 'id', 'document', 'license'];
      for (ImageLabel label in labels) {
        if (highPriorityLabels.contains(label.label.toLowerCase()) && label.confidence > lowConfidenceThreshold) {
          debugPrint('Found potential ID document: ${label.label} with lower confidence ${label.confidence} (${widget.imageType})');
          return true;
        }
      }

      // For ID back, be even more lenient - accept if we find any document-related label
      if (widget.imageType == ImageType.idBack) {
        for (ImageLabel label in labels) {
          if (relevantLabels.contains(label.label.toLowerCase()) && label.confidence > 0.1) {
            debugPrint('Accepting ID back with very low confidence: ${label.label} with confidence ${label.confidence}');
            return true;
          }
        }
      }
    }
    
    // For profile photos, allow images with person label
    if (widget.imageType == ImageType.profilePhoto) {
      // Expanded list of face/person related labels for selfies
      final personLabels = [
        'person', 'selfie', 'face', 'beard', 'hair', 'head', 
        'human', 'portrait', 'smile', 'facial', 'cool', 'dude',
        'eyelash', 'flesh', 'moustache', 'fun'
      ];
      
      // Look for high confidence matches
      for (ImageLabel label in labels) {
        if (personLabels.contains(label.label.toLowerCase()) && label.confidence > 0.6) { // Lowered from 0.7
          debugPrint('Found person-related label: ${label.label} with confidence ${label.confidence}');
          return true;
        }
      }
      
      // Check if there are any person-related labels at all
      bool hasPersonRelatedLabels = false;
      for (ImageLabel label in labels) {
        if (personLabels.contains(label.label.toLowerCase())) {
          hasPersonRelatedLabels = true;
          debugPrint('Found person-related label below threshold: ${label.label} with confidence ${label.confidence}');
        }
      }
      
      // If we have any selfie-like labels but none met the confidence threshold,
      // still accept the image for a better user experience
      if (hasPersonRelatedLabels) {
        debugPrint('Accepting selfie despite lower confidence threshold - has some person-related labels');
        return true;
      }
    }
    
    // Check if the image is just black/dark (camera covered) and reject it
    for (ImageLabel label in labels) {
      if (['dark', 'black', 'night', 'darkness', 'monochrome'].contains(label.label.toLowerCase())
          && label.confidence > 0.7) {
        debugPrint('Detected dark/covered camera: ${label.label} with confidence ${label.confidence}');
        return false; // Reject dark/covered images
      }
    }
    
    // Debug: print all labels
    for (ImageLabel label in labels) {
      debugPrint('Label: ${label.label}, confidence: ${label.confidence}');
    }
    
    // Return false if no valid document/person detected or if camera appears covered
    return false;
  }

  void _showInvalidDocumentDialog({bool noText = false}) {
    String message = '';

    if (widget.imageType == ImageType.profilePhoto) {
      message = 'Unable to detect a clear face photo. Please ensure your face is visible and well-lit.';
    } else if (widget.imageType == ImageType.idFront) {
      if (noText) {
        message = 'No readable text was found on this document. Please ensure your ID front is clearly visible and readable.';
      } else {
        message = 'This doesn\'t appear to be a valid ID card front. Please ensure your ID is clearly visible and try again.';
      }
    } else if (widget.imageType == ImageType.idBack) {
      message = 'This doesn\'t appear to be a valid ID card back. Please ensure your ID is clearly visible and try again.';
    }
    
    // Add tips for better capture
    message += '\n\nTips:\n'
             '• Make sure there is good lighting\n'
             '• Hold the device steady\n'
             '• Don\'t cover the camera\n';
             
    if (widget.imageType == ImageType.profilePhoto) {
      message += '• Position your face in the center of the frame\n'
                 '• Make sure your face is clearly visible';
    } else {
      message += '• Make sure the document is fully visible';
    }
    
    IosDialog.showAlertDialog(
      context: context,
      title: 'Invalid Image',
      message: message,
      cancelText: 'OK',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Stack(
            fit: StackFit.expand,
            children: [
              _buildCameraPreview(),
              if (widget.imageType != ImageType.profilePhoto)
                IgnorePointer(
                  child: DocumentFrameGuide(
                    imageType: widget.imageType,
                  ),
                ),
              Positioned(
                bottom: 20.h,
                left: 0,
                right: 0,
                child: _buildCaptureButton(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCameraPreview() {
    if (!_isCameraReady) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: AspectRatio(
        aspectRatio: _cameraController!.value.aspectRatio,
        child: CameraPreview(_cameraController!),
      ),
    );
  }

  Widget _buildCaptureButton() {
    return Center(
      child: GestureDetector(
        onTap: _isTakingPicture ? null : _takePicture,
        child: Container(
          width: 80.w,
          height: 80.w,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.8),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 3.w,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _isTakingPicture
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Center(
                  child: Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
        ),
      ),
    );
  }
} 