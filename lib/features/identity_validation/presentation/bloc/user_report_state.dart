import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';

abstract class UserReportState extends Equatable {
  const UserReportState();

  @override
  List<Object?> get props => [];
}

class UserReportInitial extends UserReportState {}

class UserReportLoading extends UserReportState {}

class UserReportSuccess extends UserReportState {
  final UserReportEntity report;

  const UserReportSuccess({
    required this.report,
  });

  @override
  List<Object?> get props => [report];
}

class UserReportError extends UserReportState {
  final String message;

  const UserReportError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class UserReportsLoaded extends UserReportState {
  final List<UserReportEntity> reports;

  const UserReportsLoaded({
    required this.reports,
  });

  @override
  List<Object?> get props => [reports];
}

class ReportStatusUpdated extends UserReportState {
  final UserReportEntity updatedReport;

  const ReportStatusUpdated({
    required this.updatedReport,
  });

  @override
  List<Object?> get props => [updatedReport];
}
