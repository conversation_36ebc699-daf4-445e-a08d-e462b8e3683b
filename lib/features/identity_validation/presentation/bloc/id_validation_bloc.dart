
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/upload_id_document_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/get_identity_verification_status_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/process_dual_verification_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_state.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/services/ocr_service.dart';
import 'package:intl/intl.dart';

class IdValidationBloc extends Bloc<IdValidationEvent, IdValidationState> {
  final OcrService _ocrService = OcrService();
  final UploadIdDocumentUseCase _uploadIdDocumentUseCase;
  final GetIdentityVerificationStatusUseCase _getIdentityVerificationStatusUseCase;
  final ProcessDualVerificationUseCase _processDualVerificationUseCase;
  final FirebaseAuth _firebaseAuth;

  IdValidationBloc({
    required UploadIdDocumentUseCase uploadIdDocumentUseCase,
    required GetIdentityVerificationStatusUseCase getIdentityVerificationStatusUseCase,
    required ProcessDualVerificationUseCase processDualVerificationUseCase,
    required FirebaseAuth firebaseAuth,
  }) : _uploadIdDocumentUseCase = uploadIdDocumentUseCase,
       _getIdentityVerificationStatusUseCase = getIdentityVerificationStatusUseCase,
       _processDualVerificationUseCase = processDualVerificationUseCase,
       _firebaseAuth = firebaseAuth,
       super(const IdValidationState()) {
    on<IdFrontImageCaptured>(_onIdFrontImageCaptured);
    on<IdBackImageCaptured>(_onIdBackImageCaptured);
    on<ProfileImageCaptured>(_onProfileImageCaptured);
    on<ValidateImage>(_onValidateImage);
    on<RetakeImage>(_onRetakeImage);
    on<GoToNextStep>(_onGoToNextStep);
    on<GoToPreviousStep>(_onGoToPreviousStep);
    on<GoToSpecificStep>(_onGoToSpecificStep);
    on<SubmitIdValidation>(_onSubmitIdValidation);
    on<UploadProgressUpdated>(_onUploadProgressUpdated);
    on<CheckExistingValidationStatus>(_onCheckExistingValidationStatus);
  }

  @override
  Future<void> close() {
    _ocrService.dispose();
    return super.close();
  }

  void _onIdFrontImageCaptured(
    IdFrontImageCaptured event,
    Emitter<IdValidationState> emit,
  ) {
    final capturedImage = CapturedImage(
      imageFile: event.imageFile,
      type: ImageType.idFront,
      isValid: false,
    );
    emit(state.copyWith(
      idFrontImage: capturedImage,
      clearError: true,
    ));
    add(ValidateImage(capturedImage));
  }

  void _onIdBackImageCaptured(
    IdBackImageCaptured event,
    Emitter<IdValidationState> emit,
  ) {
    final capturedImage = CapturedImage(
      imageFile: event.imageFile,
      type: ImageType.idBack,
      isValid: false,
    );
    emit(state.copyWith(
      idBackImage: capturedImage,
      clearError: true,
    ));
    add(ValidateImage(capturedImage));
  }

  void _onProfileImageCaptured(
    ProfileImageCaptured event,
    Emitter<IdValidationState> emit,
  ) {
    final capturedImage = CapturedImage(
      imageFile: event.imageFile,
      type: ImageType.profilePhoto,
      isValid: false,
    );
    emit(state.copyWith(
      profileImage: capturedImage,
      clearError: true,
    ));
    add(ValidateImage(capturedImage));
  }

  void _onValidateImage(
    ValidateImage event,
    Emitter<IdValidationState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    // For ID images, perform OCR and extract text
    String? extractedText;
    DateTime? expiryDate;

    if (event.image.type == ImageType.idFront || event.image.type == ImageType.idBack) {
      // Perform OCR
      extractedText = await _ocrService.extractText(event.image.imageFile);

      // Try to extract expiry date
      if (extractedText.isNotEmpty) {
        expiryDate = _ocrService.extractExpiryDate(extractedText);
        debugPrint('Extracted expiry date: ${expiryDate?.toString() ?? 'None'}');
      }
    }

    // Analyze image quality
    final List<ImageQualityIssue> qualityIssues = await _analyzeImageQuality(
      event.image,
      expiryDate: expiryDate,
    );

    // An image is considered valid if it has no quality issues
    final isValid = qualityIssues.isEmpty;

    final validatedImage = event.image.copyWith(
      qualityIssues: qualityIssues,
      isValid: isValid,
      extractedText: extractedText,
      expiryDate: expiryDate,
    );

    String? errorMessage;
    if (!isValid) {
      if (validatedImage.qualityIssues.contains(ImageQualityIssue.expired)) {
        final formattedExpiryDate = expiryDate != null
            ? DateFormat('MM/dd/yyyy').format(expiryDate)
            : 'unknown date';
        errorMessage = 'ID is expired (${formattedExpiryDate}). Please provide a valid ID.';
      } else {
        errorMessage = '${_getImageTypeName(event.image.type)} has quality issues. Please retake.';
      }
    }

    switch (event.image.type) {
      case ImageType.idFront:
        emit(state.copyWith(
          idFrontImage: validatedImage,
          isLoading: false,
          errorMessage: errorMessage,
        ));
        break;
      case ImageType.idBack:
        emit(state.copyWith(
          idBackImage: validatedImage,
          isLoading: false,
          errorMessage: errorMessage,
        ));
        break;
      case ImageType.profilePhoto:
        emit(state.copyWith(
          profileImage: validatedImage,
          isLoading: false,
          errorMessage: errorMessage,
        ));
        break;
    }

    // Auto-navigate to next step if image is valid
    if (isValid) {
      add(GoToNextStep());
    }
  }

  String _getImageTypeName(ImageType type) {
    switch (type) {
      case ImageType.idFront: return 'ID front image';
      case ImageType.idBack: return 'ID back image';
      case ImageType.profilePhoto: return 'Profile photo';
    }
  }

  Future<List<ImageQualityIssue>> _analyzeImageQuality(
    CapturedImage image, {
    DateTime? expiryDate,
  }) async {
    List<ImageQualityIssue> issues = [];
    
    try {
      final fileSize = await image.imageFile.length();
      
      // Check file size
      if (fileSize < 50 * 1024) { // Less than 50 KB
        issues.add(ImageQualityIssue.tooSmall);
      }
      
      if (fileSize > 10 * 1024 * 1024) { // More than 10 MB
        issues.add(ImageQualityIssue.tooLarge);
      }
      
      // Detect dark images
      if (fileSize < 100 * 1024) { // Covered cameras often produce very small files
        issues.add(ImageQualityIssue.poorLighting);
      }
      
      // Check if ID is expired (only for ID images)
      if ((image.type == ImageType.idFront || image.type == ImageType.idBack) &&
          expiryDate != null && 
          _ocrService.isExpired(expiryDate)) {
        issues.add(ImageQualityIssue.expired);
        debugPrint('ID is expired. Expiry date: $expiryDate');
      }
      
      debugPrint('Image validated with ${issues.length} issues found');
    } catch (e) {
      debugPrint('Error analyzing image quality: $e');
      issues.add(ImageQualityIssue.poorLighting); // Default error
    }
    
    return issues;
  }

  void _onRetakeImage(
    RetakeImage event,
    Emitter<IdValidationState> emit,
  ) {
    switch (event.imageType) {
      case ImageType.idFront:
        emit(state.copyWith(
          idFrontImage: null,
          clearError: true,
        ));
        break;
      case ImageType.idBack:
        emit(state.copyWith(
          idBackImage: null,
          clearError: true,
        ));
        break;
      case ImageType.profilePhoto:
        emit(state.copyWith(
          profileImage: null,
          clearError: true,
        ));
        break;
    }
  }

  void _onGoToNextStep(
    GoToNextStep event,
    Emitter<IdValidationState> emit,
  ) {
    IdValidationStep nextStep;
    
    switch (state.currentStep) {
      case IdValidationStep.idFront:
        nextStep = IdValidationStep.idBack;
        break;
      case IdValidationStep.idBack:
        nextStep = IdValidationStep.profilePhoto;
        break;
      case IdValidationStep.profilePhoto:
        nextStep = IdValidationStep.review;
        break;
      case IdValidationStep.review:
        // Already at the last step
        return;
    }
    
    emit(state.copyWith(
      currentStep: nextStep,
      clearError: true,
    ));
  }

  void _onGoToPreviousStep(
    GoToPreviousStep event,
    Emitter<IdValidationState> emit,
  ) {
    IdValidationStep previousStep;
    
    switch (state.currentStep) {
      case IdValidationStep.idFront:
        // Already at the first step
        return;
      case IdValidationStep.idBack:
        previousStep = IdValidationStep.idFront;
        break;
      case IdValidationStep.profilePhoto:
        previousStep = IdValidationStep.idBack;
        break;
      case IdValidationStep.review:
        previousStep = IdValidationStep.profilePhoto;
        break;
    }
    
    emit(state.copyWith(
      currentStep: previousStep,
      clearError: true,
    ));
  }

  void _onGoToSpecificStep(
    GoToSpecificStep event,
    Emitter<IdValidationState> emit,
  ) {
    emit(state.copyWith(
      currentStep: event.step,
      clearError: true,
    ));
  }

  void _onSubmitIdValidation(
    SubmitIdValidation event,
    Emitter<IdValidationState> emit,
  ) async {
    // Ensure all required images are valid
    if (state.idFrontImage?.isValid != true ||
        state.idBackImage?.isValid != true ||
        state.profileImage?.isValid != true) {
      emit(state.copyWith(
        errorMessage: 'All images must be valid before submitting',
      ));
      return;
    }

    // Get current user
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      emit(state.copyWith(
        errorMessage: 'User not authenticated. Please sign in again.',
      ));
      return;
    }

    emit(state.copyWith(
      isSubmitting: true,
      uploadProgress: 0.0,
      clearError: true,
    ));

    try {
      // Create upload parameters with progress callback
      final params = UploadIdDocumentParams(
        userId: currentUser.uid,
        frontImage: state.idFrontImage!.imageFile,
        backImage: state.idBackImage!.imageFile,
        profilePhoto: state.profileImage!.imageFile,
        documentType: DocumentType.governmentId, // Default to government ID
        onProgress: (progress) {
          add(UploadProgressUpdated(progress));
        },
      );

      // Upload documents using the use case
      final result = await _uploadIdDocumentUseCase(params);

      result.fold(
        (failure) {
          emit(state.copyWith(
            isSubmitting: false,
            errorMessage: failure.message,
          ));
        },
        (identityDocument) async {
          emit(state.copyWith(
            isSubmitting: false,
            isValidationComplete: true,
          ));

          // Trigger dual verification workflow after successful upload
          print('🔄 IdValidationBloc: Starting dual verification workflow');
          final dualVerificationParams = ProcessDualVerificationParams(
            userId: currentUser.uid,
          );

          final dualVerificationResult = await _processDualVerificationUseCase(dualVerificationParams);

          dualVerificationResult.fold(
            (failure) {
              // Log the error and show to user since this affects their validation status
              print('❌ IdValidationBloc: Dual verification workflow failed: ${failure.message}');
              emit(state.copyWith(
                errorMessage: 'Document uploaded but verification failed: ${failure.message}',
              ));
            },
            (updatedUser) {
              // Verification workflow completed successfully
              print('✅ IdValidationBloc: Dual verification completed for user: ${updatedUser.uid}, status: ${updatedUser.validationStatus}');
            },
          );
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isSubmitting: false,
        errorMessage: 'Failed to submit validation. Please try again.',
      ));
    }
  }

  void _onUploadProgressUpdated(
    UploadProgressUpdated event,
    Emitter<IdValidationState> emit,
  ) {
    emit(state.copyWith(uploadProgress: event.progress));
  }

  void _onCheckExistingValidationStatus(
    CheckExistingValidationStatus event,
    Emitter<IdValidationState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, clearError: true));

    try {
      final result = await _getIdentityVerificationStatusUseCase(event.userId);

      result.fold(
        (failure) {
          // If we can't get status, proceed with normal flow
          emit(state.copyWith(
            isLoading: false,
            hasExistingValidation: false,
          ));
        },
        (userEntity) {
          // Check if user already has validation in progress or completed
          final hasExisting = userEntity.validationStatus != ValidationStatus.unverified;

          if (hasExisting) {
            // User already has validation, show appropriate message
            emit(state.copyWith(
              isLoading: false,
              hasExistingValidation: true,
              errorMessage: _getStatusMessage(userEntity.validationStatus),
            ));
          } else {
            // User is unverified, proceed with normal flow
            emit(state.copyWith(
              isLoading: false,
              hasExistingValidation: false,
            ));
          }
        },
      );
    } catch (e) {
      // If error occurs, proceed with normal flow
      emit(state.copyWith(
        isLoading: false,
        hasExistingValidation: false,
        errorMessage: 'Unable to check validation status. Proceeding with verification.',
      ));
    }
  }

  String _getStatusMessage(ValidationStatus status) {
    switch (status) {
      case ValidationStatus.pendingId:
        return 'Your identity verification is pending. Documents are being processed.';
      case ValidationStatus.pendingAutomaticVerification:
        return 'Your documents are being automatically verified. This usually takes a few minutes.';
      case ValidationStatus.pendingReview:
        return 'Your documents are under manual review. This may be due to reports or verification issues.';
      case ValidationStatus.validated:
        return 'Your identity has already been verified successfully.';
      case ValidationStatus.rejected:
        return 'Your previous verification was rejected. You can submit new documents.';
      case ValidationStatus.suspended:
        return 'Your account is suspended due to identity verification issues. Please contact support.';
      case ValidationStatus.unverified:
        return 'Please complete identity verification.';
    }
  }
}