import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';

enum IdValidationStep {
  idFront,
  idBack,
  profilePhoto,
  review,
}

class IdValidationState extends Equatable {
  final IdValidationStep currentStep;
  final CapturedImage? idFrontImage;
  final CapturedImage? idBackImage;
  final CapturedImage? profileImage;
  final bool isLoading;
  final String? errorMessage;
  final bool isSubmitting;
  final double uploadProgress;
  final bool isValidationComplete;
  final bool hasExistingValidation;

  const IdValidationState({
    this.currentStep = IdValidationStep.idFront,
    this.idFrontImage,
    this.idBackImage,
    this.profileImage,
    this.isLoading = false,
    this.errorMessage,
    this.isSubmitting = false,
    this.uploadProgress = 0.0,
    this.isValidationComplete = false,
    this.hasExistingValidation = false,
  });

  IdValidationState copyWith({
    IdValidationStep? currentStep,
    CapturedImage? idFrontImage,
    CapturedImage? idBackImage,
    CapturedImage? profileImage,
    bool? isLoading,
    String? errorMessage,
    bool? clearError,
    bool? isSubmitting,
    double? uploadProgress,
    bool? isValidationComplete,
    bool? hasExistingValidation,
  }) {
    return IdValidationState(
      currentStep: currentStep ?? this.currentStep,
      idFrontImage: idFrontImage ?? this.idFrontImage,
      idBackImage: idBackImage ?? this.idBackImage,
      profileImage: profileImage ?? this.profileImage,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError == true ? null : (errorMessage ?? this.errorMessage),
      isSubmitting: isSubmitting ?? this.isSubmitting,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      isValidationComplete: isValidationComplete ?? this.isValidationComplete,
      hasExistingValidation: hasExistingValidation ?? this.hasExistingValidation,
    );
  }

  bool get isCurrentStepComplete {
    switch (currentStep) {
      case IdValidationStep.idFront:
        return idFrontImage != null && idFrontImage!.isValid;
      case IdValidationStep.idBack:
        return idBackImage != null && idBackImage!.isValid;
      case IdValidationStep.profilePhoto:
        return profileImage != null && profileImage!.isValid;
      case IdValidationStep.review:
        return idFrontImage != null && 
               idBackImage != null && 
               profileImage != null &&
               idFrontImage!.isValid &&
               idBackImage!.isValid &&
               profileImage!.isValid;
    }
  }

  bool get canProceedToNextStep {
    return isCurrentStepComplete && !isLoading && !isSubmitting;
  }

  bool get canSubmit {
    return currentStep == IdValidationStep.review && 
           idFrontImage != null && 
           idBackImage != null && 
           profileImage != null &&
           idFrontImage!.isValid &&
           idBackImage!.isValid &&
           profileImage!.isValid &&
           !isLoading &&
           !isSubmitting;
  }

  @override
  List<Object?> get props => [
    currentStep,
    idFrontImage,
    idBackImage,
    profileImage,
    isLoading,
    errorMessage,
    isSubmitting,
    uploadProgress,
    isValidationComplete,
    hasExistingValidation,
  ];
} 