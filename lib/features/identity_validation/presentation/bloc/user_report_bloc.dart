import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/report_user_use_case.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/user_report_event.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/user_report_state.dart';

class UserReportBloc extends Bloc<UserReportEvent, UserReportState> {
  final ReportUserUseCase _reportUserUseCase;
  final FirebaseAuth _firebaseAuth;

  UserReportBloc({
    required ReportUserUseCase reportUserUseCase,
    required FirebaseAuth firebaseAuth,
  })  : _reportUserUseCase = reportUserUseCase,
        _firebaseAuth = firebaseAuth,
        super(UserReportInitial()) {
    on<SubmitUserReportEvent>(_onSubmitUserReport);
    on<LoadUserReportsEvent>(_onLoadUserReports);
    on<UpdateReportStatusEvent>(_onUpdateReportStatus);
  }

  Future<void> _onSubmitUserReport(
    SubmitUserReportEvent event,
    Emitter<UserReportState> emit,
  ) async {
    emit(UserReportLoading());

    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      emit(const UserReportError(message: 'User not authenticated'));
      return;
    }

    final params = ReportUserParams(
      reportedUserId: event.reportedUserId,
      reporterUserId: currentUser.uid,
      reason: event.reason,
      additionalInfo: event.additionalInfo,
    );

    final result = await _reportUserUseCase(params);

    result.fold(
      (failure) => emit(UserReportError(message: failure.message)),
      (report) => emit(UserReportSuccess(report: report)),
    );
  }

  Future<void> _onLoadUserReports(
    LoadUserReportsEvent event,
    Emitter<UserReportState> emit,
  ) async {
    emit(UserReportLoading());
    
    // TODO: Implement loading user reports
    // This would require a new use case to get reports for a user
    emit(const UserReportsLoaded(reports: []));
  }

  Future<void> _onUpdateReportStatus(
    UpdateReportStatusEvent event,
    Emitter<UserReportState> emit,
  ) async {
    emit(UserReportLoading());
    
    // TODO: Implement updating report status
    // This would require a new use case for admin actions
    emit(const UserReportError(message: 'Not implemented yet'));
  }
}
