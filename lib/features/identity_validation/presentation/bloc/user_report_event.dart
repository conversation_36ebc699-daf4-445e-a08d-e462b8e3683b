import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';

abstract class UserReportEvent extends Equatable {
  const UserReportEvent();

  @override
  List<Object?> get props => [];
}

class SubmitUserReportEvent extends UserReportEvent {
  final String reportedUserId;
  final UserReportReason reason;
  final String? additionalInfo;

  const SubmitUserReportEvent({
    required this.reportedUserId,
    required this.reason,
    this.additionalInfo,
  });

  @override
  List<Object?> get props => [reportedUserId, reason, additionalInfo];
}

class LoadUserReportsEvent extends UserReportEvent {
  final String userId;

  const LoadUserReportsEvent({
    required this.userId,
  });

  @override
  List<Object?> get props => [userId];
}

class UpdateReportStatusEvent extends UserReportEvent {
  final String reportId;
  final UserReportStatus status;
  final String? adminNotes;

  const UpdateReportStatusEvent({
    required this.reportId,
    required this.status,
    this.adminNotes,
  });

  @override
  List<Object?> get props => [reportId, status, adminNotes];
}
