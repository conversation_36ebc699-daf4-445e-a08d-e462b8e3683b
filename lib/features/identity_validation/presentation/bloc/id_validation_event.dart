import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/bloc/id_validation_state.dart';

abstract class IdValidationEvent extends Equatable {
  const IdValidationEvent();

  @override
  List<Object?> get props => [];
}

class IdFrontImageCaptured extends IdValidationEvent {
  final File imageFile;

  const IdFrontImageCaptured(this.imageFile);

  @override
  List<Object> get props => [imageFile];
}

class IdBackImageCaptured extends IdValidationEvent {
  final File imageFile;

  const IdBackImageCaptured(this.imageFile);

  @override
  List<Object> get props => [imageFile];
}

class ProfileImageCaptured extends IdValidationEvent {
  final File imageFile;

  const ProfileImageCaptured(this.imageFile);

  @override
  List<Object> get props => [imageFile];
}

class ValidateImage extends IdValidationEvent {
  final CapturedImage image;

  const ValidateImage(this.image);

  @override
  List<Object> get props => [image];
}

class RetakeImage extends IdValidationEvent {
  final ImageType imageType;

  const RetakeImage(this.imageType);

  @override
  List<Object> get props => [imageType];
}

class GoToNextStep extends IdValidationEvent {}

class GoToPreviousStep extends IdValidationEvent {}

class GoToSpecificStep extends IdValidationEvent {
  final IdValidationStep step;

  const GoToSpecificStep(this.step);

  @override
  List<Object> get props => [step];
}

class SubmitIdValidation extends IdValidationEvent {}

class UploadProgressUpdated extends IdValidationEvent {
  final double progress;

  const UploadProgressUpdated(this.progress);

  @override
  List<Object> get props => [progress];
}

class CheckExistingValidationStatus extends IdValidationEvent {
  final String userId;

  const CheckExistingValidationStatus(this.userId);

  @override
  List<Object> get props => [userId];
}