import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

abstract class IdentityVerificationRepository {
  /// Uploads identity document images and creates a new identity document record
  Future<Either<Failure, IdentityDocumentEntity>> uploadIdDocument({
    required String userId,
    required File frontImage,
    required File backImage,
    required File profilePhoto,
    required DocumentType documentType,
    Function(double)? onProgress,
  });

  /// Retrieves the current identity verification status for a user
  Future<Either<Failure, UserEntity>> getIdentityVerificationStatus(String userId);

  /// Retrieves identity document details by document ID
  Future<Either<Failure, IdentityDocumentEntity>> getIdentityDocument(String documentId);

  /// Updates the user's validation status
  Future<Either<Failure, UserEntity>> updateUserValidationStatus({
    required String userId,
    required ValidationStatus status,
    String? identityDocumentId,
  });

  /// Stream of user validation status changes
  Stream<Either<Failure, UserEntity>> watchUserValidationStatus(String userId);

  /// Stream of identity document status changes
  Stream<Either<Failure, IdentityDocumentEntity>> watchIdentityDocumentStatus(String documentId);
}
