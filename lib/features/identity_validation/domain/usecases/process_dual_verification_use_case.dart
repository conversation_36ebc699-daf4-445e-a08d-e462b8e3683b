import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/dual_verification_workflow_service.dart';

class ProcessDualVerificationParams {
  final String userId;

  const ProcessDualVerificationParams({
    required this.userId,
  });
}

class ProcessDualVerificationUseCase implements UseCase<UserEntity, ProcessDualVerificationParams> {
  final DualVerificationWorkflowService _workflowService;

  ProcessDualVerificationUseCase(this._workflowService);

  @override
  Future<Either<Failure, UserEntity>> call(ProcessDualVerificationParams params) async {
    return await _workflowService.processUserVerification(params.userId);
  }
}
