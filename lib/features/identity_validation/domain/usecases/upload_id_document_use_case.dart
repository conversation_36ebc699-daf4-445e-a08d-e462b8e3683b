import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

class UploadIdDocumentParams extends Equatable {
  final String userId;
  final File frontImage;
  final File backImage;
  final File profilePhoto;
  final DocumentType documentType;
  final Function(double)? onProgress;

  const UploadIdDocumentParams({
    required this.userId,
    required this.frontImage,
    required this.backImage,
    required this.profilePhoto,
    this.documentType = DocumentType.governmentId,
    this.onProgress,
  });

  @override
  List<Object?> get props => [
        userId,
        frontImage,
        backImage,
        profilePhoto,
        documentType,
        onProgress,
      ];
}

class UploadIdDocumentUseCase {
  final IdentityVerificationRepository _repository;

  const UploadIdDocumentUseCase(this._repository);

  Future<Either<Failure, IdentityDocumentEntity>> call(
    UploadIdDocumentParams params,
  ) async {
    return await _repository.uploadIdDocument(
      userId: params.userId,
      frontImage: params.frontImage,
      backImage: params.backImage,
      profilePhoto: params.profilePhoto,
      documentType: params.documentType,
      onProgress: params.onProgress,
    );
  }
}
