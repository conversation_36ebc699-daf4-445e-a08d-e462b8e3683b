import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

class WatchUserValidationStatusUseCase {
  final IdentityVerificationRepository _repository;

  const WatchUserValidationStatusUseCase(this._repository);

  Stream<Either<Failure, UserEntity>> call(String userId) {
    return _repository.watchUserValidationStatus(userId);
  }
}
