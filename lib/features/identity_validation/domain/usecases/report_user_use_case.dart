import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/dual_verification_workflow_service.dart';

class ReportUserParams {
  final String reportedUserId;
  final String reporterUserId;
  final UserReportReason reason;
  final String? additionalInfo;

  const ReportUserParams({
    required this.reportedUserId,
    required this.reporterUserId,
    required this.reason,
    this.additionalInfo,
  });
}

class ReportUserUseCase implements UseCase<UserReportEntity, ReportUserParams> {
  final DualVerificationWorkflowService _workflowService;

  ReportUserUseCase(this._workflowService);

  @override
  Future<Either<Failure, UserReportEntity>> call(ReportUserParams params) async {
    return await _workflowService.reportUser(
      reportedUserId: params.reportedUserId,
      reporterUserId: params.reporterUserId,
      reason: params.reason,
      additionalInfo: params.additionalInfo,
    );
  }
}
