import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

class GetIdentityVerificationStatusUseCase {
  final IdentityVerificationRepository _repository;

  const GetIdentityVerificationStatusUseCase(this._repository);

  Future<Either<Failure, UserEntity>> call(String userId) async {
    return await _repository.getIdentityVerificationStatus(userId);
  }
}
