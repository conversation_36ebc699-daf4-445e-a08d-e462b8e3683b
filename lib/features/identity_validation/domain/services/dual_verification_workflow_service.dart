import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/automated_id_verification_service.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

enum VerificationRoute {
  automated,  // For non-reported users
  manual,     // For reported users
}

class VerificationDecision {
  final VerificationRoute route;
  final String reason;
  final List<UserReportEntity> activeReports;
  final Map<String, dynamic>? metadata;

  const VerificationDecision({
    required this.route,
    required this.reason,
    this.activeReports = const [],
    this.metadata,
  });

  bool get isAutomated => route == VerificationRoute.automated;
  bool get isManual => route == VerificationRoute.manual;
}

abstract class UserReportRepository {
  Future<Either<Failure, List<UserReportEntity>>> getActiveReportsForUser(String userId);
  Future<Either<Failure, UserReportEntity>> createUserReport(UserReportEntity report);
  Future<Either<Failure, int>> getReportCountForUser(String userId);
}

abstract class AdminNotificationService {
  Future<void> notifyAdminForManualReview(String userId, List<UserReportEntity> reports);
  Future<void> notifyAdminOfAutomatedFailure(String userId, String reason);
}

/// Simple implementation that just logs (no actual admin notifications needed for user app)
class SimpleAdminNotificationService implements AdminNotificationService {
  @override
  Future<void> notifyAdminForManualReview(String userId, List<UserReportEntity> reports) async {
    print('Manual review needed for user: $userId (${reports.length} reports)');
    // In a real implementation, this could send notifications to a backend admin system
  }

  @override
  Future<void> notifyAdminOfAutomatedFailure(String userId, String reason) async {
    print('Automated verification failed for user: $userId - $reason');
    // In a real implementation, this could send notifications to a backend admin system
  }
}



class DualVerificationWorkflowService {
  final IdentityVerificationRepository _identityRepository;
  final UserReportRepository _reportRepository;
  final AutomatedIdVerificationService _automatedService;
  final AdminNotificationService _adminNotificationService;

  const DualVerificationWorkflowService({
    required IdentityVerificationRepository identityRepository,
    required UserReportRepository reportRepository,
    required AutomatedIdVerificationService automatedService,
    required AdminNotificationService adminNotificationService,
  })  : _identityRepository = identityRepository,
        _reportRepository = reportRepository,
        _automatedService = automatedService,
        _adminNotificationService = adminNotificationService;

  /// Main entry point: Process user verification based on dual approach
  Future<Either<Failure, UserEntity>> processUserVerification(String userId) async {
    try {
      print('🔄 DualVerificationWorkflow: Starting verification for user: $userId');

      // Step 1: Make routing decision
      final decisionResult = await _makeVerificationDecision(userId);
      if (decisionResult.isLeft()) {
        print('❌ DualVerificationWorkflow: Decision failed: ${decisionResult.fold((l) => l.message, (r) => '')}');
        return decisionResult.fold((l) => Left(l), (r) => throw Exception());
      }

      final decision = decisionResult.fold((l) => throw Exception(), (r) => r);
      print('📋 DualVerificationWorkflow: Decision made - Route: ${decision.route}, Reason: ${decision.reason}');

      // Step 2: Route to appropriate verification path
      if (decision.isAutomated) {
        print('🤖 DualVerificationWorkflow: Routing to automated verification');
        return await _processAutomatedVerification(userId);
      } else {
        print('👤 DualVerificationWorkflow: Routing to manual verification');
        return await _processManualVerification(userId, decision.activeReports);
      }
    } catch (e) {
      print('💥 DualVerificationWorkflow: Exception in processUserVerification: ${e.toString()}');
      return Left(ServerFailure(message: 'Failed to process verification: ${e.toString()}'));
    }
  }

  /// Decide whether to use automated or manual verification
  Future<Either<Failure, VerificationDecision>> _makeVerificationDecision(String userId) async {
    try {
      // Get user's current validation status
      final userResult = await _identityRepository.getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return userResult.fold((failure) => Left(failure), (r) => throw Exception());
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      // Check for active reports against this user
      final reportsResult = await _reportRepository.getActiveReportsForUser(userId);
      if (reportsResult.isLeft()) {
        return reportsResult.fold((failure) => Left(failure), (r) => throw Exception());
      }

      final activeReports = reportsResult.fold((l) => throw Exception(), (r) => r);

      // If user is already validated but has been reported, route to manual review
      if (user.validationStatus == ValidationStatus.validated && activeReports.isNotEmpty) {
        return Right(VerificationDecision(
          route: VerificationRoute.manual,
          reason: 'Previously validated user has ${activeReports.length} active report(s) - may lose validation',
          activeReports: activeReports,
        ));
      } else {
        // All other cases go to automated verification (new users, unvalidated users)
        return Right(VerificationDecision(
          route: VerificationRoute.automated,
          reason: activeReports.isEmpty
              ? 'No active reports found'
              : 'User not yet validated - proceeding with automated verification',
        ));
      }
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to make verification decision: ${e.toString()}'));
    }
  }

  /// Process automated verification for non-reported users
  Future<Either<Failure, UserEntity>> _processAutomatedVerification(String userId) async {
    try {
      print('🤖 AutomatedVerification: Starting for user: $userId');

      // Step 1: Update status to pending automatic verification
      print('📝 AutomatedVerification: Updating status to pendingAutomaticVerification');
      final statusUpdateResult = await _identityRepository.updateUserValidationStatus(
        userId: userId,
        status: ValidationStatus.pendingAutomaticVerification,
      );
      if (statusUpdateResult.isLeft()) {
        print('❌ AutomatedVerification: Failed to update status: ${statusUpdateResult.fold((l) => l.message, (r) => '')}');
        return statusUpdateResult;
      }
      print('✅ AutomatedVerification: Status updated successfully');

      // Step 2: Get user's identity document
      print('📄 AutomatedVerification: Fetching user identity document');
      final userResult = await _identityRepository.getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        print('❌ AutomatedVerification: Failed to get user status: ${userResult.fold((l) => l.message, (r) => '')}');
        return userResult;
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);
      print('👤 AutomatedVerification: User found - identityDocumentId: ${user.identityDocumentId}');

      if (user.identityDocumentId == null) {
        print('❌ AutomatedVerification: No identity document ID found for user');
        return Left(ValidationFailure('No identity document found for user'));
      }

      print('📋 AutomatedVerification: Fetching document: ${user.identityDocumentId}');
      final documentResult = await _identityRepository.getIdentityDocument(user.identityDocumentId!);
      if (documentResult.isLeft()) {
        print('❌ AutomatedVerification: Failed to get document: ${documentResult.fold((l) => l.message, (r) => '')}');
        return documentResult.fold((failure) => Left(failure), (r) => throw Exception());
      }

      final document = documentResult.fold((l) => throw Exception(), (r) => r);
      print('📄 AutomatedVerification: Document retrieved - Type: ${document.documentType}');

      // Step 3: Call automated verification service
      print('🔍 AutomatedVerification: Creating verification request');
      final verificationRequest = AutomatedVerificationRequest(
        userId: userId,
        frontImageUrl: document.frontImageUrl,
        backImageUrl: document.backImageUrl,
        selfieUrl: document.profilePhotoUrl,
        documentType: document.documentType,
      );

      print('🚀 AutomatedVerification: Calling automated service');
      final verificationResult = await _automatedService.verifyIdentity(verificationRequest);
      if (verificationResult.isLeft()) {
        print('❌ AutomatedVerification: Service call failed: ${verificationResult.fold((l) => l.message, (r) => '')}');
        // Automated verification failed - notify admin and route to manual
        await _adminNotificationService.notifyAdminOfAutomatedFailure(
          userId,
          'Automated verification service failed',
        );
        return await _processManualVerification(userId, []);
      }

      final result = verificationResult.fold((l) => throw Exception(), (r) => r);
      print('📊 AutomatedVerification: Result received - isVerified: ${result.isVerified}, confidence: ${result.confidence}, isHighConfidence: ${result.isHighConfidence}');

      // Step 4: Process automated verification result
      if (result.isHighConfidence) {
        print('✅ AutomatedVerification: High confidence - auto-approving user');
        // High confidence - auto-approve
        final finalResult = await _identityRepository.updateUserValidationStatus(
          userId: userId,
          status: ValidationStatus.validated,
        );
        print('🎉 AutomatedVerification: User validated successfully');
        return finalResult;
      } else if (result.needsManualReview) {
        print('⚠️ AutomatedVerification: Needs manual review - routing to manual verification');
        // Low confidence or flags - route to manual review
        await _adminNotificationService.notifyAdminOfAutomatedFailure(
          userId,
          'Automated verification requires manual review: ${result.failureReason ?? 'Low confidence'}',
        );
        return await _processManualVerification(userId, []);
      } else {
        print('❌ AutomatedVerification: Verification failed - rejecting user');
        // Failed verification
        return await _identityRepository.updateUserValidationStatus(
          userId: userId,
          status: ValidationStatus.rejected,
        );
      }
    } catch (e) {
      print('💥 AutomatedVerification: Exception occurred: ${e.toString()}');
      return Left(ServerFailure(message: 'Automated verification failed: ${e.toString()}'));
    }
  }

  /// Process manual verification for reported users (who may lose their validation)
  Future<Either<Failure, UserEntity>> _processManualVerification(
    String userId,
    List<UserReportEntity> reports,
  ) async {
    try {
      // Get current user status
      final userResult = await _identityRepository.getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) return userResult;

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      // If user was previously validated, move them to pending review (they may lose validation)
      if (user.validationStatus == ValidationStatus.validated) {
        final result = await _identityRepository.updateUserValidationStatus(
          userId: userId,
          status: ValidationStatus.pendingReview,
        );

        // Notify admin that a validated user needs review due to reports
        await _adminNotificationService.notifyAdminForManualReview(userId, reports);

        return result;
      } else {
        // For non-validated users, just update to pending review
        final result = await _identityRepository.updateUserValidationStatus(
          userId: userId,
          status: ValidationStatus.pendingReview,
        );

        await _adminNotificationService.notifyAdminForManualReview(userId, reports);

        return result;
      }
    } catch (e) {
      return Left(ServerFailure(message: 'Manual verification setup failed: ${e.toString()}'));
    }
  }

  /// Report a user for identity issues (triggers manual verification)
  Future<Either<Failure, UserReportEntity>> reportUser({
    required String reportedUserId,
    required String reporterUserId,
    required UserReportReason reason,
    String? additionalInfo,
  }) async {
    try {
      // Create the report
      final report = UserReportEntity(
        id: '', // Will be generated by repository
        reportedUserId: reportedUserId,
        reporterUserId: reporterUserId,
        reason: reason,
        additionalInfo: additionalInfo,
        reportedAt: DateTime.now(),
      );

      final reportResult = await _reportRepository.createUserReport(report);
      if (reportResult.isLeft()) return reportResult;

      // Handle different user validation states when reported
      final userResult = await _identityRepository.getIdentityVerificationStatus(reportedUserId);
      if (userResult.isRight()) {
        final user = userResult.fold((l) => throw Exception(), (r) => r);

        if (user.validationStatus == ValidationStatus.validated) {
          // Previously validated user is reported - move to manual review (may lose validation)
          await _identityRepository.updateUserValidationStatus(
            userId: reportedUserId,
            status: ValidationStatus.pendingReview,
          );
          await _adminNotificationService.notifyAdminForManualReview(reportedUserId, [report]);
        } else if (user.validationStatus == ValidationStatus.pendingAutomaticVerification) {
          // User in automated verification - move to manual review
          await _identityRepository.updateUserValidationStatus(
            userId: reportedUserId,
            status: ValidationStatus.pendingReview,
          );
          await _adminNotificationService.notifyAdminForManualReview(reportedUserId, [report]);
        }
        // For other statuses (unverified, already pending review, rejected), no immediate action needed
      }

      return reportResult;
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to report user: ${e.toString()}'));
    }
  }
}
