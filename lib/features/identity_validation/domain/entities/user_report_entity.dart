import 'package:equatable/equatable.dart';

enum UserReportReason {
  suspiciousIdentity,
  fakeProfile,
  documentForgery,
  impersonation,
  multipleAccounts,
  other,
}

enum UserReportStatus {
  pending,
  underReview,
  resolved,
  dismissed,
}

enum UserReportPriority {
  low,
  medium,
  high,
  critical,
}

class UserReportEntity extends Equatable {
  final String id;
  final String reportedUserId;
  final String reporterUserId;
  final UserReportReason reason;
  final String? additionalInfo;
  final DateTime reportedAt;
  final UserReportStatus status;
  final UserReportPriority priority;
  final String? adminUserId;
  final DateTime? reviewedAt;
  final String? adminNotes;
  final Map<String, dynamic>? metadata;

  const UserReportEntity({
    required this.id,
    required this.reportedUserId,
    required this.reporterUserId,
    required this.reason,
    this.additionalInfo,
    required this.reportedAt,
    this.status = UserReportStatus.pending,
    this.priority = UserReportPriority.medium,
    this.adminUserId,
    this.reviewedAt,
    this.adminNotes,
    this.metadata,
  });

  UserReportEntity copyWith({
    String? id,
    String? reportedUserId,
    String? reporterUserId,
    UserReportReason? reason,
    String? additionalInfo,
    DateTime? reportedAt,
    UserReportStatus? status,
    UserReportPriority? priority,
    String? adminUserId,
    DateTime? reviewedAt,
    String? adminNotes,
    Map<String, dynamic>? metadata,
  }) {
    return UserReportEntity(
      id: id ?? this.id,
      reportedUserId: reportedUserId ?? this.reportedUserId,
      reporterUserId: reporterUserId ?? this.reporterUserId,
      reason: reason ?? this.reason,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      reportedAt: reportedAt ?? this.reportedAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      adminUserId: adminUserId ?? this.adminUserId,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      adminNotes: adminNotes ?? this.adminNotes,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        reportedUserId,
        reporterUserId,
        reason,
        additionalInfo,
        reportedAt,
        status,
        priority,
        adminUserId,
        reviewedAt,
        adminNotes,
        metadata,
      ];

  /// Check if this report is still active (not resolved or dismissed)
  bool get isActive => status == UserReportStatus.pending || status == UserReportStatus.underReview;

  /// Check if this report requires immediate attention
  bool get isHighPriority => priority == UserReportPriority.high || priority == UserReportPriority.critical;

  /// Get human-readable reason text
  String get reasonText {
    switch (reason) {
      case UserReportReason.suspiciousIdentity:
        return 'Suspicious Identity';
      case UserReportReason.fakeProfile:
        return 'Fake Profile';
      case UserReportReason.documentForgery:
        return 'Document Forgery';
      case UserReportReason.impersonation:
        return 'Impersonation';
      case UserReportReason.multipleAccounts:
        return 'Multiple Accounts';
      case UserReportReason.other:
        return 'Other';
    }
  }

  /// Get human-readable status text
  String get statusText {
    switch (status) {
      case UserReportStatus.pending:
        return 'Pending';
      case UserReportStatus.underReview:
        return 'Under Review';
      case UserReportStatus.resolved:
        return 'Resolved';
      case UserReportStatus.dismissed:
        return 'Dismissed';
    }
  }

  /// Get human-readable priority text
  String get priorityText {
    switch (priority) {
      case UserReportPriority.low:
        return 'Low';
      case UserReportPriority.medium:
        return 'Medium';
      case UserReportPriority.high:
        return 'High';
      case UserReportPriority.critical:
        return 'Critical';
    }
  }
}
