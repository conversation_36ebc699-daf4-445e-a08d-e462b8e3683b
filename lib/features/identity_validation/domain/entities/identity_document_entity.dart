import 'package:equatable/equatable.dart';

enum DocumentStatus {
  pending,
  approved,
  rejected,
}

enum DocumentType {
  governmentId,
  passport,
  drivingLicense,
}

class IdentityDocumentEntity extends Equatable {
  final String id;
  final String userId;
  final DocumentType documentType;
  final String frontImageUrl;
  final String backImageUrl;
  final String profilePhotoUrl;
  final DocumentStatus status;
  final DateTime uploadTimestamp;
  final DateTime? reviewTimestamp;
  final String? rejectionReason;
  final Map<String, dynamic>? metadata;

  const IdentityDocumentEntity({
    required this.id,
    required this.userId,
    required this.documentType,
    required this.frontImageUrl,
    required this.backImageUrl,
    required this.profilePhotoUrl,
    this.status = DocumentStatus.pending,
    required this.uploadTimestamp,
    this.reviewTimestamp,
    this.rejectionReason,
    this.metadata,
  });

  IdentityDocumentEntity copyWith({
    String? id,
    String? userId,
    DocumentType? documentType,
    String? frontImageUrl,
    String? backImageUrl,
    String? profilePhotoUrl,
    DocumentStatus? status,
    DateTime? uploadTimestamp,
    DateTime? reviewTimestamp,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return IdentityDocumentEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentType: documentType ?? this.documentType,
      frontImageUrl: frontImageUrl ?? this.frontImageUrl,
      backImageUrl: backImageUrl ?? this.backImageUrl,
      profilePhotoUrl: profilePhotoUrl ?? this.profilePhotoUrl,
      status: status ?? this.status,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      reviewTimestamp: reviewTimestamp ?? this.reviewTimestamp,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        documentType,
        frontImageUrl,
        backImageUrl,
        profilePhotoUrl,
        status,
        uploadTimestamp,
        reviewTimestamp,
        rejectionReason,
        metadata,
      ];

  bool get isPending => status == DocumentStatus.pending;
  bool get isApproved => status == DocumentStatus.approved;
  bool get isRejected => status == DocumentStatus.rejected;
}
