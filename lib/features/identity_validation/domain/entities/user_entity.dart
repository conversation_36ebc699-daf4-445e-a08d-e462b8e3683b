import 'package:equatable/equatable.dart';

enum ValidationStatus {
  unverified,
  pendingId,
  pendingAutomaticVerification,  // NEW: Automated verification in progress
  pendingReview,                 // Manual review (for reported users)
  validated,
  rejected,
  suspended,                     // NEW: For users with identity issues after reports
}

enum UserStatus {
  unverified,
  pendingId,
  pendingCommunity,
  validated,
  rejected,
}

class UserEntity extends Equatable {
  final String uid;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final ValidationStatus validationStatus;
  final String? identityDocumentId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserEntity({
    required this.uid,
    this.email,
    this.displayName,
    this.photoURL,
    this.validationStatus = ValidationStatus.unverified,
    this.identityDocumentId,
    this.createdAt,
    this.updatedAt,
  });

  UserEntity copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    ValidationStatus? validationStatus,
    String? identityDocumentId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserEntity(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      validationStatus: validationStatus ?? this.validationStatus,
      identityDocumentId: identityDocumentId ?? this.identityDocumentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        uid,
        email,
        displayName,
        photoURL,
        validationStatus,
        identityDocumentId,
        createdAt,
        updatedAt,
      ];

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'validationStatus': validationStatus.name,
      'identityDocumentId': identityDocumentId,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Create from JSON for persistence
  factory UserEntity.fromJson(Map<String, dynamic> json) {
    return UserEntity(
      uid: json['uid'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      validationStatus: ValidationStatus.values.firstWhere(
        (e) => e.name == json['validationStatus'],
        orElse: () => ValidationStatus.unverified,
      ),
      identityDocumentId: json['identityDocumentId'] as String?,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  static const empty = UserEntity(uid: '');

  bool get isEmpty => this == UserEntity.empty;
  bool get isNotEmpty => this != UserEntity.empty;
  bool get isValidated => validationStatus == ValidationStatus.validated;
  bool get isPendingValidation => 
      validationStatus == ValidationStatus.pendingId || 
      validationStatus == ValidationStatus.pendingReview;
}
