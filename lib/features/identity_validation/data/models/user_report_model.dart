import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';

class UserReportModel extends UserReportEntity {
  const UserReportModel({
    required super.id,
    required super.reportedUserId,
    required super.reporterUserId,
    required super.reason,
    super.additionalInfo,
    required super.reportedAt,
    super.status,
    super.priority,
    super.adminUserId,
    super.reviewedAt,
    super.adminNotes,
    super.metadata,
  });

  factory UserReportModel.fromEntity(UserReportEntity entity) {
    return UserReportModel(
      id: entity.id,
      reportedUserId: entity.reportedUserId,
      reporterUserId: entity.reporterUserId,
      reason: entity.reason,
      additionalInfo: entity.additionalInfo,
      reportedAt: entity.reportedAt,
      status: entity.status,
      priority: entity.priority,
      adminUserId: entity.adminUserId,
      reviewedAt: entity.reviewedAt,
      adminNotes: entity.adminNotes,
      metadata: entity.metadata,
    );
  }

  factory UserReportModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return UserReportModel(
      id: doc.id,
      reportedUserId: data['reportedUserId'] as String,
      reporterUserId: data['reporterUserId'] as String,
      reason: _parseUserReportReason(data['reason']),
      additionalInfo: data['additionalInfo'] as String?,
      reportedAt: (data['reportedAt'] as Timestamp).toDate(),
      status: _parseUserReportStatus(data['status']),
      priority: _parseUserReportPriority(data['priority']),
      adminUserId: data['adminUserId'] as String?,
      reviewedAt: (data['reviewedAt'] as Timestamp?)?.toDate(),
      adminNotes: data['adminNotes'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'reportedUserId': reportedUserId,
      'reporterUserId': reporterUserId,
      'reason': reason.name,
      'additionalInfo': additionalInfo,
      'reportedAt': Timestamp.fromDate(reportedAt),
      'status': status.name,
      'priority': priority.name,
      'adminUserId': adminUserId,
      'reviewedAt': reviewedAt != null ? Timestamp.fromDate(reviewedAt!) : null,
      'adminNotes': adminNotes,
      'metadata': metadata,
    };
  }

  static UserReportReason _parseUserReportReason(dynamic reason) {
    switch (reason?.toString()) {
      case 'suspiciousIdentity':
        return UserReportReason.suspiciousIdentity;
      case 'fakeProfile':
        return UserReportReason.fakeProfile;
      case 'documentForgery':
        return UserReportReason.documentForgery;
      case 'impersonation':
        return UserReportReason.impersonation;
      case 'multipleAccounts':
        return UserReportReason.multipleAccounts;
      case 'other':
        return UserReportReason.other;
      default:
        return UserReportReason.other;
    }
  }

  static UserReportStatus _parseUserReportStatus(dynamic status) {
    switch (status?.toString()) {
      case 'pending':
        return UserReportStatus.pending;
      case 'underReview':
        return UserReportStatus.underReview;
      case 'resolved':
        return UserReportStatus.resolved;
      case 'dismissed':
        return UserReportStatus.dismissed;
      default:
        return UserReportStatus.pending;
    }
  }

  static UserReportPriority _parseUserReportPriority(dynamic priority) {
    switch (priority?.toString()) {
      case 'low':
        return UserReportPriority.low;
      case 'medium':
        return UserReportPriority.medium;
      case 'high':
        return UserReportPriority.high;
      case 'critical':
        return UserReportPriority.critical;
      default:
        return UserReportPriority.medium;
    }
  }
}
