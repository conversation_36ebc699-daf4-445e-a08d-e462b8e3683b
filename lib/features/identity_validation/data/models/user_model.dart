import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  const UserModel({
    required super.uid,
    super.email,
    super.displayName,
    super.photoURL,
    super.validationStatus,
    super.identityDocumentId,
    super.createdAt,
    super.updatedAt,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return UserModel(uid: doc.id);
    }

    return UserModel(
      uid: doc.id,
      email: data['email'] as String?,
      displayName: data['displayName'] as String?,
      photoURL: data['photoURL'] as String?,
      validationStatus: _parseValidationStatus(data['validationStatus']),
      identityDocumentId: data['identityDocumentId'] as String?,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      uid: entity.uid,
      email: entity.email,
      displayName: entity.displayName,
      photoURL: entity.photoURL,
      validationStatus: entity.validationStatus,
      identityDocumentId: entity.identityDocumentId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'validationStatus': validationStatus.name,
      'identityDocumentId': identityDocumentId,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : FieldValue.serverTimestamp(),
    };
  }

  static ValidationStatus _parseValidationStatus(dynamic status) {
    if (status == null) return ValidationStatus.unverified;

    switch (status.toString()) {
      case 'unverified':
        return ValidationStatus.unverified;
      case 'pendingId':
        return ValidationStatus.pendingId;
      case 'pendingAutomaticVerification':
        return ValidationStatus.pendingAutomaticVerification;
      case 'pendingReview':
        return ValidationStatus.pendingReview;
      case 'validated':
        return ValidationStatus.validated;
      case 'rejected':
        return ValidationStatus.rejected;
      case 'suspended':
        return ValidationStatus.suspended;
      default:
        return ValidationStatus.unverified;
    }
  }
}
