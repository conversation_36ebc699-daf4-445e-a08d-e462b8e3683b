import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';

class IdentityDocumentModel extends IdentityDocumentEntity {
  const IdentityDocumentModel({
    required super.id,
    required super.userId,
    required super.documentType,
    required super.frontImageUrl,
    required super.backImageUrl,
    required super.profilePhotoUrl,
    super.status,
    required super.uploadTimestamp,
    super.reviewTimestamp,
    super.rejectionReason,
    super.metadata,
  });

  factory IdentityDocumentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return IdentityDocumentModel(
      id: doc.id,
      userId: data['userId'] as String,
      documentType: _parseDocumentType(data['documentType']),
      frontImageUrl: data['frontImageUrl'] as String,
      backImageUrl: data['backImageUrl'] as String,
      profilePhotoUrl: data['profilePhotoUrl'] as String,
      status: _parseDocumentStatus(data['status']),
      uploadTimestamp: (data['uploadTimestamp'] as Timestamp).toDate(),
      reviewTimestamp: (data['reviewTimestamp'] as Timestamp?)?.toDate(),
      rejectionReason: data['rejectionReason'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory IdentityDocumentModel.fromEntity(IdentityDocumentEntity entity) {
    return IdentityDocumentModel(
      id: entity.id,
      userId: entity.userId,
      documentType: entity.documentType,
      frontImageUrl: entity.frontImageUrl,
      backImageUrl: entity.backImageUrl,
      profilePhotoUrl: entity.profilePhotoUrl,
      status: entity.status,
      uploadTimestamp: entity.uploadTimestamp,
      reviewTimestamp: entity.reviewTimestamp,
      rejectionReason: entity.rejectionReason,
      metadata: entity.metadata,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'documentType': documentType.name,
      'frontImageUrl': frontImageUrl,
      'backImageUrl': backImageUrl,
      'profilePhotoUrl': profilePhotoUrl,
      'status': status.name,
      'uploadTimestamp': Timestamp.fromDate(uploadTimestamp),
      'reviewTimestamp': reviewTimestamp != null ? Timestamp.fromDate(reviewTimestamp!) : null,
      'rejectionReason': rejectionReason,
      'metadata': metadata,
    };
  }

  static DocumentType _parseDocumentType(dynamic type) {
    switch (type?.toString()) {
      case 'governmentId':
        return DocumentType.governmentId;
      case 'passport':
        return DocumentType.passport;
      case 'drivingLicense':
        return DocumentType.drivingLicense;
      default:
        return DocumentType.governmentId;
    }
  }

  static DocumentStatus _parseDocumentStatus(dynamic status) {
    switch (status?.toString()) {
      case 'pending':
        return DocumentStatus.pending;
      case 'approved':
        return DocumentStatus.approved;
      case 'rejected':
        return DocumentStatus.rejected;
      default:
        return DocumentStatus.pending;
    }
  }
}
