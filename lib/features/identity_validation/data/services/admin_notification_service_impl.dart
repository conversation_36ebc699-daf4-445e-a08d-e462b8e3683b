import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/dual_verification_workflow_service.dart';

class AdminNotificationServiceImpl implements AdminNotificationService {
  final FirebaseFirestore _firestore;

  AdminNotificationServiceImpl({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<void> notifyAdminForManualReview(String userId, List<UserReportEntity> reports) async {
    try {
      // Create admin notification document
      final notificationData = {
        'type': 'manual_id_review_required',
        'userId': userId,
        'reportCount': reports.length,
        'priority': _calculatePriority(reports),
        'reasons': reports.map((r) => r.reason.name).toList(),
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'pending',
        'metadata': {
          'reportIds': reports.map((r) => r.id).toList(),
          'highestPriority': reports.isNotEmpty 
              ? reports.map((r) => r.priority).reduce((a, b) => 
                  a.index > b.index ? a : b).name
              : 'medium',
        },
      };

      await _firestore
          .collection('admin_notifications')
          .add(notificationData);

      // TODO: Send push notification to admin users
      // TODO: Send email notification if configured
      // TODO: Update admin dashboard real-time

      print('Admin notification created for manual review of user: $userId');
    } catch (e) {
      print('Failed to notify admin for manual review: $e');
    }
  }

  @override
  Future<void> notifyAdminOfAutomatedFailure(String userId, String reason) async {
    try {
      // Create admin notification for automated verification failure
      final notificationData = {
        'type': 'automated_verification_failed',
        'userId': userId,
        'reason': reason,
        'priority': 'medium',
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'pending',
        'metadata': {
          'failureReason': reason,
          'requiresManualReview': true,
        },
      };

      await _firestore
          .collection('admin_notifications')
          .add(notificationData);

      print('Admin notification created for automated verification failure: $userId - $reason');
    } catch (e) {
      print('Failed to notify admin of automated failure: $e');
    }
  }

  String _calculatePriority(List<UserReportEntity> reports) {
    if (reports.isEmpty) return 'medium';

    // Find the highest priority among all reports
    final highestPriority = reports
        .map((r) => r.priority)
        .reduce((a, b) => a.index > b.index ? a : b);

    return highestPriority.name;
  }

  Future<void> notifyAdminOfSuspiciousActivity({
    required String userId,
    required String activityType,
    required Map<String, dynamic> details,
  }) async {
    try {
      final notificationData = {
        'type': 'suspicious_activity',
        'userId': userId,
        'activityType': activityType,
        'priority': 'high',
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'pending',
        'metadata': details,
      };

      await _firestore
          .collection('admin_notifications')
          .add(notificationData);

      print('Admin notification created for suspicious activity: $userId - $activityType');
    } catch (e) {
      print('Failed to notify admin of suspicious activity: $e');
    }
  }

  Future<void> markNotificationAsRead(String notificationId, String adminUserId) async {
    try {
      await _firestore
          .collection('admin_notifications')
          .doc(notificationId)
          .update({
        'status': 'read',
        'readBy': adminUserId,
        'readAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Failed to mark notification as read: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getPendingNotifications() async {
    try {
      final querySnapshot = await _firestore
          .collection('admin_notifications')
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      print('Failed to get pending notifications: $e');
      return [];
    }
  }
}
