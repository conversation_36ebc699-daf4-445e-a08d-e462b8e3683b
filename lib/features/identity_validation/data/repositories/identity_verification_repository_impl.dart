import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/data/datasources/firebase_identity_data_source.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

class IdentityVerificationRepositoryImpl implements IdentityVerificationRepository {
  final FirebaseIdentityDataSource _dataSource;

  IdentityVerificationRepositoryImpl(this._dataSource);

  @override
  Future<Either<Failure, IdentityDocumentEntity>> uploadIdDocument({
    required String userId,
    required File frontImage,
    required File backImage,
    required File profilePhoto,
    required DocumentType documentType,
    Function(double)? onProgress,
  }) async {
    try {
      final result = await _dataSource.uploadIdDocument(
        userId: userId,
        frontImage: frontImage,
        backImage: backImage,
        profilePhoto: profilePhoto,
        documentType: documentType,
        onProgress: onProgress,
      );
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> getIdentityVerificationStatus(String userId) async {
    try {
      final result = await _dataSource.getUserValidationStatus(userId);
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IdentityDocumentEntity>> getIdentityDocument(String documentId) async {
    try {
      final result = await _dataSource.getIdentityDocument(documentId);
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserValidationStatus({
    required String userId,
    required ValidationStatus status,
    String? identityDocumentId,
  }) async {
    try {
      final result = await _dataSource.updateUserValidationStatus(
        userId: userId,
        status: status,
        identityDocumentId: identityDocumentId,
      );
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Stream<Either<Failure, UserEntity>> watchUserValidationStatus(String userId) {
    return _dataSource.watchUserValidationStatus(userId).map(
      (user) => Right<Failure, UserEntity>(user),
    ).handleError(
      (error) => Left<Failure, UserEntity>(_mapExceptionToFailure(error)),
    );
  }

  @override
  Stream<Either<Failure, IdentityDocumentEntity>> watchIdentityDocumentStatus(String documentId) {
    return _dataSource.watchIdentityDocumentStatus(documentId).map(
      (document) => Right<Failure, IdentityDocumentEntity>(document),
    ).handleError(
      (error) => Left<Failure, IdentityDocumentEntity>(_mapExceptionToFailure(error)),
    );
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception.toString().contains('network')) {
      return const NetworkFailure(message: 'Network connection failed');
    } else if (exception.toString().contains('permission')) {
      return const ServerFailure(message: 'Permission denied');
    } else if (exception.toString().contains('not found')) {
      return const ServerFailure(message: 'Document not found');
    } else if (exception.toString().contains('storage')) {
      return const ServerFailure(message: 'File upload failed');
    } else {
      return ServerFailure(message: exception.toString());
    }
  }
}
