import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_report_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/services/dual_verification_workflow_service.dart';
import 'package:respublicaseguridad/features/identity_validation/data/models/user_report_model.dart';

class UserReportRepositoryImpl implements UserReportRepository {
  final FirebaseFirestore _firestore;
  final Uuid _uuid;

  UserReportRepositoryImpl({
    required FirebaseFirestore firestore,
    required Uuid uuid,
  })  : _firestore = firestore,
        _uuid = uuid;

  @override
  Future<Either<Failure, List<UserReportEntity>>> getActiveReportsForUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_reports')
          .where('reportedUserId', isEqualTo: userId)
          .where('status', whereIn: ['pending', 'underReview'])
          .orderBy('reportedAt', descending: true)
          .get();

      final reports = querySnapshot.docs
          .map((doc) => UserReportModel.fromFirestore(doc))
          .toList();

      return Right(reports);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get active reports: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, UserReportEntity>> createUserReport(UserReportEntity report) async {
    try {
      final reportId = _uuid.v4();
      final reportModel = UserReportModel.fromEntity(report.copyWith(id: reportId));

      await _firestore
          .collection('user_reports')
          .doc(reportId)
          .set(reportModel.toFirestore());

      return Right(reportModel);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to create user report: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, int>> getReportCountForUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_reports')
          .where('reportedUserId', isEqualTo: userId)
          .get();

      return Right(querySnapshot.docs.length);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get report count: ${e.toString()}'));
    }
  }

  Future<Either<Failure, UserReportEntity>> updateReportStatus({
    required String reportId,
    required UserReportStatus status,
    String? adminUserId,
    String? adminNotes,
  }) async {
    try {
      final updateData = {
        'status': status.name,
        'reviewedAt': FieldValue.serverTimestamp(),
      };

      if (adminUserId != null) {
        updateData['adminUserId'] = adminUserId;
      }

      if (adminNotes != null) {
        updateData['adminNotes'] = adminNotes;
      }

      await _firestore
          .collection('user_reports')
          .doc(reportId)
          .update(updateData);

      final doc = await _firestore
          .collection('user_reports')
          .doc(reportId)
          .get();

      final updatedReport = UserReportModel.fromFirestore(doc);
      return Right(updatedReport);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update report status: ${e.toString()}'));
    }
  }

  Future<Either<Failure, List<UserReportEntity>>> getReportsByStatus(UserReportStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_reports')
          .where('status', isEqualTo: status.name)
          .orderBy('reportedAt', descending: true)
          .get();

      final reports = querySnapshot.docs
          .map((doc) => UserReportModel.fromFirestore(doc))
          .toList();

      return Right(reports);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get reports by status: ${e.toString()}'));
    }
  }
}
