import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/features/identity_validation/data/models/identity_document_model.dart';
import 'package:respublicaseguridad/features/identity_validation/data/models/user_model.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

abstract class FirebaseIdentityDataSource {
  Future<IdentityDocumentModel> uploadIdDocument({
    required String userId,
    required File frontImage,
    required File backImage,
    required File profilePhoto,
    required DocumentType documentType,
    Function(double)? onProgress,
  });

  Future<UserModel> getUserValidationStatus(String userId);
  Future<IdentityDocumentModel> getIdentityDocument(String documentId);
  Future<UserModel> updateUserValidationStatus({
    required String userId,
    required ValidationStatus status,
    String? identityDocumentId,
  });

  Stream<UserModel> watchUserValidationStatus(String userId);
  Stream<IdentityDocumentModel> watchIdentityDocumentStatus(String documentId);
}

class FirebaseIdentityDataSourceImpl implements FirebaseIdentityDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final Uuid _uuid;

  FirebaseIdentityDataSourceImpl({
    required FirebaseFirestore firestore,
    required FirebaseStorage storage,
    required Uuid uuid,
  })  : _firestore = firestore,
        _storage = storage,
        _uuid = uuid;

  @override
  Future<IdentityDocumentModel> uploadIdDocument({
    required String userId,
    required File frontImage,
    required File backImage,
    required File profilePhoto,
    required DocumentType documentType,
    Function(double)? onProgress,
  }) async {
    final documentId = _uuid.v4();
    final timestamp = DateTime.now();

    try {
      final compressedFrontImage = await _compressImage(frontImage);
      final compressedBackImage = await _compressImage(backImage);
      final compressedProfilePhoto = await _compressImage(profilePhoto);

      // Track progress for 3 uploads (front, back, profile)
      double totalProgress = 0.0;
      int completedUploads = 0;

      void updateProgress(double uploadProgress) {
        final currentUploadProgress = uploadProgress / 3.0; // Each upload is 1/3 of total
        final baseProgress = completedUploads / 3.0;
        totalProgress = baseProgress + currentUploadProgress;
        onProgress?.call(totalProgress);
      }

      // Upload images to Firebase Storage with progress tracking
      final frontImageUrl = await _uploadImageWithProgress(
        compressedFrontImage,
        'identity_verifications/$userId/front_$documentId.jpg',
        updateProgress,
      );
      completedUploads++;

      final backImageUrl = await _uploadImageWithProgress(
        compressedBackImage,
        'identity_verifications/$userId/back_$documentId.jpg',
        updateProgress,
      );
      completedUploads++;

      final profilePhotoUrl = await _uploadImageWithProgress(
        compressedProfilePhoto,
        'profile_photos/$userId/profile_$documentId.jpg',
        updateProgress,
      );
      completedUploads++;

      // Create identity document record
      final identityDocument = IdentityDocumentModel(
        id: documentId,
        userId: userId,
        documentType: documentType,
        frontImageUrl: frontImageUrl,
        backImageUrl: backImageUrl,
        profilePhotoUrl: profilePhotoUrl,
        status: DocumentStatus.pending,
        uploadTimestamp: timestamp,
      );

      // Save to Firestore
      await _firestore
          .collection('identity_documents')
          .doc(documentId)
          .set(identityDocument.toFirestore());

      // Update user's validation status to pending ID (will be processed by dual workflow)
      await updateUserValidationStatus(
        userId: userId,
        status: ValidationStatus.pendingId,
        identityDocumentId: documentId,
      );

      return identityDocument;
    } catch (e) {
      // Clean up uploaded files if document creation fails
      await _cleanupUploadedFiles(userId, documentId);
      rethrow;
    }
  }

  Future<File> _compressImage(File imageFile) async {
    final compressedFile = await FlutterImageCompress.compressAndGetFile(
      imageFile.absolute.path,
      '${imageFile.parent.path}/compressed_${imageFile.uri.pathSegments.last}',
      quality: 85,
      minWidth: 800,
      minHeight: 600,
      format: CompressFormat.jpeg,
    );

    // Convert XFile to File if compression succeeded, otherwise return original
    if (compressedFile != null) {
      return File(compressedFile.path);
    }
    return imageFile;
  }

  Future<String> _uploadImageWithProgress(
    File imageFile,
    String path,
    Function(double) onProgress,
  ) async {
    final ref = _storage.ref().child(path);
    final uploadTask = ref.putFile(imageFile);

    // Listen to upload progress
    uploadTask.snapshotEvents.listen((snapshot) {
      final progress = snapshot.bytesTransferred / snapshot.totalBytes;
      onProgress(progress);
    });

    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }

  Future<void> _cleanupUploadedFiles(String userId, String documentId) async {
    try {
      final paths = [
        'identity_verifications/$userId/front_$documentId.jpg',
        'identity_verifications/$userId/back_$documentId.jpg',
        'profile_photos/$userId/profile_$documentId.jpg',
      ];

      for (final path in paths) {
        try {
          await _storage.ref().child(path).delete();
        } catch (e) {
          // Ignore individual file deletion errors
        }
      }
    } catch (e) {
      // Ignore cleanup errors
    }
  }

  @override
  Future<UserModel> getUserValidationStatus(String userId) async {
    final doc = await _firestore.collection('users').doc(userId).get();
    return UserModel.fromFirestore(doc);
  }

  @override
  Future<IdentityDocumentModel> getIdentityDocument(String documentId) async {
    final doc = await _firestore.collection('identity_documents').doc(documentId).get();
    
    if (!doc.exists) {
      throw Exception('Identity document not found');
    }
    
    return IdentityDocumentModel.fromFirestore(doc);
  }

  @override
  Future<UserModel> updateUserValidationStatus({
    required String userId,
    required ValidationStatus status,
    String? identityDocumentId,
  }) async {
    final updateData = {
      'validationStatus': status.name,
      'updatedAt': FieldValue.serverTimestamp(),
    };

    if (identityDocumentId != null) {
      updateData['identityDocumentId'] = identityDocumentId;
    }

    await _firestore.collection('users').doc(userId).set(updateData, SetOptions(merge: true));
    
    return getUserValidationStatus(userId);
  }

  @override
  Stream<UserModel> watchUserValidationStatus(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((doc) => UserModel.fromFirestore(doc));
  }

  @override
  Stream<IdentityDocumentModel> watchIdentityDocumentStatus(String documentId) {
    return _firestore
        .collection('identity_documents')
        .doc(documentId)
        .snapshots()
        .map((doc) => IdentityDocumentModel.fromFirestore(doc));
  }
}
