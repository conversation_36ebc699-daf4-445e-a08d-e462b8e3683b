import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/models/captured_image.dart';
import 'package:respublicaseguridad/features/identity_validation/presentation/widgets/camera_capture_widget.dart';
import 'package:respublicaseguridad/features/profile/presentation/cubit/profile_photo_cubit.dart';

class ProfilePhotoCaptureWidget extends StatelessWidget {
  const ProfilePhotoCaptureWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          'Take Profile Photo',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Instructions
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(20.w),
              child: Column(
                children: [
                  Icon(
                    Icons.person,
                    size: 48.r,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'Position your face in the center',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Make sure your face is well-lit and clearly visible',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Camera Section
            Expanded(
              child: Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.6,
                  ),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Camera widget
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20.r),
                        child: CameraCaptureWidget(
                          imageType: ImageType.profilePhoto,
                          instructionsText: 'Take a profile photo',
                          onImageCaptured: (File imageFile) {
                            final authState = context.read<AuthBloc>().state;
                            if (authState.user.isNotEmpty) {
                              context.read<ProfilePhotoCubit>().uploadProfilePhoto(
                                userId: authState.user.id,
                                imageFile: imageFile,
                              );
                              Navigator.of(context).pop();
                            }
                          },
                          forceFrontCamera: true,
                        ),
                      ),

                      // Selfie guide overlay with enhanced design
                      IgnorePointer(
                        child: Center(
                          child: Container(
                            width: 220.w,
                            height: 220.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 3.w,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 10,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Container(
                              margin: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.5),
                                  width: 1.w,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Tips Section
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(20.w),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        size: 20.r,
                        color: Colors.amber,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Tips for a great photo:',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  _buildTip('Look directly at the camera'),
                  _buildTip('Ensure good lighting on your face'),
                  _buildTip('Keep your face centered in the circle'),
                  _buildTip('Remove sunglasses or hats'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h),
      child: Row(
        children: [
          Container(
            width: 4.w,
            height: 4.w,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
