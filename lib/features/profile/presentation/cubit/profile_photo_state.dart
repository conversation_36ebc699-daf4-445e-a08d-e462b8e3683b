import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';

abstract class ProfilePhotoState extends Equatable {
  const ProfilePhotoState();

  @override
  List<Object?> get props => [];
}

class ProfilePhotoInitial extends ProfilePhotoState {
  const ProfilePhotoInitial();
}

class ProfilePhotoLoading extends ProfilePhotoState {
  const ProfilePhotoLoading();
}

class ProfilePhotoUploading extends ProfilePhotoState {
  final double progress;

  const ProfilePhotoUploading(this.progress);

  @override
  List<Object?> get props => [progress];
}

class ProfilePhotoLoaded extends ProfilePhotoState {
  final ProfilePhoto? currentPhoto;
  final List<ProfilePhoto> allPhotos;

  const ProfilePhotoLoaded({
    this.currentPhoto,
    this.allPhotos = const [],
  });

  @override
  List<Object?> get props => [currentPhoto, allPhotos];

  ProfilePhotoLoaded copyWith({
    ProfilePhoto? currentPhoto,
    List<ProfilePhoto>? allPhotos,
  }) {
    return ProfilePhotoLoaded(
      currentPhoto: currentPhoto ?? this.currentPhoto,
      allPhotos: allPhotos ?? this.allPhotos,
    );
  }
}

class ProfilePhotoError extends ProfilePhotoState {
  final String message;

  const ProfilePhotoError(this.message);

  @override
  List<Object?> get props => [message];
}

class ProfilePhotoUploadSuccess extends ProfilePhotoState {
  final ProfilePhoto photo;

  const ProfilePhotoUploadSuccess(this.photo);

  @override
  List<Object?> get props => [photo];
}

class ProfilePhotoDeleted extends ProfilePhotoState {
  const ProfilePhotoDeleted();
}

class ProfilePhotoActivated extends ProfilePhotoState {
  final ProfilePhoto photo;

  const ProfilePhotoActivated(this.photo);

  @override
  List<Object?> get props => [photo];
}
