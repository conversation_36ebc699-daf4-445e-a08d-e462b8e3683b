import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/features/profile/domain/usecases/delete_profile_photo_use_case.dart';
import 'package:respublicaseguridad/features/profile/domain/usecases/get_current_profile_photo_use_case.dart';
import 'package:respublicaseguridad/features/profile/domain/usecases/set_active_profile_photo_use_case.dart';
import 'package:respublicaseguridad/features/profile/domain/usecases/upload_profile_photo_use_case.dart';
import 'package:respublicaseguridad/features/profile/presentation/cubit/profile_photo_state.dart';

@injectable
class ProfilePhotoCubit extends Cubit<ProfilePhotoState> {
  final UploadProfilePhotoUseCase _uploadProfilePhotoUseCase;
  final GetCurrentProfilePhotoUseCase _getCurrentProfilePhotoUseCase;
  final DeleteProfilePhotoUseCase _deleteProfilePhotoUseCase;
  final SetActiveProfilePhotoUseCase _setActiveProfilePhotoUseCase;

  ProfilePhotoCubit(
    this._uploadProfilePhotoUseCase,
    this._getCurrentProfilePhotoUseCase,
    this._deleteProfilePhotoUseCase,
    this._setActiveProfilePhotoUseCase,
  ) : super(const ProfilePhotoInitial());

  Future<void> loadCurrentProfilePhoto(String userId) async {
    emit(const ProfilePhotoLoading());

    final result = await _getCurrentProfilePhotoUseCase(userId);
    
    result.fold(
      (failure) => emit(ProfilePhotoError(failure.message)),
      (photo) => emit(ProfilePhotoLoaded(currentPhoto: photo)),
    );
  }

  Future<void> uploadProfilePhoto({
    required String userId,
    required File imageFile,
  }) async {
    emit(const ProfilePhotoUploading(0.0));

    final result = await _uploadProfilePhotoUseCase(
      userId: userId,
      imageFile: imageFile,
      onProgress: (progress) {
        emit(ProfilePhotoUploading(progress));
      },
    );

    result.fold(
      (failure) => emit(ProfilePhotoError(failure.message)),
      (photo) {
        emit(ProfilePhotoUploadSuccess(photo));
        // Reload current photo after successful upload
        loadCurrentProfilePhoto(userId);
      },
    );
  }

  Future<void> deleteProfilePhoto(String photoId, String userId) async {
    emit(const ProfilePhotoLoading());

    final result = await _deleteProfilePhotoUseCase(photoId);
    
    result.fold(
      (failure) => emit(ProfilePhotoError(failure.message)),
      (_) {
        emit(const ProfilePhotoDeleted());
        // Reload current photo after deletion
        loadCurrentProfilePhoto(userId);
      },
    );
  }

  Future<void> setActiveProfilePhoto({
    required String userId,
    required String photoId,
  }) async {
    emit(const ProfilePhotoLoading());

    final result = await _setActiveProfilePhotoUseCase(
      userId: userId,
      photoId: photoId,
    );

    result.fold(
      (failure) => emit(ProfilePhotoError(failure.message)),
      (photo) {
        emit(ProfilePhotoActivated(photo));
        // Reload current photo after activation
        loadCurrentProfilePhoto(userId);
      },
    );
  }

  void clearError() {
    if (state is ProfilePhotoError) {
      emit(const ProfilePhotoInitial());
    }
  }
}
