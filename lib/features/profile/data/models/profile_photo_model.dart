import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';

class ProfilePhotoModel extends ProfilePhoto {
  const ProfilePhotoModel({
    required super.id,
    required super.userId,
    required super.url,
    required super.thumbnailUrl,
    required super.uploadedAt,
    super.fileName,
    super.fileSizeBytes,
    super.isActive,
  });

  factory ProfilePhotoModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return ProfilePhotoModel(
        id: doc.id,
        userId: '',
        url: '',
        thumbnailUrl: '',
        uploadedAt: DateTime.now(),
      );
    }

    return ProfilePhotoModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      url: data['url'] as String? ?? '',
      thumbnailUrl: data['thumbnailUrl'] as String? ?? '',
      fileName: data['fileName'] as String?,
      fileSizeBytes: data['fileSizeBytes'] as int?,
      uploadedAt: (data['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] as bool? ?? true,
    );
  }

  factory ProfilePhotoModel.fromEntity(ProfilePhoto entity) {
    return ProfilePhotoModel(
      id: entity.id,
      userId: entity.userId,
      url: entity.url,
      thumbnailUrl: entity.thumbnailUrl,
      fileName: entity.fileName,
      fileSizeBytes: entity.fileSizeBytes,
      uploadedAt: entity.uploadedAt,
      isActive: entity.isActive,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'url': url,
      'thumbnailUrl': thumbnailUrl,
      'fileName': fileName,
      'fileSizeBytes': fileSizeBytes,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'isActive': isActive,
    };
  }

  ProfilePhotoModel copyWith({
    String? id,
    String? userId,
    String? url,
    String? thumbnailUrl,
    String? fileName,
    int? fileSizeBytes,
    DateTime? uploadedAt,
    bool? isActive,
  }) {
    return ProfilePhotoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      isActive: isActive ?? this.isActive,
    );
  }
}
