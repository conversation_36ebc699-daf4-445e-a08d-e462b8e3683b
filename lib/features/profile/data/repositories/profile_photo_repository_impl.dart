import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/data/datasources/profile_photo_remote_data_source.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';
import 'package:respublicaseguridad/features/profile/domain/repositories/profile_photo_repository.dart';

@LazySingleton(as: ProfilePhotoRepository)
class ProfilePhotoRepositoryImpl implements ProfilePhotoRepository {
  final ProfilePhotoRemoteDataSource _remoteDataSource;

  ProfilePhotoRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, ProfilePhoto>> uploadProfilePhoto({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final result = await _remoteDataSource.uploadProfilePhoto(
        userId: userId,
        imageFile: imageFile,
        onProgress: onProgress,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ProfilePhoto?>> getCurrentProfilePhoto(String userId) async {
    try {
      final result = await _remoteDataSource.getCurrentProfilePhoto(userId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ProfilePhoto>>> getUserProfilePhotos(String userId) async {
    try {
      final result = await _remoteDataSource.getUserProfilePhotos(userId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ProfilePhoto>> updateProfilePhoto({
    required String photoId,
    bool? isActive,
  }) async {
    try {
      final result = await _remoteDataSource.updateProfilePhoto(
        photoId: photoId,
        isActive: isActive,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProfilePhoto(String photoId) async {
    try {
      await _remoteDataSource.deleteProfilePhoto(photoId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ProfilePhoto>> setActiveProfilePhoto({
    required String userId,
    required String photoId,
  }) async {
    try {
      final result = await _remoteDataSource.setActiveProfilePhoto(
        userId: userId,
        photoId: photoId,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
