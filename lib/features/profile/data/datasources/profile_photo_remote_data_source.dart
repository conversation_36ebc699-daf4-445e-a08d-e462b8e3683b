import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:injectable/injectable.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/features/profile/data/models/profile_photo_model.dart';

abstract class ProfilePhotoRemoteDataSource {
  Future<ProfilePhotoModel> uploadProfilePhoto({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  });

  Future<ProfilePhotoModel?> getCurrentProfilePhoto(String userId);
  Future<List<ProfilePhotoModel>> getUserProfilePhotos(String userId);
  Future<ProfilePhotoModel> updateProfilePhoto({
    required String photoId,
    bool? isActive,
  });
  Future<void> deleteProfilePhoto(String photoId);
  Future<ProfilePhotoModel> setActiveProfilePhoto({
    required String userId,
    required String photoId,
  });
}

@LazySingleton(as: ProfilePhotoRemoteDataSource)
class ProfilePhotoRemoteDataSourceImpl implements ProfilePhotoRemoteDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final Uuid _uuid;

  ProfilePhotoRemoteDataSourceImpl(
    this._firestore,
    this._storage,
    this._uuid,
  );

  @override
  Future<ProfilePhotoModel> uploadProfilePhoto({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final photoId = _uuid.v4();
      
      // Compress the image for better performance
      final compressedFile = await _compressImage(imageFile);
      final thumbnailFile = await _createThumbnail(compressedFile);
      
      // Upload main image
      final mainImagePath = 'profile_photos/$userId/${photoId}_main.jpg';
      final mainImageRef = _storage.ref().child(mainImagePath);
      final mainUploadTask = mainImageRef.putFile(compressedFile);
      
      // Upload thumbnail
      final thumbnailPath = 'profile_photos/$userId/${photoId}_thumb.jpg';
      final thumbnailRef = _storage.ref().child(thumbnailPath);
      final thumbnailUploadTask = thumbnailRef.putFile(thumbnailFile);
      
      // Monitor progress for main image
      if (onProgress != null) {
        mainUploadTask.snapshotEvents.listen((snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }
      
      // Wait for both uploads to complete
      final mainSnapshot = await mainUploadTask;
      final thumbnailSnapshot = await thumbnailUploadTask;
      
      // Get download URLs
      final mainUrl = await mainSnapshot.ref.getDownloadURL();
      final thumbnailUrl = await thumbnailSnapshot.ref.getDownloadURL();
      
      // Get file info
      final fileSize = await compressedFile.length();
      final fileName = path.basename(imageFile.path);
      
      // Deactivate other profile photos for this user
      await _deactivateOtherPhotos(userId);
      
      // Create profile photo document
      final profilePhoto = ProfilePhotoModel(
        id: photoId,
        userId: userId,
        url: mainUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: fileName,
        fileSizeBytes: fileSize,
        uploadedAt: DateTime.now(),
        isActive: true,
      );
      
      // Save to Firestore
      await _firestore
          .collection('profile_photos')
          .doc(photoId)
          .set(profilePhoto.toFirestore());
      
      // Update user's photoURL in auth
      await _updateUserPhotoURL(userId, mainUrl);
      
      return profilePhoto;
    } catch (e) {
      throw Exception('Failed to upload profile photo: $e');
    }
  }

  @override
  Future<ProfilePhotoModel?> getCurrentProfilePhoto(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('profile_photos')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        return null;
      }
      
      return ProfilePhotoModel.fromFirestore(querySnapshot.docs.first);
    } catch (e) {
      throw Exception('Failed to get current profile photo: $e');
    }
  }

  @override
  Future<List<ProfilePhotoModel>> getUserProfilePhotos(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('profile_photos')
          .where('userId', isEqualTo: userId)
          .orderBy('uploadedAt', descending: true)
          .get();
      
      return querySnapshot.docs
          .map((doc) => ProfilePhotoModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user profile photos: $e');
    }
  }

  @override
  Future<ProfilePhotoModel> updateProfilePhoto({
    required String photoId,
    bool? isActive,
  }) async {
    try {
      final docRef = _firestore.collection('profile_photos').doc(photoId);
      final updateData = <String, dynamic>{};
      
      if (isActive != null) {
        updateData['isActive'] = isActive;
      }
      
      await docRef.update(updateData);
      
      final updatedDoc = await docRef.get();
      return ProfilePhotoModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw Exception('Failed to update profile photo: $e');
    }
  }

  @override
  Future<void> deleteProfilePhoto(String photoId) async {
    try {
      // Get photo data first
      final doc = await _firestore.collection('profile_photos').doc(photoId).get();
      if (!doc.exists) return;
      
      final photo = ProfilePhotoModel.fromFirestore(doc);
      
      // Delete from storage
      try {
        await _storage.refFromURL(photo.url).delete();
        await _storage.refFromURL(photo.thumbnailUrl).delete();
      } catch (e) {
        // Ignore storage deletion errors
      }
      
      // Delete from Firestore
      await _firestore.collection('profile_photos').doc(photoId).delete();
    } catch (e) {
      throw Exception('Failed to delete profile photo: $e');
    }
  }

  @override
  Future<ProfilePhotoModel> setActiveProfilePhoto({
    required String userId,
    required String photoId,
  }) async {
    try {
      // Deactivate other photos
      await _deactivateOtherPhotos(userId);
      
      // Activate the selected photo
      final updatedPhoto = await updateProfilePhoto(
        photoId: photoId,
        isActive: true,
      );
      
      // Update user's photoURL in auth
      await _updateUserPhotoURL(userId, updatedPhoto.url);
      
      return updatedPhoto;
    } catch (e) {
      throw Exception('Failed to set active profile photo: $e');
    }
  }

  Future<File> _compressImage(File imageFile) async {
    final compressedFile = await FlutterImageCompress.compressAndGetFile(
      imageFile.absolute.path,
      '${imageFile.parent.path}/compressed_${path.basename(imageFile.path)}',
      quality: 85,
      minWidth: 800,
      minHeight: 800,
    );

    if (compressedFile != null) {
      return File(compressedFile.path);
    }
    return imageFile;
  }

  Future<File> _createThumbnail(File imageFile) async {
    final thumbnailFile = await FlutterImageCompress.compressAndGetFile(
      imageFile.absolute.path,
      '${imageFile.parent.path}/thumb_${path.basename(imageFile.path)}',
      quality: 70,
      minWidth: 200,
      minHeight: 200,
    );

    if (thumbnailFile != null) {
      return File(thumbnailFile.path);
    }
    return imageFile;
  }

  Future<void> _deactivateOtherPhotos(String userId) async {
    final batch = _firestore.batch();
    
    final querySnapshot = await _firestore
        .collection('profile_photos')
        .where('userId', isEqualTo: userId)
        .where('isActive', isEqualTo: true)
        .get();
    
    for (final doc in querySnapshot.docs) {
      batch.update(doc.reference, {'isActive': false});
    }
    
    await batch.commit();
  }

  Future<void> _updateUserPhotoURL(String userId, String photoURL) async {
    try {
      // Update in users collection if it exists
      await _firestore.collection('users').doc(userId).update({
        'photoURL': photoURL,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // Ignore if user document doesn't exist
    }
  }
}
