import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';

abstract class ProfilePhotoRepository {
  /// Upload a new profile photo
  Future<Either<Failure, ProfilePhoto>> uploadProfilePhoto({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  });

  /// Get the current active profile photo for a user
  Future<Either<Failure, ProfilePhoto?>> getCurrentProfilePhoto(String userId);

  /// Get all profile photos for a user (including inactive ones)
  Future<Either<Failure, List<ProfilePhoto>>> getUserProfilePhotos(String userId);

  /// Update profile photo metadata
  Future<Either<Failure, ProfilePhoto>> updateProfilePhoto({
    required String photoId,
    bool? isActive,
  });

  /// Delete a profile photo
  Future<Either<Failure, void>> deleteProfilePhoto(String photoId);

  /// Set a profile photo as active (and deactivate others)
  Future<Either<Failure, ProfilePhoto>> setActiveProfilePhoto({
    required String userId,
    required String photoId,
  });
}
