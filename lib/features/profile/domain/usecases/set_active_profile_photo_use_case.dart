import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';
import 'package:respublicaseguridad/features/profile/domain/repositories/profile_photo_repository.dart';

@lazySingleton
class SetActiveProfilePhotoUseCase {
  final ProfilePhotoRepository _repository;

  const SetActiveProfilePhotoUseCase(this._repository);

  Future<Either<Failure, ProfilePhoto>> call({
    required String userId,
    required String photoId,
  }) {
    return _repository.setActiveProfilePhoto(
      userId: userId,
      photoId: photoId,
    );
  }
}
