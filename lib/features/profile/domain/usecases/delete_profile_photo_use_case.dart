import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/domain/repositories/profile_photo_repository.dart';

@lazySingleton
class DeleteProfilePhotoUseCase {
  final ProfilePhotoRepository _repository;

  const DeleteProfilePhotoUseCase(this._repository);

  Future<Either<Failure, void>> call(String photoId) {
    return _repository.deleteProfilePhoto(photoId);
  }
}
