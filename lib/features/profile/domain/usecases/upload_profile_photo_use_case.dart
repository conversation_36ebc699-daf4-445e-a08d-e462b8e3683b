import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';
import 'package:respublicaseguridad/features/profile/domain/repositories/profile_photo_repository.dart';

@lazySingleton
class UploadProfilePhotoUseCase {
  final ProfilePhotoRepository _repository;

  const UploadProfilePhotoUseCase(this._repository);

  Future<Either<Failure, ProfilePhoto>> call({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) {
    return _repository.uploadProfilePhoto(
      userId: userId,
      imageFile: imageFile,
      onProgress: onProgress,
    );
  }
}
