import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/profile/domain/entities/profile_photo.dart';
import 'package:respublicaseguridad/features/profile/domain/repositories/profile_photo_repository.dart';

@lazySingleton
class GetCurrentProfilePhotoUseCase {
  final ProfilePhotoRepository _repository;

  const GetCurrentProfilePhotoUseCase(this._repository);

  Future<Either<Failure, ProfilePhoto?>> call(String userId) {
    return _repository.getCurrentProfilePhoto(userId);
  }
}
