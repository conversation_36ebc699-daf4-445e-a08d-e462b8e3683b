import 'package:equatable/equatable.dart';

class ProfilePhoto extends Equatable {
  const ProfilePhoto({
    required this.id,
    required this.userId,
    required this.url,
    required this.thumbnailUrl,
    required this.uploadedAt,
    this.fileName,
    this.fileSizeBytes,
    this.isActive = true,
  });

  final String id;
  final String userId;
  final String url;
  final String thumbnailUrl;
  final String? fileName;
  final int? fileSizeBytes;
  final DateTime uploadedAt;
  final bool isActive;

  ProfilePhoto copyWith({
    String? id,
    String? userId,
    String? url,
    String? thumbnailUrl,
    String? fileName,
    int? fileSizeBytes,
    DateTime? uploadedAt,
    bool? isActive,
  }) {
    return ProfilePhoto(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        url,
        thumbnailUrl,
        fileName,
        fileSizeBytes,
        uploadedAt,
        isActive,
      ];

  static final empty = ProfilePhoto(
    id: '',
    userId: '',
    url: '',
    thumbnailUrl: '',
    uploadedAt: DateTime.now(),
  );

  bool get isEmpty => this == ProfilePhoto.empty;
  bool get isNotEmpty => this != ProfilePhoto.empty;
}
