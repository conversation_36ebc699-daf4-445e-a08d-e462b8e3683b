import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/core/app_theme.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
    
    // Check authentication status immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('SplashScreen - Dispatching AuthCheckRequested');
      context.read<AuthBloc>().add(const AuthCheckRequested());
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        // Debug print to track auth state changes
        print('SplashScreen - Auth State: ${state.status}');
        // Navigation happens in the router redirect logic
      },
      child: Scaffold(
        body: Stack(
          children: [
            // Background gradient
            Container(
              decoration: const BoxDecoration(
                color: AppTheme.lightBackgroundColor,
              ),
            ),
            
            // Pattern overlay
            Opacity(
              opacity: 0.05,
              child: CustomPaint(
                painter: DotPatternPainter(),
                size: Size.infinite,
              ),
            ),
            
            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ScaleTransition(
                    scale: _animation,
                    child: Image.asset(
                      'assets/logo.png',
                      width: 160.w,
                      height: 160.w,
                    ),
                  ),
                  SizedBox(height: 20.h),
                  FadeTransition(
                    opacity: _animation,
                    child: Text(
                      'República Seguridad',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 26.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DotPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.shade900.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    final spacing = 30.0;
    
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        // Adding some variation to the pattern
        final offset = (y / spacing).floor() % 2 == 0 ? 0.0 : spacing / 2;
        canvas.drawCircle(Offset(x + offset, y), 1.5, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
