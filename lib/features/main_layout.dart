import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:respublicaseguridad/core/navigation/bottom_navigation_bar.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';

class MainLayout extends StatelessWidget {
  final Widget child;
  final String currentLocation;

  const MainLayout({
    Key? key,
    required this.child,
    required this.currentLocation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScaffoldWithBottomNavBar(
      currentRoute: currentLocation,
      body: child,
    );
  }
}

class MainShell extends StatelessWidget {
  final Widget child;
  final String location;

  const MainShell({
    Key? key,
    required this.child,
    required this.location,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<AuthBloc>(),
      child: MainLayout(
        currentLocation: location,
        child: child,
      ),
    );
  }
} 