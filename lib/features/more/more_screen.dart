import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/localization/cubit/language_cubit.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_cubit.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_state.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/more/widgets/profile_card.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_cubit.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_state.dart';
import 'package:respublicaseguridad/core/di/injection.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return BlocProvider(
      create: (context) => getIt<IdentityVerificationCubit>(),
      child: Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          context.l10n.moreOptions,
        ),
        actions: [
          // Add logout action to app bar
          IconButton(
            icon: Icon(
              FluentIcons.sign_out_24_regular,
              color: isDark ? Colors.redAccent : Colors.red,
            ),
            tooltip: context.l10n.logout,
            onPressed: () {
              _showLogoutConfirmation(context);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            final user = state.user;
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Section
                  ProfileCard(user: user),
        
                  SectionHeader(title: context.l10n.settings),
                  
                  SettingsSection(user: user),
                  
                  // Bottom spacing
                  SizedBox(height: 32.h),
                ],
              ),
            );
          },
        ),
      ),
    ),
    );
  }
  
  void _showLogoutConfirmation(BuildContext context) {
    // Use iOS-style dialog for both platforms
    IosDialog.showAlertDialog(
      context: context,
      title: context.l10n.logout,
      message: context.l10n.logoutConfirmation,
      cancelText: context.l10n.cancel,
      confirmText: context.l10n.logout,
      onConfirm: () {
        context.read<AuthBloc>().add(const AuthLoggedOut());
      },
    );
  }
}

class SectionHeader extends StatelessWidget {
  final String title;
  
  const SectionHeader({Key? key, required this.title}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 8.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }
}

class SettingsSection extends StatelessWidget {
  final User user;
  
  const SettingsSection({Key? key, required this.user}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Account Settings
        _buildSettingTile(
          context,
          icon: FluentIcons.person_24_regular,
          title: context.l10n.accountSettings,
          onTap: () {
            NavigationService.navigateTo(context, 'account-settings');
          },
        ),

        // Identity Verification
        _buildIdentityVerificationTile(context, user),

        // Zone Validation
        _buildSettingTile(
          context,
          icon: FluentIcons.location_24_regular,
          title: 'My Zones',
          onTap: () {
            NavigationService.navigateTo(context, 'my-zones');
          },
        ),

        // Notifications Settings
        _buildSettingTile(
          context,
          icon: FluentIcons.alert_24_regular,
          title: context.l10n.notifications,
          onTap: () {
            NavigationService.navigateTo(context, 'notification-preferences');
          },
        ),

        // Language Settings
        BlocBuilder<LanguageCubit, Locale>(
          builder: (context, locale) {
            return _buildSettingTile(
              context,
              icon: FluentIcons.globe_24_regular,
              title: context.l10n.language,
              trailing: Text(locale.languageCode == 'es' ? context.l10n.spanish : context.l10n.english),
              onTap: () {
                NavigationService.navigateTo(context, 'settings');
              },
            );
          },
        ),
        
        // Theme Settings
        BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return _buildSettingTile(
              context,
              icon: state.themeMode == ThemeMode.light
                  ? FluentIcons.weather_sunny_24_regular
                  : FluentIcons.weather_moon_24_regular,
              title: context.l10n.themeMode,
              trailing: Text(state.themeMode == ThemeMode.light ? context.l10n.light : context.l10n.dark),
              onTap: () {
                context.read<ThemeCubit>().toggleTheme();
              },
            );
          },
        ),
        
        // Privacy Settings
        _buildSettingTile(
          context,
          icon: FluentIcons.shield_24_regular,
          title: context.l10n.privacy,
          onTap: () {},
        ),

        // Help & Support
        _buildSettingTile(
          context,
          icon: FluentIcons.question_circle_24_regular,
          title: context.l10n.helpSupport,
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildIdentityVerificationTile(BuildContext context, User user) {
    return BlocBuilder<IdentityVerificationCubit, IdentityVerificationState>(
      builder: (context, state) {
        // Only load status when the widget is first built and we have no cached data
        if (state is IdentityVerificationInitial) {
          // Use addPostFrameCallback to prevent calling during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<IdentityVerificationCubit>().loadIdentityVerificationStatus(user.id);
          });
        }

        if (state is IdentityVerificationLoading) {
          return _buildSettingTile(
            context,
            icon: FluentIcons.shield_checkmark_24_regular,
            title: context.l10n.identityVerificationTitle,
            trailing: SizedBox(
              width: 16.w,
              height: 16.h,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            onTap: () {},
          );
        }

        if (state is IdentityVerificationError) {
          return _buildSettingTile(
            context,
            icon: FluentIcons.shield_error_24_regular,
            title: context.l10n.identityVerificationTitle,
            trailing: Text(
              'Error',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12.sp,
              ),
            ),
            onTap: () => _showVerificationErrorDialog(context, state.message),
          );
        }

        if (state is IdentityVerificationLoaded) {
          return _buildSettingTile(
            context,
            icon: _getStatusIcon(state.status),
            title: context.l10n.identityVerificationTitle,
            trailing: _buildStatusChip(context, state.status),
            onTap: () => _handleIdentityVerificationTap(context, state),
          );
        }

        // Fallback for unknown states - show loading indicator for initial state
        return _buildSettingTile(
          context,
          icon: FluentIcons.shield_24_regular,
          title: context.l10n.identityVerificationTitle,
          trailing: SizedBox(
            width: 16.w,
            height: 16.h,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          onTap: () {},
        );
      },
    );
  }

  /// Handle tap on identity verification tile based on current state
  void _handleIdentityVerificationTap(BuildContext context, IdentityVerificationLoaded state) {
    if (state.canStartVerification) {
      // Show dialog for unverified or rejected users
      _showVerificationPromptDialog(context, state);
    } else {
      // Show status information dialog for verified/pending users
      _showVerificationStatusDialog(context, state);
    }
  }

  /// Show error dialog when verification status loading fails
  void _showVerificationErrorDialog(BuildContext context, String message) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: 'Unable to load verification status: $message',
      confirmText: 'Retry',
      cancelText: 'Cancel',
      onConfirm: () {
        final user = context.read<AuthBloc>().state.user;
        context.read<IdentityVerificationCubit>().refreshStatus(user.id);
      },
    );
  }

  /// Show verification status information dialog
  void _showVerificationStatusDialog(BuildContext context, IdentityVerificationLoaded state) {
    IosDialog.showAlertDialog(
      context: context,
      title: state.getDialogTitle(),
      message: state.getDetailedStatusMessage(),
      confirmText: 'OK',
    );
  }

  /// Show verification prompt dialog for unverified/rejected users
  void _showVerificationPromptDialog(BuildContext context, IdentityVerificationLoaded state) {
    IosDialog.showAlertDialog(
      context: context,
      title: state.getDialogTitle(),
      message: state.getDetailedStatusMessage(),
      confirmText: context.l10n.verifyIdentity,
      cancelText: context.l10n.cancel,
      onConfirm: () {
        NavigationService.navigateTo(context, 'identity-validation');
      },
    );
  }

  IconData _getStatusIcon(ValidationStatus status) {
    switch (status) {
      case ValidationStatus.validated:
        return FluentIcons.shield_checkmark_24_filled;
      case ValidationStatus.pendingId:
      case ValidationStatus.pendingReview:
      case ValidationStatus.pendingAutomaticVerification:
        return FluentIcons.clock_24_regular;
      case ValidationStatus.rejected:
        return FluentIcons.shield_error_24_regular;
      case ValidationStatus.suspended:
        return FluentIcons.shield_prohibited_24_regular;
      case ValidationStatus.unverified:
        return FluentIcons.shield_24_regular;
    }
  }

  Widget _buildStatusChip(BuildContext context, ValidationStatus status) {
    final theme = Theme.of(context);

    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case ValidationStatus.validated:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        text = context.l10n.verified;
        break;
      case ValidationStatus.pendingId:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        text = context.l10n.inProcess;
        break;
      case ValidationStatus.pendingReview:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        text = context.l10n.underReview;
        break;
      case ValidationStatus.pendingAutomaticVerification:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        text = 'Auto Verifying';
        break;
      case ValidationStatus.rejected:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        text = context.l10n.rejected;
        break;
      case ValidationStatus.suspended:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        text = 'Suspended';
        break;
      case ValidationStatus.unverified:
        backgroundColor = theme.colorScheme.primary.withValues(alpha: 0.1);
        textColor = theme.colorScheme.primary;
        text = context.l10n.unverified;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSettingTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: isDark
            ? []
            : [
               
              ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            children: [
              // Leading icon
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: theme.colorScheme.primary,
                  size: 20.r,
                ),
              ),
              SizedBox(width: 14.w),
              
              // Title
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: theme.textTheme.titleMedium?.color,
                      ),
                    ),
                    if (title == context.l10n.accountSettings)
                      Text(
                        context.l10n.manageAccountPreferences,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                        ),
                      ),
                    if (title == context.l10n.notifications)
                      Text(
                        context.l10n.controlNotificationSettings,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                        ),
                      ),
                    if (title == context.l10n.identityVerificationTitle)
                      Text(
                        context.l10n.identityVerificationSubtitle,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                        ),
                      ),
                    if (title == 'My Zones')
                      Text(
                        'Manage your validated zones',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                        ),
                      ),
                  ],
                ),
              ),
              
              // Trailing
              trailing != null
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          trailing is Text ? (trailing as Text).data! : '',
                          style: TextStyle(
                            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                            fontSize: 14.sp,
                          ),
                        ),
                        SizedBox(width: 6.w),
                        Icon(
                          Icons.chevron_right,
                          size: 18.r,
                          color: theme.colorScheme.onSurface.withOpacity(0.4),
                        ),
                      ],
                    )
                  : Icon(
                      Icons.chevron_right,
                      size: 18.r,
                      color: theme.colorScheme.onSurface.withOpacity(0.4),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}