import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_cubit.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_state.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/profile_photo_service.dart';

class ProfileCard extends StatelessWidget {
  
  const ProfileCard({required this.user, super.key});
  final User user;
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: EdgeInsets.all(16.r),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: isDark 
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  offset: const Offset(0, 2),
                  blurRadius: 6.r,
                ),
              ],
      ),
      child: Stack(
        children: [
          // Security icon pattern
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: CustomPaint(
                painter: SecurityPatternPainter(
                  opacity: 0.03,
                  isDark: isDark,
                  primaryColor: theme.colorScheme.primary,
                ),
                child: Container(),
              ),
            ),
          ),
          // Main content
          Row(
            children: [
              GestureDetector(
                onTap: () => _navigateToProfilePhoto(context),
                child: Hero(
                  tag: 'profile_avatar',
                  child: Container(
                    width: 90.r,
                    height: 90.r,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary.withValues(alpha: 0.8),
                          theme.colorScheme.secondary.withValues(alpha: 0.9),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Stack(
                      children: [
                        Container(
                          width: 90.r,
                          height: 90.r,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 3.r,
                            ),
                          ),
                          child: ClipOval(
                            child: user.photoURL != null
                                ? CachedNetworkImage(
                                    imageUrl: user.photoURL!,
                                    fit: BoxFit.cover,
                                    width: 90.r,
                                    height: 90.r,
                                    placeholder: (context, url) => Container(
                                      width: 90.r,
                                      height: 90.r,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            theme.colorScheme.primary.withValues(alpha: 0.8),
                                            theme.colorScheme.secondary.withValues(alpha: 0.9),
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                      ),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2.r,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) => Container(
                                      width: 90.r,
                                      height: 90.r,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            theme.colorScheme.primary.withValues(alpha: 0.8),
                                            theme.colorScheme.secondary.withValues(alpha: 0.9),
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                      ),
                                      child: Icon(
                                        FluentIcons.person_24_regular,
                                        size: 45.r,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : Container(
                                    width: 90.r,
                                    height: 90.r,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          theme.colorScheme.primary.withValues(alpha: 0.8),
                                          theme.colorScheme.secondary.withValues(alpha: 0.9),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Icon(
                                      FluentIcons.person_24_regular,
                                      size: 45.r,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                        ),
                        // Camera overlay
                        Positioned(
                          bottom: 2.r,
                          right: 2.r,
                          child: Container(
                            width: 28.r,
                            height: 28.r,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 2.5.r,
                              ),
                            ),
                            child: Icon(
                              Icons.camera_alt,
                              size: 14.r,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayName ?? 'User',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18.sp,
                        color: theme.textTheme.titleLarge?.color,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    if (user.email != null)
                      Text(
                        user.email!,
                        style: TextStyle(
                          color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                          fontSize: 14.sp,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    SizedBox(height: 8.h),
                    BlocBuilder<IdentityVerificationCubit, IdentityVerificationState>(
                      builder: (context, verificationState) {
                        return _buildValidationButton(context, verificationState, theme);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToProfilePhoto(BuildContext context) {
    _showImagePickerOptions(context);
  }

  void _navigateToIdentityValidation(BuildContext context) {
    NavigationService.navigateTo(context, 'identity-validation');
  }

  Widget _buildValidationButton(BuildContext context, IdentityVerificationState verificationState, ThemeData theme) {
    // Default button when status is unknown or loading
    if (verificationState is! IdentityVerificationLoaded) {
      return OutlinedButton(
        onPressed: () => _navigateToIdentityValidation(context),
        style: OutlinedButton.styleFrom(
          foregroundColor: theme.colorScheme.primary,
          side: BorderSide(color: theme.colorScheme.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0),
          minimumSize: Size(0, 30.h),
        ),
        child: Text(
          'Validar Perfil',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    final validationStatus = verificationState.status;

    switch (validationStatus) {
      case ValidationStatus.unverified:
        return OutlinedButton(
          onPressed: () => _navigateToIdentityValidation(context),
          style: OutlinedButton.styleFrom(
            foregroundColor: theme.colorScheme.primary,
            side: BorderSide(color: theme.colorScheme.primary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0),
            minimumSize: Size(0, 30.h),
          ),
          child: Text(
            'Validar Perfil',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        );

      case ValidationStatus.pendingId:
      case ValidationStatus.pendingReview:
      case ValidationStatus.pendingAutomaticVerification:
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            border: Border.all(color: Colors.orange),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 12.w,
                height: 12.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),
              ),
              SizedBox(width: 6.w),
              Text(
                'Pendiente',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
        );

      case ValidationStatus.validated:
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            border: Border.all(color: Colors.green),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.verified,
                size: 14.r,
                color: Colors.green,
              ),
              SizedBox(width: 6.w),
              Text(
                'Verificado',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        );

      case ValidationStatus.rejected:
        return OutlinedButton(
          onPressed: () => _navigateToIdentityValidation(context),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: BorderSide(color: Colors.red),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0),
            minimumSize: Size(0, 30.h),
          ),
          child: Text(
            'Reintentar',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        );

      case ValidationStatus.suspended:
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Text(
            'Suspendido',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
        );
    }
  }

  void _showImagePickerOptions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      
      builder: (context) => Container(
        margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 40.h),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
              offset: const Offset(0, -4),
              blurRadius: 20.r,
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: isDark ? 0.2 : 0.05),
              offset: const Offset(0, -8),
              blurRadius: 40.r,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: EdgeInsets.only(top: 8.h),
              width: 48.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),



            // Header
            Padding(
              padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 0),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(6.r),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Icon(
                      Icons.photo_camera,
                      color: theme.colorScheme.primary,
                      size: 18.r,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Actualizar Foto',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          'Elige una nueva foto de perfil',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      borderRadius: BorderRadius.circular(16.r),
                      child: Container(
                        padding: EdgeInsets.all(6.r),
                        child: Icon(
                          Icons.close_rounded,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          size: 18.r,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Options
            Padding(
              padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 32.w),
              child: Row(
                children: [
                  Expanded(
                    child: _buildImageOption(
                      context: context,
                      icon: Icons.camera_alt_rounded,
                      title: 'Cámara',
                      subtitle: 'Tomar foto',
                      onTap: () => _pickImage(context, ImageSource.camera),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _buildImageOption(
                      context: context,
                      icon: Icons.photo_library_rounded,
                      title: 'Galería',
                      subtitle: 'Elegir foto',
                      onTap: () => _pickImage(context, ImageSource.gallery),
                    ),
                  ),
                ],
              ),

            ),
            SizedBox(height: 30.h,)
          ],
        ),
      ),
    );
  }

  Widget _buildImageOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        splashColor: theme.colorScheme.primary.withValues(alpha: 0.1),
        highlightColor: theme.colorScheme.primary.withValues(alpha: 0.05),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
             
              BoxShadow(
                color: Colors.black.withValues(alpha: isDark ? 0.15 : 0.04),
                offset: const Offset(0, 2),
                blurRadius: 8.r,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.15),
                      theme.colorScheme.primary.withValues(alpha: 0.08),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  icon,
                  size: 24.r,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(height: 10.h),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
             
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    print('🎯 _pickImage called with source: $source');

    // Close the bottom sheet first
    Navigator.of(context).pop();
    print('📱 Bottom sheet closed');

    // Wait a frame to ensure the bottom sheet is fully closed
    await Future.delayed(const Duration(milliseconds: 100));

    print('📷 Starting image picker...');
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: source,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );

    print('📸 Image picker result: ${pickedFile?.path ?? 'null'}');
    print('🔍 Checking conditions: pickedFile != null: ${pickedFile != null}');
    print('🔍 Checking conditions: context.mounted: ${context.mounted}');

    if (pickedFile != null) {
      print('✅ Image picked successfully');
      print('🚀 Entering upload process...');

      // Wait a bit more for the context to be ready and check if widget is still active
      await Future.delayed(const Duration(milliseconds: 300));

      // Start upload process without any UI dependencies
      try {
        print('� Starting upload process without UI dependencies...');

        final imageFile = File(pickedFile.path);
        final profilePhotoService = ProfilePhotoService.instance;

        print('🔄 Starting profile photo upload for user: ${user.id}');
        print('📧 User email: ${user.email}');
        print('👤 User display name: ${user.displayName}');
        print('🖼️ Current photo URL: ${user.photoURL}');

        // Upload and update profile photo
        await profilePhotoService.uploadAndUpdateProfilePhoto(
          imageFile: imageFile,
          userId: user.id,
          onProgress: (progress) {
            print('📤 Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
          },
        );

        print('✅ Profile photo upload completed successfully');
        print('🎉 Upload finished! The profile photo should be updated now.');

        // Clear cached image for the old photo URL to ensure new image loads
        try {
          if (user.photoURL != null) {
            print('🗑️ Clearing cached image for old photo URL...');
            await DefaultCacheManager().removeFile(user.photoURL!);
          }
        } catch (e) {
          print('⚠️ Failed to clear image cache: $e');
        }

        // Refresh the AuthBloc to update the UI with the new profile photo
        // Use global AuthBloc access since context might not be mounted
        try {
          print('🔄 Refreshing AuthBloc to update UI...');
          final authBloc = getIt<AuthBloc>();
          authBloc.add(const AuthUserRefreshRequested());
          print('✅ AuthBloc refresh triggered successfully');
        } catch (e) {
          print('❌ Failed to refresh AuthBloc: $e');
        }
      } catch (e) {
        print('❌ Profile photo upload failed: $e');

        // Try to hide loading snackbar and show error, but don't fail if context is invalid
        try {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error al actualizar foto: ${e.toString()}'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.r),
              ),
            ),
          );
        } catch (contextError) {
          print('⚠️ Could not show error UI: $contextError');
        }
      }
    } else {
      print('❌ Upload cancelled - pickedFile: ${pickedFile != null}');
    }
  }


}

// Custom painter for security icons pattern
class SecurityPatternPainter extends CustomPainter {

  SecurityPatternPainter({
    this.opacity = 0.05,
    required this.isDark,
    required this.primaryColor,
  });
  final double opacity;
  final bool isDark;
  final Color primaryColor;

  @override
  void paint(Canvas canvas, Size size) {
    final iconSize = size.width / 10;
    final paint = Paint()
      ..color = (isDark ? Colors.white : primaryColor).withValues(alpha: opacity)
      ..style = PaintingStyle.fill;

    final patternIcons = [
      Icons.shield,
      Icons.security,
      Icons.lock,
      Icons.verified_user,
    ];

    for (int row = 0; row < size.height ~/ iconSize; row++) {
      for (int col = 0; col < size.width ~/ iconSize; col++) {
        // Staggered pattern - shift every other row
        final xOffset = col * iconSize * 2 + (row % 2 == 0 ? 0 : iconSize);
        final yOffset = row * iconSize * 2;
        
        if (xOffset < size.width && yOffset < size.height) {
          // Draw a security icon
          final icon = patternIcons[(row + col) % patternIcons.length];
          TextPainter(
            text: TextSpan(
              text: String.fromCharCode(icon.codePoint),
              style: TextStyle(
                fontSize: iconSize,
                fontFamily: icon.fontFamily,
                color: paint.color,
              ),
            ),
            textDirection: TextDirection.ltr,
          )
          ..layout()
          ..paint(canvas, Offset(xOffset, yOffset));
        }
      }
    }
  }

  @override
  bool shouldRepaint(SecurityPatternPainter oldDelegate) {
    return opacity != oldDelegate.opacity || 
           isDark != oldDelegate.isDark ||
           primaryColor != oldDelegate.primaryColor;
  }
} 