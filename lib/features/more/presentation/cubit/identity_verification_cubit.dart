import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/usecases/get_identity_verification_status_use_case.dart';
import 'package:respublicaseguridad/features/more/presentation/cubit/identity_verification_state.dart';

/// Cubit for managing identity verification status in the More screen with persistence
class IdentityVerificationCubit extends HydratedCubit<IdentityVerificationState> {
  final GetIdentityVerificationStatusUseCase _getIdentityVerificationStatusUseCase;

  IdentityVerificationCubit({
    required GetIdentityVerificationStatusUseCase getIdentityVerificationStatusUseCase,
  }) : _getIdentityVerificationStatusUseCase = getIdentityVerificationStatusUseCase,
       super(const IdentityVerificationInitial());

  /// Load identity verification status for the given user ID
  /// Only loads if current state is Initial (to avoid unnecessary API calls)
  Future<void> loadIdentityVerificationStatus(String userId) async {
    // Only load if we don't have cached data
    if (state is! IdentityVerificationLoaded) {
      await _fetchIdentityVerificationStatus(userId);
    }
  }

  /// Force refresh the identity verification status (ignores cache)
  Future<void> refreshStatus(String userId) async {
    await _fetchIdentityVerificationStatus(userId);
  }

  /// Internal method to fetch verification status from API
  Future<void> _fetchIdentityVerificationStatus(String userId) async {
    if (userId.isEmpty) {
      emit(const IdentityVerificationError(
        message: 'User ID is required to check verification status',
      ));
      return;
    }

    emit(const IdentityVerificationLoading());

    try {
      final result = await _getIdentityVerificationStatusUseCase(userId);

      result.fold(
        (failure) {
          emit(IdentityVerificationError(
            message: failure.message,
          ));
        },
        (userEntity) {
          emit(IdentityVerificationLoaded(
            userEntity: userEntity,
            status: userEntity.validationStatus,
          ));
        },
      );
    } catch (e) {
      emit(IdentityVerificationError(
        message: 'An unexpected error occurred while checking verification status: ${e.toString()}',
      ));
    }
  }

  /// Reset the cubit to initial state and clear cache
  void reset() {
    emit(const IdentityVerificationInitial());
  }

  @override
  IdentityVerificationState? fromJson(Map<String, dynamic> json) {
    try {
      return IdentityVerificationState.fromJson(json);
    } catch (e) {
      // If deserialization fails, return initial state
      return const IdentityVerificationInitial();
    }
  }

  @override
  Map<String, dynamic>? toJson(IdentityVerificationState state) {
    try {
      // Only persist loaded states to avoid caching errors or loading states
      if (state is IdentityVerificationLoaded) {
        return state.toJson();
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
