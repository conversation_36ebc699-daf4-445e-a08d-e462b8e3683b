import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

/// Base state for identity verification status
abstract class IdentityVerificationState extends Equatable {
  const IdentityVerificationState();

  @override
  List<Object?> get props => [];

  /// Convert state to JSON for persistence
  Map<String, dynamic> toJson();

  /// Create state from JSON for persistence
  static IdentityVerificationState fromJson(Map<String, dynamic> json) {
    final type = json['type'] as String;
    
    switch (type) {
      case 'initial':
        return const IdentityVerificationInitial();
      case 'loading':
        return const IdentityVerificationLoading();
      case 'loaded':
        return IdentityVerificationLoaded.fromJson(json['data']);
      case 'error':
        return IdentityVerificationError.fromJson(json['data']);
      default:
        return const IdentityVerificationInitial();
    }
  }
}

/// Initial state when the cubit is first created
class IdentityVerificationInitial extends IdentityVerificationState {
  const IdentityVerificationInitial();

  @override
  Map<String, dynamic> toJson() => {'type': 'initial'};
}

/// Loading state when fetching identity verification status
class IdentityVerificationLoading extends IdentityVerificationState {
  const IdentityVerificationLoading();

  @override
  Map<String, dynamic> toJson() => {'type': 'loading'};
}

/// Success state when identity verification status is loaded
class IdentityVerificationLoaded extends IdentityVerificationState {
  final UserEntity userEntity;
  final ValidationStatus status;

  const IdentityVerificationLoaded({
    required this.userEntity,
    required this.status,
  });

  @override
  List<Object?> get props => [userEntity, status];

  @override
  Map<String, dynamic> toJson() => {
    'type': 'loaded',
    'data': {
      'userEntity': userEntity.toJson(),
      'status': status.name,
    }
  };

  factory IdentityVerificationLoaded.fromJson(Map<String, dynamic> json) {
    return IdentityVerificationLoaded(
      userEntity: UserEntity.fromJson(json['userEntity']),
      status: ValidationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ValidationStatus.unverified,
      ),
    );
  }

  /// Helper getters for UI logic
  bool get isVerified => status == ValidationStatus.validated;
  bool get isPending => status == ValidationStatus.pendingId || status == ValidationStatus.pendingReview;
  bool get isRejected => status == ValidationStatus.rejected;
  bool get isUnverified => status == ValidationStatus.unverified;

  /// Get detailed status message for dialogs
  String getDetailedStatusMessage() {
    switch (status) {
      case ValidationStatus.validated:
        return 'Your identity has been successfully verified. You now have access to all security features.';
      case ValidationStatus.pendingId:
        return 'Your identity verification is in process. Documents are being processed and you will be notified once complete.';
      case ValidationStatus.pendingReview:
        return 'Your documents are under review by our verification team. You will be notified once the review is complete.';
      case ValidationStatus.pendingAutomaticVerification:
        return 'Your documents are being automatically verified. This process usually takes a few minutes.';
      case ValidationStatus.rejected:
        return 'Your previous verification was rejected. Please review the feedback and submit new documents if needed.';
      case ValidationStatus.suspended:
        return 'Your account has been suspended. Please contact support for assistance.';
      case ValidationStatus.unverified:
        return 'Complete identity verification to access all security features and enhance your account protection.';
    }
  }

  /// Get dialog title based on status
  String getDialogTitle() {
    switch (status) {
      case ValidationStatus.validated:
        return 'Identity Verified';
      case ValidationStatus.pendingId:
      case ValidationStatus.pendingReview:
      case ValidationStatus.pendingAutomaticVerification:
        return 'Verification in Progress';
      case ValidationStatus.rejected:
        return 'Verification Rejected';
      case ValidationStatus.suspended:
        return 'Account Suspended';
      case ValidationStatus.unverified:
        return 'Identity Verification Required';
    }
  }

  /// Check if user can start/restart verification process
  bool get canStartVerification => status == ValidationStatus.unverified || status == ValidationStatus.rejected;
}

/// Error state when there's an issue fetching identity verification status
class IdentityVerificationError extends IdentityVerificationState {
  final String message;

  const IdentityVerificationError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];

  @override
  Map<String, dynamic> toJson() => {
    'type': 'error',
    'data': {'message': message}
  };

  factory IdentityVerificationError.fromJson(Map<String, dynamic> json) {
    return IdentityVerificationError(message: json['message']);
  }
}
