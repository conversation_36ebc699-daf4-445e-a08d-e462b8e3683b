import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';

/// Custom incident marker widget that can be converted to BitmapDescriptor
class CustomIncidentMarker extends StatelessWidget {
  final IncidentMarkerEntity incident;
  final bool isSelected;
  final double size;

  const CustomIncidentMarker({
    Key? key,
    required this.incident,
    this.isSelected = false,
    this.size = 60.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Pulse effect for high severity incidents
          if (incident.shouldPulse)
            _PulseEffect(
              size: size,
              color: _getMarkerColor(),
              opacity: incident.markerOpacity,
              severity: incident.severity,
            ),
          
          // Main marker container
          Container(
            width: size * 0.7,
            height: size * 0.7,
            decoration: BoxDecoration(
              color: _getMarkerColor().withOpacity(incident.markerOpacity),
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.white : Colors.black26,
                width: isSelected ? 3.0 : 1.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Category icon or image
                  _buildCategoryImage(),
                  
                  // Anonymous indicator
                  if (incident.isAnonymous)
                    Positioned(
                      top: 2,
                      right: 2,
                      child: Container(
                        width: size * 0.15,
                        height: size * 0.15,
                        decoration: const BoxDecoration(
                          color: Colors.grey,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.visibility_off,
                          color: Colors.white,
                          size: size * 0.1,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          
          // Selection indicator
          if (isSelected)
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.blue,
                  width: 2.0,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build category image or fallback to icon
  Widget _buildCategoryImage() {
    // Always try to use imageUrl first if available
    if (incident.imageUrl != null && incident.imageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(size * 0.35),
        child: CachedNetworkImage(
          imageUrl: incident.imageUrl!,
          width: size * 0.4,
          height: size * 0.4,
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildShimmerPlaceholder(),
          errorWidget: (context, url, error) {
            // Fallback to icon if image fails to load
            return Icon(
              _getCategoryIcon(),
              color: Colors.white,
              size: size * 0.35,
            );
          },
        ),
      );
    } else {
      // Fallback to icon when no image URL is available
      return Icon(
        _getCategoryIcon(),
        color: Colors.white,
        size: size * 0.35,
      );
    }
  }

  /// Build shimmer placeholder for loading state
  Widget _buildShimmerPlaceholder() {
    return Container(
      width: size * 0.4,
      height: size * 0.4,
      decoration: BoxDecoration(
        color: Colors.white24,
        borderRadius: BorderRadius.circular(size * 0.35),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.2,
          height: size * 0.2,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Color _getMarkerColor() {
    try {
      return Color(int.parse(incident.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      // Default color based on severity
      return incident.severity == 'Grave' ? Colors.red : Colors.orange;
    }
  }

  IconData _getCategoryIcon() {
    switch (incident.categoryKey.toLowerCase()) {
      case 'robo':
        return Icons.security;
      case 'violencia':
        return Icons.warning;
      case 'sospecha':
        return Icons.visibility;
      case 'desamparo':
        return Icons.help;
      case 'infraestructura':
        return Icons.construction;
      case 'transito':
      case 'tránsito':
        return Icons.traffic;
      case 'drogas':
        return Icons.dangerous;
      case 'animales':
        return Icons.pets;
      case 'convivencia':
        return Icons.people;
      default:
        return Icons.report_problem;
    }
  }
}

/// Heartbeat effect widget for incidents with severity-based animation
class _PulseEffect extends StatefulWidget {
  final double size;
  final Color color;
  final double opacity;
  final String severity;

  const _PulseEffect({
    required this.size,
    required this.color,
    required this.opacity,
    required this.severity,
  });

  @override
  State<_PulseEffect> createState() => _PulseEffectState();
}

class _PulseEffectState extends State<_PulseEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // Set heartbeat speed based on severity
    final duration = _getHeartbeatDuration();

    _controller = AnimationController(
      duration: duration,
      vsync: this,
    );

    // Create heartbeat pattern: lub-dub (double beat)
    _scaleAnimation = TweenSequence<double>([
      // First beat (lub) - quick expansion
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.3)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 15,
      ),
      // First contraction
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.3, end: 0.9)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 10,
      ),
      // Brief pause
      TweenSequenceItem(
        tween: ConstantTween<double>(0.9),
        weight: 5,
      ),
      // Second beat (dub) - slightly smaller expansion
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.9, end: 1.15)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 12,
      ),
      // Second contraction
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.15, end: 0.8)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 8,
      ),
      // Longer pause before next heartbeat
      TweenSequenceItem(
        tween: ConstantTween<double>(0.8),
        weight: 50,
      ),
    ]).animate(_controller);

    // Opacity animation synchronized with scale for intensity effect
    _opacityAnimation = TweenSequence<double>([
      // First beat opacity
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.2, end: 0.8)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 15,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 0.3)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 10,
      ),
      TweenSequenceItem(
        tween: ConstantTween<double>(0.3),
        weight: 5,
      ),
      // Second beat opacity
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.3, end: 0.6)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 12,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.6, end: 0.2)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 8,
      ),
      TweenSequenceItem(
        tween: ConstantTween<double>(0.2),
        weight: 50,
      ),
    ]).animate(_controller);

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Get heartbeat duration based on severity
  Duration _getHeartbeatDuration() {
    switch (widget.severity.toLowerCase()) {
      case 'grave':
        return const Duration(milliseconds: 900); // Fast heartbeat for high severity
      case 'leve':
      default:
        return const Duration(milliseconds: 1800); // Slower heartbeat for low severity
    }
  }

  /// Get color intensity multiplier based on severity
  double _getColorIntensity() {
    switch (widget.severity.toLowerCase()) {
      case 'grave':
        return 1.2; // More intense colors for high severity
      case 'leve':
      default:
        return 0.8; // Softer colors for low severity
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final colorIntensity = _getColorIntensity();
        final baseOpacity = widget.opacity * 0.4; // Base opacity for the effect

        return Container(
          width: widget.size * _scaleAnimation.value,
          height: widget.size * _scaleAnimation.value,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.color.withOpacity(
              baseOpacity * _opacityAnimation.value * colorIntensity,
            ),
            // Add subtle gradient for more realistic heartbeat effect
            gradient: RadialGradient(
              colors: [
                widget.color.withOpacity(
                  baseOpacity * _opacityAnimation.value * colorIntensity * 0.6,
                ),
                widget.color.withOpacity(
                  baseOpacity * _opacityAnimation.value * colorIntensity,
                ),
                widget.color.withOpacity(
                  baseOpacity * _opacityAnimation.value * colorIntensity * 0.3,
                ),
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
          ),
        );
      },
    );
  }
}

/// Utility class to convert custom markers to BitmapDescriptor
class IncidentMarkerGenerator {
  static Future<BitmapDescriptor> createMarker(
    IncidentMarkerEntity incident, {
    bool isSelected = false,
    double size = 60.0,
  }) async {
    final widget = CustomIncidentMarker(
      incident: incident,
      isSelected: isSelected,
      size: size,
    );

    return await _createBitmapDescriptorFromWidget(widget, size);
  }

  static Future<BitmapDescriptor> _createBitmapDescriptorFromWidget(
    Widget widget,
    double size,
  ) async {
    final repaintBoundary = RenderRepaintBoundary();
    final renderView = RenderView(
      view: WidgetsBinding.instance.platformDispatcher.views.first,
      child: RenderPositionedBox(
        alignment: Alignment.center,
        child: repaintBoundary,
      ),
      configuration: ViewConfiguration.fromView(
        WidgetsBinding.instance.platformDispatcher.views.first,
      ),
    );

    final pipelineOwner = PipelineOwner();
    final buildOwner = BuildOwner(focusManager: FocusManager());

    pipelineOwner.rootNode = renderView;
    renderView.prepareInitialFrame();

    final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
      container: repaintBoundary,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: widget,
      ),
    ).attachToRenderTree(buildOwner);

    buildOwner.buildScope(rootElement);
    buildOwner.finalizeTree();

    pipelineOwner.flushLayout();
    pipelineOwner.flushCompositingBits();
    pipelineOwner.flushPaint();

    final image = await repaintBoundary.toImage(
      pixelRatio: 3.0, // High DPI for crisp markers
    );
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.fromBytes(uint8List);
  }

  /// Create a cluster marker for multiple incidents
  static Future<BitmapDescriptor> createClusterMarker(
    int count, {
    Color color = Colors.blue,
    double size = 80.0,
  }) async {
    final widget = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 3),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          count.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: size * 0.3,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );

    return await _createBitmapDescriptorFromWidget(widget, size);
  }
}
