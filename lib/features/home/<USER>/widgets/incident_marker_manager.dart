import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:respublicaseguridad/features/home/<USER>/widgets/custom_incident_marker.dart';

/// Manages the conversion of incident markers to Google Maps markers
class IncidentMarkerManager {
  final Map<String, BitmapDescriptor> _markerCache = {};
  final Map<String, Marker> _currentMarkers = {};
  
  /// Convert incident markers to Google Maps markers
  Future<Set<Marker>> createMarkersFromIncidents(
    List<IncidentMarkerEntity> incidents, {
    String? selectedIncidentId,
    Function(String)? onMarkerTap,
    Function(List<IncidentMarkerEntity>)? onClusterTap,
    bool enableClustering = true,
    double zoomLevel = 13.0,
  }) async {
    // Clear the cache if it gets too large
    if (_markerCache.length > 100) {
      _markerCache.clear();
    }
    
    if (enableClustering && incidents.length > 10) {
      return await _createClusteredMarkers(
        incidents,
        selectedIncidentId: selectedIncidentId,
        onMarkerTap: onMarkerTap,
        onClusterTap: onClusterTap,
        zoomLevel: zoomLevel,
      );
    } else {
      return await _createIndividualMarkers(
        incidents,
        selectedIncidentId: selectedIncidentId,
        onMarkerTap: onMarkerTap,
      );
    }
  }

  /// Create clustered markers for dense areas
  Future<Set<Marker>> _createClusteredMarkers(
    List<IncidentMarkerEntity> incidents, {
    String? selectedIncidentId,
    Function(String)? onMarkerTap,
    Function(List<IncidentMarkerEntity>)? onClusterTap,
    double zoomLevel = 13.0,
  }) async {
    final markers = <Marker>{};
    final clusters = _createClusters(incidents, zoomLevel);

    for (final cluster in clusters) {
      if (cluster.incidents.length == 1) {
        // Single incident - create individual marker
        final incident = cluster.incidents.first;
        final isSelected = incident.incidentId == selectedIncidentId;
        final cacheKey = _generateCacheKey(incident, isSelected);

        BitmapDescriptor? icon = _markerCache[cacheKey];
        if (icon == null) {
          icon = await IncidentMarkerGenerator.createMarker(
            incident,
            isSelected: isSelected,
          );
          _markerCache[cacheKey] = icon;
        }

        final marker = Marker(
          markerId: MarkerId(incident.incidentId),
          position: LatLng(
            incident.location.latitude,
            incident.location.longitude,
          ),
          icon: icon,
          alpha: incident.markerOpacity,
          onTap: onMarkerTap != null
              ? () => onMarkerTap(incident.incidentId)
              : null,
          infoWindow: InfoWindow(
            title: incident.categoryTitle,
            snippet: _createMarkerSnippet(incident),
          ),
          zIndex: _calculateZIndex(incident, isSelected),
        );

        markers.add(marker);
      } else {
        // Multiple incidents - create cluster marker
        final clusterIcon = await IncidentMarkerGenerator.createClusterMarker(
          cluster.incidents.length,
          color: cluster.clusterColor,
        );

        final marker = Marker(
          markerId: MarkerId('cluster_${cluster.center.latitude}_${cluster.center.longitude}'),
          position: cluster.center,
          icon: clusterIcon,
          onTap: onClusterTap != null
              ? () => onClusterTap(cluster.incidents)
              : null,
          infoWindow: InfoWindow(
            title: '${cluster.incidents.length} incidentes',
            snippet: 'Toca para ver detalles',
          ),
          zIndex: 500, // Higher than individual markers
        );

        markers.add(marker);
      }
    }

    return markers;
  }

  /// Create individual markers for each incident
  Future<Set<Marker>> _createIndividualMarkers(
    List<IncidentMarkerEntity> incidents, {
    String? selectedIncidentId,
    Function(String)? onMarkerTap,
  }) async {
    final markers = <Marker>{};
    
    for (final incident in incidents) {
      try {
        final isSelected = incident.incidentId == selectedIncidentId;
        final cacheKey = _generateCacheKey(incident, isSelected);
        
        // Check cache first
        BitmapDescriptor? icon = _markerCache[cacheKey];
        
        if (icon == null) {
          // Create new marker icon
          icon = await IncidentMarkerGenerator.createMarker(
            incident,
            isSelected: isSelected,
            size: incident.shouldPulse ? 70.0 : 60.0, // Slightly larger for pulsing markers
          );
          _markerCache[cacheKey] = icon;
        }
        
        final marker = Marker(
          markerId: MarkerId(incident.incidentId),
          position: LatLng(
            incident.location.latitude,
            incident.location.longitude,
          ),
          icon: icon,
          alpha: incident.markerOpacity,
          onTap: onMarkerTap != null 
              ? () => onMarkerTap(incident.incidentId)
              : null,
          infoWindow: InfoWindow(
            title: incident.categoryTitle,
            snippet: _createMarkerSnippet(incident),
          ),
          zIndex: _calculateZIndex(incident, isSelected),
          // Add flat: true for better performance
          flat: !incident.shouldPulse, // Non-flat for pulse effect to be visible
        );
        
        markers.add(marker);
        _currentMarkers[incident.incidentId] = marker;
      } catch (e) {
        debugPrint('❌ Error creating marker for incident ${incident.incidentId}: $e');
      }
    }
    
    return markers;
  }

  /// Generate cache key for marker icons
  String _generateCacheKey(IncidentMarkerEntity incident, bool isSelected) {
    final imageUrlHash = incident.imageUrl?.hashCode ?? 0;
    final shouldPulse = incident.shouldPulse ? 1 : 0;
    return '${incident.categoryKey}_${incident.severity}_${incident.isAnonymous}_${isSelected}_${(incident.markerOpacity * 100).round()}_${shouldPulse}_$imageUrlHash';
  }

  /// Create snippet text for marker info window
  String _createMarkerSnippet(IncidentMarkerEntity incident) {
    final parts = <String>[];
    
    if (incident.subcategoryTitle != null) {
      parts.add(incident.subcategoryTitle!);
    }
    
    parts.add('Severidad: ${incident.severity}');
    
    if (incident.isAnonymous) {
      parts.add('Anónimo');
    }
    
    final timeAgo = _getTimeAgo(incident.postedAt);
    parts.add(timeAgo);
    
    return parts.join(' • ');
  }

  /// Get human-readable time ago string
  String _getTimeAgo(DateTime postedAt) {
    final now = DateTime.now();
    final difference = now.difference(postedAt);
    
    if (difference.inDays > 0) {
      return 'Hace ${difference.inDays} día${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Hace ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'Hace ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'Hace un momento';
    }
  }

  /// Calculate z-index for marker layering
  double _calculateZIndex(IncidentMarkerEntity incident, bool isSelected) {
    double zIndex = 0;
    
    // Base z-index based on severity
    if (incident.severity == 'Grave') {
      zIndex += 100;
    } else {
      zIndex += 50;
    }
    
    // Higher z-index for high severity incidents
    if (incident.isHighSeverity) {
      zIndex += 50;
    }
    
    // Higher z-index for incidents with pulse effect
    if (incident.shouldPulse) {
      zIndex += 25;
    }
    
    // Highest z-index for selected markers
    if (isSelected) {
      zIndex += 1000;
    }
    
    // Lower z-index for older incidents
    final hoursOld = DateTime.now().difference(incident.postedAt).inHours;
    zIndex -= hoursOld * 0.1;
    
    return zIndex;
  }

  /// Update marker selection
  Future<Set<Marker>> updateMarkerSelection(
    List<IncidentMarkerEntity> incidents,
    String? selectedIncidentId, {
    Function(String)? onMarkerTap,
  }) async {
    // Clear cache for selected markers to force refresh
    if (selectedIncidentId != null) {
      final keysToRemove = _markerCache.keys
          .where((key) => key.contains('_true_'))
          .toList();
      
      for (final key in keysToRemove) {
        _markerCache.remove(key);
      }
    }
    
    return await createMarkersFromIncidents(
      incidents,
      selectedIncidentId: selectedIncidentId,
      onMarkerTap: onMarkerTap,
    );
  }

  /// Create clusters from incidents based on zoom level
  List<MarkerCluster> _createClusters(List<IncidentMarkerEntity> incidents, double zoomLevel) {
    final clusters = <MarkerCluster>[];
    final processed = <bool>[];

    // Initialize processed array
    for (int i = 0; i < incidents.length; i++) {
      processed.add(false);
    }

    for (int i = 0; i < incidents.length; i++) {
      if (processed[i]) continue;

      final incident = incidents[i];
      final clusterIncidents = <IncidentMarkerEntity>[incident];
      processed[i] = true;

      // Find nearby incidents to cluster
      for (int j = i + 1; j < incidents.length; j++) {
        if (processed[j]) continue;

        final otherIncident = incidents[j];
        final distance = Geolocator.distanceBetween(
          incident.location.latitude,
          incident.location.longitude,
          otherIncident.location.latitude,
          otherIncident.location.longitude,
        );

        // Cluster radius based on zoom level (smaller radius for higher zoom)
        final clusterRadius = 100.0 * (20 - zoomLevel) / 10;

        if (distance <= clusterRadius) {
          clusterIncidents.add(otherIncident);
          processed[j] = true;
        }
      }

      // Create cluster
      final cluster = MarkerCluster(
        center: LatLng(
          incident.location.latitude,
          incident.location.longitude,
        ),
        incidents: clusterIncidents,
      );

      clusters.add(cluster);
    }

    return clusters;
  }

  /// Clear marker cache
  void clearCache() {
    _markerCache.clear();
    _currentMarkers.clear();
  }

  /// Get current marker count
  int get markerCount => _currentMarkers.length;

  /// Get cached icon count
  int get cachedIconCount => _markerCache.length;

  /// Dispose resources
  void dispose() {
    clearCache();
  }
}

/// Clustering utility for grouping nearby markers
class MarkerCluster {
  final LatLng center;
  final List<IncidentMarkerEntity> incidents;
  final double radius;

  MarkerCluster({
    required this.center,
    required this.incidents,
    this.radius = 100.0, // meters
  });

  /// Check if an incident should be added to this cluster
  bool shouldInclude(IncidentMarkerEntity incident, double zoomLevel) {
    final distance = Geolocator.distanceBetween(
      center.latitude,
      center.longitude,
      incident.location.latitude,
      incident.location.longitude,
    );

    // Adjust clustering radius based on zoom level
    final adjustedRadius = radius * (20 - zoomLevel) / 10;

    return distance <= adjustedRadius;
  }

  /// Get the highest severity in this cluster
  String get highestSeverity {
    return incidents.any((i) => i.severity == 'Grave') ? 'Grave' : 'Leve';
  }

  /// Get cluster color based on highest severity
  Color get clusterColor {
    return highestSeverity == 'Grave' ? Colors.red : Colors.orange;
  }
}
