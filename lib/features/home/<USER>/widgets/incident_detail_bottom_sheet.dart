import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:respublicaseguridad/core/theme/app_colors.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_by_id_usecase.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/core/di/injection.dart';

/// Bottom sheet that shows incident details when a marker is tapped
class IncidentDetailBottomSheet extends StatelessWidget {
  final IncidentMarkerEntity incident;
  final VoidCallback? onViewFullDetails;
  final VoidCallback? onClose;

  const IncidentDetailBottomSheet({
    Key? key,
    required this.incident,
    this.onViewFullDetails,
    this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height:
          MediaQuery.of(context).size.height *
          0.8, // Increased height to 60% of screen
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with category and close button
                  Row(
                    children: [
                      // Category icon
                      Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: _getCategoryColor().withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Icon(
                          _getCategoryIcon(),
                          color: _getCategoryColor(),
                          size: 24.sp,
                        ),
                      ),

                      SizedBox(width: 12.w),

                      // Category and subcategory
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              incident.categoryTitle,
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            if (incident.subcategoryTitle != null)
                              Text(
                                incident.subcategoryTitle!,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Close button
                      IconButton(
                        onPressed: onClose,
                        icon: Icon(
                          Icons.close,
                          color: Colors.grey[600],
                          size: 24.sp,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 16.h),

                  // Severity badge
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getSeverityColor().withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                          border: Border.all(
                            color: _getSeverityColor().withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Severidad: ${incident.severity}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: _getSeverityColor(),
                          ),
                        ),
                      ),

                      SizedBox(width: 8.w),

                      // Anonymous indicator
                      if (incident.isAnonymous)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 6.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12.r),
                            border: Border.all(
                              color: Colors.grey.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.visibility_off,
                                size: 12.sp,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                'Anónimo',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),

                  SizedBox(height: 16.h),

                  // Location
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Colors.grey[600],
                        size: 16.sp,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          incident.location.address.isNotEmpty
                              ? incident.location.address
                              : '${incident.location.latitude.toStringAsFixed(4)}, ${incident.location.longitude.toStringAsFixed(4)}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(width: 12.h),

                  // Time posted
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: Colors.grey[600],
                        size: 16.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        _getTimeAgo(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 16.h),

                  // Description section (if available)
                  if (incident.imageUrl != null &&
                      incident.imageUrl!.isNotEmpty) ...[
                    Text(
                      'Vista previa',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      height: 120.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: Colors.grey[100],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: Image.network(
                          incident.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey[400],
                                size: 40.sp,
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                  ],

                  SizedBox(height: 20.h),

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _navigateToIncidentDetails(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'Ver más detalles',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to the full incident details screen
  void _navigateToIncidentDetails(BuildContext context) {
    print(
      '🔍 Starting navigation to incident details for ID: ${incident.incidentId}',
    );

    try {
      // Get the current user ID from auth bloc
      final authState = context.read<AuthBloc>().state;
      print(
        '🔍 Auth state: ${authState.isAuthenticated ? 'authenticated' : 'not authenticated'}',
      );

      if (!authState.isAuthenticated) {
        print('❌ User not authenticated');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Debes iniciar sesión para ver los detalles completos',
            ),
          ),
        );
        return;
      }

      print('✅ User authenticated with ID: ${authState.user.id}');

      // Store references before closing bottom sheet
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      final goRouter = GoRouter.of(context);
      final userId = authState.user.id;

      // Close the bottom sheet first
      Navigator.of(context).pop();
      print('✅ Bottom sheet closed');

      // Start the async operation without blocking the UI
      _fetchAndNavigate(scaffoldMessenger, goRouter, userId);
    } catch (e) {
      print('❌ General exception: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error inesperado: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Fetch incident details and navigate
  Future<void> _fetchAndNavigate(
    ScaffoldMessengerState scaffoldMessenger,
    GoRouter goRouter,
    String userId,
  ) async {
    try {
      // Get the full incident details using the use case with timeout
      final getIncidentUseCase = getIt<GetIncidentByIdUseCase>();
      print('🔍 Fetching incident details...');

      final result = await getIncidentUseCase(
        GetIncidentByIdParams(incidentId: incident.incidentId, userId: userId),
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          print('❌ Timeout while fetching incident details');
          throw Exception('Timeout al cargar los detalles del incidente');
        },
      );

      print('✅ Incident details fetched successfully');

      result.fold(
        (failure) {
          print('❌ Error from use case: $failure');
          // Handle error using stored scaffold messenger
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'Error al cargar los detalles: ${failure.toString()}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        },
        (fullIncident) {
          print('✅ Navigating to incident details screen');
          // Navigate to incident details screen using stored router
          goRouter.push('/incidents/details', extra: fullIncident);
        },
      );
    } catch (e) {
      print('❌ Exception during fetch: $e');
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error al cargar los detalles: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Color _getCategoryColor() {
    try {
      return Color(int.parse(incident.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppColors.primary;
    }
  }

  Color _getSeverityColor() {
    return incident.severity == 'Grave' ? Colors.red : Colors.orange;
  }

  IconData _getCategoryIcon() {
    switch (incident.categoryKey.toLowerCase()) {
      case 'robo':
        return Icons.security;
      case 'violencia':
        return Icons.warning;
      case 'sospecha':
        return Icons.visibility;
      case 'desamparo':
        return Icons.help;
      case 'infraestructura':
        return Icons.construction;
      case 'transito':
      case 'tránsito':
        return Icons.traffic;
      case 'drogas':
        return Icons.dangerous;
      case 'animales':
        return Icons.pets;
      case 'convivencia':
        return Icons.people;
      default:
        return Icons.report_problem;
    }
  }

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(incident.postedAt);

    if (difference.inDays > 0) {
      return 'Hace ${difference.inDays} día${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Hace ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'Hace ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'Hace un momento';
    }
  }

  /// Show the incident detail bottom sheet
  static void show(BuildContext context, IncidentMarkerEntity incident) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => IncidentDetailBottomSheet(
            incident: incident,
            onClose: () => Navigator.of(context).pop(),
          ),
    );
  }
}
