import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

class HomeStateEntity extends Equatable {
  final bool isLoading;
  final bool hasShownIdentityDialog;
  final UserEntity? userValidationStatus;
  final String? errorMessage;
  final Set<Marker> markers;
  final LatLng currentLocation;
  final List<String> searchSuggestions;
  final bool isSearchActive;

  const HomeStateEntity({
    this.isLoading = false,
    this.hasShownIdentityDialog = false,
    this.userValidationStatus,
    this.errorMessage,
    this.markers = const {},
    this.currentLocation = const LatLng(40.7128, -74.0060), // Default NYC
    this.searchSuggestions = const [],
    this.isSearchActive = false,
  });

  HomeStateEntity copyWith({
    bool? isLoading,
    bool? hasShownIdentityDialog,
    UserEntity? userValidationStatus,
    String? errorMessage,
    bool? clearError,
    Set<Marker>? markers,
    LatLng? currentLocation,
    List<String>? searchSuggestions,
    bool? isSearchActive,
  }) {
    return HomeStateEntity(
      isLoading: isLoading ?? this.isLoading,
      hasShownIdentityDialog: hasShownIdentityDialog ?? this.hasShownIdentityDialog,
      userValidationStatus: userValidationStatus ?? this.userValidationStatus,
      errorMessage: clearError == true ? null : (errorMessage ?? this.errorMessage),
      markers: markers ?? this.markers,
      currentLocation: currentLocation ?? this.currentLocation,
      searchSuggestions: searchSuggestions ?? this.searchSuggestions,
      isSearchActive: isSearchActive ?? this.isSearchActive,
    );
  }

  bool get shouldShowIdentityDialog {
    return !hasShownIdentityDialog &&
           (userValidationStatus == null ||
            userValidationStatus!.validationStatus == ValidationStatus.unverified ||
            userValidationStatus!.validationStatus == ValidationStatus.rejected);
  }

  /// Check if user should be prompted for identity verification
  /// Excludes users with pending documents (pendingId, pendingReview) and validated users
  bool get shouldPromptForVerification {
    if (hasShownIdentityDialog) return false;

    if (userValidationStatus == null) return true; // Unknown status, show dialog

    switch (userValidationStatus!.validationStatus) {
      case ValidationStatus.unverified:
      case ValidationStatus.rejected:
      case ValidationStatus.suspended:
        return true; // Show dialog for unverified, rejected, and suspended users
      case ValidationStatus.pendingId:
      case ValidationStatus.pendingReview:
      case ValidationStatus.pendingAutomaticVerification:
      case ValidationStatus.validated:
        return false; // Don't show dialog for pending or validated users
    }
  }

  @override
  List<Object?> get props => [
    isLoading,
    hasShownIdentityDialog,
    userValidationStatus,
    errorMessage,
    markers,
    currentLocation,
    searchSuggestions,
    isSearchActive,
  ];
}
