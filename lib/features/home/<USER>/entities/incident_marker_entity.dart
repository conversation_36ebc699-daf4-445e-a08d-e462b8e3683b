import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_enums.dart';

/// Entity representing an incident marker on the map
/// This is optimized for real-time map display with minimal data transfer
class IncidentMarkerEntity extends Equatable {
  final String incidentId;
  final String categoryKey;
  final String categoryTitle;
  final String? subcategoryKey;
  final String? subcategoryTitle;
  final String severity; // 'Leve' or 'Grave'
  final LocationEntity location;
  final DateTime postedAt;
  final bool isAnonymous;
  final String? zoneId;
  final double currentVisibilityPercentage; // 0-100% based on time decay
  final bool isHighSeverity; // Calculated field for pulse effect
  final String iconName; // Category icon name
  final String color; // Category color
  final String? userId;
  final String? imageUrl;
  final IncidentVisibilityStatus visibilityStatus;

  const IncidentMarkerEntity({
    required this.incidentId,
    required this.categoryKey,
    required this.categoryTitle,
    this.subcategoryKey,
    this.subcategoryTitle,
    required this.severity,
    required this.location,
    required this.postedAt,
    required this.isAnonymous,
    this.zoneId,
    required this.currentVisibilityPercentage,
    required this.isHighSeverity,
    required this.iconName,
    required this.color,
    this.userId,
    this.imageUrl,
    this.visibilityStatus = IncidentVisibilityStatus.visibleToSelf,
  });

  /// Calculate visibility percentage based on time decay rules
  /// Linear decay from 100% to 0% over 72 hours (consistent with IncidentVisibilityService)
  static double calculateVisibilityPercentage(DateTime postedAt) {
    final now = DateTime.now();
    final timeDifference = now.difference(postedAt);
    const visibilityWindow = Duration(hours: 72);

    if (timeDifference >= visibilityWindow) {
      return 0.0; // Completely faded out after 72 hours
    }

    // Linear decay from 100% to 0% over 72 hours
    final decayPercentage = timeDifference.inMilliseconds / visibilityWindow.inMilliseconds;
    return ((1.0 - decayPercentage) * 100.0).clamp(0.0, 100.0);
  }

  /// Check if incident should be visible on map (visibility > 0%)
  bool get isVisible => currentVisibilityPercentage > 0;

  /// Get opacity value for map marker (0.0 to 1.0)
  double get markerOpacity => currentVisibilityPercentage / 100.0;

  /// Check if marker should have pulse effect (high severity incidents)
  bool get shouldPulse => isHighSeverity && severity == 'Grave';

  /// Create a copy with updated visibility percentage
  IncidentMarkerEntity copyWithUpdatedVisibility() {
    return copyWith(
      currentVisibilityPercentage: calculateVisibilityPercentage(postedAt),
    );
  }

  IncidentMarkerEntity copyWith({
    String? incidentId,
    String? categoryKey,
    String? categoryTitle,
    String? subcategoryKey,
    String? subcategoryTitle,
    String? severity,
    LocationEntity? location,
    DateTime? postedAt,
    bool? isAnonymous,
    String? zoneId,
    double? currentVisibilityPercentage,
    bool? isHighSeverity,
    String? iconName,
    String? color,
    String? userId,
    String? imageUrl,
  }) {
    return IncidentMarkerEntity(
      incidentId: incidentId ?? this.incidentId,
      categoryKey: categoryKey ?? this.categoryKey,
      categoryTitle: categoryTitle ?? this.categoryTitle,
      subcategoryKey: subcategoryKey ?? this.subcategoryKey,
      subcategoryTitle: subcategoryTitle ?? this.subcategoryTitle,
      severity: severity ?? this.severity,
      location: location ?? this.location,
      postedAt: postedAt ?? this.postedAt,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      zoneId: zoneId ?? this.zoneId,
      currentVisibilityPercentage: currentVisibilityPercentage ?? this.currentVisibilityPercentage,
      isHighSeverity: isHighSeverity ?? this.isHighSeverity,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      userId: userId ?? this.userId,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  @override
  List<Object?> get props => [
        incidentId,
        categoryKey,
        categoryTitle,
        subcategoryKey,
        subcategoryTitle,
        severity,
        location,
        postedAt,
        isAnonymous,
        zoneId,
        currentVisibilityPercentage,
        isHighSeverity,
        iconName,
        color,
        userId,
        imageUrl,
        visibilityStatus,
      ];
}
