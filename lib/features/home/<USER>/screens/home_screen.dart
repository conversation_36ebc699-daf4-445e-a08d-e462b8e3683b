import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:lottie/lottie.dart' as lottie;
import 'package:respublicaseguridad/core/widgets/google_maps_widget.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/navigation/bottom_navigation_bar.dart';
import 'package:respublicaseguridad/core/widgets/app_drawer.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_bloc.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_event.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_state.dart';
import 'package:respublicaseguridad/features/home/<USER>/widgets/incident_marker_manager.dart';
import 'package:respublicaseguridad/features/home/<USER>/widgets/incident_detail_bottom_sheet.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:flutter/foundation.dart';
import 'package:respublicaseguridad/core/services/app_service_manager.dart';


class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<HomeBloc>(),
      child: const _HomeScreenContent(),
    );
  }
}

class _HomeScreenContent extends StatefulWidget {
  const _HomeScreenContent();

  @override
  State<_HomeScreenContent> createState() => _HomeScreenContentState();
}

class _HomeScreenContentState extends State<_HomeScreenContent> {
  GoogleMapController? _mapController;
  bool _hasAnimatedToUserLocation = false;
  final IncidentMarkerManager _markerManager = IncidentMarkerManager();
  Set<Marker> _incidentMarkers = {};
  List<dynamic> _lastProcessedMarkers = [];
  bool _isDrawerOpen = false;

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  void _initializeScreen() {
    // Initialize home screen with user ID
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated && authState.user.isNotEmpty) {
      context.read<HomeBloc>().add(HomeInitialized(authState.user.id));
      // Request current location
      context.read<HomeBloc>().add(CurrentLocationRequested());
    }
  }

  void _startIncidentMarkersStream(LatLng center) {
    final authState = context.read<AuthBloc>().state;
    final String? currentUserId = authState.isAuthenticated ? authState.user.id : null;

    context.read<HomeBloc>().add(IncidentMarkersStreamStarted(
      centerLat: center.latitude,
      centerLng: center.longitude,
      radiusKm: 10.0, // 10km radius
      // Pass the current user ID to ensure we see our own incidents
      // even if they're not visible to the community
      currentUserId: currentUserId,
    ));
  }

  void _onIncidentMarkerTap(String incidentId) {
    context.read<HomeBloc>().add(IncidentMarkerSelected(incidentId));

    // Find the incident and show bottom sheet
    final homeState = context.read<HomeBloc>().state;
    final incident = homeState.incidentMarkers.firstWhere(
      (i) => i.incidentId == incidentId,
      orElse: () => homeState.incidentMarkers.first,
    );

    IncidentDetailBottomSheet.show(
      context,
      incident,
    );
  }

  Future<void> _updateIncidentMarkers(HomeState state) async {
    print('🗺️ HomeScreen: Updating incident markers - ${state.incidentMarkers.length} markers');

    if (state.incidentMarkers.isNotEmpty) {
      final markers = await _markerManager.createMarkersFromIncidents(
        state.incidentMarkers,
        selectedIncidentId: state.selectedIncidentId,
        onMarkerTap: _onIncidentMarkerTap,
      );

      if (mounted) {
        setState(() {
          _incidentMarkers = markers;
        });
        print('✅ HomeScreen: Updated ${markers.length} markers on map');
      }
    } else {
      setState(() {
        _incidentMarkers = {};
      });
      print('🗺️ HomeScreen: Cleared all markers from map');
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    
    // Get the current camera position to start the incident markers stream
    controller.getVisibleRegion().then((visibleRegion) {
      // Calculate the center of the visible region
      final center = LatLng(
        (visibleRegion.northeast.latitude + visibleRegion.southwest.latitude) / 2,
        (visibleRegion.northeast.longitude + visibleRegion.southwest.longitude) / 2,
      );
      
      // Start the incident markers stream with the initial map center
      _startIncidentMarkersStream(center);
      
      print('🗺️ HomeScreen: Starting incident markers stream with initial center: $center');
    });
  }

  void _onMapTapped(LatLng position) {
    context.read<HomeBloc>().add(MapTapped(position));
    // Deselect any selected incident marker
    context.read<HomeBloc>().add(IncidentMarkerDeselected());
  }

  void _onMapCameraMove(CameraPosition position) {
    // Update camera position in bloc for potential stream updates
    context.read<HomeBloc>().add(MapCameraChanged(
      center: position.target,
      zoom: position.zoom,
    ));
  }

  void _animateToLocation(LatLng location) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: location,
            zoom: 15.0,
          ),
        ),
      );
    }
  }





  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, authState) {
        // Handle auth state changes if needed
      },
      builder: (context, authState) {
        return BlocConsumer<HomeBloc, HomeState>(
          listener: (context, homeState) {
            // Handle error messages
            if (homeState.errorMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(homeState.errorMessage!),
                  backgroundColor: Colors.red,
                ),
              );
            }

            // Handle location permission denied - show dialog and keep asking
            if (!homeState.hasLocationPermission &&
                homeState.hasRequestedLocationPermission &&
                !homeState.isLocationLoading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showLocationPermissionDialog();
              });
            }

            // Animate camera to user's location when it's updated (only once)
            if (homeState.hasLocationPermission &&
                homeState.currentLocation != const LatLng(0.0, 0.0) &&
                _mapController != null &&
                !_hasAnimatedToUserLocation) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _animateToLocation(homeState.currentLocation);
                _hasAnimatedToUserLocation = true;
                
                // Don't start incident markers stream here anymore
                // It's now started in _onMapCreated and updated in _onMapCameraChanged
                // This prevents duplicate streams
              });
            }

            // Update incident markers when they change
            // Only update if the markers list has actually changed to avoid unnecessary rebuilds
            if (homeState.incidentMarkers != _lastProcessedMarkers) {
              _lastProcessedMarkers = homeState.incidentMarkers;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _updateIncidentMarkers(homeState);
              });
            }

            // Show identity verification dialog when needed
            // Only check after loading is complete and we have user validation status
            if (!homeState.isLoading && homeState.userValidationStatus != null) {
              print('🏠 HomeScreen: Checking if should prompt for verification...');
              print('🏠 HomeScreen: shouldPromptForVerification: ${homeState.shouldPromptForVerification}');
              if (homeState.shouldPromptForVerification) {
                print('🏠 HomeScreen: Showing identity verification dialog');
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _showIdentityVerificationDialog();
                });
              } else {
                print('🏠 HomeScreen: Not showing dialog - conditions not met');
              }
            } else {
              print('🏠 HomeScreen: Still loading or no user status yet - not checking dialog');
            }
          },
          builder: (context, homeState) {
            return Scaffold(
              drawer: AppDrawer(
                authState: authState,
                homeState: homeState,
              ),
              onDrawerChanged: (isOpened) {
                setState(() {
                  _isDrawerOpen = isOpened;
                });
                // Notify parent about drawer state
                final provider = DrawerStateProvider.of(context);
                provider?.setDrawerState(isOpened);
              },

              body: Stack(
                children: [
                  // Google Maps Widget
                  GoogleMapsWidget(
                    initialLocation: homeState.hasLocationPermission &&
                                   homeState.currentLocation != const LatLng(0.0, 0.0)
                        ? homeState.currentLocation
                        : null, // Let GoogleMapsWidget use its default location until we get user's location
                    initialZoom: 13.0,
                    markers: _incidentMarkers,
                    onMapCreated: _onMapCreated,
                    onTap: _onMapTapped,
                    onCameraMove: _onMapCameraMove,
                    showMyLocationButton: true,
                    showZoomControls: false,
                  ),

                  // Loading overlay with QR animation
                  if (homeState.isLoading)
                    Container(
                      color: Colors.black.withValues(alpha: 0.3),
                      child: Center(
                        child: lottie.Lottie.asset(
                          'assets/animations/qr_loading.json',
                          width: 150.w,
                          height: 150.h,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),

                  // Drawer menu button - positioned in top left
                  Positioned(
                    top: 50.h,
                    left: 16.w,
                    child: Builder(
                      builder: (context) => Material(
                        elevation: 6,
                        shape: const CircleBorder(),
                        color: Theme.of(context).colorScheme.primary,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(28),
                          onTap: () => Scaffold.of(context).openDrawer(),
                          child: Container(
                            width: 56,
                            height: 56,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              FluentIcons.navigation_24_regular,
                              color: Colors.white,
                              size: 24.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              floatingActionButton: Padding(
                padding: EdgeInsets.only(bottom: 80.h), // Move buttons higher
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
              if (_shouldShowQRScannerButton(homeState, authState))
                      Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: FloatingActionButton(
                          heroTag: "qr_scanner",
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(32)),
                          ),
                          onPressed: () => _handleQRScannerPressed(context, authState),
                          backgroundColor: Theme.of(context).colorScheme.secondary,
                          child: Icon(
                            FluentIcons.qr_code_24_regular,
                            color: Colors.white,
                            size: 24.sp,
                          ),
                        ),
                      ),
                      
                     

                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }



  void _showIdentityVerificationDialog() {
    context.read<HomeBloc>().add(IdentityDialogShown());

    IosDialog.showAlertDialog(
      context: context,
      title: context.l10n.identityVerification,
      message: context.l10n.identityVerificationMessage,
      cancelText: context.l10n.skipForNow,
      confirmText: context.l10n.verifyIdentity,
      onCancel: () {
        // User chose to skip verification - no action needed
      },
      onConfirm: () {
        // Navigate to identity verification process
        context.push('/identity-validation');
      },
    );
  }

  void _showLocationPermissionDialog() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Location Permission Required',
      message: 'This app needs location access to show your current position on the map and provide location-based features.',
      cancelText: 'Not Now',
      confirmText: 'Allow Location',
      onCancel: () {
        // User denied permission - we'll ask again next time they try to use location
      },
      onConfirm: () {
        // Request permission again
        context.read<HomeBloc>().add(LocationPermissionRequested());
      },
    );
  }

  /// Check if QR scanner button should be shown
  /// Only show for users with validated or pendingReview status
  bool _shouldShowQRScannerButton(HomeState homeState, AuthState authState) {
    // Must be authenticated
    if (!authState.isAuthenticated || authState.user.isEmpty) {
      return false;
    }

    // Must have user validation status
    if (homeState.userValidationStatus == null) {
      return false;
    }

    // Only show for validated or pending review users
    final validationStatus = homeState.userValidationStatus!.validationStatus;
    return validationStatus == ValidationStatus.validated ||
           validationStatus == ValidationStatus.pendingReview;
  }

  /// Handle QR scanner button press
  void _handleQRScannerPressed(BuildContext context, AuthState authState) async {
    context.push('/standalone-qr-scanner');
  }

  /// Handle manual sync button press
  void _handleManualSyncPressed() async {
    if (_mapController == null) return;
    
    // Get the current camera position
    final position = await _mapController!.getVisibleRegion();
    final center = LatLng(
      (position.northeast.latitude + position.southwest.latitude) / 2,
      (position.northeast.longitude + position.southwest.longitude) / 2,
    );
    
    // Trigger a refresh of incident markers with the current map center
    context.read<HomeBloc>().add(IncidentMarkersStreamStarted(
      centerLat: center.latitude,
      centerLng: center.longitude,
      radiusKm: 10.0, // 10km radius
      currentUserId: context.read<AuthBloc>().state.user.id,
    ));
    
    // Also trigger a manual cleanup of expired markers
    GetIt.instance<AppServiceManager>().manualCleanup();
    
    print('🏠 HomeScreen: Manually triggered incident markers refresh and cleanup');
    
    // Show a snackbar to confirm the action
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Refreshing incident markers...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _markerManager.dispose();
    super.dispose();
  }

}
