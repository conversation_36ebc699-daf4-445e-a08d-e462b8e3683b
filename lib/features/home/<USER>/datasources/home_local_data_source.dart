import 'package:google_maps_flutter/google_maps_flutter.dart';

abstract class HomeLocalDataSource {
  /// Get cached search suggestions
  List<String> getCachedSearchSuggestions(String query);

  /// Cache search suggestions
  Future<void> cacheSearchSuggestions(String query, List<String> suggestions);

  /// Get default location
  LatLng getDefaultLocation();
}

class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  // In-memory cache for search suggestions
  final Map<String, List<String>> _searchCache = {};
  
  @override
  List<String> getCachedSearchSuggestions(String query) {
    return _searchCache[query.toLowerCase()] ?? [];
  }
  
  @override
  Future<void> cacheSearchSuggestions(String query, List<String> suggestions) async {
    _searchCache[query.toLowerCase()] = suggestions;
  }
  
  @override
  LatLng getDefaultLocation() {
    return const LatLng(40.7128, -74.0060); 
  }
}
