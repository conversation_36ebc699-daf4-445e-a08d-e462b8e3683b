import 'package:firebase_database/firebase_database.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_enums.dart';
import 'package:flutter/foundation.dart';

/// Abstract data source for incident markers using Firebase Realtime Database
abstract class IncidentMarkerFirebaseDataSource {
  /// Stream incident markers within a geographical area
  Stream<List<IncidentMarkerModel>> watchIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Stream incident markers for specific zones
  Stream<List<IncidentMarkerModel>> watchZoneIncidentMarkers({
    required List<String> zoneIds,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Get incident markers within a geographical area (one-time fetch)
  Future<List<IncidentMarkerModel>> getIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Create or update an incident marker
  Future<void> upsertIncidentMarker(IncidentMarkerModel marker);

  /// Delete an incident marker
  Future<void> deleteIncidentMarker(String incidentId);

  /// Clean up expired markers (called periodically)
  Future<void> cleanupExpiredMarkers();
}

/// Implementation of incident marker data source using Firebase Realtime Database
class IncidentMarkerFirebaseDataSourceImpl implements IncidentMarkerFirebaseDataSource {
  final FirebaseDatabase _database;
  
  // Database paths
  static const String _markersPath = 'incident_markers';
  static const String _zoneMarkersPath = 'zone_incident_markers';
  static const String _geoIndexPath = 'incident_markers_geo_index';

  IncidentMarkerFirebaseDataSourceImpl(this._database);

  @override
  Stream<List<IncidentMarkerModel>> watchIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    try {
      // For now, get all markers and filter client-side
      // In production, implement proper geospatial indexing
      final ref = _database.ref(_markersPath);
      
      print('🔍 IncidentMarkerFirebaseDataSource: Watching markers at center ($centerLat, $centerLng) with radius ${radiusKm}km');
      if (currentUserId != null) {
        print('🔍 IncidentMarkerFirebaseDataSource: Including user-owned markers for user: $currentUserId');
      }
      
      return ref.onValue.map((event) {
        if (!event.snapshot.exists) {
          print('❌ IncidentMarkerFirebaseDataSource: No markers found in database');
          return <IncidentMarkerModel>[];
        }

        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        print('🔍 IncidentMarkerFirebaseDataSource: Found ${data.length} total markers in database');
        
        final markers = <IncidentMarkerModel>[];
        int filteredByVisibility = 0;
        int filteredByDistance = 0;
        int filteredByCategory = 0;
        int filteredBySeverity = 0;
        int userOwned = 0;
        int communityVisible = 0;

        for (final entry in data.entries) {
          try {
            final markerData = Map<String, dynamic>.from(entry.value as Map);
            final marker = IncidentMarkerModel.fromRealtimeDB(markerData);
            
            // Filter by visibility (time decay)
            if (!marker.isVisible) {
              filteredByVisibility++;
              continue;
            }
            
            // Filter by distance (client-side for now)
            final distance = Geolocator.distanceBetween(
              centerLat, centerLng,
              marker.location.latitude, marker.location.longitude,
            ) / 1000; // Convert to kilometers
            if (distance > radiusKm) {
              filteredByDistance++;
              continue;
            }
            
            // Filter by category if specified
            if (categoryFilter != null && marker.categoryKey != categoryFilter) {
              filteredByCategory++;
              continue;
            }
            
            // Filter by severity if specified
            if (severityFilter != null && marker.severity != severityFilter) {
              filteredBySeverity++;
              continue;
            }
            
            // Include marker if it belongs to the current user OR is visible to community
            final isUserOwned = currentUserId != null && marker.userId == currentUserId;

            if (isUserOwned) {
              // Always include the user's own markers
              markers.add(marker);
              userOwned++;
              debugPrint('Including user-owned marker: ${marker.incidentId}');
            } else {
              // For non-user markers, only include if they are marked as visible to community
              if (marker.visibilityStatus == IncidentVisibilityStatus.visibleToCommunity) {
                markers.add(marker);
                communityVisible++;
                debugPrint('Including community-visible marker: ${marker.incidentId}');
              } else {
                debugPrint('Filtering out private marker: ${marker.incidentId}');
              }
            }
          } catch (e) {
            debugPrint('Error parsing incident marker: $e');
            continue;
          }
        }

        print('🔍 IncidentMarkerFirebaseDataSource: Filter results:');
        print('   - Filtered by visibility: $filteredByVisibility');
        print('   - Filtered by distance: $filteredByDistance');
        print('   - Filtered by category: $filteredByCategory');
        print('   - Filtered by severity: $filteredBySeverity');
        print('   - User-owned markers: $userOwned');
        print('   - Community-visible markers: $communityVisible');
        print('   - Total markers after filtering: ${markers.length}');

        return markers;
      });
    } catch (e) {
      debugPrint('Error watching incident markers: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<List<IncidentMarkerModel>> watchZoneIncidentMarkers({
    required List<String> zoneIds,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    try {
      if (zoneIds.isEmpty) {
        return Stream.value([]);
      }

      // Watch markers for specific zones
      final ref = _database.ref(_zoneMarkersPath);
      
      return ref.onValue.map((event) {
        if (!event.snapshot.exists) {
          return <IncidentMarkerModel>[];
        }

        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        final markers = <IncidentMarkerModel>[];

        for (final zoneId in zoneIds) {
          final zoneData = data[zoneId];
          if (zoneData == null) continue;

          final zoneMarkers = Map<String, dynamic>.from(zoneData as Map);
          
          for (final entry in zoneMarkers.entries) {
            try {
              final markerData = Map<String, dynamic>.from(entry.value as Map);
              final marker = IncidentMarkerModel.fromRealtimeDB(markerData);
              
              // Filter by visibility (time decay)
              if (!marker.isVisible) continue;
              
              // Filter by category if specified
              if (categoryFilter != null && marker.categoryKey != categoryFilter) continue;
              
              // Filter by severity if specified
              if (severityFilter != null && marker.severity != severityFilter) continue;
              
              // Include marker if it belongs to the current user OR is visible to community
              final isUserOwned = currentUserId != null && marker.userId == currentUserId;

              if (isUserOwned) {
                // Always include the user's own markers
                markers.add(marker);
                debugPrint('Including user-owned zone marker: ${marker.incidentId}');
              } else {
                // For non-user markers, only include if they are marked as visible to community
                if (marker.visibilityStatus == IncidentVisibilityStatus.visibleToCommunity) {
                  markers.add(marker);
                  debugPrint('Including community-visible zone marker: ${marker.incidentId}');
                } else {
                  debugPrint('Filtering out private zone marker: ${marker.incidentId}');
                }
              }
            } catch (e) {
              debugPrint('Error parsing zone incident marker: $e');
              continue;
            }
          }
        }

        return markers;
      });
    } catch (e) {
      debugPrint('Error watching zone incident markers: $e');
      return Stream.value([]);
    }
  }

  @override
  Future<List<IncidentMarkerModel>> getIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) async {
    try {
      final ref = _database.ref(_markersPath);
      final snapshot = await ref.get();
      
      if (!snapshot.exists) {
        return [];
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final markers = <IncidentMarkerModel>[];

      for (final entry in data.entries) {
        try {
          final markerData = Map<String, dynamic>.from(entry.value as Map);
          final marker = IncidentMarkerModel.fromRealtimeDB(markerData);
          
          // Filter by visibility (time decay)
          if (!marker.isVisible) continue;
          
          // Filter by distance
          final distance = Geolocator.distanceBetween(
            centerLat, centerLng,
            marker.location.latitude, marker.location.longitude,
          ) / 1000; // Convert to kilometers
          if (distance > radiusKm) continue;
          
          // Filter by category if specified
          if (categoryFilter != null && marker.categoryKey != categoryFilter) continue;
          
          // Filter by severity if specified
          if (severityFilter != null && marker.severity != severityFilter) continue;
          
          // Include marker if it belongs to the current user OR is visible to community
          final isUserOwned = currentUserId != null && marker.userId == currentUserId;

          if (isUserOwned) {
            // Always include the user's own markers
            markers.add(marker);
            debugPrint('Including user-owned marker: ${marker.incidentId}');
          } else {
            // For non-user markers, only include if they are marked as visible to community
            if (marker.visibilityStatus == IncidentVisibilityStatus.visibleToCommunity) {
              markers.add(marker);
              debugPrint('Including community-visible marker: ${marker.incidentId}');
            } else {
              debugPrint('Filtering out private marker: ${marker.incidentId}');
            }
          }
        } catch (e) {
          debugPrint('Error parsing incident marker: $e');
          continue;
        }
      }

      return markers;
    } catch (e) {
      debugPrint('Error getting incident markers: $e');
      return [];
    }
  }

  @override
  Future<void> upsertIncidentMarker(IncidentMarkerModel marker) async {
    try {
      // Store in main markers collection
      final markerRef = _database.ref(_markersPath).child(marker.incidentId);
      await markerRef.set(marker.toRealtimeDB());
      
      // Store in zone-specific collection if zoneId exists
      if (marker.zoneId != null) {
        final zoneMarkerRef = _database.ref(_zoneMarkersPath)
            .child(marker.zoneId!)
            .child(marker.incidentId);
        await zoneMarkerRef.set(marker.toRealtimeDB());
      }
      
      debugPrint('Incident marker upserted: ${marker.incidentId}');
    } catch (e) {
      debugPrint('Error upserting incident marker: $e');
      throw Exception('Failed to upsert incident marker: $e');
    }
  }

  @override
  Future<void> deleteIncidentMarker(String incidentId) async {
    try {
      // Delete from main markers collection
      final markerRef = _database.ref(_markersPath).child(incidentId);
      await markerRef.remove();
      
      // Delete from zone collections (we need to find which zones)
      // For now, this is a simplified approach
      debugPrint('Incident marker deleted: $incidentId');
    } catch (e) {
      debugPrint('Error deleting incident marker: $e');
      throw Exception('Failed to delete incident marker: $e');
    }
  }

  @override
  Future<void> cleanupExpiredMarkers() async {
    try {
      final ref = _database.ref(_markersPath);
      final snapshot = await ref.get();
      
      if (!snapshot.exists) return;

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final expiredMarkers = <String>[];

      for (final entry in data.entries) {
        try {
          final markerData = Map<String, dynamic>.from(entry.value as Map);
          final marker = IncidentMarkerModel.fromRealtimeDB(markerData);
          
          if (!marker.isVisible) {
            expiredMarkers.add(marker.incidentId);
          }
        } catch (e) {
          debugPrint('Error checking marker expiry: $e');
          continue;
        }
      }

      // Delete expired markers
      for (final incidentId in expiredMarkers) {
        await deleteIncidentMarker(incidentId);
      }
      
      debugPrint('Cleaned up ${expiredMarkers.length} expired markers');
    } catch (e) {
      debugPrint('Error cleaning up expired markers: $e');
    }
  }


}
