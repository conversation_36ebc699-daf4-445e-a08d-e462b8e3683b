import 'package:get_it/get_it.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/features/home/<USER>/datasources/home_local_data_source.dart';
import 'package:respublicaseguridad/features/home/<USER>/datasources/incident_marker_firebase_datasource.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository_impl.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/incident_marker_repository_impl.dart';
import 'package:respublicaseguridad/features/home/<USER>/services/incident_marker_sync_service.dart';
import 'package:respublicaseguridad/features/home/<USER>/services/incident_marker_firestore_service.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/incident_marker_repository.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/get_user_verification_status_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/get_search_suggestions_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/check_pending_documents_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/watch_incident_markers_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_bloc.dart';

import 'package:respublicaseguridad/core/services/location_service.dart';

class HomeInjection {
  static void init() {
    final getIt = GetIt.instance;

    // Data Sources
    getIt.registerLazySingleton<HomeLocalDataSource>(
      () => HomeLocalDataSourceImpl(),
    );

    getIt.registerLazySingleton<IncidentMarkerFirebaseDataSource>(
      () => IncidentMarkerFirebaseDataSourceImpl(
        getIt<FirebaseDatabase>(),
      ),
    );

    // Services
    getIt.registerLazySingleton<IncidentMarkerSyncService>(
      () => IncidentMarkerSyncService(
        firestore: getIt<FirebaseFirestore>(),
        database: getIt<FirebaseDatabase>(),
        auth: getIt<FirebaseAuth>(),
      ),
    );

    getIt.registerLazySingleton<IncidentMarkerFirestoreService>(
      () => IncidentMarkerFirestoreService(
        getIt<FirebaseFirestore>(),
      ),
    );

   

    // Repositories
    getIt.registerLazySingleton<HomeRepository>(
      () => HomeRepositoryImpl(
        getIt<HomeLocalDataSource>(),
        getIt(), // IdentityVerificationRepository
      ),
    );

    getIt.registerLazySingleton<IncidentMarkerRepository>(
      () => IncidentMarkerRepositoryImpl(
        getIt<IncidentMarkerFirebaseDataSource>(),
      ),
    );

    // Use Cases
    getIt.registerLazySingleton(
      () => GetUserVerificationStatusUseCase(getIt<HomeRepository>()),
    );

    getIt.registerLazySingleton(
      () => GetSearchSuggestionsUseCase(getIt<HomeRepository>()),
    );

    getIt.registerLazySingleton(
      () => CheckPendingDocumentsUseCase(getIt<HomeRepository>()),
    );

    getIt.registerLazySingleton(
      () => WatchIncidentMarkersUseCase(getIt<IncidentMarkerRepository>()),
    );

    getIt.registerLazySingleton(
      () => WatchZoneIncidentMarkersUseCase(getIt<IncidentMarkerRepository>()),
    );

    getIt.registerLazySingleton(
      () => GetIncidentMarkersUseCase(getIt<IncidentMarkerRepository>()),
    );

    // BLoC
    getIt.registerFactory(
      () => HomeBloc(
        getUserVerificationStatusUseCase: getIt<GetUserVerificationStatusUseCase>(),
        getSearchSuggestionsUseCase: getIt<GetSearchSuggestionsUseCase>(),
        checkPendingDocumentsUseCase: getIt<CheckPendingDocumentsUseCase>(),
        watchIncidentMarkersUseCase: getIt<WatchIncidentMarkersUseCase>(),
        locationService: getIt<LocationService>(),
      ),
    );
  }
}
