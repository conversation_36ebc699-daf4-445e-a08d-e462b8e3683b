import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object?> get props => [];
}

class HomeInitialized extends HomeEvent {
  final String userId;
  
  const HomeInitialized(this.userId);
  
  @override
  List<Object> get props => [userId];
}

class IdentityDialogShown extends HomeEvent {}

class SearchQueryChanged extends HomeEvent {
  final String query;
  
  const SearchQueryChanged(this.query);
  
  @override
  List<Object> get props => [query];
}

class SearchFocusChanged extends HomeEvent {
  final bool hasFocus;
  
  const SearchFocusChanged(this.hasFocus);
  
  @override
  List<Object> get props => [hasFocus];
}

class MapTapped extends HomeEvent {
  final LatLng position;
  
  const MapTapped(this.position);
  
  @override
  List<Object> get props => [position];
}

class NavigateToIdentityVerification extends HomeEvent {}

class CurrentLocationRequested extends HomeEvent {}

class LocationPermissionRequested extends HomeEvent {}

class LocationPermissionDenied extends HomeEvent {}

class CurrentLocationUpdated extends HomeEvent {
  final LatLng location;

  const CurrentLocationUpdated(this.location);

  @override
  List<Object> get props => [location];
}

class IncidentMarkersRequested extends HomeEvent {
  final double centerLat;
  final double centerLng;
  final double radiusKm;
  final String? categoryFilter;
  final String? severityFilter;

  const IncidentMarkersRequested({
    required this.centerLat,
    required this.centerLng,
    required this.radiusKm,
    this.categoryFilter,
    this.severityFilter,
  });

  @override
  List<Object?> get props => [centerLat, centerLng, radiusKm, categoryFilter, severityFilter];
}

class IncidentMarkersStreamStarted extends HomeEvent {
  final double centerLat;
  final double centerLng;
  final double radiusKm;
  final String? categoryFilter;
  final String? severityFilter;
  final String? currentUserId;

  const IncidentMarkersStreamStarted({
    required this.centerLat,
    required this.centerLng,
    required this.radiusKm,
    this.categoryFilter,
    this.severityFilter,
    this.currentUserId,
  });

  @override
  List<Object?> get props => [centerLat, centerLng, radiusKm, categoryFilter, severityFilter, currentUserId];
}

class IncidentMarkersUpdated extends HomeEvent {
  final List<dynamic> markers; // Will be List<IncidentMarkerEntity>

  const IncidentMarkersUpdated(this.markers);

  @override
  List<Object> get props => [markers];
}

class IncidentMarkerSelected extends HomeEvent {
  final String incidentId;

  const IncidentMarkerSelected(this.incidentId);

  @override
  List<Object> get props => [incidentId];
}

class IncidentMarkerDeselected extends HomeEvent {}

class MapCameraChanged extends HomeEvent {
  final LatLng center;
  final double zoom;

  const MapCameraChanged({
    required this.center,
    required this.zoom,
  });

  @override
  List<Object> get props => [center, zoom];
}
