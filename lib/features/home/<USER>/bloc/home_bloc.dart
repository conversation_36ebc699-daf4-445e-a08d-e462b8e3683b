import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_event.dart';
import 'package:respublicaseguridad/features/home/<USER>/bloc/home_state.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/get_user_verification_status_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/get_search_suggestions_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/check_pending_documents_use_case.dart';
import 'package:respublicaseguridad/features/home/<USER>/usecases/watch_incident_markers_use_case.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetUserVerificationStatusUseCase _getUserVerificationStatusUseCase;
  final GetSearchSuggestionsUseCase _getSearchSuggestionsUseCase;
  final CheckPendingDocumentsUseCase _checkPendingDocumentsUseCase;
  final WatchIncidentMarkersUseCase _watchIncidentMarkersUseCase;
  final LocationService _locationService;

  StreamSubscription? _incidentMarkersSubscription;

  HomeBloc({
    required GetUserVerificationStatusUseCase getUserVerificationStatusUseCase,
    required GetSearchSuggestionsUseCase getSearchSuggestionsUseCase,
    required CheckPendingDocumentsUseCase checkPendingDocumentsUseCase,
    required WatchIncidentMarkersUseCase watchIncidentMarkersUseCase,
    required LocationService locationService,
  }) : _getUserVerificationStatusUseCase = getUserVerificationStatusUseCase,
       _getSearchSuggestionsUseCase = getSearchSuggestionsUseCase,
       _checkPendingDocumentsUseCase = checkPendingDocumentsUseCase,
       _watchIncidentMarkersUseCase = watchIncidentMarkersUseCase,
       _locationService = locationService,
       super(const HomeState()) {
    on<HomeInitialized>(_onHomeInitialized);
    on<IdentityDialogShown>(_onIdentityDialogShown);
    on<SearchQueryChanged>(_onSearchQueryChanged);
    on<SearchFocusChanged>(_onSearchFocusChanged);
    on<MapTapped>(_onMapTapped);
    on<NavigateToIdentityVerification>(_onNavigateToIdentityVerification);
    on<CurrentLocationRequested>(_onCurrentLocationRequested);
    on<LocationPermissionRequested>(_onLocationPermissionRequested);
    on<LocationPermissionDenied>(_onLocationPermissionDenied);
    on<CurrentLocationUpdated>(_onCurrentLocationUpdated);
    on<IncidentMarkersStreamStarted>(_onIncidentMarkersStreamStarted);
    on<IncidentMarkersUpdated>(_onIncidentMarkersUpdated);
    on<IncidentMarkerSelected>(_onIncidentMarkerSelected);
    on<IncidentMarkerDeselected>(_onIncidentMarkerDeselected);
    on<MapCameraChanged>(_onMapCameraChanged);
  }

  void _onHomeInitialized(
    HomeInitialized event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, clearError: true));

    try {
      // Check for pending documents first
      print('🏠 HomeBloc: Checking for pending documents...');
      final pendingResult = await _checkPendingDocumentsUseCase(event.userId);
      bool hasPending = false;

      pendingResult.fold(
        (failure) {
          print('❌ HomeBloc: Failed to check pending documents: ${failure.message}');
          // If we can't check pending status, proceed with normal verification check
        },
        (hasPendingDocs) {
          hasPending = hasPendingDocs;
          print('📋 HomeBloc: Has pending documents: $hasPending');
        },
      );

      // Get user verification status
      print('🏠 HomeBloc: Getting user verification status...');
      final statusResult = await _getUserVerificationStatusUseCase(event.userId);
      statusResult.fold(
        (failure) {
          print('❌ HomeBloc: Failed to get user verification status: ${failure.message}');
          // Don't show error for verification status - it's optional
          emit(state.copyWith(isLoading: false));
        },
        (userEntity) {
          print('👤 HomeBloc: User validation status: ${userEntity.validationStatus.name}');
          print('📄 HomeBloc: User identity document ID: ${userEntity.identityDocumentId}');

          // If user has pending documents or is validated, mark dialog as shown to prevent prompting
          final shouldMarkAsShown = hasPending ||
                                   userEntity.validationStatus == ValidationStatus.pendingId ||
                                   userEntity.validationStatus == ValidationStatus.pendingReview ||
                                   userEntity.validationStatus == ValidationStatus.validated;

          print('🔔 HomeBloc: Should mark dialog as shown: $shouldMarkAsShown');
          print('🔔 HomeBloc: Breakdown - hasPending: $hasPending, status: ${userEntity.validationStatus.name}');

          emit(state.copyWith(
            isLoading: false,
            userValidationStatus: userEntity,
            hasShownIdentityDialog: shouldMarkAsShown,
          ));
        },
      );

    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to initialize home screen',
      ));
    }
  }

  void _onIdentityDialogShown(
    IdentityDialogShown event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(hasShownIdentityDialog: true));
  }

  void _onSearchQueryChanged(
    SearchQueryChanged event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(searchQuery: event.query));

    if (event.query.trim().isEmpty) {
      emit(state.copyWith(searchSuggestions: []));
      return;
    }

    final result = await _getSearchSuggestionsUseCase(event.query);
    result.fold(
      (failure) {
        // Don't show error for search suggestions
      },
      (suggestions) {
        emit(state.copyWith(searchSuggestions: suggestions));
      },
    );
  }

  void _onSearchFocusChanged(
    SearchFocusChanged event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isSearchActive: event.hasFocus));
  }

  void _onMapTapped(
    MapTapped event,
    Emitter<HomeState> emit,
  ) {
    // Handle map tap - could add markers, show info, etc.
    // For now, just clear search focus
    emit(state.copyWith(isSearchActive: false));
  }

  void _onNavigateToIdentityVerification(
    NavigateToIdentityVerification event,
    Emitter<HomeState> emit,
  ) {
    // This event can be handled by the UI layer for navigation
    // The bloc doesn't handle navigation directly
  }

  void _onCurrentLocationRequested(
    CurrentLocationRequested event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(isLocationLoading: true));

    try {
      // Check if we have location permission
      final permission = await _locationService.getLocationPermission();

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        // Request permission first
        add(LocationPermissionRequested());
        return;
      }

      // Get current position
      final position = await _locationService.getCurrentPosition();
      if (position != null) {
        final location = LatLng(position.latitude, position.longitude);
        add(CurrentLocationUpdated(location));
      } else {
        emit(state.copyWith(
          isLocationLoading: false,
          errorMessage: 'Unable to get current location',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLocationLoading: false,
        errorMessage: 'Error getting location: $e',
      ));
    }
  }

  void _onLocationPermissionRequested(
    LocationPermissionRequested event,
    Emitter<HomeState> emit,
  ) async {
    try {
      final result = await _locationService.requestLocationPermissions();

      if (result.status == LocationPermissionStatus.granted) {
        emit(state.copyWith(
          hasLocationPermission: true,
          hasRequestedLocationPermission: true,
        ));
        // Now try to get location
        add(CurrentLocationRequested());
      } else {
        emit(state.copyWith(
          hasLocationPermission: false,
          hasRequestedLocationPermission: true,
          isLocationLoading: false,
        ));
        add(LocationPermissionDenied());
      }
    } catch (e) {
      emit(state.copyWith(
        hasLocationPermission: false,
        hasRequestedLocationPermission: true,
        isLocationLoading: false,
        errorMessage: 'Error requesting location permission: $e',
      ));
    }
  }

  void _onLocationPermissionDenied(
    LocationPermissionDenied event,
    Emitter<HomeState> emit,
  ) {
    // This will be handled by the UI to show the permission dialog
    emit(state.copyWith(
      hasLocationPermission: false,
      isLocationLoading: false,
    ));
  }

  void _onCurrentLocationUpdated(
    CurrentLocationUpdated event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(
      currentLocation: event.location,
      isLocationLoading: false,
      hasLocationPermission: true,
    ));
  }

  void _onIncidentMarkersStreamStarted(
    IncidentMarkersStreamStarted event,
    Emitter<HomeState> emit,
  ) async {
    print('🗺️ HomeBloc: Starting incident markers stream');

    // Cancel existing subscription
    await _incidentMarkersSubscription?.cancel();

    emit(state.copyWith(
      isIncidentMarkersLoading: true,
      isIncidentMarkersStreamActive: true,
      clearError: true,
      // Store the center point of this query
      currentIncidentQueryCenter: LatLng(event.centerLat, event.centerLng),
    ));

    try {
      // Use the current user ID from the event if provided, otherwise fallback to the state
      final String? currentUserId = event.currentUserId ?? state.userValidationStatus?.uid;
      print('🗺️ HomeBloc: Current user ID for incident markers: $currentUserId');

      _incidentMarkersSubscription = _watchIncidentMarkersUseCase(
        centerLat: event.centerLat,
        centerLng: event.centerLng,
        radiusKm: event.radiusKm,
        categoryFilter: event.categoryFilter,
        severityFilter: event.severityFilter,
        currentUserId: currentUserId, // Pass the current user ID
      ).listen(
        (result) {
          result.fold(
            (failure) {
              add(IncidentMarkersUpdated([]));
              print('❌ HomeBloc: Error in incident markers stream: ${failure.message}');
            },
            (markers) {
              add(IncidentMarkersUpdated(markers));
              print('✅ HomeBloc: Received ${markers.length} incident markers');
            },
          );
        },
        onError: (error) {
          print('❌ HomeBloc: Stream error: $error');
          add(IncidentMarkersUpdated([]));
        },
      );
    } catch (e) {
      print('❌ HomeBloc: Error starting incident markers stream: $e');
      emit(state.copyWith(
        isIncidentMarkersLoading: false,
        isIncidentMarkersStreamActive: false,
        errorMessage: 'Failed to load incident markers',
      ));
    }
  }

  void _onIncidentMarkersUpdated(
    IncidentMarkersUpdated event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(
      incidentMarkers: event.markers.cast(),
      isIncidentMarkersLoading: false,
    ));
  }

  void _onIncidentMarkerSelected(
    IncidentMarkerSelected event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(selectedIncidentId: event.incidentId));
  }

  void _onIncidentMarkerDeselected(
    IncidentMarkerDeselected event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(clearSelectedIncident: true));
  }

  void _onMapCameraChanged(
    MapCameraChanged event,
    Emitter<HomeState> emit,
  ) {
    // Log the camera change
    print('🗺️ HomeBloc: Map camera changed to ${event.center}');
    
    // Check if we've moved far enough to justify restarting the stream
    // Only restart if we've moved more than 5km from the last query center
    final currentCenter = state.currentIncidentQueryCenter;
    if (currentCenter == null) {
      // First camera change, store the center but don't restart yet
      emit(state.copyWith(currentIncidentQueryCenter: event.center));
      return;
    }
    
    // Calculate distance between current query center and new camera position
    final distanceKm = Geolocator.distanceBetween(
      currentCenter.latitude, 
      currentCenter.longitude,
      event.center.latitude, 
      event.center.longitude
    ) / 1000; // Convert to km
    
    // If we've moved more than half the query radius, restart the stream
    if (distanceKm > 5.0) {
      print('🗺️ HomeBloc: Camera moved ${distanceKm.toStringAsFixed(1)}km, restarting incident markers stream');
      
      // Store the new query center
      emit(state.copyWith(currentIncidentQueryCenter: event.center));
      
      // Restart the stream with the new center
      add(IncidentMarkersStreamStarted(
        centerLat: event.center.latitude,
        centerLng: event.center.longitude,
        radiusKm: 10.0, // Keep the same radius
        categoryFilter: state.categoryFilter,
        severityFilter: state.severityFilter,
        currentUserId: state.userValidationStatus?.uid,
      ));
    }
  }

  @override
  Future<void> close() async {
    await _incidentMarkersSubscription?.cancel();
    return super.close();
  }

}
