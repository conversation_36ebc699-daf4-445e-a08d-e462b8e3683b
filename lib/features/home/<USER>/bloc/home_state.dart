import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';

class HomeState extends Equatable {
  final bool isLoading;
  final bool hasShownIdentityDialog;
  final UserEntity? userValidationStatus;
  final String? errorMessage;
  final LatLng currentLocation;
  final List<String> searchSuggestions;
  final bool isSearchActive;
  final String searchQuery;
  final bool isLocationLoading;
  final bool hasLocationPermission;
  final bool hasRequestedLocationPermission;
  final List<IncidentMarkerEntity> incidentMarkers;
  final bool isIncidentMarkersLoading;
  final String? selectedIncidentId;
  final bool isIncidentMarkersStreamActive;
  final LatLng? currentIncidentQueryCenter; // Center point of the current incident query
  final String? categoryFilter; // Optional category filter
  final String? severityFilter; // Optional severity filter

  const HomeState({
    this.isLoading = false,
    this.hasShownIdentityDialog = false,
    this.userValidationStatus,
    this.errorMessage,
    this.currentLocation = const LatLng(0.0, 0.0), // Will be updated with actual location
    this.searchSuggestions = const [],
    this.isSearchActive = false,
    this.searchQuery = '',
    this.isLocationLoading = false,
    this.hasLocationPermission = false,
    this.hasRequestedLocationPermission = false,
    this.incidentMarkers = const [],
    this.isIncidentMarkersLoading = false,
    this.selectedIncidentId,
    this.isIncidentMarkersStreamActive = false,
    this.currentIncidentQueryCenter,
    this.categoryFilter,
    this.severityFilter,
  });

  HomeState copyWith({
    bool? isLoading,
    bool? hasShownIdentityDialog,
    UserEntity? userValidationStatus,
    String? errorMessage,
    bool? clearError,
    LatLng? currentLocation,
    List<String>? searchSuggestions,
    bool? isSearchActive,
    String? searchQuery,
    bool? isLocationLoading,
    bool? hasLocationPermission,
    bool? hasRequestedLocationPermission,
    List<IncidentMarkerEntity>? incidentMarkers,
    bool? isIncidentMarkersLoading,
    String? selectedIncidentId,
    bool? clearSelectedIncident,
    bool? isIncidentMarkersStreamActive,
    LatLng? currentIncidentQueryCenter,
    String? categoryFilter,
    String? severityFilter,
  }) {
    return HomeState(
      isLoading: isLoading ?? this.isLoading,
      hasShownIdentityDialog: hasShownIdentityDialog ?? this.hasShownIdentityDialog,
      userValidationStatus: userValidationStatus ?? this.userValidationStatus,
      errorMessage: clearError == true ? null : (errorMessage ?? this.errorMessage),
      currentLocation: currentLocation ?? this.currentLocation,
      searchSuggestions: searchSuggestions ?? this.searchSuggestions,
      isSearchActive: isSearchActive ?? this.isSearchActive,
      searchQuery: searchQuery ?? this.searchQuery,
      isLocationLoading: isLocationLoading ?? this.isLocationLoading,
      hasLocationPermission: hasLocationPermission ?? this.hasLocationPermission,
      hasRequestedLocationPermission: hasRequestedLocationPermission ?? this.hasRequestedLocationPermission,
      incidentMarkers: incidentMarkers ?? this.incidentMarkers,
      isIncidentMarkersLoading: isIncidentMarkersLoading ?? this.isIncidentMarkersLoading,
      selectedIncidentId: clearSelectedIncident == true ? null : (selectedIncidentId ?? this.selectedIncidentId),
      isIncidentMarkersStreamActive: isIncidentMarkersStreamActive ?? this.isIncidentMarkersStreamActive,
      currentIncidentQueryCenter: currentIncidentQueryCenter ?? this.currentIncidentQueryCenter,
      categoryFilter: categoryFilter ?? this.categoryFilter,
      severityFilter: severityFilter ?? this.severityFilter,
    );
  }

  bool get shouldShowIdentityDialog {
    return !hasShownIdentityDialog &&
           (userValidationStatus == null ||
            userValidationStatus!.validationStatus == ValidationStatus.unverified ||
            userValidationStatus!.validationStatus == ValidationStatus.rejected);
  }

  /// Check if user should be prompted for identity verification
  /// Excludes users with pending documents (pendingId, pendingReview) and validated users
  bool get shouldPromptForVerification {
    print('🔔 HomeState: Checking shouldPromptForVerification...');
    print('🔔 HomeState: hasShownIdentityDialog: $hasShownIdentityDialog');
    print('🔔 HomeState: userValidationStatus: ${userValidationStatus?.validationStatus.name}');

    if (hasShownIdentityDialog) {
      print('🔔 HomeState: Dialog already shown, returning false');
      return false;
    }

    if (userValidationStatus == null) {
      print('🔔 HomeState: No validation status yet, returning false');
      return false; // Don't show dialog until we have actual status
    }

    final shouldPrompt = switch (userValidationStatus!.validationStatus) {
      ValidationStatus.unverified => true, // Show dialog for unverified users
      ValidationStatus.rejected => true, // Show dialog for rejected users
      ValidationStatus.pendingId => false, // Don't show dialog for pending users
      ValidationStatus.pendingReview => false, // Don't show dialog for pending users
      ValidationStatus.pendingAutomaticVerification => false, // Don't show dialog for automatic verification
      ValidationStatus.suspended => true, // Show dialog for suspended users
      ValidationStatus.validated => false, // Don't show dialog for validated users
    };

    print('🔔 HomeState: Final shouldPrompt result: $shouldPrompt');
    return shouldPrompt;
  }

  @override
  List<Object?> get props => [
    isLoading,
    hasShownIdentityDialog,
    userValidationStatus,
    errorMessage,
    currentLocation,
    searchSuggestions,
    isSearchActive,
    searchQuery,
    isLocationLoading,
    hasLocationPermission,
    hasRequestedLocationPermission,
    incidentMarkers,
    isIncidentMarkersLoading,
    selectedIncidentId,
    isIncidentMarkersStreamActive,
    currentIncidentQueryCenter,
    categoryFilter,
    severityFilter,
  ];
}
