import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/identity_document_entity.dart';

abstract class HomeRepository {
  /// Get user's current identity verification status
  Future<Either<Failure, UserEntity>> getUserVerificationStatus(String userId);

  /// Check if user has pending documents in Firestore
  Future<Either<Failure, bool>> hasPendingDocuments(String userId);

  /// Get search suggestions based on query
  Future<Either<Failure, List<String>>> getSearchSuggestions(String query);

  /// Get user's current location
  Future<Either<Failure, LatLng>> getCurrentLocation();
}
