import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository.dart';
import 'package:respublicaseguridad/features/home/<USER>/datasources/home_local_data_source.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeLocalDataSource _localDataSource;
  final IdentityVerificationRepository _identityRepository;

  HomeRepositoryImpl(this._localDataSource, this._identityRepository);

  @override
  Future<Either<Failure, UserEntity>> getUserVerificationStatus(String userId) async {
    try {
      return await _identityRepository.getIdentityVerificationStatus(userId);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user verification status'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasPendingDocuments(String userId) async {
    print('📋 HomeRepository: Checking pending documents for user: $userId');
    try {
      // Check if user has any documents with pending status
      final userResult = await _identityRepository.getIdentityVerificationStatus(userId);

      return userResult.fold(
        (failure) {
          print('❌ HomeRepository: Failed to get identity verification status: ${failure.message}');
          return Left(failure);
        },
        (userEntity) {
          print('👤 HomeRepository: User validation status: ${userEntity.validationStatus.name}');
          print('📄 HomeRepository: User identity document ID: ${userEntity.identityDocumentId}');

          // Check if user has pending validation status (excluding validated users)
          final hasPendingStatus = userEntity.validationStatus == ValidationStatus.pendingId ||
                                  userEntity.validationStatus == ValidationStatus.pendingReview;

          print('📋 HomeRepository: Has pending status: $hasPendingStatus');
          print('📋 HomeRepository: Status breakdown - pendingId: ${userEntity.validationStatus == ValidationStatus.pendingId}, pendingReview: ${userEntity.validationStatus == ValidationStatus.pendingReview}');

          return Right(hasPendingStatus);
        },
      );
    } catch (e) {
      print('❌ HomeRepository: Exception checking pending documents: $e');
      return Left(ServerFailure(message: 'Failed to check pending documents'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getSearchSuggestions(String query) async {
    try {
      // First check cache
      final cached = _localDataSource.getCachedSearchSuggestions(query);
      if (cached.isNotEmpty) {
        return Right(cached);
      }

      // Generate dynamic suggestions based on query
      final suggestions = _generateSearchSuggestions(query);
      
      // Cache the results
      await _localDataSource.cacheSearchSuggestions(query, suggestions);
      
      return Right(suggestions);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to get search suggestions'));
    }
  }

  @override
  Future<Either<Failure, LatLng>> getCurrentLocation() async {
    try {
      // For now, return default location
      // In the future, this could use location services
      final location = _localDataSource.getDefaultLocation();
      return Right(location);
    } catch (e) {
      return Left(LocationFailure('Failed to get current location'));
    }
  }

  List<String> _generateSearchSuggestions(String query) {
    final lowerQuery = query.toLowerCase();
    final allSuggestions = [
      'Security Point',
      'Emergency Services',
      'Police Station',
      'Hospital',
      'Fire Department',
      'Safe Zone',
      'Patrol Area',
      'Surveillance Camera',
      'Emergency Exit',
      'Safe House',
    ];

    return allSuggestions
        .where((suggestion) => suggestion.toLowerCase().contains(lowerQuery))
        .take(5)
        .toList();
  }
}
