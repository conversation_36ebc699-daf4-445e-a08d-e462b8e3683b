import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/datasources/incident_marker_firebase_datasource.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/incident_marker_repository.dart';
import 'package:flutter/foundation.dart';

/// Implementation of incident marker repository
class IncidentMarkerRepositoryImpl implements IncidentMarkerRepository {
  final IncidentMarkerFirebaseDataSource _firebaseDataSource;

  const IncidentMarkerRepositoryImpl(this._firebaseDataSource);

  @override
  Stream<Either<Failure, List<IncidentMarkerEntity>>> watchIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    try {
      return _firebaseDataSource.watchIncidentMarkers(
        centerLat: centerLat,
        centerLng: centerLng,
        radiusKm: radiusKm,
        categoryFilter: categoryFilter,
        severityFilter: severityFilter,
        currentUserId: currentUserId,
      ).map((markers) {
        try {
          // Convert models to entities and update visibility only for old incidents
          final entities = markers.map((marker) {
            // Only recalculate visibility for incidents older than 24 hours to reduce flicker
            final hoursSincePosted = DateTime.now().difference(marker.postedAt).inHours;
            if (hoursSincePosted > 24) {
              return marker.copyWithUpdatedVisibility();
            } else {
              // For recent incidents, keep the current visibility to avoid flicker
              return marker;
            }
          }).where((marker) => marker.isVisible).toList();

          return Right<Failure, List<IncidentMarkerEntity>>(entities);
        } catch (e) {
          debugPrint('Error processing incident markers: $e');
          return Left<Failure, List<IncidentMarkerEntity>>(
            ServerFailure(message: 'Failed to process incident markers: $e'),
          );
        }
      }).handleError((error) {
        debugPrint('Error in watchIncidentMarkers stream: $error');
        return Left<Failure, List<IncidentMarkerEntity>>(
          NetworkFailure(message: 'Failed to watch incident markers: $error'),
        );
      });
    } catch (e) {
      debugPrint('Error setting up watchIncidentMarkers stream: $e');
      return Stream.value(Left(ServerFailure(message: 'Failed to setup incident markers stream: $e')));
    }
  }

  @override
  Stream<Either<Failure, List<IncidentMarkerEntity>>> watchZoneIncidentMarkers({
    required List<String> zoneIds,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    try {
      return _firebaseDataSource.watchZoneIncidentMarkers(
        zoneIds: zoneIds,
        categoryFilter: categoryFilter,
        severityFilter: severityFilter,
        currentUserId: currentUserId,
      ).map((markers) {
        try {
          // Convert models to entities and update visibility only for old incidents
          final entities = markers.map((marker) {
            // Only recalculate visibility for incidents older than 24 hours to reduce flicker
            final hoursSincePosted = DateTime.now().difference(marker.postedAt).inHours;
            if (hoursSincePosted > 24) {
              return marker.copyWithUpdatedVisibility();
            } else {
              // For recent incidents, keep the current visibility to avoid flicker
              return marker;
            }
          }).where((marker) => marker.isVisible).toList();
          
          return Right<Failure, List<IncidentMarkerEntity>>(entities);
        } catch (e) {
          debugPrint('Error processing zone incident markers: $e');
          return Left<Failure, List<IncidentMarkerEntity>>(
            ServerFailure(message: 'Failed to process zone incident markers: $e'),
          );
        }
      }).handleError((error) {
        debugPrint('Error in watchZoneIncidentMarkers stream: $error');
        return Left<Failure, List<IncidentMarkerEntity>>(
          NetworkFailure(message: 'Failed to watch zone incident markers: $error'),
        );
      });
    } catch (e) {
      debugPrint('Error setting up watchZoneIncidentMarkers stream: $e');
      return Stream.value(Left(ServerFailure(message: 'Failed to setup zone incident markers stream: $e')));
    }
  }

  @override
  Future<Either<Failure, List<IncidentMarkerEntity>>> getIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) async {
    try {
      final markers = await _firebaseDataSource.getIncidentMarkers(
        centerLat: centerLat,
        centerLng: centerLng,
        radiusKm: radiusKm,
        categoryFilter: categoryFilter,
        severityFilter: severityFilter,
        currentUserId: currentUserId,
      );

      // Convert models to entities and update visibility only for old incidents
      final entities = markers.map((marker) {
        // Only recalculate visibility for incidents older than 24 hours to reduce flicker
        final hoursSincePosted = DateTime.now().difference(marker.postedAt).inHours;
        if (hoursSincePosted > 24) {
          return marker.copyWithUpdatedVisibility();
        } else {
          // For recent incidents, keep the current visibility to avoid flicker
          return marker;
        }
      }).where((marker) => marker.isVisible).toList();

      return Right(entities);
    } catch (e) {
      debugPrint('Error getting incident markers: $e');
      return Left(ServerFailure(message: 'Failed to get incident markers: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> upsertIncidentMarker(IncidentMarkerEntity marker) async {
    try {
      final model = IncidentMarkerModel.fromEntity(marker);
      await _firebaseDataSource.upsertIncidentMarker(model);
      return const Right(null);
    } catch (e) {
      debugPrint('Error upserting incident marker: $e');
      return Left(ServerFailure(message: 'Failed to upsert incident marker: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteIncidentMarker(String incidentId) async {
    try {
      await _firebaseDataSource.deleteIncidentMarker(incidentId);
      return const Right(null);
    } catch (e) {
      debugPrint('Error deleting incident marker: $e');
      return Left(ServerFailure(message: 'Failed to delete incident marker: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cleanupExpiredMarkers() async {
    try {
      await _firebaseDataSource.cleanupExpiredMarkers();
      return const Right(null);
    } catch (e) {
      debugPrint('Error cleaning up expired markers: $e');
      return Left(ServerFailure(message: 'Failed to cleanup expired markers: $e'));
    }
  }
}
