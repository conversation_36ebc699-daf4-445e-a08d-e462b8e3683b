import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';

/// Repository interface for incident markers
abstract class IncidentMarkerRepository {
  /// Stream incident markers within a geographical area
  Stream<Either<Failure, List<IncidentMarkerEntity>>> watchIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Stream incident markers for specific zones
  Stream<Either<Failure, List<IncidentMarkerEntity>>> watchZoneIncidentMarkers({
    required List<String> zoneIds,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Get incident markers within a geographical area (one-time fetch)
  Future<Either<Failure, List<IncidentMarkerEntity>>> getIncidentMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  });

  /// Create or update an incident marker
  Future<Either<Failure, void>> upsertIncidentMarker(IncidentMarkerEntity marker);

  /// Delete an incident marker
  Future<Either<Failure, void>> deleteIncidentMarker(String incidentId);

  /// Clean up expired markers
  Future<Either<Failure, void>> cleanupExpiredMarkers();
}
