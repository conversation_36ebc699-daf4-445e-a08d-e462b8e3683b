import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/incident_marker_repository.dart';

/// Use case for watching incident markers in real-time
class WatchIncidentMarkersUseCase {
  final IncidentMarkerRepository _repository;

  const WatchIncidentMarkersUseCase(this._repository);

  /// Watch incident markers within a geographical area
  Stream<Either<Failure, List<IncidentMarkerEntity>>> call({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    return _repository.watchIncidentMarkers(
      centerLat: centerLat,
      centerLng: centerLng,
      radiusKm: radiusKm,
      categoryFilter: categoryFilter,
      severityFilter: severityFilter,
      currentUserId: currentUserId,
    );
  }
}

/// Use case for watching zone-specific incident markers
class WatchZoneIncidentMarkersUseCase {
  final IncidentMarkerRepository _repository;

  const WatchZoneIncidentMarkersUseCase(this._repository);

  /// Watch incident markers for specific zones
  Stream<Either<Failure, List<IncidentMarkerEntity>>> call({
    required List<String> zoneIds,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    return _repository.watchZoneIncidentMarkers(
      zoneIds: zoneIds,
      categoryFilter: categoryFilter,
      severityFilter: severityFilter,
      currentUserId: currentUserId,
    );
  }
}

/// Use case for getting incident markers (one-time fetch)
class GetIncidentMarkersUseCase {
  final IncidentMarkerRepository _repository;

  const GetIncidentMarkersUseCase(this._repository);

  /// Get incident markers within a geographical area
  Future<Either<Failure, List<IncidentMarkerEntity>>> call({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
    String? currentUserId,
  }) {
    return _repository.getIncidentMarkers(
      centerLat: centerLat,
      centerLng: centerLng,
      radiusKm: radiusKm,
      categoryFilter: categoryFilter,
      severityFilter: severityFilter,
      currentUserId: currentUserId,
    );
  }
}
