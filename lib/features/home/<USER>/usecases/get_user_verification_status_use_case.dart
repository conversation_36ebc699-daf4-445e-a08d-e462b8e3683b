import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';

class GetUserVerificationStatusUseCase {
  final HomeRepository _repository;

  const GetUserVerificationStatusUseCase(this._repository);

  Future<Either<Failure, UserEntity>> call(String userId) async {
    return await _repository.getUserVerificationStatus(userId);
  }
}
