import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository.dart';

class CheckPendingDocumentsUseCase {
  final HomeRepository _repository;

  const CheckPendingDocumentsUseCase(this._repository);

  Future<Either<Failure, bool>> call(String userId) async {
    return await _repository.hasPendingDocuments(userId);
  }
}
