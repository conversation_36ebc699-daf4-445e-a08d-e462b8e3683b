import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/home/<USER>/repositories/home_repository.dart';

class GetSearchSuggestionsUseCase {
  final HomeRepository _repository;

  const GetSearchSuggestionsUseCase(this._repository);

  Future<Either<Failure, List<String>>> call(String query) async {
    if (query.trim().isEmpty) {
      return const Right([]);
    }
    return await _repository.getSearchSuggestions(query);
  }
}
