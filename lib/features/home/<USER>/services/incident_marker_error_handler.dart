import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// Production-grade error handling for incident marker operations
class IncidentMarkerErrorHandler {
  static const int maxRetries = 3;
  static const Duration baseDelay = Duration(seconds: 1);

  /// Execute operation with exponential backoff retry
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = maxRetries,
    Duration baseDelay = baseDelay,
    String operationName = 'operation',
  }) async {
    int attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;
        
        if (attempt >= maxRetries) {
          debugPrint('❌ $operationName failed after $maxRetries attempts: $e');
          rethrow;
        }
        
        if (_shouldRetry(e)) {
          final delay = _calculateDelay(attempt, baseDelay);
          debugPrint('⚠️ $operationName failed (attempt $attempt/$maxRetries), retrying in ${delay.inSeconds}s: $e');
          await Future.delayed(delay);
        } else {
          debugPrint('❌ $operationName failed with non-retryable error: $e');
          rethrow;
        }
      }
    }
    
    throw Exception('Max retries exceeded for $operationName');
  }

  /// Check if error is retryable
  static bool _shouldRetry(dynamic error) {
    if (error is SocketException) return true;
    if (error is TimeoutException) return true;
    if (error is HttpException) {
      // Retry on server errors (5xx) but not client errors (4xx)
      return error.message.contains('500') || 
             error.message.contains('502') || 
             error.message.contains('503') || 
             error.message.contains('504');
    }
    
    // Firebase specific errors that are retryable
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('network')) return true;
    if (errorString.contains('timeout')) return true;
    if (errorString.contains('unavailable')) return true;
    if (errorString.contains('deadline exceeded')) return true;
    
    return false;
  }

  /// Calculate exponential backoff delay
  static Duration _calculateDelay(int attempt, Duration baseDelay) {
    final multiplier = math.pow(2, attempt - 1).toInt();
    final jitter = math.Random().nextDouble() * 0.1; // Add 10% jitter
    final delayMs = (baseDelay.inMilliseconds * multiplier * (1 + jitter)).round();
    return Duration(milliseconds: delayMs);
  }
}

/// Circuit breaker for incident marker operations
class IncidentMarkerCircuitBreaker {
  final int failureThreshold;
  final Duration timeout;
  final Duration resetTimeout;
  
  int _failureCount = 0;
  DateTime? _lastFailureTime;
  bool _isOpen = false;

  IncidentMarkerCircuitBreaker({
    this.failureThreshold = 5,
    this.timeout = const Duration(seconds: 30),
    this.resetTimeout = const Duration(minutes: 1),
  });

  /// Execute operation through circuit breaker
  Future<T> execute<T>(Future<T> Function() operation) async {
    if (_isOpen) {
      if (_shouldAttemptReset()) {
        _reset();
      } else {
        throw Exception('Circuit breaker is open - operation blocked');
      }
    }

    try {
      final result = await operation();
      _onSuccess();
      return result;
    } catch (e) {
      _onFailure();
      rethrow;
    }
  }

  void _onSuccess() {
    _failureCount = 0;
    _isOpen = false;
  }

  void _onFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();
    
    if (_failureCount >= failureThreshold) {
      _isOpen = true;
      debugPrint('🔴 Circuit breaker opened after $_failureCount failures');
    }
  }

  bool _shouldAttemptReset() {
    if (_lastFailureTime == null) return false;
    return DateTime.now().difference(_lastFailureTime!) > resetTimeout;
  }

  void _reset() {
    _failureCount = 0;
    _isOpen = false;
    _lastFailureTime = null;
    debugPrint('🟢 Circuit breaker reset');
  }

  bool get isOpen => _isOpen;
  int get failureCount => _failureCount;
}
