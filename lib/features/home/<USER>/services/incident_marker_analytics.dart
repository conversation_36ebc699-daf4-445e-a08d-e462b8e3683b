import 'dart:async';
import 'package:flutter/foundation.dart';

/// Production analytics and monitoring for incident markers
class IncidentMarkerAnalytics {
  static final Map<String, int> _operationCounts = {};
  static final Map<String, List<Duration>> _operationDurations = {};
  static final Map<String, int> _errorCounts = {};
  static Timer? _reportingTimer;

  /// Initialize analytics reporting
  static void initialize() {
    // Report metrics every 5 minutes in production
    _reportingTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _reportMetrics(),
    );
  }

  /// Track operation performance
  static Future<T> trackOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      
      _recordSuccess(operationName, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      _recordError(operationName, e);
      rethrow;
    }
  }

  /// Record successful operation
  static void _recordSuccess(String operationName, Duration duration) {
    _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;
    _operationDurations[operationName] ??= [];
    _operationDurations[operationName]!.add(duration);
    
    // Keep only last 100 durations to prevent memory leaks
    if (_operationDurations[operationName]!.length > 100) {
      _operationDurations[operationName]!.removeAt(0);
    }
  }

  /// Record operation error
  static void _recordError(String operationName, dynamic error) {
    final errorKey = '${operationName}_error';
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;
    
    debugPrint('📊 Analytics: Error in $operationName - $error');
  }

  /// Get average duration for operation
  static Duration? getAverageDuration(String operationName) {
    final durations = _operationDurations[operationName];
    if (durations == null || durations.isEmpty) return null;
    
    final totalMs = durations.fold<int>(0, (sum, d) => sum + d.inMilliseconds);
    return Duration(milliseconds: totalMs ~/ durations.length);
  }

  /// Get operation success rate
  static double getSuccessRate(String operationName) {
    final successCount = _operationCounts[operationName] ?? 0;
    final errorCount = _errorCounts['${operationName}_error'] ?? 0;
    final totalCount = successCount + errorCount;
    
    if (totalCount == 0) return 1.0;
    return successCount / totalCount;
  }

  /// Report metrics (integrate with your analytics service)
  static void _reportMetrics() {
    if (kDebugMode) {
      debugPrint('📊 === Incident Marker Analytics Report ===');
      
      for (final operation in _operationCounts.keys) {
        final count = _operationCounts[operation]!;
        final avgDuration = getAverageDuration(operation);
        final successRate = getSuccessRate(operation);
        
        debugPrint('📊 $operation: $count ops, ${avgDuration?.inMilliseconds}ms avg, ${(successRate * 100).toStringAsFixed(1)}% success');
      }
      
      if (_errorCounts.isNotEmpty) {
        debugPrint('📊 Errors:');
        _errorCounts.forEach((error, count) {
          debugPrint('📊   $error: $count');
        });
      }
      
      debugPrint('📊 ==========================================');
    }
    
    // TODO: Send to production analytics service
    // Example: FirebaseAnalytics.instance.logEvent(...)
    // Example: Crashlytics.instance.setCustomKey(...)
  }

  /// Track marker rendering performance
  static void trackMarkerRendering({
    required int markerCount,
    required Duration renderTime,
    required double zoomLevel,
  }) {
    debugPrint('🎨 Marker Rendering: $markerCount markers in ${renderTime.inMilliseconds}ms at zoom $zoomLevel');
    
    // TODO: Send to analytics
    // FirebaseAnalytics.instance.logEvent(
    //   name: 'marker_rendering',
    //   parameters: {
    //     'marker_count': markerCount,
    //     'render_time_ms': renderTime.inMilliseconds,
    //     'zoom_level': zoomLevel,
    //   },
    // );
  }

  /// Track user interactions
  static void trackMarkerInteraction({
    required String interactionType, // 'tap', 'cluster_expand', etc.
    required String incidentCategory,
    required String severity,
  }) {
    debugPrint('👆 Marker Interaction: $interactionType on $incidentCategory ($severity)');
    
    // TODO: Send to analytics
    // FirebaseAnalytics.instance.logEvent(
    //   name: 'marker_interaction',
    //   parameters: {
    //     'interaction_type': interactionType,
    //     'incident_category': incidentCategory,
    //     'severity': severity,
    //   },
    // );
  }

  /// Track data sync performance
  static void trackSyncPerformance({
    required int syncedCount,
    required int failedCount,
    required Duration syncTime,
  }) {
    debugPrint('🔄 Sync Performance: $syncedCount synced, $failedCount failed in ${syncTime.inSeconds}s');
    
    // TODO: Send to analytics
  }

  /// Dispose analytics
  static void dispose() {
    _reportingTimer?.cancel();
    _reportingTimer = null;
  }
}

/// Performance thresholds for alerting
class PerformanceThresholds {
  static const Duration maxMarkerRenderTime = Duration(milliseconds: 500);
  static const Duration maxSyncTime = Duration(seconds: 10);
  static const double minSuccessRate = 0.95; // 95%
  static const int maxMarkersPerView = 100;

  /// Check if performance is within acceptable limits
  static bool isPerformanceAcceptable({
    required Duration renderTime,
    required Duration syncTime,
    required double successRate,
    required int markerCount,
  }) {
    return renderTime <= maxMarkerRenderTime &&
           syncTime <= maxSyncTime &&
           successRate >= minSuccessRate &&
           markerCount <= maxMarkersPerView;
  }
}
