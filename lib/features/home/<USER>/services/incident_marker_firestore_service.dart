import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';

/// Production-grade Firestore service for geospatial incident marker queries
/// Uses geoflutterfire_plus for efficient geospatial operations
class IncidentMarkerFirestoreService {
  final FirebaseFirestore _firestore;

  static const String _collectionName = 'incident_markers_geo';
  static const String _geoFieldName = 'position';

  IncidentMarkerFirestoreService(this._firestore);

  /// Stream incident markers within a geographical radius using geoflutterfire_plus
  Stream<List<IncidentMarkerModel>> streamMarkersInRadius({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
  }) {
    try {
      debugPrint('🌍 Streaming markers in radius: ($centerLat, $centerLng) ${radiusKm}km');

      // Create GeoFirePoint for the center
      final center = GeoFirePoint(GeoPoint(centerLat, centerLng));

      // Create geo query using geoflutterfire_plus v0.0.21 API
      final geoQuery = GeoCollectionReference(_firestore.collection(_collectionName))
          .subscribeWithin(
            center: center,
            radiusInKm: radiusKm,
            field: _geoFieldName,
            geopointFrom: (data) => (data[_geoFieldName] as Map<String, dynamic>)['geopoint'] as GeoPoint,
          );

      return geoQuery.map((docs) {
        final markers = <IncidentMarkerModel>[];

        for (final doc in docs) {
          try {
            final data = doc.data() as Map<String, dynamic>;
            final marker = _createMarkerFromFirestore(data);

            // Apply additional filters
            if (categoryFilter != null && marker.categoryKey != categoryFilter) continue;
            if (severityFilter != null && marker.severity != severityFilter) continue;

            // Check visibility (time decay)
            if (marker.isVisible) {
              markers.add(marker);
            }
          } catch (e) {
            debugPrint('❌ Error parsing marker from Firestore: $e');
          }
        }

        debugPrint('✅ Found ${markers.length} markers in radius');
        return markers;
      });

    } catch (e) {
      debugPrint('❌ Error in streamMarkersInRadius: $e');
      return Stream.value([]);
    }
  }

  /// Get markers within radius (one-time fetch)
  Future<List<IncidentMarkerModel>> getMarkersInRadius({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
    String? categoryFilter,
    String? severityFilter,
  }) async {
    try {
      debugPrint('🌍 Getting markers in radius: ($centerLat, $centerLng) ${radiusKm}km');

      // Create GeoFirePoint for the center
      final center = GeoFirePoint(GeoPoint(centerLat, centerLng));

      // Get documents within radius
      final docs = await GeoCollectionReference(_firestore.collection(_collectionName))
          .fetchWithin(
            center: center,
            radiusInKm: radiusKm,
            field: _geoFieldName,
            geopointFrom: (data) => (data[_geoFieldName] as Map<String, dynamic>)['geopoint'] as GeoPoint,
          );

      final markers = <IncidentMarkerModel>[];

      for (final doc in docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          final marker = _createMarkerFromFirestore(data);

          // Apply additional filters
          if (categoryFilter != null && marker.categoryKey != categoryFilter) continue;
          if (severityFilter != null && marker.severity != severityFilter) continue;

          // Check visibility (time decay)
          if (marker.isVisible) {
            markers.add(marker);
          }
        } catch (e) {
          debugPrint('❌ Error parsing marker from Firestore: $e');
        }
      }

      debugPrint('✅ Found ${markers.length} markers in radius');
      return markers;

    } catch (e) {
      debugPrint('❌ Error in getMarkersInRadius: $e');
      return [];
    }
  }

  /// Store incident marker with geospatial data
  Future<void> storeMarker(IncidentMarkerModel marker) async {
    try {
      // Create GeoFirePoint for the marker location
      final geoPoint = GeoFirePoint(GeoPoint(
        marker.location.latitude,
        marker.location.longitude,
      ));

      final data = marker.toFirestore();
      data[_geoFieldName] = geoPoint.data;

      await _firestore
          .collection(_collectionName)
          .doc(marker.incidentId)
          .set(data);

      debugPrint('✅ Stored marker with geospatial data: ${marker.incidentId}');

    } catch (e) {
      debugPrint('❌ Error storing marker: $e');
      throw Exception('Failed to store marker: $e');
    }
  }

  /// Helper method to create marker from Firestore data
  IncidentMarkerModel _createMarkerFromFirestore(Map<String, dynamic> data) {
    // Convert Firestore timestamps if needed
    if (data['createdAt'] is Timestamp) {
      data['createdAt'] = (data['createdAt'] as Timestamp).toDate().toIso8601String();
    }
    if (data['updatedAt'] is Timestamp) {
      data['updatedAt'] = (data['updatedAt'] as Timestamp).toDate().toIso8601String();
    }

    return IncidentMarkerModel.fromRealtimeDB(data);
  }

  /// Delete incident marker
  Future<void> deleteMarker(String incidentId) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(incidentId)
          .delete();
      
      debugPrint('✅ Deleted marker: $incidentId');
    } catch (e) {
      debugPrint('❌ Error deleting marker: $e');
      throw Exception('Failed to delete marker: $e');
    }
  }

  /// Query markers by category within a bounding box
  Future<List<IncidentMarkerModel>> getMarkersByCategory({
    required String categoryKey,
    required double northLat,
    required double southLat,
    required double eastLng,
    required double westLng,
  }) async {
    try {
      debugPrint('🌍 Getting markers by category: $categoryKey in bounding box');
      
      // Use Firestore compound queries for category + location
      final query = _firestore
          .collection(_collectionName)
          .where('categoryKey', isEqualTo: categoryKey)
          .where('location.latitude', isGreaterThanOrEqualTo: southLat)
          .where('location.latitude', isLessThanOrEqualTo: northLat);
      
      final snapshot = await query.get();
      final markers = <IncidentMarkerModel>[];
      
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          final marker = _createMarkerFromFirestore(data);
          
          // Additional longitude filtering (Firestore limitation)
          if (marker.location.longitude >= westLng && 
              marker.location.longitude <= eastLng &&
              marker.isVisible) {
            markers.add(marker);
          }
        } catch (e) {
          debugPrint('❌ Error parsing marker: $e');
        }
      }
      
      debugPrint('✅ Found ${markers.length} markers for category $categoryKey');
      return markers;
      
    } catch (e) {
      debugPrint('❌ Error getting markers by category: $e');
      return [];
    }
  }



  /// Clean up expired markers
  Future<void> cleanupExpiredMarkers() async {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 72));
      
      final query = _firestore
          .collection(_collectionName)
          .where('postedAt', isLessThan: cutoffTime.toIso8601String());
      
      final snapshot = await query.get();
      
      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      
      debugPrint('🧹 Cleaned up ${snapshot.docs.length} expired markers from Firestore');
    } catch (e) {
      debugPrint('❌ Error cleaning up expired markers: $e');
    }
  }
}

/// Extension to add Firestore serialization to IncidentMarkerModel
extension IncidentMarkerModelFirestore on IncidentMarkerModel {
  /// Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    final data = toMap();

    // Add Firestore-specific fields
    data['createdAt'] = FieldValue.serverTimestamp();
    data['updatedAt'] = FieldValue.serverTimestamp();

    return data;
  }
}
