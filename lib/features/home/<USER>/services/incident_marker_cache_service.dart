import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';

/// Production-grade caching for incident markers with offline support
class IncidentMarkerCacheService {
  static const String _cacheKeyPrefix = 'incident_markers_';
  static const String _lastUpdateKey = 'incident_markers_last_update';
  static const Duration _cacheExpiry = Duration(hours: 1);
  static const int _maxCacheSize = 1000; // Maximum markers to cache
  
  final SharedPreferences _prefs;
  final Map<String, IncidentMarkerModel> _memoryCache = {};
  Timer? _cleanupTimer;

  IncidentMarkerCacheService(this._prefs) {
    _startPeriodicCleanup();
  }

  /// Cache incident markers for a specific area
  Future<void> cacheMarkers({
    required List<IncidentMarkerModel> markers,
    required double centerLat,
    required double centerLng,
    required double radiusKm,
  }) async {
    try {
      final cacheKey = _generateCacheKey(centerLat, centerLng, radiusKm);
      
      // Update memory cache
      for (final marker in markers) {
        _memoryCache[marker.incidentId] = marker;
      }
      
      // Limit memory cache size
      _limitMemoryCacheSize();
      
      // Prepare data for persistent cache
      final cacheData = {
        'markers': markers.map((m) => m.toMap()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'centerLat': centerLat,
        'centerLng': centerLng,
        'radiusKm': radiusKm,
      };
      
      // Save to persistent cache
      await _prefs.setString(cacheKey, jsonEncode(cacheData));
      await _prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
      
      debugPrint('💾 Cached ${markers.length} markers for area ($centerLat, $centerLng, ${radiusKm}km)');
    } catch (e) {
      debugPrint('❌ Error caching markers: $e');
    }
  }

  /// Get cached markers for a specific area
  Future<List<IncidentMarkerModel>?> getCachedMarkers({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
  }) async {
    try {
      final cacheKey = _generateCacheKey(centerLat, centerLng, radiusKm);
      
      // Check persistent cache first
      final cachedData = _prefs.getString(cacheKey);
      if (cachedData == null) {
        debugPrint('💾 No cached data found for area');
        return null;
      }
      
      final data = jsonDecode(cachedData) as Map<String, dynamic>;
      final timestamp = data['timestamp'] as int;
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      
      // Check if cache is expired
      if (cacheAge > _cacheExpiry.inMilliseconds) {
        debugPrint('💾 Cache expired for area (age: ${Duration(milliseconds: cacheAge).inMinutes}min)');
        await _prefs.remove(cacheKey);
        return null;
      }
      
      // Parse cached markers
      final markersData = data['markers'] as List<dynamic>;
      final markers = markersData
          .map((m) => IncidentMarkerModel.fromRealtimeDB(Map<String, dynamic>.from(m)))
          .toList();
      
      // Update memory cache
      for (final marker in markers) {
        _memoryCache[marker.incidentId] = marker;
      }
      
      debugPrint('💾 Retrieved ${markers.length} cached markers for area');
      return markers;
    } catch (e) {
      debugPrint('❌ Error retrieving cached markers: $e');
      return null;
    }
  }

  /// Get a specific marker from cache
  IncidentMarkerModel? getCachedMarker(String incidentId) {
    return _memoryCache[incidentId];
  }

  /// Check if we have recent cached data for an area
  Future<bool> hasFreshCache({
    required double centerLat,
    required double centerLng,
    required double radiusKm,
  }) async {
    try {
      final cacheKey = _generateCacheKey(centerLat, centerLng, radiusKm);
      final cachedData = _prefs.getString(cacheKey);
      
      if (cachedData == null) return false;
      
      final data = jsonDecode(cachedData) as Map<String, dynamic>;
      final timestamp = data['timestamp'] as int;
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      
      return cacheAge <= _cacheExpiry.inMilliseconds;
    } catch (e) {
      debugPrint('❌ Error checking cache freshness: $e');
      return false;
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      _memoryCache.clear();
      
      // Remove all incident marker cache keys
      final keys = _prefs.getKeys()
          .where((key) => key.startsWith(_cacheKeyPrefix))
          .toList();
      
      for (final key in keys) {
        await _prefs.remove(key);
      }
      
      await _prefs.remove(_lastUpdateKey);
      
      debugPrint('💾 Cleared all incident marker cache');
    } catch (e) {
      debugPrint('❌ Error clearing cache: $e');
    }
  }

  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final keys = _prefs.getKeys()
          .where((key) => key.startsWith(_cacheKeyPrefix))
          .toList();
      
      int removedCount = 0;
      
      for (final key in keys) {
        final cachedData = _prefs.getString(key);
        if (cachedData != null) {
          try {
            final data = jsonDecode(cachedData) as Map<String, dynamic>;
            final timestamp = data['timestamp'] as int;
            final cacheAge = now - timestamp;
            
            if (cacheAge > _cacheExpiry.inMilliseconds) {
              await _prefs.remove(key);
              removedCount++;
            }
          } catch (e) {
            // Remove corrupted cache entries
            await _prefs.remove(key);
            removedCount++;
          }
        }
      }
      
      if (removedCount > 0) {
        debugPrint('💾 Removed $removedCount expired cache entries');
      }
    } catch (e) {
      debugPrint('❌ Error clearing expired cache: $e');
    }
  }

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics() async {
    try {
      final keys = _prefs.getKeys()
          .where((key) => key.startsWith(_cacheKeyPrefix))
          .toList();
      
      int totalMarkers = 0;
      int expiredEntries = 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      
      for (final key in keys) {
        final cachedData = _prefs.getString(key);
        if (cachedData != null) {
          try {
            final data = jsonDecode(cachedData) as Map<String, dynamic>;
            final timestamp = data['timestamp'] as int;
            final cacheAge = now - timestamp;
            
            if (cacheAge > _cacheExpiry.inMilliseconds) {
              expiredEntries++;
            } else {
              final markersData = data['markers'] as List<dynamic>;
              totalMarkers += markersData.length;
            }
          } catch (e) {
            expiredEntries++;
          }
        }
      }
      
      return CacheStatistics(
        totalCacheEntries: keys.length,
        totalCachedMarkers: totalMarkers,
        memoryCachedMarkers: _memoryCache.length,
        expiredEntries: expiredEntries,
      );
    } catch (e) {
      debugPrint('❌ Error getting cache statistics: $e');
      return CacheStatistics(
        totalCacheEntries: 0,
        totalCachedMarkers: 0,
        memoryCachedMarkers: _memoryCache.length,
        expiredEntries: 0,
      );
    }
  }

  /// Generate cache key for geographic area
  String _generateCacheKey(double centerLat, double centerLng, double radiusKm) {
    // Round coordinates to reduce cache fragmentation
    final lat = (centerLat * 100).round() / 100;
    final lng = (centerLng * 100).round() / 100;
    final radius = (radiusKm * 10).round() / 10;
    
    return '${_cacheKeyPrefix}${lat}_${lng}_$radius';
  }

  /// Limit memory cache size to prevent memory issues
  void _limitMemoryCacheSize() {
    if (_memoryCache.length > _maxCacheSize) {
      // Remove oldest entries (simple LRU approximation)
      final keysToRemove = _memoryCache.keys.take(_memoryCache.length - _maxCacheSize);
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
      }
      debugPrint('💾 Limited memory cache size to $_maxCacheSize entries');
    }
  }

  /// Start periodic cleanup of expired cache
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 1),
      (_) => clearExpiredCache(),
    );
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _memoryCache.clear();
  }
}

/// Cache statistics information
class CacheStatistics {
  final int totalCacheEntries;
  final int totalCachedMarkers;
  final int memoryCachedMarkers;
  final int expiredEntries;

  const CacheStatistics({
    required this.totalCacheEntries,
    required this.totalCachedMarkers,
    required this.memoryCachedMarkers,
    required this.expiredEntries,
  });

  @override
  String toString() {
    return 'CacheStatistics(entries: $totalCacheEntries, markers: $totalCachedMarkers, memory: $memoryCachedMarkers, expired: $expiredEntries)';
  }
}
