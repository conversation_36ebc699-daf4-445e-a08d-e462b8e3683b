import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:respublicaseguridad/features/incidents/data/models/incident_model.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';
import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:flutter/foundation.dart';

/// Service to sync incidents from Firestore to Realtime Database as optimized map markers
class IncidentMarkerSyncService {
  final FirebaseFirestore _firestore;
  final FirebaseDatabase _database;
  final FirebaseAuth _auth;

  StreamSubscription? _publicIncidentSubscription;
  StreamSubscription? _userIncidentSubscription;

  IncidentMarkerSyncService({
    required FirebaseFirestore firestore,
    required FirebaseDatabase database,
    required FirebaseAuth auth,
  }) : _firestore = firestore,
       _database = database,
       _auth = auth;

  /// Start syncing incidents to map markers
  void startSync() {
    debugPrint('🔄 IncidentMarkerSyncService: Starting sync');

    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      debugPrint('❌ IncidentMarkerSyncService: No authenticated user, cannot start sync');
      return;
    }

    // Listen to public incidents (visible to community)
    _publicIncidentSubscription = _firestore
        .collection('incidents')
        .where('isBlocked', isEqualTo: false)
        .where('status', isEqualTo: 'active')
        .where('visibilityStatus', isEqualTo: 'visibleToCommunity')
        .snapshots()
        .listen(
          _handleIncidentChanges,
          onError: (error) {
            debugPrint('❌ IncidentMarkerSyncService: Error in public incident stream: $error');
          },
        );

    // Listen to user's own incidents (regardless of visibility)
    _userIncidentSubscription = _firestore
        .collection('incidents')
        .where('isBlocked', isEqualTo: false)
        .where('status', isEqualTo: 'active')
        .where('userId', isEqualTo: currentUser.uid)
        .snapshots()
        .listen(
          _handleIncidentChanges,
          onError: (error) {
            debugPrint('❌ IncidentMarkerSyncService: Error in user incident stream: $error');
          },
        );

    debugPrint('✅ IncidentMarkerSyncService: Stream listeners set up successfully');
  }

  /// Stop syncing incidents to map markers
  void stopSync() {
    debugPrint('🔄 IncidentMarkerSyncService: Stopping sync');
    _publicIncidentSubscription?.cancel();
    _publicIncidentSubscription = null;
    _userIncidentSubscription?.cancel();
    _userIncidentSubscription = null;
    debugPrint('✅ IncidentMarkerSyncService: Sync stopped');
  }

  /// Manual sync for testing - processes all incidents once
  Future<void> manualSync() async {
    try {
      debugPrint('🔄 IncidentMarkerSyncService: Starting manual sync');

      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ IncidentMarkerSyncService: No authenticated user, cannot perform manual sync');
        return;
      }

      // Sync public incidents
      final publicSnapshot = await _firestore
          .collection('incidents')
          .where('isBlocked', isEqualTo: false)
          .where('status', isEqualTo: 'active')
          .where('visibilityStatus', isEqualTo: 'visibleToCommunity')
          .get();

      debugPrint('🔄 IncidentMarkerSyncService: Found ${publicSnapshot.docs.length} public incidents for manual sync');

      for (final doc in publicSnapshot.docs) {
        try {
          debugPrint('🔄 IncidentMarkerSyncService: Manually syncing public incident ${doc.id}');
          await _syncIncidentToMarker(doc);
        } catch (e) {
          debugPrint('❌ IncidentMarkerSyncService: Error in manual sync for public incident ${doc.id}: $e');
        }
      }

      // Sync user's own incidents
      final userSnapshot = await _firestore
          .collection('incidents')
          .where('isBlocked', isEqualTo: false)
          .where('status', isEqualTo: 'active')
          .where('userId', isEqualTo: currentUser.uid)
          .get();

      debugPrint('🔄 IncidentMarkerSyncService: Found ${userSnapshot.docs.length} user incidents for manual sync');

      for (final doc in userSnapshot.docs) {
        try {
          debugPrint('🔄 IncidentMarkerSyncService: Manually syncing user incident ${doc.id}');
          await _syncIncidentToMarker(doc);
        } catch (e) {
          debugPrint('❌ IncidentMarkerSyncService: Error in manual sync for user incident ${doc.id}: $e');
        }
      }

      debugPrint('✅ IncidentMarkerSyncService: Manual sync completed');
    } catch (e) {
      debugPrint('❌ IncidentMarkerSyncService: Manual sync failed: $e');
    }
  }

  /// Handle incident changes from Firestore
  void _handleIncidentChanges(QuerySnapshot<Map<String, dynamic>> snapshot) async {
    debugPrint('🔄 IncidentMarkerSyncService: Processing ${snapshot.docs.length} incidents');
    debugPrint('🔄 IncidentMarkerSyncService: Document changes: ${snapshot.docChanges.length}');

    if (snapshot.docs.isEmpty) {
      debugPrint('⚠️ IncidentMarkerSyncService: No incidents found matching filters');
      return;
    }

    for (final change in snapshot.docChanges) {
      try {
        debugPrint('🔄 IncidentMarkerSyncService: Processing ${change.type.name} for incident ${change.doc.id}');
        switch (change.type) {
          case DocumentChangeType.added:
          case DocumentChangeType.modified:
            await _syncIncidentToMarker(change.doc);
            break;
          case DocumentChangeType.removed:
            await _removeIncidentMarker(change.doc.id);
            break;
        }
      } catch (e) {
        debugPrint('❌ IncidentMarkerSyncService: Error processing incident ${change.doc.id}: $e');
      }
    }
  }

  /// Sync a single incident to map marker
  Future<void> _syncIncidentToMarker(DocumentSnapshot<Map<String, dynamic>> doc) async {
    try {
      final incident = IncidentModel.fromFirestore(doc);
      
      // Check if incident should be visible (time decay)
      final visibilityPercentage = IncidentMarkerEntity.calculateVisibilityPercentage(incident.postedAt);
      if (visibilityPercentage <= 0) {
        // Remove expired marker
        await _removeIncidentMarker(incident.incidentId);
        return;
      }

      // Get category information (you might want to cache this)
      final categoryInfo = await _getCategoryInfo(incident.categoryKey);

      // Get the first image from incident media for the marker
      String? markerImageUrl;
      if (incident.media.isNotEmpty) {
        // Find the first image in the media list
        final firstImage = incident.media.firstWhere(
          (media) => media.isImage,
          orElse: () => incident.media.first, // Fallback to first media if no images
        );
        markerImageUrl = firstImage.displayUrl; // Use displayUrl which handles thumbnails for videos
      }

      // Create optimized marker
      final marker = IncidentMarkerModel(
        incidentId: incident.incidentId,
        categoryKey: incident.categoryKey,
        categoryTitle: incident.categoryTitle,
        subcategoryKey: incident.subcategoryKey,
        subcategoryTitle: incident.subcategoryTitle,
        severity: incident.severity ?? 'Leve',
        location: incident.location,
        postedAt: incident.postedAt,
        isAnonymous: incident.isAnonymous,
        zoneId: incident.zoneId,
        currentVisibilityPercentage: visibilityPercentage,
        isHighSeverity: _isHighSeverity(incident.categoryKey, incident.severity),
        iconName: categoryInfo['iconName'] ?? 'incident',
        color: categoryInfo['color'] ?? '#FF0000',
        userId: incident.isAnonymous ? null : incident.userId,
        imageUrl: markerImageUrl, // Use incident's media instead of category imageUrl
      );

      // Save to Realtime Database
      await _saveMarkerToRealtimeDB(marker);
      
      debugPrint('✅ IncidentMarkerSyncService: Synced marker ${marker.incidentId}');
    } catch (e) {
      debugPrint('❌ IncidentMarkerSyncService: Error syncing incident ${doc.id}: $e');
    }
  }

  /// Save marker to Realtime Database
  Future<void> _saveMarkerToRealtimeDB(IncidentMarkerModel marker) async {
    final markerData = marker.toRealtimeDB();
    
    // Save to main markers collection
    final markerRef = _database.ref('incident_markers').child(marker.incidentId);
    await markerRef.set(markerData);
    
    // Save to zone-specific collection if zoneId exists
    if (marker.zoneId != null) {
      final zoneMarkerRef = _database.ref('zone_incident_markers')
          .child(marker.zoneId!)
          .child(marker.incidentId);
      await zoneMarkerRef.set(markerData);
    }
  }

  /// Remove incident marker from Realtime Database
  Future<void> _removeIncidentMarker(String incidentId) async {
    try {
      // Remove from main markers collection
      final markerRef = _database.ref('incident_markers').child(incidentId);
      await markerRef.remove();
      
      // Remove from zone collections (we'd need to track which zones to clean up)
      // For now, we'll rely on periodic cleanup
      
      debugPrint('🗑️ IncidentMarkerSyncService: Removed marker $incidentId');
    } catch (e) {
      debugPrint('❌ IncidentMarkerSyncService: Error removing marker $incidentId: $e');
    }
  }

  /// Get category information (icon, color) - this should be cached in production
  Future<Map<String, dynamic>> _getCategoryInfo(String categoryKey) async {
    try {
      final categoryDoc = await _firestore
          .collection('incident_categories')
          .where('key', isEqualTo: categoryKey)
          .limit(1)
          .get();
      
      if (categoryDoc.docs.isNotEmpty) {
        final data = categoryDoc.docs.first.data();
        return {
          'iconName': data['iconName'] ?? 'incident',
          'color': data['color'] ?? '#FF0000',
          'imageUrl': data['imageUrl'],
        };
      }
    } catch (e) {
      debugPrint('❌ IncidentMarkerSyncService: Error getting category info for $categoryKey: $e');
    }
    
    // Default values
    return {
      'iconName': 'incident',
      'color': '#FF0000',
      'imageUrl': null,
    };
  }

  /// Determine if incident is high severity based on category and severity
  bool _isHighSeverity(String categoryKey, String? severity) {
    // High severity categories
    const highSeverityCategories = [
      'violencia', // Violence
      'robo', // Theft
      'drogas', // Drugs
    ];
    
    // Always high severity for these categories
    if (highSeverityCategories.contains(categoryKey.toLowerCase())) {
      return true;
    }
    
    // High severity if marked as 'Grave'
    return severity == 'Grave';
  }

  /// Clean up expired markers (should be called periodically)
  Future<void> cleanupExpiredMarkers() async {
    try {
      debugPrint('🧹 IncidentMarkerSyncService: Starting cleanup of expired markers');
      
      final markersRef = _database.ref('incident_markers');
      final snapshot = await markersRef.get();
      
      if (!snapshot.exists) return;
      
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final expiredMarkers = <String>[];
      
      for (final entry in data.entries) {
        try {
          final markerData = Map<String, dynamic>.from(entry.value as Map);
          final postedAt = DateTime.parse(markerData['postedAt'] as String);
          final visibility = IncidentMarkerEntity.calculateVisibilityPercentage(postedAt);
          
          if (visibility <= 0) {
            expiredMarkers.add(entry.key);
          }
        } catch (e) {
          debugPrint('❌ IncidentMarkerSyncService: Error checking marker ${entry.key}: $e');
          // Remove malformed markers
          expiredMarkers.add(entry.key);
        }
      }
      
      // Remove expired markers
      for (final incidentId in expiredMarkers) {
        await _removeIncidentMarker(incidentId);
      }
      
      debugPrint('🧹 IncidentMarkerSyncService: Cleaned up ${expiredMarkers.length} expired markers');
    } catch (e) {
      debugPrint('❌ IncidentMarkerSyncService: Error during cleanup: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    stopSync();
  }
}
