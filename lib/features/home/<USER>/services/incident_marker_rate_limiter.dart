import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

/// Production-grade rate limiting for incident marker operations
class IncidentMarkerRateLimiter {
  final FirebaseDatabase _database;
  final String _userId;
  
  // Rate limits
  static const int maxDailyMarkers = 50;
  static const int maxHourlyMarkers = 10;
  static const int maxMarkersPerMinute = 3;
  
  // Cache for local rate limiting
  final List<DateTime> _recentOperations = [];
  
  IncidentMarkerRateLimiter({
    required FirebaseDatabase database,
    required String userId,
  }) : _database = database,
       _userId = userId;

  /// Check if user can perform marker operation
  Future<bool> canPerformOperation() async {
    try {
      // Check local rate limits first (faster)
      if (!_checkLocalRateLimit()) {
        debugPrint('🚫 Rate limit exceeded: Too many operations per minute');
        return false;
      }
      
      // Check server-side rate limits
      final serverLimits = await _checkServerRateLimit();
      if (!serverLimits) {
        debugPrint('🚫 Rate limit exceeded: Server-side limits');
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('❌ Error checking rate limits: $e');
      // Fail safe: allow operation if rate limit check fails
      return true;
    }
  }

  /// Record successful operation
  Future<void> recordOperation() async {
    try {
      // Record locally
      _recentOperations.add(DateTime.now());
      _cleanupOldOperations();
      
      // Record on server
      await _updateServerRateLimit();
    } catch (e) {
      debugPrint('❌ Error recording operation: $e');
    }
  }

  /// Check local rate limits (per minute)
  bool _checkLocalRateLimit() {
    _cleanupOldOperations();
    
    final now = DateTime.now();
    final oneMinuteAgo = now.subtract(const Duration(minutes: 1));
    
    final recentCount = _recentOperations
        .where((op) => op.isAfter(oneMinuteAgo))
        .length;
    
    return recentCount < maxMarkersPerMinute;
  }

  /// Clean up old operations from local cache
  void _cleanupOldOperations() {
    final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
    _recentOperations.removeWhere((op) => op.isBefore(oneHourAgo));
  }

  /// Check server-side rate limits
  Future<bool> _checkServerRateLimit() async {
    final rateLimitRef = _database.ref('incident_marker_rate_limits').child(_userId);
    final snapshot = await rateLimitRef.get();
    
    if (!snapshot.exists) {
      // First time user - allow operation
      return true;
    }
    
    final data = Map<String, dynamic>.from(snapshot.value as Map);
    final now = DateTime.now();
    
    // Check daily limit
    final lastUpdate = DateTime.tryParse(data['lastUpdate'] ?? '');
    final dailyCount = data['dailyCount'] as int? ?? 0;
    
    if (lastUpdate != null && _isSameDay(lastUpdate, now)) {
      if (dailyCount >= maxDailyMarkers) {
        return false;
      }
    }
    
    // Check hourly limit
    final hourlyCount = data['hourlyCount'] as int? ?? 0;
    final lastHourUpdate = DateTime.tryParse(data['lastHourUpdate'] ?? '');
    
    if (lastHourUpdate != null && _isSameHour(lastHourUpdate, now)) {
      if (hourlyCount >= maxHourlyMarkers) {
        return false;
      }
    }
    
    return true;
  }

  /// Update server-side rate limit counters
  Future<void> _updateServerRateLimit() async {
    final rateLimitRef = _database.ref('incident_marker_rate_limits').child(_userId);
    final snapshot = await rateLimitRef.get();
    
    final now = DateTime.now();
    final nowString = now.toIso8601String();
    
    Map<String, dynamic> data = {};
    
    if (snapshot.exists) {
      data = Map<String, dynamic>.from(snapshot.value as Map);
    }
    
    // Update daily count
    final lastUpdate = DateTime.tryParse(data['lastUpdate'] ?? '');
    if (lastUpdate == null || !_isSameDay(lastUpdate, now)) {
      // New day - reset daily count
      data['dailyCount'] = 1;
    } else {
      data['dailyCount'] = (data['dailyCount'] as int? ?? 0) + 1;
    }
    
    // Update hourly count
    final lastHourUpdate = DateTime.tryParse(data['lastHourUpdate'] ?? '');
    if (lastHourUpdate == null || !_isSameHour(lastHourUpdate, now)) {
      // New hour - reset hourly count
      data['hourlyCount'] = 1;
      data['lastHourUpdate'] = nowString;
    } else {
      data['hourlyCount'] = (data['hourlyCount'] as int? ?? 0) + 1;
    }
    
    data['lastUpdate'] = nowString;
    
    await rateLimitRef.set(data);
  }

  /// Check if two dates are on the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Check if two dates are in the same hour
  bool _isSameHour(DateTime date1, DateTime date2) {
    return _isSameDay(date1, date2) && date1.hour == date2.hour;
  }

  /// Get remaining operations for user
  Future<RateLimitStatus> getRemainingOperations() async {
    try {
      final rateLimitRef = _database.ref('incident_marker_rate_limits').child(_userId);
      final snapshot = await rateLimitRef.get();
      
      if (!snapshot.exists) {
        return RateLimitStatus(
          dailyRemaining: maxDailyMarkers,
          hourlyRemaining: maxHourlyMarkers,
          minuteRemaining: maxMarkersPerMinute,
        );
      }
      
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final now = DateTime.now();
      
      // Calculate daily remaining
      final lastUpdate = DateTime.tryParse(data['lastUpdate'] ?? '');
      final dailyCount = data['dailyCount'] as int? ?? 0;
      int dailyRemaining = maxDailyMarkers;
      
      if (lastUpdate != null && _isSameDay(lastUpdate, now)) {
        dailyRemaining = maxDailyMarkers - dailyCount;
      }
      
      // Calculate hourly remaining
      final lastHourUpdate = DateTime.tryParse(data['lastHourUpdate'] ?? '');
      final hourlyCount = data['hourlyCount'] as int? ?? 0;
      int hourlyRemaining = maxHourlyMarkers;
      
      if (lastHourUpdate != null && _isSameHour(lastHourUpdate, now)) {
        hourlyRemaining = maxHourlyMarkers - hourlyCount;
      }
      
      // Calculate minute remaining
      _cleanupOldOperations();
      final oneMinuteAgo = now.subtract(const Duration(minutes: 1));
      final recentCount = _recentOperations
          .where((op) => op.isAfter(oneMinuteAgo))
          .length;
      final minuteRemaining = maxMarkersPerMinute - recentCount;
      
      return RateLimitStatus(
        dailyRemaining: dailyRemaining.clamp(0, maxDailyMarkers),
        hourlyRemaining: hourlyRemaining.clamp(0, maxHourlyMarkers),
        minuteRemaining: minuteRemaining.clamp(0, maxMarkersPerMinute),
      );
    } catch (e) {
      debugPrint('❌ Error getting rate limit status: $e');
      return RateLimitStatus(
        dailyRemaining: maxDailyMarkers,
        hourlyRemaining: maxHourlyMarkers,
        minuteRemaining: maxMarkersPerMinute,
      );
    }
  }
}

/// Rate limit status information
class RateLimitStatus {
  final int dailyRemaining;
  final int hourlyRemaining;
  final int minuteRemaining;

  const RateLimitStatus({
    required this.dailyRemaining,
    required this.hourlyRemaining,
    required this.minuteRemaining,
  });

  bool get canPerformOperation => 
      dailyRemaining > 0 && hourlyRemaining > 0 && minuteRemaining > 0;

  @override
  String toString() {
    return 'RateLimitStatus(daily: $dailyRemaining, hourly: $hourlyRemaining, minute: $minuteRemaining)';
  }
}
