import 'package:respublicaseguridad/features/home/<USER>/entities/incident_marker_entity.dart';
import 'package:respublicaseguridad/features/incidents/data/models/location_model.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_enums.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for incident markers with Firebase Realtime Database serialization
class IncidentMarkerModel extends IncidentMarkerEntity {
  const IncidentMarkerModel({
    required super.incidentId,
    required super.categoryKey,
    required super.categoryTitle,
    super.subcategoryKey,
    super.subcategoryTitle,
    required super.severity,
    required super.location,
    required super.postedAt,
    required super.isAnonymous,
    super.zoneId,
    required super.currentVisibilityPercentage,
    required super.isHighSeverity,
    required super.iconName,
    required super.color,
    super.userId,
    super.imageUrl,
    super.visibilityStatus,
  });

  /// Create model from entity
  factory IncidentMarkerModel.fromEntity(IncidentMarkerEntity entity) {
    return IncidentMarkerModel(
      incidentId: entity.incidentId,
      categoryKey: entity.categoryKey,
      categoryTitle: entity.categoryTitle,
      subcategoryKey: entity.subcategoryKey,
      subcategoryTitle: entity.subcategoryTitle,
      severity: entity.severity,
      location: entity.location,
      postedAt: entity.postedAt,
      isAnonymous: entity.isAnonymous,
      zoneId: entity.zoneId,
      currentVisibilityPercentage: entity.currentVisibilityPercentage,
      isHighSeverity: entity.isHighSeverity,
      iconName: entity.iconName,
      color: entity.color,
      userId: entity.userId,
      imageUrl: entity.imageUrl,
      visibilityStatus: entity.visibilityStatus,
    );
  }

  /// Create model from Firebase Realtime Database data with validation
  factory IncidentMarkerModel.fromRealtimeDB(Map<String, dynamic> data) {
    // Validate required fields
    _validateRequiredFields(data);

    final locationData = Map<String, dynamic>.from(data['location'] ?? {});
    final location = LocationModel.fromMap(locationData);

    // Validate and parse timestamp
    final postedAtString = data['postedAt'] as String;
    final postedAt = _parseAndValidateDateTime(postedAtString);

    // Calculate current visibility percentage
    final currentVisibility = IncidentMarkerEntity.calculateVisibilityPercentage(postedAt);

    // Validate and sanitize data
    final incidentId = _validateAndSanitizeString(data['incidentId'] as String, 'incidentId');
    final categoryKey = _validateAndSanitizeString(data['categoryKey'] as String, 'categoryKey');
    final categoryTitle = _validateAndSanitizeString(data['categoryTitle'] as String, 'categoryTitle');
    final severity = _validateSeverity(data['severity'] as String);
    final iconName = _validateAndSanitizeString(data['iconName'] as String, 'iconName');
    final color = _validateColor(data['color'] as String);
    
    // Optional fields
    final imageUrl = data['imageUrl'] as String?;
    final visibilityStatus = _parseVisibilityStatus(data['visibilityStatus']);

    return IncidentMarkerModel(
      incidentId: incidentId,
      categoryKey: categoryKey,
      categoryTitle: categoryTitle,
      subcategoryKey: data['subcategoryKey'] as String?,
      subcategoryTitle: data['subcategoryTitle'] as String?,
      severity: severity,
      location: location,
      postedAt: postedAt,
      isAnonymous: data['isAnonymous'] as bool? ?? false,
      zoneId: data['zoneId'] as String?,
      currentVisibilityPercentage: currentVisibility,
      isHighSeverity: data['isHighSeverity'] as bool? ?? false,
      iconName: iconName,
      color: color,
      userId: data['userId'] as String?,
      imageUrl: imageUrl,
      visibilityStatus: visibilityStatus,
    );
  }

  /// Validate required fields are present
  static void _validateRequiredFields(Map<String, dynamic> data) {
    final requiredFields = [
      'incidentId', 'categoryKey', 'categoryTitle', 'severity',
      'location', 'postedAt', 'iconName', 'color'
    ];

    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        throw ArgumentError('Missing required field: $field');
      }
    }
  }

  /// Validate and sanitize string fields
  static String _validateAndSanitizeString(String value, String fieldName) {
    if (value.isEmpty) {
      throw ArgumentError('$fieldName cannot be empty');
    }

    // Sanitize: remove potentially dangerous characters
    final sanitized = value
        .replaceAll(RegExp(r'[<>"' + "']"), '') // Remove HTML/script chars
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '') // Remove control chars
        .trim();

    if (sanitized.isEmpty) {
      throw ArgumentError('$fieldName contains only invalid characters');
    }

    // Length validation
    if (sanitized.length > 500) {
      throw ArgumentError('$fieldName is too long (max 500 characters)');
    }

    return sanitized;
  }

  /// Validate severity value
  static String _validateSeverity(String severity) {
    const validSeverities = ['Leve', 'Grave'];
    if (!validSeverities.contains(severity)) {
      throw ArgumentError('Invalid severity: $severity. Must be one of: ${validSeverities.join(', ')}');
    }
    return severity;
  }

  /// Validate color format
  static String _validateColor(String color) {
    // Check if it's a valid hex color
    final hexColorRegex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    if (!hexColorRegex.hasMatch(color)) {
      throw ArgumentError('Invalid color format: $color. Must be hex format like #FF0000');
    }
    return color.toUpperCase();
  }

  /// Parse and validate DateTime
  static DateTime _parseAndValidateDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);

      // Validate date is not in the future (with 1 minute tolerance)
      final now = DateTime.now();
      if (dateTime.isAfter(now.add(const Duration(minutes: 1)))) {
        throw ArgumentError('DateTime cannot be in the future: $dateTimeString');
      }

      // Validate date is not too old (more than 1 year)
      if (dateTime.isBefore(now.subtract(const Duration(days: 365)))) {
        throw ArgumentError('DateTime is too old: $dateTimeString');
      }

      return dateTime;
    } catch (e) {
      throw ArgumentError('Invalid DateTime format: $dateTimeString');
    }
  }

  /// Parse visibility status from dynamic value
  static IncidentVisibilityStatus _parseVisibilityStatus(dynamic value) {
    if (value == null) return IncidentVisibilityStatus.visibleToSelf;

    if (value is IncidentVisibilityStatus) return value;

    final stringValue = value.toString().toLowerCase();
    switch (stringValue) {
      case 'visibletoself':
        return IncidentVisibilityStatus.visibleToSelf;
      case 'visibletocommunity':
        return IncidentVisibilityStatus.visibleToCommunity;
      default:
        return IncidentVisibilityStatus.visibleToSelf;
    }
  }

  /// Convert to Firebase Realtime Database format
  Map<String, dynamic> toRealtimeDB() {
    return {
      'incidentId': incidentId,
      'categoryKey': categoryKey,
      'categoryTitle': categoryTitle,
      'subcategoryKey': subcategoryKey,
      'subcategoryTitle': subcategoryTitle,
      'severity': severity,
      'location': LocationModel.fromEntity(location).toMap(),
      'postedAt': postedAt.toIso8601String(),
      'isAnonymous': isAnonymous,
      'zoneId': zoneId,
      'isHighSeverity': isHighSeverity,
      'iconName': iconName,
      'color': color,
      'userId': userId,
      'imageUrl': imageUrl,
      'visibilityStatus': visibilityStatus.name,
      // Add geohash for efficient geospatial queries
      'geohash': _generateGeohash(location.latitude, location.longitude),
      // Add timestamp for TTL and cleanup
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      // Add expiration timestamp (72 hours from now)
      'expiresAt': DateTime.now().add(const Duration(hours: 72)).millisecondsSinceEpoch,
    };
  }

  /// Convert to regular Map for local processing
  Map<String, dynamic> toMap() {
    return {
      'incidentId': incidentId,
      'categoryKey': categoryKey,
      'categoryTitle': categoryTitle,
      'subcategoryKey': subcategoryKey,
      'subcategoryTitle': subcategoryTitle,
      'severity': severity,
      'location': LocationModel.fromEntity(location).toMap(),
      'postedAt': postedAt.toIso8601String(),
      'isAnonymous': isAnonymous,
      'zoneId': zoneId,
      'currentVisibilityPercentage': currentVisibilityPercentage,
      'isHighSeverity': isHighSeverity,
      'iconName': iconName,
      'color': color,
      'userId': userId,
      'imageUrl': imageUrl,
      'visibilityStatus': visibilityStatus.name,
    };
  }

  /// Generate a proper geohash for geospatial indexing
  String _generateGeohash(double lat, double lng) {
    // Use geoflutterfire_plus for proper geohash generation
    final geoFirePoint = GeoFirePoint(GeoPoint(lat, lng));
    return geoFirePoint.geohash;
  }

  @override
  IncidentMarkerModel copyWith({
    String? incidentId,
    String? categoryKey,
    String? categoryTitle,
    String? subcategoryKey,
    String? subcategoryTitle,
    String? severity,
    LocationEntity? location,
    DateTime? postedAt,
    bool? isAnonymous,
    String? zoneId,
    double? currentVisibilityPercentage,
    bool? isHighSeverity,
    String? iconName,
    String? color,
    String? userId,
    String? imageUrl,
  }) {
    return IncidentMarkerModel(
      incidentId: incidentId ?? this.incidentId,
      categoryKey: categoryKey ?? this.categoryKey,
      categoryTitle: categoryTitle ?? this.categoryTitle,
      subcategoryKey: subcategoryKey ?? this.subcategoryKey,
      subcategoryTitle: subcategoryTitle ?? this.subcategoryTitle,
      severity: severity ?? this.severity,
      location: location ?? this.location,
      postedAt: postedAt ?? this.postedAt,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      zoneId: zoneId ?? this.zoneId,
      currentVisibilityPercentage: currentVisibilityPercentage ?? this.currentVisibilityPercentage,
      isHighSeverity: isHighSeverity ?? this.isHighSeverity,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      userId: userId ?? this.userId,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}
