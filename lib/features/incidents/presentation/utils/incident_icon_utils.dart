import 'package:flutter/material.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'dart:ui';

/// Utility class for converting icon names to FluentIcons and colors
class IncidentIconUtils {
  /// Convert icon name string to FluentIcons IconData
  static IconData getIconFromName(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'shield_error':
        return FluentIcons.shield_error_24_regular;
      case 'heart_pulse':
        return FluentIcons.heart_pulse_24_regular;
      case 'fire':
        return FluentIcons.fire_24_regular;
      case 'vehicle_car_collision':
        return FluentIcons.vehicle_car_collision_24_regular;
      case 'eye':
        return FluentIcons.eye_24_regular;
      case 'cloud':
        return FluentIcons.cloud_24_regular;
      case 'building':
        return FluentIcons.building_24_regular;
      case 'people':
        return FluentIcons.people_24_regular;
      case 'globe':
        return FluentIcons.globe_24_regular;
      case 'heart':
        return FluentIcons.heart_24_regular;
      case 'more_horizontal':
        return FluentIcons.more_horizontal_24_regular;
      
      // Additional icon mappings for backward compatibility
      case 'weather_thunderstorm':
        return FluentIcons.cloud_24_regular;
      case 'building_factory':
        return FluentIcons.building_24_regular;
      case 'people_community':
        return FluentIcons.people_24_regular;
      case 'leaf':
        return FluentIcons.globe_24_regular;
      case 'animal_dog':
        return FluentIcons.heart_24_regular;
      
      default:
        return FluentIcons.more_horizontal_24_regular;
    }
  }

  /// Convert hex color string to Color object
  static Color getColorFromHex(String hexColor) {
    try {
      // Remove # if present
      String colorString = hexColor.replaceAll('#', '');
      
      // Add alpha if not present (make it fully opaque)
      if (colorString.length == 6) {
        colorString = 'FF$colorString';
      }
      
      return Color(int.parse(colorString, radix: 16));
    } catch (e) {
      // Return default gray color if parsing fails
      return const Color(0xFF718096);
    }
  }

  /// Get a lighter version of the color for backgrounds
  static Color getLightColor(String hexColor) {
    final color = getColorFromHex(hexColor);
    return color.withOpacity(0.1);
  }

  /// Get a darker version of the color for borders
  static Color getDarkColor(String hexColor) {
    final color = getColorFromHex(hexColor);
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - 0.2).clamp(0.0, 1.0)).toColor();
  }

  /// Get predefined colors for incident types (fallback)
  static Color getDefaultTypeColor(String typeKey) {
    switch (typeKey.toLowerCase()) {
      case 'crime':
        return const Color(0xFFE53E3E); // Red
      case 'medicalemergency':
        return const Color(0xFFD53F8C); // Pink
      case 'fire':
        return const Color(0xFFDD6B20); // Orange
      case 'accident':
        return const Color(0xFF3182CE); // Blue
      case 'suspiciousactivity':
        return const Color(0xFF805AD5); // Purple
      case 'naturaldisaster':
        return const Color(0xFF38A169); // Green
      case 'infrastructure':
        return const Color(0xFF319795); // Teal
      case 'publicdisturbance':
        return const Color(0xFFD69E2E); // Yellow
      case 'environmental':
        return const Color(0xFF48BB78); // Light Green
      case 'animalrelated':
        return const Color(0xFFED8936); // Orange
      case 'other':
      default:
        return const Color(0xFF718096); // Gray
    }
  }

  /// Get predefined icons for incident types (fallback)
  static IconData getDefaultTypeIcon(String typeKey) {
    switch (typeKey.toLowerCase()) {
      case 'crime':
        return FluentIcons.shield_error_24_regular;
      case 'medicalemergency':
        return FluentIcons.heart_pulse_24_regular;
      case 'fire':
        return FluentIcons.fire_24_regular;
      case 'accident':
        return FluentIcons.vehicle_car_collision_24_regular;
      case 'suspiciousactivity':
        return FluentIcons.eye_24_regular;
      case 'naturaldisaster':
        return FluentIcons.cloud_24_regular;
      case 'infrastructure':
        return FluentIcons.building_24_regular;
      case 'publicdisturbance':
        return FluentIcons.people_24_regular;
      case 'environmental':
        return FluentIcons.globe_24_regular;
      case 'animalrelated':
        return FluentIcons.heart_24_regular;
      case 'other':
      default:
        return FluentIcons.more_horizontal_24_regular;
    }
  }
}
