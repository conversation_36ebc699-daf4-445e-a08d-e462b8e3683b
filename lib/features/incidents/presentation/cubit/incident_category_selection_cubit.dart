import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_categories_usecase.dart';

// States
abstract class IncidentCategorySelectionState extends Equatable {
  const IncidentCategorySelectionState();

  @override
  List<Object?> get props => [];
}

class IncidentCategorySelectionInitial extends IncidentCategorySelectionState {
  const IncidentCategorySelectionInitial();
}

class IncidentCategorySelectionLoading extends IncidentCategorySelectionState {
  const IncidentCategorySelectionLoading();
}

class IncidentCategorySelectionLoaded extends IncidentCategorySelectionState {
  final List<IncidentCategoryEntity> categories;
  final List<IncidentCategoryEntity> filteredCategories;
  final String searchQuery;

  const IncidentCategorySelectionLoaded({
    required this.categories,
    required this.filteredCategories,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [categories, filteredCategories, searchQuery];

  IncidentCategorySelectionLoaded copyWith({
    List<IncidentCategoryEntity>? categories,
    List<IncidentCategoryEntity>? filteredCategories,
    String? searchQuery,
  }) {
    return IncidentCategorySelectionLoaded(
      categories: categories ?? this.categories,
      filteredCategories: filteredCategories ?? this.filteredCategories,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class IncidentCategorySelectionError extends IncidentCategorySelectionState {
  final String message;

  const IncidentCategorySelectionError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Cubit
class IncidentCategorySelectionCubit extends Cubit<IncidentCategorySelectionState> {
  final GetIncidentCategoriesUseCase _getCategoriesUseCase;

  IncidentCategorySelectionCubit({
    required GetIncidentCategoriesUseCase getCategoriesUseCase,
  }) : _getCategoriesUseCase = getCategoriesUseCase,
       super(const IncidentCategorySelectionInitial());

  Future<void> loadCategories() async {
    emit(const IncidentCategorySelectionLoading());

    try {
      final result = await _getCategoriesUseCase(const GetIncidentCategoriesParams());

      result.fold(
        (failure) => emit(IncidentCategorySelectionError(message: failure.message)),
        (categories) {
          emit(IncidentCategorySelectionLoaded(
            categories: categories,
            filteredCategories: categories,
          ));
        },
      );
    } catch (e) {
      emit(IncidentCategorySelectionError(message: 'Failed to load categories: $e'));
    }
  }

  void searchCategories(String query) {
    final currentState = state;
    if (currentState is IncidentCategorySelectionLoaded) {
      final filteredCategories = _filterCategories(currentState.categories, query);
      
      emit(currentState.copyWith(
        filteredCategories: filteredCategories,
        searchQuery: query,
      ));
    }
  }

  void clearSearch() {
    final currentState = state;
    if (currentState is IncidentCategorySelectionLoaded) {
      emit(currentState.copyWith(
        filteredCategories: currentState.categories,
        searchQuery: '',
      ));
    }
  }

  List<IncidentCategoryEntity> _filterCategories(
    List<IncidentCategoryEntity> categories,
    String query,
  ) {
    if (query.isEmpty) return categories;

    final lowercaseQuery = query.toLowerCase();
    return categories.where((category) {
      return category.title.toLowerCase().contains(lowercaseQuery) ||
             category.description.toLowerCase().contains(lowercaseQuery) ||
             category.subcategories.any((subcategory) =>
               subcategory.title.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  void retry() {
    loadCategories();
  }
}
