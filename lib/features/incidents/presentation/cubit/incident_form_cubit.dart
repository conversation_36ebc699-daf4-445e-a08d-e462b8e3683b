import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

// States
abstract class IncidentFormState extends Equatable {
  const IncidentFormState();

  @override
  List<Object?> get props => [];
}

class IncidentFormInitial extends IncidentFormState {
  const IncidentFormInitial();
}

class IncidentFormUpdated extends IncidentFormState {
  final String title;
  final String description;
  final IncidentCategoryEntity? selectedCategory;
  final IncidentSubcategoryEntity? selectedSubcategory;
  final String? selectedSeverity;
  final LocationEntity? selectedLocation;
  final List<MediaEntity> selectedMedia;
  final bool isAnonymous;
  final bool canPostAnonymously;
  final bool isFormValid;

  const IncidentFormUpdated({
    this.title = '',
    this.description = '',
    this.selectedCategory,
    this.selectedSubcategory,
    this.selectedSeverity,
    this.selectedLocation,
    this.selectedMedia = const [],
    this.isAnonymous = false,
    this.canPostAnonymously = false,
    this.isFormValid = false,
  });

  @override
  List<Object?> get props => [
    title,
    description,
    selectedCategory,
    selectedSubcategory,
    selectedSeverity,
    selectedLocation,
    selectedMedia,
    isAnonymous,
    canPostAnonymously,
    isFormValid,
  ];

  IncidentFormUpdated copyWith({
    String? title,
    String? description,
    IncidentCategoryEntity? selectedCategory,
    IncidentSubcategoryEntity? selectedSubcategory,
    String? selectedSeverity,
    LocationEntity? selectedLocation,
    List<MediaEntity>? selectedMedia,
    bool? isAnonymous,
    bool? canPostAnonymously,
    bool? isFormValid,
  }) {
    return IncidentFormUpdated(
      title: title ?? this.title,
      description: description ?? this.description,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedSubcategory: selectedSubcategory ?? this.selectedSubcategory,
      selectedSeverity: selectedSeverity ?? this.selectedSeverity,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      selectedMedia: selectedMedia ?? this.selectedMedia,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      canPostAnonymously: canPostAnonymously ?? this.canPostAnonymously,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }
}

// Cubit
class IncidentFormCubit extends Cubit<IncidentFormState> {
  IncidentFormCubit() : super(const IncidentFormInitial());

  void initializeForm({
    IncidentCategoryEntity? preselectedCategory,
    bool canPostAnonymously = false,
  }) {
    emit(IncidentFormUpdated(
      selectedCategory: preselectedCategory,
      canPostAnonymously: canPostAnonymously,
      isFormValid: _validateForm(
        title: '',
        selectedLocation: null,
        selectedCategory: preselectedCategory,
      ),
    ));
  }

  void updateTitle(String title) {
    final currentState = _getCurrentState();
    final updatedState = currentState.copyWith(
      title: title,
      isFormValid: _validateForm(
        title: title,
        selectedLocation: currentState.selectedLocation,
        selectedCategory: currentState.selectedCategory,
      ),
    );
    emit(updatedState);
  }

  void updateDescription(String description) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(description: description));
  }

  void updateCategory(IncidentCategoryEntity category) {
    final currentState = _getCurrentState();
    final updatedState = currentState.copyWith(
      selectedCategory: category,
      // Clear subcategory and severity when category changes
      selectedSubcategory: null,
      selectedSeverity: null,
      isFormValid: _validateForm(
        title: currentState.title,
        selectedLocation: currentState.selectedLocation,
        selectedCategory: category,
      ),
    );
    emit(updatedState);
  }

  void updateSubcategory(IncidentSubcategoryEntity? subcategory) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(
      selectedSubcategory: subcategory,
      // Clear severity when subcategory changes
      selectedSeverity: null,
    ));
  }

  void updateSeverity(String? severity) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(selectedSeverity: severity));
  }

  void updateLocation(LocationEntity location) {
    final currentState = _getCurrentState();
    final updatedState = currentState.copyWith(
      selectedLocation: location,
      isFormValid: _validateForm(
        title: currentState.title,
        selectedLocation: location,
        selectedCategory: currentState.selectedCategory,
      ),
    );
    emit(updatedState);
  }

  void updateMedia(List<MediaEntity> media) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(selectedMedia: media));
  }

  void updateAnonymous(bool isAnonymous) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(isAnonymous: isAnonymous));
  }

  void updateSubcategoryAndSeverity({
    IncidentSubcategoryEntity? subcategory,
    String? severity,
  }) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(
      selectedSubcategory: subcategory,
      selectedSeverity: severity,
    ));
  }

  bool _validateForm({
    required String title,
    required LocationEntity? selectedLocation,
    required IncidentCategoryEntity? selectedCategory,
  }) {
    return title.trim().isNotEmpty &&
           selectedLocation != null &&
           selectedCategory != null;
  }

  IncidentFormUpdated _getCurrentState() {
    final currentState = state;
    if (currentState is IncidentFormUpdated) {
      return currentState;
    }
    return const IncidentFormUpdated();
  }

  void reset() {
    emit(const IncidentFormInitial());
  }
}
