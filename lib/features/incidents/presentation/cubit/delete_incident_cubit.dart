import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/delete_incident_usecase.dart';

// States
abstract class DeleteIncidentState extends Equatable {
  const DeleteIncidentState();

  @override
  List<Object?> get props => [];
}

class DeleteIncidentInitial extends DeleteIncidentState {
  const DeleteIncidentInitial();
}

class DeleteIncidentLoading extends DeleteIncidentState {
  const DeleteIncidentLoading();
}

class DeleteIncidentSuccess extends DeleteIncidentState {
  final String message;
  
  const DeleteIncidentSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeleteIncidentError extends DeleteIncidentState {
  final String message;
  
  const DeleteIncidentError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Cubit
class DeleteIncidentCubit extends Cubit<DeleteIncidentState> {
  final DeleteIncidentUseCase _deleteIncidentUseCase;

  DeleteIncidentCubit({
    required DeleteIncidentUseCase deleteIncidentUseCase,
  }) : _deleteIncidentUseCase = deleteIncidentUseCase,
       super(const DeleteIncidentInitial());

  Future<void> deleteIncident({
    required String incidentId,
    required String userId,
  }) async {
    emit(const DeleteIncidentLoading());

    final params = DeleteIncidentParams(
      incidentId: incidentId,
      userId: userId,
    );

    final result = await _deleteIncidentUseCase(params);

    result.fold(
      (failure) => emit(DeleteIncidentError(message: failure.message)),
      (_) => emit(const DeleteIncidentSuccess(
        message: 'Incident deleted successfully',
      )),
    );
  }

  void reset() {
    emit(const DeleteIncidentInitial());
  }
}
