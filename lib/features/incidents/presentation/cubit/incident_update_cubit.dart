import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/add_incident_update_usecase.dart';

/// State for incident update management
abstract class IncidentUpdateState extends Equatable {
  const IncidentUpdateState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class IncidentUpdateInitial extends IncidentUpdateState {
  const IncidentUpdateInitial();
}

/// Loading state when posting an update
class IncidentUpdatePosting extends IncidentUpdateState {
  final String? message;

  const IncidentUpdatePosting({this.message});

  @override
  List<Object?> get props => [message];
}

/// State when updates are being loaded
class IncidentUpdatesLoading extends IncidentUpdateState {
  const IncidentUpdatesLoading();
}

/// State when updates are successfully loaded
class IncidentUpdatesLoaded extends IncidentUpdateState {
  final List<IncidentUpdateEntity> updates;
  final String incidentId;

  const IncidentUpdatesLoaded({
    required this.updates,
    required this.incidentId,
  });

  @override
  List<Object?> get props => [updates, incidentId];

  IncidentUpdatesLoaded copyWith({
    List<IncidentUpdateEntity>? updates,
    String? incidentId,
  }) {
    return IncidentUpdatesLoaded(
      updates: updates ?? this.updates,
      incidentId: incidentId ?? this.incidentId,
    );
  }
}

/// State when an update is successfully posted
class IncidentUpdatePosted extends IncidentUpdateState {
  final IncidentUpdateEntity update;

  const IncidentUpdatePosted({
    required this.update,
  });

  @override
  List<Object?> get props => [update];
}

/// Error state
class IncidentUpdateError extends IncidentUpdateState {
  final String message;
  final String? errorCode;

  const IncidentUpdateError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// State when validating update content
class IncidentUpdateValidating extends IncidentUpdateState {
  const IncidentUpdateValidating();
}

/// State when update content is valid
class IncidentUpdateValid extends IncidentUpdateState {
  final String content;
  final IncidentUpdateType type;

  const IncidentUpdateValid({
    required this.content,
    required this.type,
  });

  @override
  List<Object?> get props => [content, type];
}

/// State when update content is invalid
class IncidentUpdateInvalid extends IncidentUpdateState {
  final String error;

  const IncidentUpdateInvalid({
    required this.error,
  });

  @override
  List<Object?> get props => [error];
}

class IncidentUpdateCubit extends Cubit<IncidentUpdateState> {
  final AddIncidentUpdateUseCase _addIncidentUpdateUseCase;
  final GetIncidentUpdatesUseCase _getIncidentUpdatesUseCase;

  IncidentUpdateCubit({
    required AddIncidentUpdateUseCase addIncidentUpdateUseCase,
    required GetIncidentUpdatesUseCase getIncidentUpdatesUseCase,
  }) : _addIncidentUpdateUseCase = addIncidentUpdateUseCase,
       _getIncidentUpdatesUseCase = getIncidentUpdatesUseCase,
       super(const IncidentUpdateInitial());

  Future<void> postUpdate({
    required String incidentId,
    required IncidentUpdateEntity update,
  }) async {
    emit(const IncidentUpdatePosting(message: 'Posting update...'));

    final params = AddIncidentUpdateParams(
      incidentId: incidentId,
      update: update,
    );

    final result = await _addIncidentUpdateUseCase(params);

    result.fold(
      (failure) => emit(IncidentUpdateError(message: failure.message)),
      (update) {
        emit(IncidentUpdatePosted(update: update));
        loadUpdates(incidentId: incidentId);
      },
    );
  }

  Future<void> loadUpdates({
    required String incidentId,
  }) async {
    emit(const IncidentUpdatesLoading());

    final result = await _getIncidentUpdatesUseCase(incidentId);

    result.fold(
      (failure) => emit(IncidentUpdateError(message: failure.message)),
      (updates) => emit(IncidentUpdatesLoaded(
        updates: updates,
        incidentId: incidentId,
      )),
    );
  }

  void clearState() {
    emit(const IncidentUpdateInitial());
  }

  bool get canPostUpdate {
    return state is! IncidentUpdatePosting &&
           state is! IncidentUpdatesLoading;
  }
}
