import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';

abstract class IncidentsListState extends Equatable {
  const IncidentsListState();

  @override
  List<Object?> get props => [];
}

class IncidentsListInitial extends IncidentsListState {
  const IncidentsListInitial();
}

class IncidentsListLoading extends IncidentsListState {
  const IncidentsListLoading();
}

class IncidentsListLoaded extends IncidentsListState {
  final List<IncidentEntity> communityIncidents;
  final List<IncidentEntity> userIncidents;
  final String? categoryFilter;

  const IncidentsListLoaded({
    required this.communityIncidents,
    required this.userIncidents,
    this.categoryFilter,
  });

  @override
  List<Object?> get props => [communityIncidents, userIncidents, categoryFilter];

  IncidentsListLoaded copyWith({
    List<IncidentEntity>? communityIncidents,
    List<IncidentEntity>? userIncidents,
    String? categoryFilter,
  }) {
    return IncidentsListLoaded(
      communityIncidents: communityIncidents ?? this.communityIncidents,
      userIncidents: userIncidents ?? this.userIncidents,
      categoryFilter: categoryFilter ?? this.categoryFilter,
    );
  }
}

class IncidentsListError extends IncidentsListState {
  final String message;

  const IncidentsListError({required this.message});

  @override
  List<Object?> get props => [message];
}

class IncidentsListCubit extends Cubit<IncidentsListState> {
  final GetCommunityIncidentsUseCase _getCommunityIncidentsUseCase;
  final GetUserIncidentsUseCase _getUserIncidentsUseCase;

  IncidentsListCubit({
    required GetCommunityIncidentsUseCase getCommunityIncidentsUseCase,
    required GetUserIncidentsUseCase getUserIncidentsUseCase,
  }) : _getCommunityIncidentsUseCase = getCommunityIncidentsUseCase,
       _getUserIncidentsUseCase = getUserIncidentsUseCase,
       super(const IncidentsListInitial());

  Future<void> loadCommunityIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
  }) async {
    emit(const IncidentsListLoading());

    final params = GetCommunityIncidentsParams(
      userId: userId,
      userValidatedZoneIds: userValidatedZoneIds,
      categoryFilter: categoryFilter,
    );

    final result = await _getCommunityIncidentsUseCase(params);

    result.fold(
      (failure) => emit(IncidentsListError(message: failure.message)),
      (incidents) {
        final currentState = state;
        final userIncidents = currentState is IncidentsListLoaded
            ? currentState.userIncidents
            : <IncidentEntity>[];

        emit(IncidentsListLoaded(
          communityIncidents: incidents,
          userIncidents: userIncidents,
          categoryFilter: categoryFilter,
        ));
      },
    );
  }

  Future<void> loadUserIncidents({
    required String userId,
    String? categoryFilter,
  }) async {
    emit(const IncidentsListLoading());

    final params = GetUserIncidentsParams(
      userId: userId,
      categoryFilter: categoryFilter,
    );
    final result = await _getUserIncidentsUseCase(params);

    result.fold(
      (failure) => emit(IncidentsListError(message: failure.message)),
      (userResult) {
        final filteredIncidents = userResult.incidents;

        final currentState = state;
        final communityIncidents = currentState is IncidentsListLoaded
            ? currentState.communityIncidents
            : <IncidentEntity>[];

        emit(IncidentsListLoaded(
          communityIncidents: communityIncidents,
          userIncidents: filteredIncidents,
          categoryFilter: categoryFilter,
        ));
      },
    );
  }

  Future<void> loadAllIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
  }) async {
    emit(const IncidentsListLoading());

    final communityParams = GetCommunityIncidentsParams(
      userId: userId,
      userValidatedZoneIds: userValidatedZoneIds,
      categoryFilter: categoryFilter,
    );

    final communityResult = await _getCommunityIncidentsUseCase(communityParams);
    final userParams = GetUserIncidentsParams(
      userId: userId,
      categoryFilter: categoryFilter,
    );
    final userResult = await _getUserIncidentsUseCase(userParams);

    if (communityResult.isLeft() || userResult.isLeft()) {
      final errorMessage = communityResult.isLeft()
          ? communityResult.fold((l) => l.message, (r) => '')
          : userResult.fold((l) => l.message, (r) => '');
      emit(IncidentsListError(message: errorMessage));
      return;
    }

    final communityIncidents = communityResult.fold((l) => <IncidentEntity>[], (r) => r);
    final userIncidentsResult = userResult.fold((l) => null, (r) => r);
    final filteredUserIncidents = userIncidentsResult?.incidents ?? <IncidentEntity>[];

    emit(IncidentsListLoaded(
      communityIncidents: communityIncidents,
      userIncidents: filteredUserIncidents,
      categoryFilter: categoryFilter,
    ));
  }

  void applyFilter(String? categoryFilter) {
    final currentState = state;
    if (currentState is IncidentsListLoaded) {
      emit(currentState.copyWith(categoryFilter: categoryFilter));
    }
  }

  void clearFilter() {
    applyFilter(null);
  }

  void refresh({
    required String userId,
    required List<String> userValidatedZoneIds,
  }) {
    final currentState = state;
    final categoryFilter = currentState is IncidentsListLoaded ? currentState.categoryFilter : null;

    loadAllIncidents(
      userId: userId,
      userValidatedZoneIds: userValidatedZoneIds,
      categoryFilter: categoryFilter,
    );
  }
}
