import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/add_incident_update_usecase.dart';

/// States for adding incident updates
abstract class AddIncidentUpdateState extends Equatable {
  const AddIncidentUpdateState();

  @override
  List<Object?> get props => [];
}

class AddIncidentUpdateInitial extends AddIncidentUpdateState {
  const AddIncidentUpdateInitial();
}

class AddIncidentUpdateLoading extends AddIncidentUpdateState {
  const AddIncidentUpdateLoading();
}

class AddIncidentUpdateFormUpdated extends AddIncidentUpdateState {
  final IncidentUpdateType selectedType;
  final String content;
  final String linkUrl;
  final String linkDescription;
  final bool isAnonymous;
  final List<MediaEntity> selectedMedia;
  final bool isFormValid;

  const AddIncidentUpdateFormUpdated({
    required this.selectedType,
    required this.content,
    required this.linkUrl,
    required this.linkDescription,
    required this.isAnonymous,
    required this.selectedMedia,
    required this.isFormValid,
  });

  @override
  List<Object?> get props => [
        selectedType,
        content,
        linkUrl,
        linkDescription,
        isAnonymous,
        selectedMedia,
        isFormValid,
      ];

  AddIncidentUpdateFormUpdated copyWith({
    IncidentUpdateType? selectedType,
    String? content,
    String? linkUrl,
    String? linkDescription,
    bool? isAnonymous,
    List<MediaEntity>? selectedMedia,
    bool? isFormValid,
  }) {
    return AddIncidentUpdateFormUpdated(
      selectedType: selectedType ?? this.selectedType,
      content: content ?? this.content,
      linkUrl: linkUrl ?? this.linkUrl,
      linkDescription: linkDescription ?? this.linkDescription,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      selectedMedia: selectedMedia ?? this.selectedMedia,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }
}

class AddIncidentUpdateSuccess extends AddIncidentUpdateState {
  final IncidentUpdateEntity update;

  const AddIncidentUpdateSuccess({required this.update});

  @override
  List<Object?> get props => [update];
}

class AddIncidentUpdateError extends AddIncidentUpdateState {
  final String message;

  const AddIncidentUpdateError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AddIncidentUpdateUnauthorized extends AddIncidentUpdateState {
  const AddIncidentUpdateUnauthorized();
}

/// Cubit for managing add incident update form and submission
class AddIncidentUpdateCubit extends Cubit<AddIncidentUpdateState> {
  final AddIncidentUpdateUseCase _addIncidentUpdateUseCase;

  AddIncidentUpdateCubit({
    required AddIncidentUpdateUseCase addIncidentUpdateUseCase,
  }) : _addIncidentUpdateUseCase = addIncidentUpdateUseCase,
       super(const AddIncidentUpdateInitial());

  /// Initialize the form
  void initializeForm() {
    emit(const AddIncidentUpdateFormUpdated(
      selectedType: IncidentUpdateType.textUpdate,
      content: '',
      linkUrl: '',
      linkDescription: '',
      isAnonymous: false,
      selectedMedia: [],
      isFormValid: false,
    ));
  }

  /// Update the selected update type
  void updateType(IncidentUpdateType type) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(
      selectedType: type,
      isFormValid: _validateForm(
        type: type,
        content: currentState.content,
        linkUrl: currentState.linkUrl,
      ),
    ));
  }

  /// Update the content text
  void updateContent(String content) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(
      content: content,
      isFormValid: _validateForm(
        type: currentState.selectedType,
        content: content,
        linkUrl: currentState.linkUrl,
      ),
    ));
  }

  /// Update the link URL
  void updateLinkUrl(String url) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(
      linkUrl: url,
      isFormValid: _validateForm(
        type: currentState.selectedType,
        content: currentState.content,
        linkUrl: url,
      ),
    ));
  }

  /// Update the link description
  void updateLinkDescription(String description) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(
      linkDescription: description,
      isFormValid: _validateForm(
        type: currentState.selectedType,
        content: currentState.content,
        linkUrl: currentState.linkUrl,
      ),
    ));
  }

  /// Update anonymous setting
  void updateAnonymous(bool isAnonymous) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(isAnonymous: isAnonymous));
  }

  /// Update selected media
  void updateMedia(List<MediaEntity> media) {
    final currentState = _getCurrentFormState();
    emit(currentState.copyWith(selectedMedia: media));
  }

  /// Submit the update
  Future<void> submitUpdate({
    required String incidentId,
    required String userId,
    required String? userDisplayName,
  }) async {
    final currentState = _getCurrentFormState();
    
    if (!currentState.isFormValid) {
      emit(const AddIncidentUpdateError(message: 'Please fill in all required fields'));
      return;
    }

    emit(const AddIncidentUpdateLoading());

    try {
      final update = _buildUpdateEntity(
        incidentId: incidentId,
        userId: userId,
        userDisplayName: userDisplayName,
        formState: currentState,
      );

      final params = AddIncidentUpdateParams(
        incidentId: incidentId,
        update: update,
      );

      final result = await _addIncidentUpdateUseCase(params);

      result.fold(
        (failure) => emit(AddIncidentUpdateError(message: failure.message)),
        (update) => emit(AddIncidentUpdateSuccess(update: update)),
      );
    } catch (e) {
      emit(AddIncidentUpdateError(message: 'Failed to submit update: ${e.toString()}'));
    }
  }

  /// Check if user can add updates to this incident
  bool canAddUpdate({
    required String currentUserId,
    required String incidentUserId,
  }) {
    return currentUserId.isNotEmpty && currentUserId == incidentUserId;
  }

  /// Build the update entity from form state
  IncidentUpdateEntity _buildUpdateEntity({
    required String incidentId,
    required String userId,
    required String? userDisplayName,
    required AddIncidentUpdateFormUpdated formState,
  }) {
    final content = formState.selectedType == IncidentUpdateType.link 
        ? formState.linkUrl
        : formState.content;

    // Prepare metadata
    Map<String, dynamic>? metadata;
    if (formState.selectedType == IncidentUpdateType.link) {
      metadata = {
        'description': formState.linkDescription,
        'url': formState.linkUrl,
      };
    }
    
    // Add media to metadata if any
    if (formState.selectedMedia.isNotEmpty) {
      metadata ??= {};
      metadata['media'] = formState.selectedMedia.map((media) => {
        'mediaId': media.mediaId,
        'type': media.type.name,
        'url': media.url,
        'fileName': media.fileName,
        'fileSizeBytes': media.fileSizeBytes,
        'thumbnailUrl': media.thumbnailUrl,
        'uploadedAt': media.uploadedAt.toIso8601String(),
      }).toList();
      metadata['mediaCount'] = formState.selectedMedia.length;
    }

    return IncidentUpdateEntity(
      updateId: const Uuid().v4(),
      incidentId: incidentId,
      content: content,
      timestamp: DateTime.now(),
      authorId: userId,
      type: formState.selectedType,
      authorDisplayName: formState.isAnonymous ? null : userDisplayName,
      isAnonymous: formState.isAnonymous,
      metadata: metadata,
    );
  }

  /// Validate the form based on update type
  bool _validateForm({
    required IncidentUpdateType type,
    required String content,
    required String linkUrl,
  }) {
    switch (type) {
      case IncidentUpdateType.link:
        return linkUrl.trim().isNotEmpty && _isValidUrl(linkUrl);
      case IncidentUpdateType.textUpdate:
      case IncidentUpdateType.statusChange:
      case IncidentUpdateType.communityObservation:
        return content.trim().isNotEmpty;
    }
  }

  /// Check if URL is valid
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get current form state or return initial state
  AddIncidentUpdateFormUpdated _getCurrentFormState() {
    final currentState = state;
    if (currentState is AddIncidentUpdateFormUpdated) {
      return currentState;
    }
    return const AddIncidentUpdateFormUpdated(
      selectedType: IncidentUpdateType.textUpdate,
      content: '',
      linkUrl: '',
      linkDescription: '',
      isAnonymous: false,
      selectedMedia: [],
      isFormValid: false,
    );
  }

  /// Reset the form
  void resetForm() {
    emit(const AddIncidentUpdateInitial());
  }
}
