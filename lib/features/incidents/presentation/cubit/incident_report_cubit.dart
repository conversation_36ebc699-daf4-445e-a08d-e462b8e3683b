import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/post_incident_orchestrator_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/services/incident_visibility_service.dart';

// States
abstract class IncidentReportState extends Equatable {
  const IncidentReportState();

  @override
  List<Object?> get props => [];
}

class IncidentReportInitial extends IncidentReportState {
  const IncidentReportInitial();
}

class IncidentReportSubmitting extends IncidentReportState {
  final String message;

  const IncidentReportSubmitting({this.message = 'Submitting report...'});

  @override
  List<Object?> get props => [message];
}

class IncidentReportSuccess extends IncidentReportState {
  final IncidentEntity incident;
  final String message;

  const IncidentReportSuccess({
    required this.incident,
    this.message = 'Report submitted successfully',
  });

  @override
  List<Object?> get props => [incident, message];
}

class IncidentReportError extends IncidentReportState {
  final String message;

  const IncidentReportError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Cubit
class IncidentReportCubit extends Cubit<IncidentReportState> {
  final PostIncidentOrchestratorUseCase _postIncidentUseCase;
  final IncidentVisibilityService _visibilityService;

  IncidentReportCubit({
    required PostIncidentOrchestratorUseCase postIncidentUseCase,
    required IncidentVisibilityService visibilityService,
  }) : _postIncidentUseCase = postIncidentUseCase,
       _visibilityService = visibilityService,
       super(const IncidentReportInitial());

  Future<void> submitIncident({
    required String userId,
    required String categoryKey,
    required String categoryTitle,
    String? subcategoryKey,
    String? subcategoryTitle,
    String? severity,
    required String title,
    required String description,
    required LocationEntity location,
    List<MediaEntity> media = const [],
    bool isAnonymous = false,
    String? zoneId,
    Map<String, dynamic>? metadata,
  }) async {
    emit(const IncidentReportSubmitting());

    final params = PostIncidentParams(
      userId: userId,
      categoryKey: categoryKey,
      categoryTitle: categoryTitle,
      subcategoryKey: subcategoryKey,
      subcategoryTitle: subcategoryTitle,
      severity: severity,
      title: title,
      description: description,
      location: location,
      media: media,
      isAnonymous: isAnonymous,
      zoneId: zoneId,
      metadata: metadata,
    );

    final result = await _postIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentReportError(message: failure.message)),
      (incident) async {
        final message = await _getSuccessMessage(incident, location);
        emit(IncidentReportSuccess(incident: incident, message: message));
      },
    );
  }

  Future<String> _getSuccessMessage(
    IncidentEntity incident,
    LocationEntity location,
  ) async {
    if (incident.visibilityStatus ==
        IncidentVisibilityStatus.visibleToCommunity) {
      return 'Report submitted successfully! Your report is visible to the community.';
    }

    // Get detailed validation status for better feedback
    final statusResult = await _visibilityService.getUserValidationStatus(
      userId: incident.userId,
      currentLocation: location,
    );

    return statusResult.fold(
      (failure) =>
          'Report submitted successfully! Your report is only visible to you.',
      (status) {
        final actionMessage = status.actionMessage;
        return 'Report submitted successfully! Your report is only visible to you. $actionMessage';
      },
    );
  }

  void reset() {
    emit(const IncidentReportInitial());
  }
}
