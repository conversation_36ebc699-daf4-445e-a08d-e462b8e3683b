import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/delete_incident_usecase.dart';

// States
abstract class MyIncidentsState extends Equatable {
  const MyIncidentsState();

  @override
  List<Object?> get props => [];
}

class MyIncidentsInitial extends MyIncidentsState {
  const MyIncidentsInitial();
}

class MyIncidentsLoading extends MyIncidentsState {
  const MyIncidentsLoading();
}

class MyIncidentsLoaded extends MyIncidentsState {
  final List<IncidentEntity> publishedIncidents;
  final List<IncidentEntity> draftIncidents;

  const MyIncidentsLoaded({
    required this.publishedIncidents,
    required this.draftIncidents,
  });

  @override
  List<Object?> get props => [publishedIncidents, draftIncidents];

  // Serialization for Hydrated Bloc
  Map<String, dynamic> toJson() {
    return {
      'publishedIncidents': publishedIncidents.map((incident) => incident.toJson()).toList(),
      'draftIncidents': draftIncidents.map((incident) => incident.toJson()).toList(),
    };
  }

  static MyIncidentsLoaded fromJson(Map<String, dynamic> json) {
    return MyIncidentsLoaded(
      publishedIncidents: (json['publishedIncidents'] as List<dynamic>?)
          ?.map((item) => IncidentEntity.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      draftIncidents: (json['draftIncidents'] as List<dynamic>?)
          ?.map((item) => IncidentEntity.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
}

class MyIncidentsError extends MyIncidentsState {
  final String message;

  const MyIncidentsError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Cubit with Hydrated Bloc for state persistence
class MyIncidentsCubit extends HydratedCubit<MyIncidentsState> {
  final GetUserIncidentsUseCase _getUserIncidentsUseCase;
  final DeleteIncidentUseCase _deleteIncidentUseCase;

  MyIncidentsCubit({
    required GetUserIncidentsUseCase getUserIncidentsUseCase,
    required DeleteIncidentUseCase deleteIncidentUseCase,
  }) : _getUserIncidentsUseCase = getUserIncidentsUseCase,
       _deleteIncidentUseCase = deleteIncidentUseCase,
       super(const MyIncidentsInitial());

  Future<void> loadUserIncidents(String userId) async {
    emit(const MyIncidentsLoading());

    final params = GetUserIncidentsParams(userId: userId);
    final result = await _getUserIncidentsUseCase(params);

    result.fold(
      (failure) => emit(MyIncidentsError(message: failure.message)),
      (userResult) {
        final incidents = userResult.incidents;
        // Separate incidents into published and drafts
        // For now, we'll consider all incidents as published since there's no draft status in the domain
        // In a real implementation, you might have a separate draft storage or status field
        final publishedIncidents = incidents.where((incident) =>
          !incident.incidentId.startsWith('draft_') &&
          incident.status != IncidentStatus.archived
        ).toList();

        final draftIncidents = incidents.where((incident) =>
          incident.incidentId.startsWith('draft_') ||
          incident.status == IncidentStatus.archived
        ).toList();

        emit(MyIncidentsLoaded(
          publishedIncidents: publishedIncidents,
          draftIncidents: draftIncidents,
        ));
      },
    );
  }

  Future<void> refreshIncidents(String userId) async {
    await loadUserIncidents(userId);
  }

  Future<void> deleteIncident(String incidentId, String userId) async {
    final currentState = state;
    if (currentState is MyIncidentsLoaded) {
      // Optimistically update UI first
      final updatedPublished = currentState.publishedIncidents
          .where((incident) => incident.incidentId != incidentId)
          .toList();

      final updatedDrafts = currentState.draftIncidents
          .where((incident) => incident.incidentId != incidentId)
          .toList();

      emit(MyIncidentsLoaded(
        publishedIncidents: updatedPublished,
        draftIncidents: updatedDrafts,
      ));

      // Then perform actual deletion
      final params = DeleteIncidentParams(incidentId: incidentId, userId: userId);
      final result = await _deleteIncidentUseCase(params);

      result.fold(
        (failure) {
          // Revert the optimistic update on failure
          emit(currentState);
          emit(MyIncidentsError(message: failure.message));
        },
        (_) {
          // Deletion successful, keep the updated state
        },
      );
    }
  }

  @override
  MyIncidentsState? fromJson(Map<String, dynamic> json) {
    try {
      final stateType = json['type'] as String?;

      if (stateType == 'MyIncidentsLoaded') {
        return MyIncidentsLoaded.fromJson(json['data'] as Map<String, dynamic>);
      }

      // Return null for other states to use initial state
      return null;
    } catch (e) {
      // Return null to use initial state if deserialization fails
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(MyIncidentsState state) {
    try {
      if (state is MyIncidentsLoaded) {
        return {
          'type': 'MyIncidentsLoaded',
          'data': state.toJson(),
        };
      }

      // Don't persist other states
      return null;
    } catch (e) {
      // Return null if serialization fails
      return null;
    }
  }
}
