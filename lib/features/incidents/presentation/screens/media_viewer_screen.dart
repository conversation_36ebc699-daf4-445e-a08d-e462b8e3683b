import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class MediaViewerScreen extends StatefulWidget {
  final List<MediaEntity> mediaList;
  final int initialIndex;

  const MediaViewerScreen({
    Key? key,
    required this.mediaList,
    this.initialIndex = 0,
  }) : super(key: key);

  @override
  State<MediaViewerScreen> createState() => _MediaViewerScreenState();
}

class _MediaViewerScreenState extends State<MediaViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _toggleVisibility() {
    setState(() {
      _isVisible = !_isVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _isVisible ? _buildAppBar(theme) : null,
      body: Stack(
        children: [
          // Main content
          PageView.builder(
            controller: _pageController,
            itemCount: widget.mediaList.length,
            onPageChanged: _onPageChanged,
            itemBuilder: (context, index) {
              final media = widget.mediaList[index];
              return GestureDetector(
                onTap: _toggleVisibility,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.black,
                  child: Center(
                    child: _buildMediaWidget(media),
                  ),
                ),
              );
            },
          ),

          // Bottom info panel
          if (_isVisible)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomPanel(theme),
            ),

          // Page indicator
          if (_isVisible && widget.mediaList.length > 1)
            Positioned(
              top: kToolbarHeight + MediaQuery.of(context).padding.top + 16.h,
              left: 0,
              right: 0,
              child: _buildPageIndicator(theme),
            ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: Colors.black.withValues(alpha: 0.7),
      elevation: 0,
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: const Icon(FluentIcons.arrow_left_24_regular, color: Colors.white),
      ),
      title: Text(
        '${_currentIndex + 1} of ${widget.mediaList.length}',
        style: const TextStyle(color: Colors.white),
      ),
      actions: [
        IconButton(
          onPressed: _shareMedia,
          icon: const Icon(FluentIcons.share_24_regular, color: Colors.white),
        ),
        IconButton(
          onPressed: _downloadMedia,
          icon: const Icon(FluentIcons.arrow_download_24_regular, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildMediaWidget(MediaEntity media) {
    if (media.isVideo) {
      // Professional video placeholder - no complex video player
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FluentIcons.play_circle_24_filled,
              color: Colors.white,
              size: 80.sp,
            ),
            SizedBox(height: 16.h),
            Text(
              'Video Preview',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Tap to open in external player',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14.sp,
              ),
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () => _openVideoExternally(media),
              icon: const Icon(FluentIcons.open_24_regular),
              label: const Text('Open Video'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    } else {
      // Image display
      return CachedNetworkImage(
        imageUrl: media.url,
        fit: BoxFit.contain,
        placeholder: (context, url) => Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.image_24_regular,
                color: Colors.white,
                size: 64.sp,
              ),
              SizedBox(height: 16.h),
              const Text(
                'Failed to load image',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildBottomPanel(ThemeData theme) {
    final media = widget.mediaList[_currentIndex];
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.8),
          ],
        ),
      ),
      padding: EdgeInsets.fromLTRB(16.w, 24.h, 16.w, 
          MediaQuery.of(context).padding.bottom + 16.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            media.fileName ?? 'Media ${_currentIndex + 1}',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(
                _getMediaIcon(media.type),
                color: Colors.white70,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                media.type.displayName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
              const Spacer(),
              Text(
                media.fileSizeFormatted,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(ThemeData theme) {
    return Center(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          '${_currentIndex + 1} / ${widget.mediaList.length}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  IconData _getMediaIcon(MediaType type) {
    switch (type) {
      case MediaType.photo:
        return FluentIcons.image_24_regular;
      case MediaType.video:
        return FluentIcons.video_24_regular;
      case MediaType.audio:
        return FluentIcons.speaker_2_24_regular;
    }
  }

  void _openVideoExternally(MediaEntity media) {
    // TODO: Implement external video player opening
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('External video player coming soon')),
    );
  }

  void _shareMedia() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }

  void _downloadMedia() {
    // TODO: Implement download functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Download functionality coming soon')),
    );
  }
}
