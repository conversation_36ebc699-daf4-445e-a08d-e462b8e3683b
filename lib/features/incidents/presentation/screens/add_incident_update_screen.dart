import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/add_incident_update_cubit.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/add_update/incident_context_card.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/add_update/update_type_selector.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/add_update/update_content_fields.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/add_update/update_media_section.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/add_update/anonymous_toggle_card.dart';

class AddIncidentUpdateScreen extends StatefulWidget {
  final IncidentEntity incident;

  const AddIncidentUpdateScreen({
    Key? key,
    required this.incident,
  }) : super(key: key);

  @override
  State<AddIncidentUpdateScreen> createState() => _AddIncidentUpdateScreenState();
}

class _AddIncidentUpdateScreenState extends State<AddIncidentUpdateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();
  final _linkController = TextEditingController();
  final _linkDescriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize the form when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AddIncidentUpdateCubit>().initializeForm();
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _linkController.dispose();
    _linkDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AddIncidentUpdateCubit, AddIncidentUpdateState>(
      listener: (context, state) {
        if (state is AddIncidentUpdateSuccess) {
          _showSuccessDialog();
        } else if (state is AddIncidentUpdateError) {
          _showErrorDialog(state.message);
        } else if (state is AddIncidentUpdateUnauthorized) {
          _showUnauthorizedDialog();
        }
      },
      child: _buildScaffold(context),
    );
  }

  Widget _buildScaffold(BuildContext context) {
    final theme = Theme.of(context);
    final authState = context.read<AuthBloc>().state;

    if (!authState.isAuthenticated) {
      return _buildUnauthorizedScaffold(theme);
    }

    final cubit = context.read<AddIncidentUpdateCubit>();
    final canAdd = cubit.canAddUpdate(
      currentUserId: authState.user.id,
      incidentUserId: widget.incident.userId,
    );

    if (!canAdd) {
      return _buildUnauthorizedScaffold(theme);
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(theme),
      body: BlocBuilder<AddIncidentUpdateCubit, AddIncidentUpdateState>(
        builder: (context, state) {


          if (state is AddIncidentUpdateFormUpdated ) {
            return _buildForm(theme, state);
          }

          return const SizedBox.shrink();
        },
      ),
      bottomNavigationBar: _buildSubmitButton(theme),
    );
  }

  Widget _buildUnauthorizedScaffold(ThemeData theme) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Update'),
      
      ),
      body: const Center(
        child: Text('You can only add updates to your own incidents.'),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: const Text('Add Update'),
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: const Icon(CupertinoIcons.back),
      ),
     
    );
  }

  Widget _buildForm(ThemeData theme, AddIncidentUpdateFormUpdated state) {
    final authState = context.read<AuthBloc>().state;

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),

            IncidentContextCard(incident: widget.incident),
            SizedBox(height: 24.h),

            UpdateTypeSelector(
              selectedType: state.selectedType,
              onTypeChanged: (type) {
                context.read<AddIncidentUpdateCubit>().updateType(type);
              },
            ),
            SizedBox(height: 24.h),
            UpdateContentFields(
              selectedType: state.selectedType,
              contentController: _contentController,
              linkController: _linkController,
              linkDescriptionController: _linkDescriptionController,
              onContentChanged: (value) {
                context.read<AddIncidentUpdateCubit>().updateContent(value);
              },
              onLinkChanged: (value) {
                context.read<AddIncidentUpdateCubit>().updateLinkUrl(value);
              },
              onLinkDescriptionChanged: (value) {
                context.read<AddIncidentUpdateCubit>().updateLinkDescription(value);
              },
            ),
            SizedBox(height: 24.h),

            UpdateMediaSection(
              selectedMedia: state.selectedMedia,
              userId: authState.user.id,
              onMediaChanged: (media) {
                context.read<AddIncidentUpdateCubit>().updateMedia(media);
              },
            ),
            SizedBox(height: 24.h),
            AnonymousToggleCard(
              isAnonymous: state.isAnonymous,
              onChanged: (value) {
                context.read<AddIncidentUpdateCubit>().updateAnonymous(value);
              },
            ),

            // Bottom padding for submit button
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton(ThemeData theme) {
    return BlocBuilder<AddIncidentUpdateCubit, AddIncidentUpdateState>(
      builder: (context, state) {
        final isLoading = state is AddIncidentUpdateLoading;
        final isFormValid = state is AddIncidentUpdateFormUpdated && state.isFormValid;
        final authState = context.read<AuthBloc>().state;

        return Container(
          padding: EdgeInsets.all(16.w),
          child: CustomButton(
            text: 'Post Update',
            onPressed: () => _submitUpdate(authState),
            disabled: !isFormValid || isLoading,
            isLoading: isLoading,
          ),
        );
      },
    );
  }

  void _submitUpdate(AuthState authState) {
    if (!_formKey.currentState!.validate()) return;

    context.read<AddIncidentUpdateCubit>().submitUpdate(
      incidentId: widget.incident.incidentId,
      userId: authState.user.id,
      userDisplayName: authState.user.displayName,
    );
  }

  void _showSuccessDialog() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Update Posted',
      message: 'Your update has been posted successfully.',
      confirmText: 'OK',
      onConfirm: () => context.pop(true), // Return true to indicate success
    );
  }

  void _showErrorDialog(String message) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: message,
      confirmText: 'OK',
    );
  }

  void _showUnauthorizedDialog() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Unauthorized',
      message: 'You can only add updates to incidents that you reported.',
      confirmText: 'OK',
      onConfirm: () => context.pop(),
    );
  }
}
