import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/incident_media_gallery.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/incident_updates_list.dart';
import 'package:respublicaseguridad/features/incidents/presentation/utils/incident_icon_utils.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/share_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_by_id_usecase.dart';

import 'package:respublicaseguridad/core/services/share_service.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class IncidentDetailsScreen extends StatefulWidget {
  final IncidentEntity incident;

  const IncidentDetailsScreen({
    Key? key,
    required this.incident,
  }) : super(key: key);

  @override
  State<IncidentDetailsScreen> createState() => _IncidentDetailsScreenState();
}

class _IncidentDetailsScreenState extends State<IncidentDetailsScreen> {
  GoogleMapController? _mapController;
  IncidentEntity? _currentIncident;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _currentIncident = widget.incident;
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _refreshIncidentData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      final authState = context.read<AuthBloc>().state;
      if (authState.isAuthenticated) {
        // Fetch incident with embedded updates
        final incidentUseCase = getIt<GetIncidentByIdUseCase>();

        // Get the incident (which now includes updates in the document)
        final incidentResult = await incidentUseCase(GetIncidentByIdParams(
          incidentId: _currentIncident!.incidentId,
          userId: authState.user.id,
        ));

        final incident = incidentResult.fold(
          (failure) => null,
          (incident) => incident,
        );

        if (incident != null && mounted) {
          setState(() {
            _currentIncident = incident;
          });
        } else if (mounted) {
          // Show error if incident fetch failed
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to refresh incident data'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh incident data'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Incident Details'),
        leading: IconButton(
          onPressed: () => _handleBackNavigation(context),
          icon: const Icon(CupertinoIcons.back),
        ),
        actions: [
          IconButton(
            onPressed: _shareIncident,
            icon: const Icon(FluentIcons.share_24_regular),
            tooltip: 'Share',
          ),
          IconButton(
            onPressed: () => _showActionMenu(context),
            icon: const Icon(FluentIcons.more_vertical_24_regular),
            tooltip: 'More',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Main content
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    SizedBox(height: 20.h),

                    // Incident Details Section
                    _buildSectionTitle(
                      theme,
                      'Incident Details',
                      FluentIcons.info_24_regular,
                      theme.colorScheme.primary,
                    ),
                    _buildDetailsCard(theme),
                    SizedBox(height: 24.h),

                    // Location Section
                    _buildSectionTitle(
                      theme,
                      'Location',
                      FluentIcons.location_24_regular,
                      theme.colorScheme.secondary,
                    ),
                    _buildLocationCard(theme),
                    SizedBox(height: 24.h),

                    // Media Section
                    if (_currentIncident!.hasMedia) ...[
                      _buildSectionTitle(
                        theme,
                        'Media',
                        FluentIcons.image_24_regular,
                        theme.colorScheme.tertiary,
                        subtitle: '${_currentIncident!.mediaCount} files',
                      ),
                      _buildMediaCard(theme),
                      SizedBox(height: 24.h),
                    ],

                    // Updates Section
                    _buildSectionTitle(
                      theme,
                      'Updates',
                      FluentIcons.comment_24_regular,
                      theme.colorScheme.primary,
                      subtitle: '${_currentIncident!.updateCount} updates',
                    ),
                    _buildUpdatesCard(theme),
                    SizedBox(height: 24.h),

                    // Add bottom padding to account for the bottom action button
                    SizedBox(height: 90.h), // Height of bottom button + padding
                  ],
                ),
              ),
            ),
          ],
        ),
      ),

      // Bottom Action Button
      bottomNavigationBar: _buildBottomActionButton(theme),
    );
  }

  Widget _buildSectionTitle(
    ThemeData theme,
    String title,
    IconData icon,
    Color iconColor, {
    String? subtitle,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, bottom: 4.h),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 16.sp,
          ),
          SizedBox(width: 6.w),
          Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          if (subtitle != null) ...[
            SizedBox(width: 4.w),
            Text(
              '($subtitle)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 11.sp,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailsCard(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with type chip and time
          Row(
            children: [
              _buildTypeChip(),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _currentIncident!.timeSincePosted,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontSize: 11.sp,
                    ),
                  ),
                  if (_currentIncident!.isAnonymous) ...[
                    SizedBox(height: 2.h),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        'Anonymous',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 10.sp,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Title
          Text(
            _currentIncident!.title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8.h),

          // Description
          Text(
            _currentIncident!.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              height: 1.5,
            ),
          ),

          // Subcategory and Severity (if available)
          if (_currentIncident!.subcategoryTitle != null || _currentIncident!.severity != null) ...[
            SizedBox(height: 16.h),
            _buildCategoryDetails(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildTypeChip() {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getTypeColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: [
          BoxShadow(
            color: _getTypeColor().withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getTypeIcon(),
            size: 14.sp,
            color: _getTypeColor(),
          ),
          SizedBox(width: 4.w),
          Text(
            _currentIncident!.categoryTitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: _getTypeColor(),
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDetails(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                FluentIcons.tag_24_regular,
                size: 14.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 6.w),
              Text(
                'Category Details',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 11.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // Subcategory (if available)
          if (_currentIncident!.subcategoryTitle != null) ...[
            _buildDetailRow(
              theme,
              'Subcategory',
              _currentIncident!.subcategoryTitle!,
              FluentIcons.folder_24_regular,
            ),
            if (_currentIncident!.severity != null) SizedBox(height: 6.h),
          ],

          // Severity (if available)
          if (_currentIncident!.severity != null) ...[
            _buildDetailRow(
              theme,
              'Severity',
              _currentIncident!.severity!,
              _getSeverityIcon(_currentIncident!.severity!),
              severityColor: _getSeverityColor(_currentIncident!.severity!),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme,
    String label,
    String value,
    IconData icon, {
    Color? severityColor,
  }) {
    final color = severityColor ?? theme.colorScheme.onSurfaceVariant;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 12.sp,
          color: color,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '$label: ',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                    fontSize: 11.sp,
                  ),
                ),
                TextSpan(
                  text: value,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                    fontSize: 11.sp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  IconData _getSeverityIcon(String severity) {
    final severityLower = severity.toLowerCase();
    if (severityLower.contains('grave') || severityLower.contains('high')) {
      return FluentIcons.warning_24_filled;
    } else if (severityLower.contains('leve') || severityLower.contains('low')) {
      return FluentIcons.info_24_regular;
    }
    return FluentIcons.circle_24_regular;
  }

  Color _getSeverityColor(String severity) {
    final theme = Theme.of(context);
    final severityLower = severity.toLowerCase();
    if (severityLower.contains('grave') || severityLower.contains('high')) {
      return theme.colorScheme.error;
    } else if (severityLower.contains('leve') || severityLower.contains('low')) {
      return theme.colorScheme.tertiary;
    }
    return theme.colorScheme.onSurfaceVariant;
  }

  Widget _buildLocationCard(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address
          Row(
            children: [
              Icon(
                FluentIcons.location_24_regular,
                size: 16.sp,
                color: theme.colorScheme.secondary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  _currentIncident!.location.address,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // Map
          Container(
            height: 160.h,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: GoogleMap(
                onMapCreated: (controller) => _mapController = controller,
                initialCameraPosition: CameraPosition(
                  target: LatLng(
                    _currentIncident!.location.latitude,
                    _currentIncident!.location.longitude,
                  ),
                  zoom: 15.0,
                ),
                markers: {
                  Marker(
                    markerId: const MarkerId('incident'),
                    position: LatLng(
                      _currentIncident!.location.latitude,
                      _currentIncident!.location.longitude,
                    ),
                    infoWindow: InfoWindow(
                      title: _currentIncident!.title,
                      snippet: _currentIncident!.categoryTitle,
                    ),
                  ),
                },
                zoomControlsEnabled: false,
                mapToolbarEnabled: false,
                myLocationButtonEnabled: false,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaCard(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: IncidentMediaGallery(media: _currentIncident!.media),
    );
  }

  Widget _buildUpdatesCard(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: _currentIncident!.updates.isNotEmpty
          ? IncidentUpdatesList(
              updates: _currentIncident!.updates,
              onUpdateTap: _viewUpdate,
            )
          : _buildEmptyUpdates(theme),
    );
  }

  Widget _buildEmptyUpdates(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
      child: Column(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Icon(
              FluentIcons.comment_add_24_regular,
              size: 24.sp,
              color: theme.colorScheme.primary,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'No updates yet',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'Be the first to add an update',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionButton(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
          child: CustomButton(
            text: 'Add Update',
            onPressed: _addUpdate,
            height: 54.h,
            borderRadius: 5.r,
            prefixIcon: Icon(
              FluentIcons.comment_add_24_regular,
              size: 18.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _handleBackNavigation(BuildContext context) {
    // Ensure any open modals are closed before navigating back
    if (Navigator.of(context).canPop()) {
      // Check if there are any modal routes (like action sheets) open
      final modalRoute = ModalRoute.of(context);
      if (modalRoute != null && modalRoute.isCurrent) {
        // If we're on the current route, proceed with normal navigation
        if (context.canPop()) {
          context.pop();
        } else {
          context.go('/home');
        }
      }
    } else {
      context.go('/home');
    }
  }

  void _showActionMenu(BuildContext context) {
    // Ensure we have a valid context before showing the action sheet
    if (!mounted) return;

    IosDialog.showActionSheet(
      context: context,
      title: 'Incident Actions',
      message: 'Choose an action for this incident',
      actions: [
        CupertinoActionSheetAction(
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
            // Use a slight delay to ensure the action sheet is fully dismissed
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted) {
                _reportIncident();
              }
            });
          },
          child: Row(
            children: [
              Icon(FluentIcons.flag_24_regular),
              SizedBox(width: 12.w),
              Text('Report Issue'),
            ],
          ),
        ),
        if (_currentIncident!.userId == 'current_user') // User's own incident
          CupertinoActionSheetAction(
            onPressed: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              }
              // Use a slight delay to ensure the action sheet is fully dismissed
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  _editIncident();
                }
              });
            },
            child: Row(
              children: [
                Icon(FluentIcons.edit_24_regular),
                SizedBox(width: 12.w),
                Text('Edit Incident'),
              ],
            ),
          ),
      ],
    );
  }

  Color _getTypeColor() {
    // Use the dynamic color system based on the incident category key
    return IncidentIconUtils.getDefaultTypeColor(_currentIncident!.categoryKey);
  }

  IconData _getTypeIcon() {
    // Use the dynamic icon system based on the incident category key
    return IncidentIconUtils.getDefaultTypeIcon(_currentIncident!.categoryKey);
  }

  void _shareIncident() {
    _showShareOptions();
  }

  void _showShareOptions() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text(
          'Share Incident',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        message: Text(
          'Choose how you want to share this incident',
          style: TextStyle(
            fontSize: 13.sp,
            color: CupertinoColors.systemGrey,
          ),
        ),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _performShare(ShareType.full);
            },
            child: Row(
              children: [
                Icon(FluentIcons.share_24_regular),
                SizedBox(width: 12.w),
                Text('Share Full Details'),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _performShare(ShareType.link);
            },
            child: Row(
              children: [
                Icon(FluentIcons.link_24_regular),
                SizedBox(width: 12.w),
                Text('Share Link Only'),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _performShare(ShareType.text);
            },
            child: Row(
              children: [
                Icon(FluentIcons.document_text_24_regular),
                SizedBox(width: 12.w),
                Text('Share Text Only'),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _copyIncidentLink();
            },
            child: Row(
              children: [
                Icon(FluentIcons.copy_24_regular),
                SizedBox(width: 12.w),
                Text('Copy Link'),
              ],
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          isDefaultAction: true,
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  Future<void> _performShare(ShareType shareType) async {
    try {
      final shareUseCase = getIt<ShareIncidentUseCase>();

      final result = await shareUseCase(ShareIncidentParams(
        incident: _currentIncident!,
        shareType: shareType,
      ));

      result.fold(
        (failure) {
          _showErrorMessage('Failed to prepare share content');
        },
        (shareResult) async {
          try {
            await ShareService.shareText(
              text: shareResult.shareText,
              subject: 'Incident Report - ${_currentIncident!.categoryTitle}',
            );
          } catch (e) {
            _showErrorMessage('Failed to share incident');
          }
        },
      );
    } catch (e) {
      _showErrorMessage('An error occurred while sharing');
    }
  }

  Future<void> _copyIncidentLink() async {
    try {
      final shareUseCase = getIt<ShareIncidentUseCase>();

      final result = await shareUseCase(ShareIncidentParams(
        incident: _currentIncident!,
        shareType: ShareType.link,
      ));

      result.fold(
        (failure) {
          _showErrorMessage('Failed to generate link');
        },
        (shareResult) async {
          try {
            final link = shareResult.shareLink ?? 'https://respublicaseguridad.app/incidents/${_currentIncident!.incidentId}';
            await ShareService.copyToClipboard(link);
            _showSuccessMessage('Link copied to clipboard');
          } catch (e) {
            _showErrorMessage('Failed to copy link');
          }
        },
      );
    } catch (e) {
      _showErrorMessage('An error occurred while copying link');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _reportIncident() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Report Incident',
      message: 'Are you sure you want to report this incident as inappropriate?',
      confirmText: 'Report',
      cancelText: 'Cancel',
      onConfirm: () {
        IosDialog.showAlertDialog(
          context: context,
          title: 'Reported',
          message: 'Thank you for your report. We will review this incident.',
          confirmText: 'OK',
        );
      },
    );
  }

  void _editIncident() {
    // TODO: Navigate to edit screen
    IosDialog.showAlertDialog(
      context: context,
      title: 'Edit Incident',
      message: 'Edit functionality will be implemented soon.',
      confirmText: 'OK',
    );
  }

  void _addUpdate() async {
    // Check if user can add updates (only incident reporter)
    final currentUserId = _getCurrentUserId();
    if (currentUserId.isEmpty || currentUserId != _currentIncident!.userId) {
      IosDialog.showAlertDialog(
        context: context,
        title: 'Unauthorized',
        message: 'You can only add updates to incidents that you reported.',
        confirmText: 'OK',
      );
      return;
    }

    // Navigate to add update screen and refresh when returning
    print('🚀 Navigating to add update screen for incident: ${_currentIncident!.incidentId}');
    final result = await context.push('/incidents/add-update', extra: _currentIncident!);

    print('🔙 Returned from add update screen with result: $result');

    // If an update was added, refresh the incident data
    if (result == true && mounted) {
      print('✅ Update was successful, refreshing incident data...');
      await _refreshIncidentData();
    } else {
      print('❌ No update added or result was not true');
    }
  }

  String _getCurrentUserId() {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      return authState.user.id;
    }
    return '';
  }

  void _viewUpdate(IncidentUpdateEntity update) {
    // Navigate to update details screen
    // context.push('/incidents/update-details/${_currentIncident!.incidentId}/${update.updateId}');
  }
}
