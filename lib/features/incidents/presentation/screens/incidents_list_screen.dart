import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/incident_card.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/shimmer_incident_card.dart';

import 'package:respublicaseguridad/features/incidents/presentation/cubit/incidents_list_cubit.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';

class IncidentsListScreen extends StatelessWidget {
  const IncidentsListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => IncidentsListCubit(
        getCommunityIncidentsUseCase: GetIt.instance<GetCommunityIncidentsUseCase>(),
        getUserIncidentsUseCase: GetIt.instance<GetUserIncidentsUseCase>(),
      ),
      child: const _IncidentsListView(),
    );
  }
}

class _IncidentsListView extends StatefulWidget {
  const _IncidentsListView({Key? key}) : super(key: key);

  @override
  State<_IncidentsListView> createState() => _IncidentsListViewState();
}

class _IncidentsListViewState extends State<_IncidentsListView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String? _categoryFilter;

  String get _currentUserId {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      return authState.user.id;
    }
    return '';
  }

  List<String> get _userValidatedZoneIds {
    return [];
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Load incidents when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<IncidentsListCubit>().loadAllIncidents(
        userId: _currentUserId,
        userValidatedZoneIds: _userValidatedZoneIds,
        categoryFilter: _categoryFilter,
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<IncidentEntity> _getFilteredIncidents(IncidentsListLoaded state) {
    List<IncidentEntity> incidents;

    // Filter by tab
    switch (_tabController.index) {
      case 0: // Community
        incidents = state.communityIncidents;
        break;
      case 1: // My Reports
        incidents = state.userIncidents;
        break;
      case 2: // Recent (all incidents)
        incidents = [...state.communityIncidents, ...state.userIncidents];
        break;
      default:
        incidents = [];
    }

    // Apply category filter
    if (_categoryFilter != null) {
      incidents = incidents.where((i) => i.categoryKey == _categoryFilter).toList();
    }

    // Sort by most recent
    incidents.sort((a, b) => b.postedAt.compareTo(a.postedAt));

    return incidents;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Incidents'),
        leading: IconButton(
          onPressed: () => context.go('/home'),
          icon: const Icon(CupertinoIcons.back),
        ),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: Icon(
              _categoryFilter != null
                  ? FluentIcons.filter_24_filled
                  : FluentIcons.filter_24_regular,
            ),
            tooltip: 'Filter',
          ),
          IconButton(
            onPressed: _refreshIncidents,
            icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          onTap: (_) => setState(() {}),
          tabs: const [
            Tab(text: 'Community'),
            Tab(text: 'My Reports'),
            Tab(text: 'Recent'),
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async => _refreshIncidents(),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildIncidentsList(),
            _buildIncidentsList(),
            _buildIncidentsList(),
          ],
        ),
      ),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: 80.h),
        child: FloatingActionButton.extended(
          onPressed: _navigateToReportIncident,
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 8,
          extendedPadding: EdgeInsets.symmetric(horizontal: 20.w),
          icon: Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              FluentIcons.add_24_regular,
              size: 20.sp,
            ),
          ),
          label: Text(
            'Report Incident',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14.sp,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildIncidentsList() {
    return BlocBuilder<IncidentsListCubit, IncidentsListState>(
      builder: (context, state) {
        if (state is IncidentsListLoading) {
          return ListView.builder(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.h),
            itemCount: 6,
            itemBuilder: (context, index) => const ShimmerIncidentCard(),
          );
        }

        if (state is IncidentsListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  FluentIcons.error_circle_24_regular,
                  size: 48.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
                SizedBox(height: 16.h),
                Text(
                  'Error loading incidents',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8.h),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        if (state is IncidentsListLoaded) {
          final incidents = _getFilteredIncidents(state);

          if (incidents.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.h),
            itemCount: incidents.length,
            itemBuilder: (context, index) {
              final incident = incidents[index];
              return IncidentCard(
                incident: incident,
                onTap: () => _navigateToIncidentDetails(incident),
                onUpdate: () => _navigateToAddUpdate(incident),
              );
            },
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FluentIcons.document_search_24_regular,
              size: 64.sp,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'No Incidents Found',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              _getEmptyStateMessage(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            if (_tabController.index != 1) // Not on "My Reports" tab
              ElevatedButton.icon(
                onPressed: _navigateToReportIncident,
                icon: const Icon(FluentIcons.add_24_regular),
                label: const Text('Report Incident'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getEmptyStateMessage() {
    switch (_tabController.index) {
      case 0:
        return 'No community incidents to display.\nBe the first to report an incident.';
      case 1:
        return 'You haven\'t reported any incidents yet.\nTap the + button to create your first report.';
      case 2:
        return 'No recent incidents in your area.\nCheck back later for updates.';
      default:
        return 'No incidents found.';
    }
  }

  void _showFilterDialog() {
    // TODO: Update filter dialog to work with new category system
    // IncidentFilterBottomSheet.show(
    //   context: context,
    //   initialCategoryFilter: _categoryFilter,
    //   onApplyFilters: (categoryFilter) {
    //     setState(() {
    //       _categoryFilter = categoryFilter;
    //     });
    //   },
    // );
  }

  void _refreshIncidents() {
    context.read<IncidentsListCubit>().refresh(
      userId: _currentUserId,
      userValidatedZoneIds: _userValidatedZoneIds,
    );
  }

  void _navigateToReportIncident() {
    context.push('/incidents/report');
  }

  void _navigateToIncidentDetails(IncidentEntity incident) {
    context.push('/incidents/details', extra: incident);
  }

  void _navigateToAddUpdate(IncidentEntity incident) {
    // TODO: Implement add update navigation
    context.push('/incidents/details', extra: incident);
  }
}
