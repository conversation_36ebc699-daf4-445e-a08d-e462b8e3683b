import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/incident_location_picker.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/incident_media_picker.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/anonymous_toggle.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/subcategory_selection_dialog.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_report_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_form_cubit.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';

class IncidentReportScreen extends StatelessWidget {
  final IncidentCategoryEntity? preselectedCategory;

  const IncidentReportScreen({
    Key? key,
    this.preselectedCategory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => GetIt.instance<IncidentReportCubit>(),
        ),
        BlocProvider(
          create: (context) => IncidentFormCubit()..initializeForm(
            preselectedCategory: preselectedCategory,
            canPostAnonymously: true, // This could be determined by user permissions
          ),
        ),
      ],
      child: _IncidentReportView(
        preselectedCategory: preselectedCategory,
      ),
    );
  }
}

class _IncidentReportView extends StatefulWidget {
  final IncidentCategoryEntity? preselectedCategory;

  const _IncidentReportView({
    Key? key,
    this.preselectedCategory,
  }) : super(key: key);

  @override
  State<_IncidentReportView> createState() => _IncidentReportViewState();
}

class _IncidentReportViewState extends State<_IncidentReportView> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  String get _currentUserId {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      return authState.user.id;
    }
    return '';
  }

  @override
  void initState() {
    super.initState();
    _titleController.addListener(_onTitleChanged);
    _descriptionController.addListener(_onDescriptionChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _onTitleChanged() {
    context.read<IncidentFormCubit>().updateTitle(_titleController.text);
  }

  void _onDescriptionChanged() {
    context.read<IncidentFormCubit>().updateDescription(_descriptionController.text);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<IncidentReportCubit, IncidentReportState>(
      listener: (context, state) {
        if (state is IncidentReportSuccess) {
          IosDialog.showAlertDialog(
            context: context,
            title: 'Report Submitted',
            message: state.message,
            confirmText: 'OK',
            onConfirm: () {
              Navigator.of(context).pop(); // Close dialog
              // Use go_router to navigate back safely with result
              if (context.canPop()) {
                context.pop(true); // Return true to indicate successful creation
              } else {
                // If we can't pop, go to home
                context.go('/home');
              }
            },
          );
        } else if (state is IncidentReportError) {
          IosDialog.showAlertDialog(
            context: context,
            title: 'Error',
            message: state.message,
            confirmText: 'OK',
          );
        }
      },
      child: BlocBuilder<IncidentFormCubit, IncidentFormState>(
        builder: (context, formState) {
          if (formState is! IncidentFormUpdated) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          return Scaffold(
            backgroundColor: theme.scaffoldBackgroundColor,
            appBar: AppBar(title: Text('Report Incident')),
            body: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Anonymous toggle at top if available
                  if (formState.canPostAnonymously) _buildAnonymousSection(theme, formState),

                  // Main content
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: [
                          SizedBox(height: 20.h),

                          // Incident Details Section
                          _buildSectionTitle(
                            theme,
                            'Incident Details',
                            FluentIcons.edit_24_regular,
                            theme.colorScheme.primary,
                          ),
                          _buildDetailsCard(theme),
                          SizedBox(height: 24.h),

                          // Location Section
                          _buildSectionTitle(
                            theme,
                            'Location',
                            FluentIcons.location_24_regular,
                            theme.colorScheme.secondary,
                          ),
                          _buildLocationCard(theme, formState),
                          SizedBox(height: 24.h),

                          // Media Section
                          _buildSectionTitle(
                            theme,
                            'Media',
                            FluentIcons.image_24_regular,
                            theme.colorScheme.tertiary,
                            subtitle: 'Optional',
                          ),
                          _buildMediaCard(theme, formState),
                          SizedBox(height: 24.h), // Bottom spacing
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom Submit Button
            bottomNavigationBar: _buildSubmitButton(theme, formState),
          );
        },
      ),
    );
  }

  Widget _buildAnonymousSection(ThemeData theme, IncidentFormUpdated formState) {
    return AnonymousToggle(
      isAnonymous: formState.isAnonymous,
      onChanged: (value) {
        context.read<IncidentFormCubit>().updateAnonymous(value);
      },
    );
  }

  Widget _buildSectionTitle(
    ThemeData theme,
    String title,
    IconData icon,
    Color iconColor, {
    String? subtitle,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, bottom: 4.h),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 16.sp),
          SizedBox(width: 6.w),
          Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          if (subtitle != null) ...[
            SizedBox(width: 4.w),
            Text(
              '($subtitle)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 11.sp,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailsCard(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title Field
          _buildModernTextField(
            controller: _titleController,
            label: 'Title',
            hint: 'Brief description of the incident',
            icon: FluentIcons.text_header_1_24_regular,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a title';
              }
              if (value.trim().length < 5) {
                return 'Title must be at least 5 characters';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),

          // Description Field
          _buildModernTextField(
            controller: _descriptionController,
            label: 'Description (Optional)',
            hint: 'Provide detailed information about the incident',
            icon: FluentIcons.text_align_left_24_regular,
            maxLines: 4,
            validator: (value) {
              // Description is optional, only validate if provided
              if (value != null && value.trim().isNotEmpty && value.trim().length < 10) {
                return 'Description must be at least 10 characters';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }






  Widget _buildLocationCard(ThemeData theme, IncidentFormUpdated formState) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: IncidentLocationPicker(
        selectedLocation: formState.selectedLocation,
        onLocationSelected: (location) {
          context.read<IncidentFormCubit>().updateLocation(location);
        },
      ),
    );
  }

  Widget _buildMediaCard(ThemeData theme, IncidentFormUpdated formState) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: IncidentMediaPicker(
        selectedMedia: formState.selectedMedia,
        userId: _currentUserId,
        onMediaChanged: (media) {
          context.read<IncidentFormCubit>().updateMedia(media);
        },
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, size: 20.sp),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            filled: true,
            fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
              alpha: 0.3,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(ThemeData theme, IncidentFormUpdated formState) {
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 24.h),
      child: SafeArea(
        child: BlocBuilder<IncidentReportCubit, IncidentReportState>(
          builder: (context, state) {
            final isSubmitting = state is IncidentReportSubmitting;

            return CustomButton(
              text: isSubmitting ? 'Submitting...' : 'Submit Report',
              onPressed: () => _submitReport(formState),
              disabled: !formState.isFormValid || isSubmitting,
              height: 54.h,
              borderRadius: 5.r,
              prefixIcon: isSubmitting
                  ? SizedBox(
                      width: 18.sp,
                      height: 18.sp,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(
                      FluentIcons.send_24_regular,
                      size: 18.sp,
                      color: Colors.white,
                    ),
            );
          },
        ),
      ),
    );
  }

  void _submitReport(IncidentFormUpdated formState) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final userId = _currentUserId;
    if (userId.isEmpty) {
      IosDialog.showAlertDialog(
        context: context,
        title: 'Error',
        message: 'You must be logged in to submit a report.',
        confirmText: 'OK',
      );
      return;
    }

    if (formState.selectedLocation == null) {
      IosDialog.showAlertDialog(
        context: context,
        title: 'Location Required',
        message: 'Please select a location for your incident report.',
        confirmText: 'OK',
      );
      return;
    }

    if (formState.selectedCategory == null) {
      IosDialog.showAlertDialog(
        context: context,
        title: 'Category Required',
        message: 'Please select a category for your incident report.',
        confirmText: 'OK',
      );
      return;
    }

    // Check if category has subcategories and show dialog if needed
    if (formState.selectedCategory!.subcategories.isNotEmpty) {
      _showSubcategorySelectionDialog(formState);
    } else {
      _performSubmit(formState);
    }
  }

  void _showSubcategorySelectionDialog(IncidentFormUpdated formState) {
    SubcategorySelectionDialog.show(
      context: context,
      category: formState.selectedCategory!,
      selectedSubcategory: formState.selectedSubcategory,
      selectedSeverity: formState.selectedSeverity,
      onSelectionComplete: (subcategory, severity) {
        context.read<IncidentFormCubit>().updateSubcategoryAndSeverity(
          subcategory: subcategory,
          severity: severity,
        );
        _performSubmit(formState.copyWith(
          selectedSubcategory: subcategory,
          selectedSeverity: severity,
        ));
      },
      onSkip: () {
        _performSubmit(formState);
      },
    );
  }

  void _performSubmit(IncidentFormUpdated formState) {
    // Submit the incident using the cubit
    final description = _descriptionController.text.trim();
    context.read<IncidentReportCubit>().submitIncident(
      userId: _currentUserId,
      categoryKey: formState.selectedCategory!.key,
      categoryTitle: formState.selectedCategory!.title,
      subcategoryKey: formState.selectedSubcategory?.key,
      subcategoryTitle: formState.selectedSubcategory?.title,
      severity: formState.selectedSeverity,
      title: _titleController.text.trim(),
      description: description.isEmpty ? 'No additional details provided.' : description,
      location: formState.selectedLocation!,
      media: formState.selectedMedia,
      isAnonymous: formState.isAnonymous,
    );
  }
}
