import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/my_incident_card.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/shimmer_incident_card.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/my_incidents_cubit.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';

class MyIncidentsScreen extends StatelessWidget {
  const MyIncidentsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<MyIncidentsCubit>(),
      child: const _MyIncidentsView(),
    );
  }
}

class _MyIncidentsView extends StatefulWidget {
  const _MyIncidentsView({Key? key}) : super(key: key);

  @override
  State<_MyIncidentsView> createState() => _MyIncidentsViewState();
}

class _MyIncidentsViewState extends State<_MyIncidentsView> {
  int _selectedTabIndex = 0;

  String get _currentUserId {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      return authState.user.id;
    }
    return '';
  }

  @override
  void initState() {
    super.initState();
    _loadIncidents();
  }

  void _loadIncidents() {
    final userId = _currentUserId;
    if (userId.isNotEmpty) {
      print('🔍 MyIncidentsScreen: Loading incidents for user $userId');
      context.read<MyIncidentsCubit>().loadUserIncidents(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('My Incidents'),
        actions: [
          IconButton(
            onPressed: _refreshIncidents,
            icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocListener<MyIncidentsCubit, MyIncidentsState>(
        listener: (context, state) {
          if (state is MyIncidentsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
                action: SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: _refreshIncidents,
                ),
              ),
            );
          }
        },
        child: BlocBuilder<MyIncidentsCubit, MyIncidentsState>(
          builder: (context, state) {
            return Column(
              children: [
                // Button-style tab navigation
                _buildTabNavigation(context),

                // Content area
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async => _refreshIncidents(),
                    child: _selectedTabIndex == 0
                        ? _buildPublishedIncidents(state)
                        : _buildDraftIncidents(state),
                  ),
                ),
              ],
            );
          },
        ),
      ),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: 80.h),
        child: FloatingActionButton.extended(
          onPressed: _navigateToReportIncident,
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 8,
          extendedPadding: EdgeInsets.symmetric(horizontal: 20.w),
          icon: Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              FluentIcons.add_24_regular,
              size: 20.sp,
            ),
          ),
          label: Text(
            'New Report',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14.sp,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildPublishedIncidents(MyIncidentsState state) {
    if (state is MyIncidentsLoading) {
      return ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: 5, // Show 5 shimmer cards
        itemBuilder: (context, index) => const ShimmerIncidentCard(),
      );
    }

    if (state is MyIncidentsError) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.error_circle_24_regular,
                size: 64.sp,
                color: Theme.of(context).colorScheme.error,
              ),
              SizedBox(height: 16.h),
              Text(
                'Error Loading Incidents',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                state.message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: _refreshIncidents,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (state is MyIncidentsLoaded) {
      final publishedIncidents = state.publishedIncidents;
      
      // Debug print for published incidents
      print('📋 MyIncidentsScreen: Loaded ${publishedIncidents.length} published incidents');
      for (var i = 0; i < publishedIncidents.length; i++) {
        final incident = publishedIncidents[i];
        print('📋 Published #${i+1}: ID=${incident.incidentId}, Title=${incident.title}, Status=${incident.status.name}');
      }

      if (publishedIncidents.isEmpty) {
        return _buildEmptyState(
          icon: FluentIcons.document_checkmark_24_regular,
          title: 'No Published Incidents',
          message: 'You haven\'t published any incident reports yet.\nTap the + button to create your first report.',
          showCreateButton: true,
        );
      }

      return ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: publishedIncidents.length,
        itemBuilder: (context, index) {
          final incident = publishedIncidents[index];
          return MyIncidentCard(
            incident: incident,
            onTap: () => _navigateToIncidentDetails(incident),
            onEdit: null, // Published incidents can't be edited
            onDelete: () => _showDeleteConfirmation(incident),
            showStatus: true,
          );
        },
      );
    }

    // Default case for initial state
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildDraftIncidents(MyIncidentsState state) {
    if (state is MyIncidentsLoading) {
      return ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: 3, // Show 3 shimmer cards for drafts
        itemBuilder: (context, index) => const ShimmerIncidentCard(),
      );
    }

    if (state is MyIncidentsError) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.error_circle_24_regular,
                size: 64.sp,
                color: Theme.of(context).colorScheme.error,
              ),
              SizedBox(height: 16.h),
              Text(
                'Error Loading Drafts',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                state.message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: _refreshIncidents,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (state is MyIncidentsLoaded) {
      final draftIncidents = state.draftIncidents;
      
      // Debug print for draft incidents
      print('📝 MyIncidentsScreen: Loaded ${draftIncidents.length} draft incidents');
      for (var i = 0; i < draftIncidents.length; i++) {
        final incident = draftIncidents[i];
        print('📝 Draft #${i+1}: ID=${incident.incidentId}, Title=${incident.title}, PostedAt=${incident.postedAt}');
      }

      if (draftIncidents.isEmpty) {
        return _buildEmptyState(
          icon: FluentIcons.document_edit_24_regular,
          title: 'No Draft Incidents',
          message: 'You don\'t have any draft incident reports.\nStart creating a report to save it as a draft.',
          showCreateButton: true,
        );
      }

      return ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: draftIncidents.length,
        itemBuilder: (context, index) {
          final incident = draftIncidents[index];
          return MyIncidentCard(
            incident: incident,
            onTap: () => _navigateToEditDraft(incident),
            onEdit: () => _navigateToEditDraft(incident),
            onDelete: () => _showDeleteConfirmation(incident),
            showStatus: false, // Drafts don't need status indicators
            isDraft: true,
          );
        },
      );
    }

    // Default case for initial state
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String message,
    bool showCreateButton = false,
  }) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64.sp,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (showCreateButton) ...[
              SizedBox(height: 24.h),
              ElevatedButton.icon(
                onPressed: _navigateToReportIncident,
                icon: const Icon(FluentIcons.add_24_regular),
                label: const Text('Create Report'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _refreshIncidents() {
    final userId = _currentUserId;
    if (userId.isNotEmpty) {
      print('🔄 MyIncidentsScreen: Refreshing incidents for user $userId');
      context.read<MyIncidentsCubit>().refreshIncidents(userId);
    }
  }

  void _navigateToReportIncident() async {
    final result = await context.push('/incidents/report');

    // If an incident was successfully created, refresh the list
    if (result == true && mounted) {
      _refreshIncidents();
    }
  }

  void _navigateToIncidentDetails(IncidentEntity incident) {
    context.push('/incidents/details', extra: incident);
  }

  void _navigateToEditDraft(IncidentEntity incident) async {
    final result = await context.push('/incidents/report', extra: incident);

    // If the draft was successfully updated/published, refresh the list
    if (result == true && mounted) {
      _refreshIncidents();
    }
  }

  void _showDeleteConfirmation(IncidentEntity incident) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Incident'),
        content: Text(
          incident.incidentId.startsWith('draft_')
              ? 'Are you sure you want to delete this draft? This action cannot be undone.'
              : 'Are you sure you want to delete this incident report? This action cannot be undone.',
        ),
        actions: [
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.of(context).pop();
              _deleteIncident(incident);
            },
            child: const Text('Delete'),
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _deleteIncident(IncidentEntity incident) {
    final userId = context.read<AuthBloc>().state.user.id;
    print('🗑️ MyIncidentsScreen: Deleting incident ${incident.incidentId} for user $userId');
    context.read<MyIncidentsCubit>().deleteIncident(incident.incidentId, userId);

    // Show confirmation snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          incident.incidentId.startsWith('draft_')
              ? 'Draft deleted successfully'
              : 'Incident deleted successfully',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildTabNavigation(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              context: context,
              index: 0,
              icon: FluentIcons.document_checkmark_24_regular,
              label: 'Published',
              isSelected: _selectedTabIndex == 0,
            ),
          ),
          Expanded(
            child: _buildTabButton(
              context: context,
              index: 1,
              icon: FluentIcons.document_edit_24_regular,
              label: 'Drafts',
              isSelected: _selectedTabIndex == 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required BuildContext context,
    required int index,
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTabIndex = index;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: EdgeInsets.all(4.w),
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : Colors.transparent,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 20.r,
              color: isSelected
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(width: 8.w),
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
