import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/view_incident_update_usecase.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/core/di/injection.dart';

class IncidentUpdateDetailsScreen extends StatefulWidget {
  final String incidentId;
  final String updateId;

  const IncidentUpdateDetailsScreen({
    Key? key,
    required this.incidentId,
    required this.updateId,
  }) : super(key: key);

  @override
  State<IncidentUpdateDetailsScreen> createState() => _IncidentUpdateDetailsScreenState();
}

class _IncidentUpdateDetailsScreenState extends State<IncidentUpdateDetailsScreen> {
  ViewIncidentUpdateResult? _updateResult;
  bool _isLoading = true;
  String? _errorMessage;

  String get _currentUserId {
    final authState = context.read<AuthBloc>().state;
    if (authState.isAuthenticated) {
      return authState.user.id;
    }
    return '';
  }

  @override
  void initState() {
    super.initState();
    _loadUpdateDetails();
  }

  Future<void> _loadUpdateDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final useCase = getIt<ViewIncidentUpdateUseCase>();
      final result = await useCase(ViewIncidentUpdateParams(
        incidentId: widget.incidentId,
        updateId: widget.updateId,
        userId: _currentUserId,
      ));

      result.fold(
        (failure) {
          setState(() {
            _isLoading = false;
            _errorMessage = failure.message;
          });
        },
        (updateResult) {
          setState(() {
            _isLoading = false;
            _updateResult = updateResult;
          });
        },
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'An error occurred while loading the update';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(theme),
      body: _buildBody(theme),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: const Text('Update Details'),
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: const Icon(CupertinoIcons.back),
      ),
      actions: _updateResult != null ? [
        if (_updateResult!.canEdit)
          IconButton(
            onPressed: _editUpdate,
            icon: const Icon(FluentIcons.edit_24_regular),
            tooltip: 'Edit',
          ),
        IconButton(
          onPressed: _showMoreOptions,
          icon: const Icon(FluentIcons.more_vertical_24_regular),
          tooltip: 'More',
        ),
      ] : null,
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FluentIcons.error_circle_24_regular,
              size: 64.sp,
              color: theme.colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _loadUpdateDetails,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_updateResult == null) {
      return const Center(
        child: Text('Update not found'),
      );
    }

    return SafeArea(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildIncidentContext(theme),
              SizedBox(height: 24.h),
              _buildUpdateCard(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIncidentContext(ThemeData theme) {
    final incident = _updateResult!.incident;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FluentIcons.info_24_regular,
                size: 16.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                'Incident Context',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            incident.title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            '${incident.categoryTitle} • ${incident.location.address}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateCard(ThemeData theme) {
    final update = _updateResult!.update;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with type and timestamp
          Row(
            children: [
              _buildTypeChip(theme, update.type),
              const Spacer(),
              Text(
                update.formattedTimestamp,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Author info
          Row(
            children: [
              Icon(
                update.isAnonymous 
                    ? FluentIcons.person_24_regular 
                    : FluentIcons.person_24_filled,
                size: 16.sp,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 8.w),
              Text(
                update.displayAuthorName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Content
          _buildUpdateContent(theme, update),

          // Media attachments (if any)
          if (_hasMedia(update)) ...[
            SizedBox(height: 16.h),
            _buildMediaSection(theme, update),
          ],
        ],
      ),
    );
  }

  Widget _buildTypeChip(ThemeData theme, IncidentUpdateType type) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getTypeColor(type).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getTypeIcon(type),
            size: 12.sp,
            color: _getTypeColor(type),
          ),
          SizedBox(width: 4.w),
          Text(
            _getTypeDisplayName(type),
            style: theme.textTheme.bodySmall?.copyWith(
              color: _getTypeColor(type),
              fontWeight: FontWeight.w600,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateContent(ThemeData theme, IncidentUpdateEntity update) {
    if (update.type == IncidentUpdateType.link) {
      return _buildLinkContent(theme, update);
    }

    return Text(
      update.content,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: theme.colorScheme.onSurface,
        height: 1.5,
      ),
    );
  }

  Widget _buildLinkContent(ThemeData theme, IncidentUpdateEntity update) {
    final metadata = update.metadata;
    final description = metadata?['description'] as String?;
    final url = metadata?['url'] as String? ?? update.content;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (description != null && description.isNotEmpty) ...[
          Text(
            description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              height: 1.5,
            ),
          ),
          SizedBox(height: 12.h),
        ],
        InkWell(
          onTap: () => _launchUrl(url),
          borderRadius: BorderRadius.circular(4.r),
          child: Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerLowest,
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  FluentIcons.link_24_regular,
                  size: 16.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    url,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      decoration: TextDecoration.underline,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  FluentIcons.open_24_regular,
                  size: 14.sp,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _hasMedia(IncidentUpdateEntity update) {
    final metadata = update.metadata;
    if (metadata == null) return false;
    final media = metadata['media'] as List?;
    return media != null && media.isNotEmpty;
  }

  Widget _buildMediaSection(ThemeData theme, IncidentUpdateEntity update) {
    final metadata = update.metadata!;
    final mediaList = metadata['media'] as List;
    final mediaCount = metadata['mediaCount'] as int? ?? mediaList.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FluentIcons.attach_24_regular,
              size: 14.sp,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 6.w),
            Text(
              'Attachments ($mediaCount)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
                fontSize: 11.sp,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Container(
          height: 80.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: mediaList.length,
            itemBuilder: (context, index) {
              final mediaData = mediaList[index] as Map<String, dynamic>;
              return _buildMediaThumbnail(theme, mediaData, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMediaThumbnail(ThemeData theme, Map<String, dynamic> mediaData, int index) {
    final type = mediaData['type'] as String;
    final url = mediaData['url'] as String;
    final thumbnailUrl = mediaData['thumbnailUrl'] as String?;

    return Container(
      margin: EdgeInsets.only(right: 8.w),
      width: 80.w,
      height: 80.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.r),
        child: Stack(
          children: [
            if (type == 'photo')
              Image.network(
                thumbnailUrl ?? url,
                width: 80.w,
                height: 80.h,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: theme.colorScheme.surfaceContainerLowest,
                    child: Icon(
                      FluentIcons.image_24_regular,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  );
                },
              )
            else
              Container(
                color: theme.colorScheme.surfaceContainerLowest,
                child: Center(
                  child: Icon(
                    FluentIcons.video_24_regular,
                    color: theme.colorScheme.onSurfaceVariant,
                    size: 24.sp,
                  ),
                ),
              ),

            // Tap overlay
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _viewMedia(url, type),
                child: Container(
                  width: 80.w,
                  height: 80.h,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _viewMedia(String url, String type) {
    // TODO: Implement media viewer
    IosDialog.showAlertDialog(
      context: context,
      title: 'View Media',
      message: 'Media viewer will be implemented soon.',
      confirmText: 'OK',
    );
  }

  IconData _getTypeIcon(IncidentUpdateType type) {
    switch (type) {
      case IncidentUpdateType.textUpdate:
        return FluentIcons.document_text_24_regular;
      case IncidentUpdateType.link:
        return FluentIcons.link_24_regular;
      case IncidentUpdateType.statusChange:
        return FluentIcons.arrow_sync_24_regular;
      case IncidentUpdateType.communityObservation:
        return FluentIcons.people_24_regular;
    }
  }

  Color _getTypeColor(IncidentUpdateType type) {
    final theme = Theme.of(context);
    switch (type) {
      case IncidentUpdateType.textUpdate:
        return theme.colorScheme.primary;
      case IncidentUpdateType.link:
        return theme.colorScheme.secondary;
      case IncidentUpdateType.statusChange:
        return theme.colorScheme.tertiary;
      case IncidentUpdateType.communityObservation:
        return Colors.orange;
    }
  }

  String _getTypeDisplayName(IncidentUpdateType type) {
    switch (type) {
      case IncidentUpdateType.textUpdate:
        return 'Text Update';
      case IncidentUpdateType.link:
        return 'Link';
      case IncidentUpdateType.statusChange:
        return 'Status Change';
      case IncidentUpdateType.communityObservation:
        return 'Observation';
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showErrorDialog('Cannot open this link');
      }
    } catch (e) {
      _showErrorDialog('Invalid URL');
    }
  }

  void _editUpdate() {
    // TODO: Navigate to edit update screen
    IosDialog.showAlertDialog(
      context: context,
      title: 'Edit Update',
      message: 'Edit functionality will be implemented in a future update.',
      confirmText: 'OK',
    );
  }

  void _showMoreOptions() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        actions: [
          if (_updateResult!.canDelete)
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmDeleteUpdate();
              },
              isDestructiveAction: true,
              child: Row(
                children: [
                  Icon(FluentIcons.delete_24_regular),
                  SizedBox(width: 12.w),
                  Text('Delete Update'),
                ],
              ),
            ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _shareUpdate();
            },
            child: Row(
              children: [
                Icon(FluentIcons.share_24_regular),
                SizedBox(width: 12.w),
                Text('Share Update'),
              ],
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          isDefaultAction: true,
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  void _confirmDeleteUpdate() {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Delete Update',
      message: 'Are you sure you want to delete this update? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: _deleteUpdate,
    );
  }

  void _deleteUpdate() {
    // TODO: Implement delete update functionality
    IosDialog.showAlertDialog(
      context: context,
      title: 'Delete Update',
      message: 'Delete functionality will be implemented in a future update.',
      confirmText: 'OK',
    );
  }

  void _shareUpdate() {
    // TODO: Implement share update functionality
    IosDialog.showAlertDialog(
      context: context,
      title: 'Share Update',
      message: 'Share functionality will be implemented in a future update.',
      confirmText: 'OK',
    );
  }

  void _showErrorDialog(String message) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: message,
      confirmText: 'OK',
    );
  }
}
