import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_category_selection_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/category_card_entity.dart';
import 'package:respublicaseguridad/features/incidents/presentation/widgets/shimmer_category_card.dart';

class IncidentCategorySelectionScreen extends StatelessWidget {
  const IncidentCategorySelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<IncidentCategorySelectionCubit>()..loadCategories(),
      child: const _IncidentCategorySelectionView(),
    );
  }
}

class _IncidentCategorySelectionView extends StatefulWidget {
  const _IncidentCategorySelectionView({Key? key}) : super(key: key);

  @override
  State<_IncidentCategorySelectionView> createState() => _IncidentCategorySelectionViewState();
}

class _IncidentCategorySelectionViewState extends State<_IncidentCategorySelectionView> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    context.read<IncidentCategorySelectionCubit>().searchCategories(query);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Report Incident',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Section
          _buildSearchSection(theme),

          // Categories Grid
          Expanded(
            child: BlocBuilder<IncidentCategorySelectionCubit, IncidentCategorySelectionState>(
              builder: (context, state) {
                if (state is IncidentCategorySelectionLoading) {
                  return _buildShimmerGrid();
                } else if (state is IncidentCategorySelectionLoaded) {
                  return _buildCategoriesGrid(state.filteredCategories);
                } else if (state is IncidentCategorySelectionError) {
                  return _buildErrorState(state.message);
                }
                return _buildShimmerGrid();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection(ThemeData theme) {
    return Container(
      color: Theme.of(context).cardColor,
      child: Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
      
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search categories',
            hintStyle: TextStyle(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
              fontSize: 15.sp,
            ),
            prefixIcon: Icon(
              FluentIcons.search_24_regular,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
              size: 18.sp,
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      context.read<IncidentCategorySelectionCubit>().clearSearch();
                    },
                    icon: Icon(
                      FluentIcons.dismiss_24_regular,
                      color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
                      size: 18.sp,
                    ),
                  )
                : null,
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          ),
          style: TextStyle(
            fontSize: 15.sp,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
    ));
  }

  Widget _buildShimmerGrid() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 6.w,
          mainAxisSpacing: 6.h,
          childAspectRatio: 0.85,
        ),
        itemCount: 6,
        itemBuilder: (context, index) => const ShimmerCategoryCard(),
      ),
    );
  }

  Widget _buildCategoriesGrid(List<IncidentCategoryEntity> filteredCategories) {
    if (filteredCategories.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 6.w,
          mainAxisSpacing: 6.h,
          childAspectRatio: 0.85,
        ),
        itemCount: filteredCategories.length,
        itemBuilder: (context, index) {
          final category = filteredCategories[index];
          return CategoryCardEntity(
            category: category,
            onTap: () => _navigateToReportForm(category),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.search_24_regular,
            size: 64.sp,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            'No categories found',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try adjusting your search terms',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.error_circle_24_regular,
            size: 64.sp,
            color: theme.colorScheme.error.withValues(alpha: 0.7),
          ),
          SizedBox(height: 16.h),
          Text(
            'Error loading categories',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              context.read<IncidentCategorySelectionCubit>().retry();
            },
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _navigateToReportForm(IncidentCategoryEntity selectedCategory) {
    context.push('/incidents/report', extra: selectedCategory);
  }


}


 