import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class IncidentUpdatesList extends StatelessWidget {
  final List<IncidentUpdateEntity> updates;
  final Function(IncidentUpdateEntity)? onUpdateTap;

  const IncidentUpdatesList({
    Key? key,
    required this.updates,
    this.onUpdateTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (updates.isEmpty) {
      return _buildEmptyState(context);
    }

    final sortedUpdates = updates.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return Column(
      children: sortedUpdates.asMap().entries.map((entry) {
        final index = entry.key;
        final update = entry.value;
        return Container(
          margin: EdgeInsets.only(
            left: 16.w,
            right: 16.w,
            bottom: index == sortedUpdates.length - 1 ? 0 : 8.h,
          ),
          child: _buildUpdateCard(context, update),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            FluentIcons.comment_24_regular,
            size: 48.sp,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            'No Updates Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Be the first to add an update to this incident',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateCard(BuildContext context, IncidentUpdateEntity update) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => onUpdateTap?.call(update),
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(5.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              offset: const Offset(0, 2),
              blurRadius: 8.r,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => onUpdateTap?.call(update),
            borderRadius: BorderRadius.circular(5.r),
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with type badge and timestamp
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: _getUpdateTypeColor(update.type).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getUpdateTypeIcon(update.type),
                              size: 14.sp,
                              color: _getUpdateTypeColor(update.type),
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              update.type.displayName,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: _getUpdateTypeColor(update.type),
                                fontWeight: FontWeight.w600,
                                fontSize: 12.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          update.formattedTimestamp,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),

                  // Author information
                  Row(
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Icon(
                          update.isAnonymous
                              ? FluentIcons.person_question_mark_24_regular
                              : FluentIcons.person_24_regular,
                          size: 12.sp,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          update.displayAuthorName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                      Icon(
                        FluentIcons.chevron_right_24_regular,
                        size: 16.sp,
                        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),

                  // Update content preview
                  _buildUpdateContentPreview(context, update),

                  // Media indicator if present
                  if (_hasMediaAttachments(update)) ...[
                    SizedBox(height: 12.h),
                    _buildMediaIndicator(theme, update),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateContentPreview(BuildContext context, IncidentUpdateEntity update) {
    final theme = Theme.of(context);

    if (update.isLink) {
      final linkDescription = update.metadata?['description'] as String?;
      final displayText = linkDescription?.isNotEmpty == true ? linkDescription! : update.content;

      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FluentIcons.link_24_regular,
                  size: 16.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Link Shared',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              displayText,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      );
    } else {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Text(
          update.content,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface,
            height: 1.4,
          ),
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }
  }

  bool _hasMediaAttachments(IncidentUpdateEntity update) {
    final metadata = update.metadata;
    if (metadata == null) return false;
    final media = metadata['media'] as List?;
    return media != null && media.isNotEmpty;
  }

  Widget _buildMediaIndicator(ThemeData theme, IncidentUpdateEntity update) {
    final metadata = update.metadata!;
    final mediaList = metadata['media'] as List;
    final mediaCount = metadata['mediaCount'] as int? ?? mediaList.length;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: theme.colorScheme.secondary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.attach_24_regular,
            size: 14.sp,
            color: theme.colorScheme.secondary,
          ),
          SizedBox(width: 6.w),
          Text(
            '$mediaCount attachment${mediaCount > 1 ? 's' : ''}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.secondary,
              fontWeight: FontWeight.w600,
              fontSize: 11.sp,
            ),
          ),
        ],
      ),
    );
  }

  Color _getUpdateTypeColor(IncidentUpdateType type) {
    switch (type) {
      case IncidentUpdateType.textUpdate:
        return Colors.blue;
      case IncidentUpdateType.link:
        return Colors.purple;
      case IncidentUpdateType.statusChange:
        return Colors.orange;
      case IncidentUpdateType.communityObservation:
        return Colors.green;
    }
  }

  IconData _getUpdateTypeIcon(IncidentUpdateType type) {
    switch (type) {
      case IncidentUpdateType.textUpdate:
        return FluentIcons.comment_24_regular;
      case IncidentUpdateType.link:
        return FluentIcons.link_24_regular;
      case IncidentUpdateType.statusChange:
        return FluentIcons.arrow_sync_24_regular;
      case IncidentUpdateType.communityObservation:
        return FluentIcons.people_24_regular;
    }
  }


}
