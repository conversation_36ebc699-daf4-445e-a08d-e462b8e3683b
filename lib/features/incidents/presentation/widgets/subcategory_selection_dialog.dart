import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

/// iOS-style dialog for selecting incident subcategory and severity
class SubcategorySelectionDialog extends StatefulWidget {
  final IncidentCategoryEntity category;
  final IncidentSubcategoryEntity? selectedSubcategory;
  final String? selectedSeverity;
  final Function(IncidentSubcategoryEntity?, String?) onSelectionComplete;
  final VoidCallback onSkip;

  const SubcategorySelectionDialog({
    Key? key,
    required this.category,
    this.selectedSubcategory,
    this.selectedSeverity,
    required this.onSelectionComplete,
    required this.onSkip,
  }) : super(key: key);

  @override
  State<SubcategorySelectionDialog> createState() => _SubcategorySelectionDialogState();

  /// Static method to show the dialog
  static Future<void> show({
    required BuildContext context,
    required IncidentCategoryEntity category,
    IncidentSubcategoryEntity? selectedSubcategory,
    String? selectedSeverity,
    required Function(IncidentSubcategoryEntity?, String?) onSelectionComplete,
    required VoidCallback onSkip,
  }) {
    return showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SubcategorySelectionDialog(
        category: category,
        selectedSubcategory: selectedSubcategory,
        selectedSeverity: selectedSeverity,
        onSelectionComplete: onSelectionComplete,
        onSkip: onSkip,
      ),
    );
  }
}

class _SubcategorySelectionDialogState extends State<SubcategorySelectionDialog> {
  IncidentSubcategoryEntity? _selectedSubcategory;
  String? _selectedSeverity;

  @override
  void initState() {
    super.initState();
    _selectedSubcategory = widget.selectedSubcategory;
    _selectedSeverity = widget.selectedSeverity;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CupertinoAlertDialog(
      title: Text(
        'Additional Details',
        style: GoogleFonts.outfit(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 8.h),
          Text(
            'Would you like to specify a subcategory and severity level for "${widget.category.title}"?',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          _buildSubcategorySelection(theme),
          if (_selectedSubcategory != null && _selectedSubcategory!.severities.isNotEmpty) ...[
            SizedBox(height: 16.h),
            _buildSeveritySelection(theme),
          ],
        ],
      ),
      actions: [
        CupertinoDialogAction(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onSkip();
          },
          child: Text(
            'Skip',
            style: GoogleFonts.outfit(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        CupertinoDialogAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.of(context).pop();
            widget.onSelectionComplete(_selectedSubcategory, _selectedSeverity);
          },
          child: Text(
            'Continue',
            style: GoogleFonts.outfit(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubcategorySelection(ThemeData theme) {
    return Container(
      constraints: BoxConstraints(maxHeight: 200.h),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subcategory',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
            SizedBox(height: 8.h),
            ...widget.category.subcategories.map((subcategory) {
              final isSelected = _selectedSubcategory?.key == subcategory.key;
              
              return Padding(
                padding: EdgeInsets.only(bottom: 6.h),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedSubcategory = subcategory;
                      _selectedSeverity = null; // Reset severity when subcategory changes
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: isSelected
                        ? theme.colorScheme.primary.withValues(alpha: 0.1)
                        : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(
                        color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withValues(alpha: 0.2),
                        width: isSelected ? 1.5 : 1,
                      ),
                    ),
                    child: Text(
                      subcategory.title,
                      style: GoogleFonts.outfit(
                        fontSize: 13.sp,
                        color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSeveritySelection(ThemeData theme) {
    if (_selectedSubcategory == null || _selectedSubcategory!.severities.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      constraints: BoxConstraints(maxHeight: 150.h),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Severity Level',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.secondary,
              ),
            ),
            SizedBox(height: 8.h),
            ..._selectedSubcategory!.severities.map((severity) {
              final isSelected = _selectedSeverity == severity;
              final isGrave = severity.toLowerCase().contains('grave');
              
              return Padding(
                padding: EdgeInsets.only(bottom: 6.h),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedSeverity = severity;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: isSelected
                        ? (isGrave
                            ? theme.colorScheme.error.withValues(alpha: 0.1)
                            : theme.colorScheme.secondary.withValues(alpha: 0.1))
                        : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(
                        color: isSelected
                          ? (isGrave ? theme.colorScheme.error : theme.colorScheme.secondary)
                          : theme.colorScheme.outline.withValues(alpha: 0.2),
                        width: isSelected ? 1.5 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isGrave ? FluentIcons.warning_24_filled : FluentIcons.info_24_regular,
                          size: 14.sp,
                          color: isSelected
                            ? (isGrave ? theme.colorScheme.error : theme.colorScheme.secondary)
                            : theme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            severity,
                            style: GoogleFonts.outfit(
                              fontSize: 13.sp,
                              color: isSelected
                                ? (isGrave ? theme.colorScheme.error : theme.colorScheme.secondary)
                                : theme.colorScheme.onSurfaceVariant,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
