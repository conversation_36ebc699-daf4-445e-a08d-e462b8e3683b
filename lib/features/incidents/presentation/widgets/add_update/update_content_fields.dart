import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class UpdateContentFields extends StatelessWidget {
  final IncidentUpdateType selectedType;
  final TextEditingController contentController;
  final TextEditingController linkController;
  final TextEditingController linkDescriptionController;
  final Function(String) onContentChanged;
  final Function(String) onLinkChanged;
  final Function(String) onLinkDescriptionChanged;

  const UpdateContentFields({
    Key? key,
    required this.selectedType,
    required this.contentController,
    required this.linkController,
    required this.linkDescriptionController,
    required this.onContentChanged,
    required this.onLinkChanged,
    required this.onLinkDescriptionChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            offset: Offset(0, 2.h),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getContentIcon(),
                size: 20.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                _getContentTitle(),
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          if (selectedType == IncidentUpdateType.link) ...[
            _buildTextField(
              context: context,
              controller: linkController,
              label: 'Link URL',
              hint: 'https://example.com',
              onChanged: onLinkChanged,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'Please enter a URL';
                }
                return null;
              },
              prefixIcon: FluentIcons.link_24_regular,
            ),
            SizedBox(height: 16.h),
            _buildTextField(
              context: context,
              controller: linkDescriptionController,
              label: 'Description (Optional)',
              hint: 'Describe what this link is about...',
              maxLines: 3,
              onChanged: onLinkDescriptionChanged,
              prefixIcon: FluentIcons.text_description_24_regular,
            ),
          ] else ...[
            _buildTextField(
              context: context,
              controller: contentController,
              label: _getContentLabel(),
              hint: _getContentHint(),
              maxLines: 4,
              onChanged: onContentChanged,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'Please enter update content';
                }
                return null;
              },
              prefixIcon: _getContentIcon(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    required Function(String) onChanged,
    String? Function(String?)? validator,
    required IconData prefixIcon,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          onChanged: onChanged,
          validator: validator,
          style: theme.textTheme.bodyMedium,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            prefixIcon: Icon(
              prefixIcon,
              size: 20.sp,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 1,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getContentIcon() {
    switch (selectedType) {
      case IncidentUpdateType.textUpdate:
        return FluentIcons.comment_24_regular;
      case IncidentUpdateType.link:
        return FluentIcons.link_24_regular;
      case IncidentUpdateType.statusChange:
        return FluentIcons.arrow_sync_24_regular;
      case IncidentUpdateType.communityObservation:
        return FluentIcons.people_24_regular;
    }
  }

  String _getContentTitle() {
    switch (selectedType) {
      case IncidentUpdateType.textUpdate:
        return 'Update Content';
      case IncidentUpdateType.link:
        return 'Link Information';
      case IncidentUpdateType.statusChange:
        return 'Status Update';
      case IncidentUpdateType.communityObservation:
        return 'Community Observation';
    }
  }

  String _getContentLabel() {
    switch (selectedType) {
      case IncidentUpdateType.textUpdate:
        return 'Update Content';
      case IncidentUpdateType.statusChange:
        return 'Status Change Details';
      case IncidentUpdateType.communityObservation:
        return 'Observation Details';
      default:
        return 'Content';
    }
  }

  String _getContentHint() {
    switch (selectedType) {
      case IncidentUpdateType.textUpdate:
        return 'Write your update...';
      case IncidentUpdateType.statusChange:
        return 'Describe the status change...';
      case IncidentUpdateType.communityObservation:
        return 'Share your observation...';
      default:
        return 'Write your content...';
    }
  }
}
