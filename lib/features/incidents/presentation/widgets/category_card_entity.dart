import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class CategoryCardEntity extends StatelessWidget {
  final IncidentCategoryEntity category;
  final VoidCallback onTap;

  const CategoryCardEntity({
    Key? key,
    required this.category,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode ? theme.cardColor : Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            boxShadow:
                isDarkMode
                    ? []
                    : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        offset: const Offset(0, 4),
                        blurRadius: 12.r,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.04),
                        offset: const Offset(0, 2),
                        blurRadius: 6.r,
                      ),
                    ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image/Icon Section with padding
              Expanded(
                flex: 3,
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: _parseColor(category.color).withValues(alpha: 0.06),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Center(child: _buildCategoryImage()),
                  ),
                ),
              ),

              // Text Section
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 8.h),
                  child: Center(
                    child: Text(
                      category.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                        fontSize: 11.sp,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build category image or fallback to icon
  Widget _buildCategoryImage() {
    // Always use imageUrl if it exists, even if empty
    final imageUrl = category.imageUrl ?? '';

    return ClipRRect(
      borderRadius: BorderRadius.circular(4.r),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: 100.sp,
        height: 100.sp,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildShimmerPlaceholder(context),
        errorWidget: (context, url, error) {
          // Return empty container if image fails to load
          return Container(
            width: 32.sp,
            height: 32.sp,
            decoration: BoxDecoration(
              color: _parseColor(category.color).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4.r),
            ),
          );
        },
      ),
    );
  }

  /// Build shimmer placeholder for loading state
  Widget _buildShimmerPlaceholder(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Shimmer.fromColors(
      baseColor: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDarkMode ? Colors.grey[700]! : Colors.grey[100]!,
      child: Container(
        width: 100.sp,
        height: 100.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
      ),
    );
  }

  /// Parse color string to Color object
  Color _parseColor(String colorString) {
    try {
      // Remove # if present and ensure it's 6 characters
      String cleanColor = colorString.replaceAll('#', '');
      if (cleanColor.length == 6) {
        return Color(int.parse('FF$cleanColor', radix: 16));
      }
    } catch (e) {
      // Fallback to a default color if parsing fails
    }
    return Colors.grey;
  }

}
