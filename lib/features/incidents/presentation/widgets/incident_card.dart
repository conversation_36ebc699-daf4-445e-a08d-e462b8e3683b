import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/utils/incident_icon_utils.dart';

class IncidentCard extends StatelessWidget {
  final IncidentEntity incident;
  final VoidCallback? onTap;
  final VoidCallback? onUpdate;

  const IncidentCard({
    Key? key,
    required this.incident,
    this.onTap,
    this.onUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 13.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: isDarkMode ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: isDarkMode
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  offset: const Offset(0, 2),
                  blurRadius: 6.r,
                ),
              ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(5.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type and time
              Row(
                children: [
                  _buildTypeChip(context),
                  const Spacer(),
                  _buildTimeStamp(context),
                ],
              ),
              SizedBox(height: 10.h),

              // Title
              Text(
                incident.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 6.h),

              // Description
              Text(
                incident.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10.h),

              // Location
              Row(
                children: [
                  Icon(
                    FluentIcons.location_24_regular,
                    size: 14.sp,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: 5.w),
                  Expanded(
                    child: Text(
                      incident.location.address,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              
              // Media and updates info
              if (incident.hasMedia || incident.hasUpdates) ...[
                SizedBox(height: 10.h),
                Row(
                  children: [
                    if (incident.hasMedia) ...[
                      Icon(
                        FluentIcons.image_24_regular,
                        size: 14.sp,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        '${incident.mediaCount}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      SizedBox(width: 13.w),
                    ],
                    if (incident.hasUpdates) ...[
                      Icon(
                        FluentIcons.comment_24_regular,
                        size: 14.sp,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        '${incident.updateCount}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    const Spacer(),
                    _buildVisibilityIndicator(context),
                  ],
                ),
              ],
              
              // Action buttons
              if (onUpdate != null) ...[
                SizedBox(height: 10.h),
                Row(
                  children: [
                    const Spacer(),
                    TextButton.icon(
                      onPressed: onUpdate,
                      icon: Icon(
                        FluentIcons.add_24_regular,
                        size: 16.sp,
                      ),
                      label: const Text('Add Update'),
                      style: TextButton.styleFrom(
                        foregroundColor: theme.colorScheme.primary,
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(BuildContext context) {
    final theme = Theme.of(context);
    final typeColor = _getCategoryColor(incident.categoryKey);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: typeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getCategoryIcon(incident.categoryKey),
            size: 12.sp,
            color: typeColor,
          ),
          SizedBox(width: 4.w),
          Text(
            incident.categoryTitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: typeColor,
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeStamp(BuildContext context) {
    final theme = Theme.of(context);
    
    return Text(
      incident.timeSincePosted,
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildVisibilityIndicator(BuildContext context) {
    final theme = Theme.of(context);
    
    if (incident.isAnonymous) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FluentIcons.person_prohibited_24_regular,
              size: 10.sp,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 2.w),
            Text(
              'Anonymous',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 10.sp,
              ),
            ),
          ],
        ),
      );
    }
    
    if (incident.isVisibleToSelfOnly) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FluentIcons.eye_off_24_regular,
              size: 10.sp,
              color: Colors.orange,
            ),
            SizedBox(width: 2.w),
            Text(
              'Private',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.orange,
                fontSize: 10.sp,
              ),
            ),
          ],
        ),
      );
    }
    
    return const SizedBox.shrink();
  }

  Color _getCategoryColor(String categoryKey) {
    return IncidentIconUtils.getDefaultTypeColor(categoryKey);
  }

  IconData _getCategoryIcon(String categoryKey) {
    return IncidentIconUtils.getDefaultTypeIcon(categoryKey);
  }
}
