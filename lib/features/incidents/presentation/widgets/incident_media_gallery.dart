import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class IncidentMediaGallery extends StatelessWidget {
  final List<MediaEntity> media;

  const IncidentMediaGallery({
    Key? key,
    required this.media,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (media.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.zero,
        itemCount: media.length,
        itemBuilder: (context, index) {
          final mediaItem = media[index];
          return Padding(
            padding: EdgeInsets.only(right: 8.w),
            child: _buildMediaItem(context, mediaItem),
          );
        },
      ),
    );
  }

  Widget _buildMediaItem(BuildContext context, MediaEntity mediaItem) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _viewMedia(context, mediaItem),
      child: Container(
        width: 80.w,
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(4.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4.r),
          child: Stack(
            children: [
              // Media preview
              _buildMediaPreview(mediaItem),

              // Media type indicator
              if (mediaItem.isVideo)
                Positioned(
                  top: 4.w,
                  right: 4.w,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                    child: Icon(
                      FluentIcons.play_24_filled,
                      size: 8.sp,
                      color: Colors.white,
                    ),
                  ),
                ),

              // File size indicator
              Positioned(
                bottom: 4.w,
                left: 4.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                  child: Text(
                    mediaItem.fileSizeFormatted,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontSize: 8.sp,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaPreview(MediaEntity mediaItem) {
    if (mediaItem.isImage) {
      return CachedNetworkImage(
        imageUrl: mediaItem.displayUrl,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[300],
          child: Center(
            child: Icon(
              FluentIcons.image_24_regular,
              color: Colors.grey[600],
              size: 20.sp,
            ),
          ),
        ),
      );
    } else if (mediaItem.isVideo) {
      return Container(
        color: Colors.black,
        child: mediaItem.hasThumbnail
            ? CachedNetworkImage(
                imageUrl: mediaItem.thumbnailUrl!,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Center(
                  child: Icon(
                    FluentIcons.video_24_regular,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                ),
              )
            : Center(
                child: Icon(
                  FluentIcons.video_24_regular,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
      );
    } else {
      return Container(
        color: Colors.grey[300],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.document_24_regular,
                color: Colors.grey[600],
                size: 16.sp,
              ),
              SizedBox(height: 4.h),
              Text(
                mediaItem.type.displayName,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 8.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  void _viewMedia(BuildContext context, MediaEntity mediaItem) {
    final currentIndex = media.indexOf(mediaItem);

    context.push(
      '/media/preview',
      extra: {
        'mediaList': media,
        'initialIndex': currentIndex,
      },
    );
  }


}
