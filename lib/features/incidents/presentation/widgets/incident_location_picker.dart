import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:respublicaseguridad/core/services/location_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/presentation/widgets/interactive_zone_map.dart';

class IncidentLocationPicker extends StatefulWidget {
  final LocationEntity? selectedLocation;
  final Function(LocationEntity) onLocationSelected;

  const IncidentLocationPicker({
    Key? key,
    required this.selectedLocation,
    required this.onLocationSelected,
  }) : super(key: key);

  @override
  State<IncidentLocationPicker> createState() => _IncidentLocationPickerState();
}

class _IncidentLocationPickerState extends State<IncidentLocationPicker> {
  LatLng? _selectedLocation;
  String _selectedAddress = '';
  bool _hasUserSelectedLocation = false;

  @override
  void initState() {
    super.initState();
    if (widget.selectedLocation != null) {
      _selectedLocation = LatLng(
        widget.selectedLocation!.latitude,
        widget.selectedLocation!.longitude,
      );
      _selectedAddress = widget.selectedLocation!.address;
      _hasUserSelectedLocation = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Interactive Map
        InteractiveZoneMap(
          height: 180,
          initialLocation: _selectedLocation,
          initialAddress: _selectedAddress,
          onLocationSelected: _onLocationSelected,
          onCurrentLocationRequested: _requestCurrentLocation,
          showLocationInfo: true,
          showSearchOverlay: false,
          isInteractive: true,
          autoRequestLocation: !_hasUserSelectedLocation,
          margin: EdgeInsets.zero,
          borderRadius: BorderRadius.circular(4.r),
          showFullScreenToggle: false,
        ),

        // Location requirement message
        if (!_hasUserSelectedLocation) ...[
          SizedBox(height: 8.h),
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Row(
              children: [
                Icon(
                  FluentIcons.location_add_24_regular,
                  color: theme.colorScheme.primary,
                  size: 14.sp,
                ),
                SizedBox(width: 6.w),
                Expanded(
                  child: Text(
                    'Tap on the map to select incident location',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                      fontSize: 11.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        // Selected location info
        if (_hasUserSelectedLocation && _selectedAddress.isNotEmpty) ...[
          SizedBox(height: 8.h),
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Row(
              children: [
                Icon(
                  FluentIcons.location_24_filled,
                  color: theme.colorScheme.primary,
                  size: 14.sp,
                ),
                SizedBox(width: 6.w),
                Expanded(
                  child: Text(
                    _selectedAddress,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontSize: 11.sp,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  void _onLocationSelected(LatLng location, String address) {
    setState(() {
      _selectedLocation = location;
      _selectedAddress = address;
      _hasUserSelectedLocation = true;
    });

    // Create LocationEntity and notify parent
    final locationEntity = LocationEntity(
      latitude: location.latitude,
      longitude: location.longitude,
      address: address.isNotEmpty ? address : 'Unknown location',
    );
    widget.onLocationSelected(locationEntity);
  }

  void _requestCurrentLocation() async {
    try {
      final locationService = LocationService.instance;
      final position = await locationService.getCurrentPosition();

      if (position != null && mounted) {
        final location = LatLng(position.latitude, position.longitude);
        final address = await locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
        _onLocationSelected(location, address);
      }
    } catch (e) {
      // Handle location error silently or show a subtle message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unable to get current location'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
}
