import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/core/services/media_upload_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class IncidentMediaPicker extends StatefulWidget {
  final List<MediaEntity> selectedMedia;
  final Function(List<MediaEntity>) onMediaChanged;
  final int maxMediaCount;
  final String userId;

  const IncidentMediaPicker({
    Key? key,
    required this.selectedMedia,
    required this.onMediaChanged,
    required this.userId,
    this.maxMediaCount = 5,
  }) : super(key: key);

  @override
  State<IncidentMediaPicker> createState() => _IncidentMediaPickerState();
}

class _IncidentMediaPickerState extends State<IncidentMediaPicker> {
  final ImagePicker _picker = ImagePicker();
  final MediaUploadService _uploadService = MediaUploadService.instance;
  bool _isUploading = false;
  double _uploadProgress = 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add Media Button or Upload Progress
        if (widget.selectedMedia.length < widget.maxMediaCount)
          Container(
            margin: EdgeInsets.only(bottom: 12.h),
            child: _isUploading
                ? _buildUploadProgress(theme)
                : Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _showMediaOptions(context),
                      borderRadius: BorderRadius.circular(4.r),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.08),
                          borderRadius: BorderRadius.circular(4.r),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.primary.withValues(alpha: 0.04),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              FluentIcons.camera_add_24_regular,
                              size: 16.sp,
                              color: theme.colorScheme.primary,
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              'Add Media',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),

        // Media Grid or Empty State
        if (widget.selectedMedia.isNotEmpty)
          _buildMediaGrid(context)
        else
          _buildEmptyState(context),

        // Media Count
        if (widget.selectedMedia.isNotEmpty) ...[
          SizedBox(height: 8.h),
          Padding(
            padding: EdgeInsets.only(left: 4.w),
            child: Text(
              '${widget.selectedMedia.length}/${widget.maxMediaCount} files',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: 11.sp,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showMediaOptions(context),
        borderRadius: BorderRadius.circular(4.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(4.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.02),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Icon(
                  FluentIcons.camera_add_24_regular,
                  size: 24.sp,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(height: 12.h),
              Text(
                'No media added yet',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                'Tap to add photos or videos',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 6.w,
        mainAxisSpacing: 6.h,
        childAspectRatio: 1,
      ),
      itemCount: widget.selectedMedia.length,
      itemBuilder: (context, index) {
        final media = widget.selectedMedia[index];
        return _buildMediaItem(context, media, index);
      },
    );
  }

  Widget _buildMediaItem(BuildContext context, MediaEntity media, int index) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Media Container
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: _buildMediaPreview(media),
            ),
          ),

          // Remove button
          Positioned(
            top: 6.w,
            right: 6.w,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _removeMedia(index),
                borderRadius: BorderRadius.circular(10.r),
                child: Container(
                  width: 20.w,
                  height: 20.w,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Icon(
                    FluentIcons.dismiss_24_regular,
                    size: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),

          // Media type indicator
          if (media.isVideo)
            Positioned(
              bottom: 6.w,
              left: 6.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Icon(
                  FluentIcons.play_24_filled,
                  size: 8.sp,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMediaPreview(MediaEntity media) {
    if (media.isImage) {
      return Image.network(
        media.displayUrl,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: Icon(
              FluentIcons.image_24_regular,
              color: Colors.grey[600],
            ),
          );
        },
      );
    } else if (media.isVideo) {
      return Container(
        color: Colors.black,
        child: media.hasThumbnail
            ? Image.network(
                media.thumbnailUrl!,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
              )
            : Icon(
                FluentIcons.video_24_regular,
                color: Colors.white,
                size: 24.sp,
              ),
      );
    } else {
      return Container(
        color: Colors.grey[300],
        child: Icon(
          FluentIcons.document_24_regular,
          color: Colors.grey[600],
          size: 24.sp,
        ),
      );
    }
  }

  void _showMediaOptions(BuildContext context) {
    IosDialog.showActionSheet(
      context: context,
      title: 'Add Media',
      message: 'Choose how to add photos or videos',
      actions: [
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _capturePhoto();
          },
          child: Row(
            children: [
              Icon(FluentIcons.camera_24_regular),
              SizedBox(width: 12.w),
              Text('Take Photo'),
            ],
          ),
        ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _captureVideo();
          },
          child: Row(
            children: [
              Icon(FluentIcons.video_24_regular),
              SizedBox(width: 12.w),
              Text('Record Video'),
            ],
          ),
        ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _selectFromGallery();
          },
          child: Row(
            children: [
              Icon(FluentIcons.image_24_regular),
              SizedBox(width: 12.w),
              Text('Choose from Gallery'),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _capturePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        await _uploadAndAddMedia(File(image.path), MediaUploadType.photo);
      }
    } catch (e) {
      _showErrorDialog('Failed to capture photo: $e');
    }
  }

  Future<void> _captureVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        await _uploadAndAddMedia(File(video.path), MediaUploadType.video);
      }
    } catch (e) {
      _showErrorDialog('Failed to capture video: $e');
    }
  }

  Future<void> _selectFromGallery() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      for (final image in images) {
        if (widget.selectedMedia.length >= widget.maxMediaCount) break;
        await _uploadAndAddMedia(File(image.path), MediaUploadType.photo);
      }
    } catch (e) {
      _showErrorDialog('Failed to select images: $e');
    }
  }

  void _removeMedia(int index) {
    final updatedMedia = List<MediaEntity>.from(widget.selectedMedia);
    updatedMedia.removeAt(index);
    widget.onMediaChanged(updatedMedia);
  }

  Future<void> _uploadAndAddMedia(File file, MediaUploadType type) async {
    if (_isUploading) return;

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      final result = await _uploadService.uploadMedia(
        file: file,
        userId: widget.userId,
        type: type,
        onProgress: (progress) {
          setState(() {
            _uploadProgress = progress;
          });
        },
      );

      final mediaEntity = MediaEntity(
        mediaId: result.mediaId,
        type: type == MediaUploadType.photo ? MediaType.photo : MediaType.video,
        url: result.url,
        uploadedAt: DateTime.now(),
        fileName: result.fileName,
        fileSizeBytes: result.fileSizeBytes,
        thumbnailUrl: result.thumbnailUrl,
      );

      final updatedMedia = List<MediaEntity>.from(widget.selectedMedia)..add(mediaEntity);
      widget.onMediaChanged(updatedMedia);
    } catch (e) {
      _showErrorDialog('Failed to upload media: $e');
    } finally {
      setState(() {
        _isUploading = false;
        _uploadProgress = 0.0;
      });
    }
  }

  Widget _buildUploadProgress(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16.sp,
            height: 16.sp,
            child: CircularProgressIndicator(
              value: _uploadProgress,
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            'Uploading... ${(_uploadProgress * 100).toInt()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    IosDialog.showAlertDialog(
      context: context,
      title: 'Error',
      message: message,
      confirmText: 'OK',
    );
  }
}
