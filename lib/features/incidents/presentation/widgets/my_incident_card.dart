import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/utils/incident_icon_utils.dart';

class MyIncidentCard extends StatelessWidget {
  final IncidentEntity incident;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showStatus;
  final bool isDraft;

  const MyIncidentCard({
    Key? key,
    required this.incident,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showStatus = true,
    this.isDraft = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isDarkMode ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(5.r),
        boxShadow: isDarkMode
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  offset: const Offset(0, 2),
                  blurRadius: 6.r,
                ),
              ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(5.r),
        child: Stack(
          children: [
            // Draft indicator stripe
            if (isDraft)
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 3.w,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(5.r),
                      bottomLeft: Radius.circular(5.r),
                    ),
                  ),
                ),
              ),

            Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              // Header with type, status, and actions
              Row(
                children: [
                  _buildTypeChip(context),
                  SizedBox(width: 4.w),
                  if (isDraft) _buildDraftChip(context),
                  if (!isDraft && incident.isReported) _buildReportedChip(context),
                  if (!isDraft && incident.isBlocked) _buildBlockedChip(context),
                  if (!isDraft && incident.isResolved) _buildResolvedChip(context),
                  const Spacer(),
                  _buildActionMenu(context),
                ],
              ),
              SizedBox(height: 8.h),

              // Title
              Text(
                incident.title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),

              // Description
              Text(
                incident.description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8.h),

              // Location
              Row(
                children: [
                  Icon(
                    FluentIcons.location_24_regular,
                    size: 12.sp,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: Text(
                      incident.location.address,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 11.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),

              // Footer with media count, updates, and status
              Row(
                children: [
                  if (incident.hasMedia) ...[
                    Icon(
                      FluentIcons.image_24_regular,
                      size: 12.sp,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      '${incident.mediaCount}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 11.sp,
                      ),
                    ),
                    SizedBox(width: 10.w),
                  ],
                  if (incident.hasUpdates) ...[
                    Icon(
                      FluentIcons.comment_24_regular,
                      size: 12.sp,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      '${incident.updateCount}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 11.sp,
                      ),
                    ),
                    SizedBox(width: 10.w),
                  ],
                  Text(
                    incident.timeSincePosted,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                      fontSize: 11.sp,
                    ),
                  ),
                  const Spacer(),
                  if (showStatus && !isDraft) _buildVisibilityStatus(context),
                ],
              ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeChip(BuildContext context) {
    final theme = Theme.of(context);
    final typeColor = _getCategoryColor(incident.categoryKey);
    final typeIcon = _getCategoryIcon(incident.categoryKey);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: typeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            typeIcon,
            size: 10.sp,
            color: typeColor,
          ),
          SizedBox(width: 3.w),
          Text(
            incident.categoryTitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: typeColor,
              fontWeight: FontWeight.w600,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDraftChip(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.document_edit_24_regular,
            size: 10.sp,
            color: Colors.orange,
          ),
          SizedBox(width: 3.w),
          Text(
            'Draft',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportedChip(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.flag_24_regular,
            size: 12.sp,
            color: Colors.red,
          ),
          SizedBox(width: 4.w),
          Text(
            'Reported (${incident.reportCount})',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBlockedChip(BuildContext context) {
    final theme = Theme.of(context);
    final blockedColor = Colors.red.shade800;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: blockedColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: blockedColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.shield_prohibited_24_regular,
            size: 12.sp,
            color: blockedColor,
          ),
          SizedBox(width: 4.w),
          Text(
            'Blocked',
            style: theme.textTheme.bodySmall?.copyWith(
              color: blockedColor,
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResolvedChip(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.checkmark_circle_24_regular,
            size: 12.sp,
            color: Colors.green,
          ),
          SizedBox(width: 4.w),
          Text(
            'Resolved',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.w600,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionMenu(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showActionSheet(context),
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          // Reduced touch target size
          width: 36.w,
          height: 36.h,
          padding: EdgeInsets.all(8.w),
          child: Icon(
            FluentIcons.more_vertical_24_regular,
            size: 16.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  void _showActionSheet(BuildContext context) {
    final actions = <Widget>[];

    if (onEdit != null) {
      actions.add(
        CupertinoActionSheetAction(
          onPressed: () {
            if (context.canPop()) {
              context.pop();
            }
            onEdit?.call();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Icon(
                FluentIcons.edit_24_regular,
                size: 20.sp,
                color: CupertinoColors.systemBlue,
              ),
              SizedBox(width: 12.w),
              Text(
                isDraft ? 'Continue Editing' : 'Edit',
                style: TextStyle(
                  color: CupertinoColors.systemBlue,
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (onDelete != null) {
      actions.add(
        CupertinoActionSheetAction(
          onPressed: () {
            if (context.canPop()) {
              context.pop();
            }
            onDelete?.call();
          },
          isDestructiveAction: true,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Icon(
                FluentIcons.delete_24_regular,
                size: 20.sp,
                color: CupertinoColors.destructiveRed,
              ),
              SizedBox(width: 12.w),
              Text(
                'Delete',
                style: TextStyle(
                  color: CupertinoColors.destructiveRed,
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (actions.isNotEmpty) {
      showCupertinoModalPopup<void>(
        context: context,
        builder: (BuildContext context) => CupertinoActionSheet(
          title: Text(
            'Incident Actions',
            style: TextStyle(
              fontSize: 14.sp,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          actions: actions,
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              if (context.canPop()) {
                context.pop();
              }
            },
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      );
    }
  }

  Widget _buildVisibilityStatus(BuildContext context) {
    final theme = Theme.of(context);
    
    if (incident.isVisibleToCommunity) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(
            color: Colors.green.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FluentIcons.people_24_regular,
              size: 10.sp,
              color: Colors.green,
            ),
            SizedBox(width: 3.w),
            Text(
              'Community',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.w600,
                fontSize: 9.sp,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(
            color: Colors.orange.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FluentIcons.eye_off_24_regular,
              size: 10.sp,
              color: Colors.orange,
            ),
            SizedBox(width: 3.w),
            Text(
              'Private',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.orange,
                fontWeight: FontWeight.w600,
                fontSize: 9.sp,
              ),
            ),
          ],
        ),
      );
    }
  }

  Color _getCategoryColor(String categoryKey) {
    return IncidentIconUtils.getDefaultTypeColor(categoryKey);
  }

  IconData _getCategoryIcon(String categoryKey) {
    return IncidentIconUtils.getDefaultTypeIcon(categoryKey);
  }


}
