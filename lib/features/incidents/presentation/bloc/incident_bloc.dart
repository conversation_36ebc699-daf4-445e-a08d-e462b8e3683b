import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/post_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incidents_near_location_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/search_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/search_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_statistics_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/report_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/block_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/presentation/bloc/incident_event.dart';
import 'package:respublicaseguridad/features/incidents/presentation/bloc/incident_state.dart';

/// BLoC for managing incident state
class IncidentBloc extends Bloc<IncidentEvent, IncidentState> {
  final PostIncidentWithVisibilityInfoUseCase _postIncidentUseCase;
  final GetCommunityIncidentsUseCase _getCommunityIncidentsUseCase;
  final GetUserIncidentsUseCase _getUserIncidentsUseCase;
  final GetIncidentsNearLocationUseCase _getIncidentsNearLocationUseCase;
  final SearchUserIncidentsUseCase _searchUserIncidentsUseCase;
  final GetIncidentStatisticsUseCase _getIncidentStatisticsUseCase;
  final GetIncidentByIdUseCase _getIncidentByIdUseCase;
  final ReportIncidentUseCase _reportIncidentUseCase;
  final BlockIncidentUseCase _blockIncidentUseCase;
  final UpdateIncidentUseCase _updateIncidentUseCase;
  final DeleteIncidentUseCase _deleteIncidentUseCase;
  final SearchCommunityIncidentsUseCase _searchCommunityIncidentsUseCase;

  // Pagination state
  static const int _pageSize = 20;
  bool _hasReachedMax = false;
  DateTime? _lastLoadedTimestamp;

  IncidentBloc({
    required PostIncidentWithVisibilityInfoUseCase postIncidentUseCase,
    required GetCommunityIncidentsUseCase getCommunityIncidentsUseCase,
    required GetUserIncidentsUseCase getUserIncidentsUseCase,
    required GetIncidentsNearLocationUseCase getIncidentsNearLocationUseCase,
    required SearchUserIncidentsUseCase searchUserIncidentsUseCase,
    required GetIncidentStatisticsUseCase getIncidentStatisticsUseCase,
    required GetIncidentByIdUseCase getIncidentByIdUseCase,
    required ReportIncidentUseCase reportIncidentUseCase,
    required BlockIncidentUseCase blockIncidentUseCase,
    required UpdateIncidentUseCase updateIncidentUseCase,
    required DeleteIncidentUseCase deleteIncidentUseCase,
    required SearchCommunityIncidentsUseCase searchCommunityIncidentsUseCase,
  }) : _postIncidentUseCase = postIncidentUseCase,
       _getCommunityIncidentsUseCase = getCommunityIncidentsUseCase,
       _getUserIncidentsUseCase = getUserIncidentsUseCase,
       _getIncidentsNearLocationUseCase = getIncidentsNearLocationUseCase,
       _searchUserIncidentsUseCase = searchUserIncidentsUseCase,
       _getIncidentStatisticsUseCase = getIncidentStatisticsUseCase,
       _getIncidentByIdUseCase = getIncidentByIdUseCase,
       _reportIncidentUseCase = reportIncidentUseCase,
       _blockIncidentUseCase = blockIncidentUseCase,
       _updateIncidentUseCase = updateIncidentUseCase,
       _deleteIncidentUseCase = deleteIncidentUseCase,
       _searchCommunityIncidentsUseCase = searchCommunityIncidentsUseCase,
       super(const IncidentInitial()) {
    
    // Register event handlers
    on<PostIncidentEvent>(_onPostIncident);
    on<LoadCommunityIncidentsEvent>(_onLoadCommunityIncidents);
    on<LoadUserIncidentsEvent>(_onLoadUserIncidents);
    on<LoadIncidentsNearLocationEvent>(_onLoadIncidentsNearLocation);
    on<LoadIncidentByIdEvent>(_onLoadIncidentById);
    on<SearchIncidentsEvent>(_onSearchIncidents);
    on<ReportIncidentEvent>(_onReportIncident);
    on<BlockIncidentEvent>(_onBlockIncident);
    on<UpdateIncidentEvent>(_onUpdateIncident);
    on<DeleteIncidentEvent>(_onDeleteIncident);
    on<LoadIncidentStatisticsEvent>(_onLoadIncidentStatistics);
    on<RefreshIncidentsEvent>(_onRefreshIncidents);
    on<ClearIncidentStateEvent>(_onClearIncidentState);
    on<LoadMoreIncidentsEvent>(_onLoadMoreIncidents);
    on<FilterIncidentsByCategoryEvent>(_onFilterIncidentsByCategory);
    on<ToggleVisibilityFilterEvent>(_onToggleVisibilityFilter);
  }

  /// Handle posting a new incident
  Future<void> _onPostIncident(
    PostIncidentEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentPosting());

    final params = PostIncidentParams(
      userId: event.userId,
      categoryKey: event.categoryKey,
      categoryTitle: event.categoryTitle,
      subcategoryKey: event.subcategoryKey,
      subcategoryTitle: event.subcategoryTitle,
      severity: event.severity,
      title: event.title,
      description: event.description,
      location: event.location,
      media: event.media,
      isAnonymous: event.isAnonymous,
      zoneId: event.zoneId,
      metadata: event.metadata,
    );

    final result = await _postIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incident) => emit(IncidentPosted(incident: incident)),
    );
  }

  /// Handle loading community incidents
  Future<void> _onLoadCommunityIncidents(
    LoadCommunityIncidentsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    if (event.refresh) {
      emit(const IncidentRefreshing());
      _hasReachedMax = false;
      _lastLoadedTimestamp = null;
    } else if (state is! CommunityIncidentsLoaded) {
      emit(const IncidentLoading(message: 'Loading community incidents...'));
    }

    // TODO: Get user's validated zone IDs from a zone service or user state
    final userValidatedZoneIds = <String>[]; // Placeholder - should be fetched from user's validated zones

    final params = GetCommunityIncidentsParams(
      userId: event.userId,
      userValidatedZoneIds: userValidatedZoneIds,
      categoryFilter: event.categoryFilter,
      limit: event.limit ?? _pageSize,
      lastIncidentTimestamp: event.lastIncidentTimestamp ?? _lastLoadedTimestamp,
    );

    final result = await _getCommunityIncidentsUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incidents) {
        _hasReachedMax = incidents.length < _pageSize;
        if (incidents.isNotEmpty) {
          _lastLoadedTimestamp = incidents.last.postedAt;
        }

        if (event.refresh || state is! CommunityIncidentsLoaded) {
          emit(CommunityIncidentsLoaded(
            incidents: incidents,
            hasReachedMax: _hasReachedMax,
            currentCategoryFilter: event.categoryFilter,
            lastLoadedTimestamp: _lastLoadedTimestamp,
          ));
        } else {
          final currentState = state as CommunityIncidentsLoaded;
          final updatedIncidents = List<IncidentWithVisibilityEntity>.from(currentState.incidents)
            ..addAll(incidents);
          
          emit(currentState.copyWith(
            incidents: updatedIncidents,
            hasReachedMax: _hasReachedMax,
            lastLoadedTimestamp: _lastLoadedTimestamp,
          ));
        }
      },
    );
  }

  /// Handle loading user incidents
  Future<void> _onLoadUserIncidents(
    LoadUserIncidentsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    if (event.refresh) {
      emit(const IncidentRefreshing());
    } else if (state is! UserIncidentsLoaded) {
      emit(const IncidentLoading(message: 'Loading your incidents...'));
    }

    final params = GetUserIncidentsParams(
      userId: event.userId,
      statusFilter: event.statusFilter,
      categoryFilter: event.categoryFilter,
      includeStatistics: event.includeStatistics,
    );

    final result = await _getUserIncidentsUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (userIncidentsResult) => emit(UserIncidentsLoaded(
        result: userIncidentsResult,
        currentStatusFilter: event.statusFilter,
        currentCategoryFilter: event.categoryFilter,
      )),
    );
  }

  /// Handle loading incidents near location
  Future<void> _onLoadIncidentsNearLocation(
    LoadIncidentsNearLocationEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Loading nearby incidents...'));

    final params = GetIncidentsNearLocationParams(
      userId: event.userId,
      latitude: event.latitude,
      longitude: event.longitude,
      radiusInMeters: event.radiusInMeters,
      categoryFilter: event.categoryFilter,
    );

    final result = await _getIncidentsNearLocationUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incidents) => emit(IncidentsNearLocationLoaded(
        incidents: incidents,
        latitude: event.latitude,
        longitude: event.longitude,
        radiusInMeters: event.radiusInMeters,
        currentCategoryFilter: event.categoryFilter,
      )),
    );
  }

  /// Handle loading incident by ID
  Future<void> _onLoadIncidentById(
    LoadIncidentByIdEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Loading incident details...'));

    final params = GetIncidentByIdParams(
      incidentId: event.incidentId,
      userId: event.userId,
    );

    final result = await _getIncidentByIdUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incident) => emit(IncidentDetailsLoaded(incident: incident)),
    );
  }

  /// Handle searching incidents
  Future<void> _onSearchIncidents(
    SearchIncidentsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Searching incidents...'));

    if (event.searchUserIncidents) {
      final params = SearchUserIncidentsParams(
        userId: event.userId,
        query: event.query,
        categoryFilter: event.categoryFilter,
      );

      final result = await _searchUserIncidentsUseCase(params);

      result.fold(
        (failure) => emit(IncidentError(message: failure.message)),
        (incidents) => emit(IncidentSearchResultsLoaded(
          incidents: incidents,
          query: event.query,
          categoryFilter: event.categoryFilter,
          isUserIncidentsSearch: true,
        )),
      );
    } else {
      // Search community incidents
      final params = SearchCommunityIncidentsParams(
        userId: event.userId,
        query: event.query,
        categoryFilter: event.categoryFilter,
      );

      final result = await _searchCommunityIncidentsUseCase(params);

      result.fold(
        (failure) => emit(IncidentError(message: failure.message)),
        (incidents) => emit(IncidentSearchResultsLoaded(
          incidents: incidents,
          query: event.query,
          categoryFilter: event.categoryFilter,
          isUserIncidentsSearch: false,
        )),
      );
    }
  }

  /// Handle reporting an incident
  Future<void> _onReportIncident(
    ReportIncidentEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Reporting incident...'));

    final params = ReportIncidentParams(
      incidentId: event.incidentId,
      reporterUserId: event.reporterUserId,
      reason: event.reason,
      additionalInfo: event.additionalInfo,
    );

    final result = await _reportIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incident) => emit(IncidentReported(incident: incident)),
    );
  }

  /// Handle blocking an incident
  Future<void> _onBlockIncident(
    BlockIncidentEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Blocking incident...'));

    final params = BlockIncidentParams(
      incidentId: event.incidentId,
      adminUserId: event.adminUserId,
      reason: event.reason ?? 'No reason provided',
    );

    final result = await _blockIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incident) => emit(IncidentBlocked(incident: incident)),
    );
  }

  /// Handle updating an incident
  Future<void> _onUpdateIncident(
    UpdateIncidentEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Updating incident...'));

    final params = UpdateIncidentParams(
      incident: event.incident,
      userId: event.incident.userId, // Assuming the incident contains the user ID
    );

    final result = await _updateIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (incident) => emit(IncidentUpdated(incident: incident)),
    );
  }

  /// Handle deleting an incident
  Future<void> _onDeleteIncident(
    DeleteIncidentEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Deleting incident...'));

    final params = DeleteIncidentParams(
      incidentId: event.incidentId,
      userId: event.userId,
    );

    final result = await _deleteIncidentUseCase(params);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (_) => emit(IncidentDeleted(incidentId: event.incidentId)),
    );
  }

  /// Handle loading incident statistics
  Future<void> _onLoadIncidentStatistics(
    LoadIncidentStatisticsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    emit(const IncidentLoading(message: 'Loading statistics...'));

    final result = await _getIncidentStatisticsUseCase(event.userId);

    result.fold(
      (failure) => emit(IncidentError(message: failure.message)),
      (statistics) => emit(IncidentStatisticsLoaded(statistics: statistics)),
    );
  }

  /// Handle refreshing incidents
  Future<void> _onRefreshIncidents(
    RefreshIncidentsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    // Reset pagination state
    _hasReachedMax = false;
    _lastLoadedTimestamp = null;

    // Trigger refresh based on current state
    if (state is CommunityIncidentsLoaded) {
      add(LoadCommunityIncidentsEvent(
        userId: event.userId,
        categoryFilter: event.categoryFilter,
        refresh: true,
      ));
    } else if (state is UserIncidentsLoaded) {
      add(LoadUserIncidentsEvent(
        userId: event.userId,
        categoryFilter: event.categoryFilter,
        refresh: true,
      ));
    } else {
      // Default to loading community incidents
      add(LoadCommunityIncidentsEvent(
        userId: event.userId,
        categoryFilter: event.categoryFilter,
        refresh: true,
      ));
    }
  }

  /// Handle clearing incident state
  Future<void> _onClearIncidentState(
    ClearIncidentStateEvent event,
    Emitter<IncidentState> emit,
  ) async {
    _hasReachedMax = false;
    _lastLoadedTimestamp = null;
    emit(const IncidentInitial());
  }

  /// Handle loading more incidents (pagination)
  Future<void> _onLoadMoreIncidents(
    LoadMoreIncidentsEvent event,
    Emitter<IncidentState> emit,
  ) async {
    if (_hasReachedMax) return;

    if (state is CommunityIncidentsLoaded) {
      final currentState = state as CommunityIncidentsLoaded;
      emit(IncidentLoadingMore(currentIncidents: currentState.incidents));

      add(LoadCommunityIncidentsEvent(
        userId: event.userId,
        categoryFilter: event.categoryFilter,
        lastIncidentTimestamp: _lastLoadedTimestamp,
      ));
    }
  }

  /// Handle filtering incidents by category
  Future<void> _onFilterIncidentsByCategory(
    FilterIncidentsByCategoryEvent event,
    Emitter<IncidentState> emit,
  ) async {
    if (state is CommunityIncidentsLoaded) {
      final currentState = state as CommunityIncidentsLoaded;
      emit(currentState.copyWith(currentCategoryFilter: event.categoryFilter));
    } else if (state is UserIncidentsLoaded) {
      final currentState = state as UserIncidentsLoaded;
      emit(currentState.copyWith(currentCategoryFilter: event.categoryFilter));
    }
  }

  /// Handle toggling visibility filter
  Future<void> _onToggleVisibilityFilter(
    ToggleVisibilityFilterEvent event,
    Emitter<IncidentState> emit,
  ) async {
    if (state is CombinedIncidentsLoaded) {
      final currentState = state as CombinedIncidentsLoaded;
      emit(currentState.copyWith(
        showCommunityVisible: event.showCommunityVisible,
        showSelfVisible: event.showSelfVisible,
      ));
    }
  }
}
