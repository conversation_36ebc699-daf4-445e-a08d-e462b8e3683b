import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';

/// Base class for all incident states
abstract class IncidentState extends Equatable {
  const IncidentState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class IncidentInitial extends IncidentState {
  const IncidentInitial();
}

/// Loading state
class IncidentLoading extends IncidentState {
  final String? message;

  const IncidentLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// State when posting an incident
class IncidentPosting extends IncidentState {
  final double? progress;

  const IncidentPosting({this.progress});

  @override
  List<Object?> get props => [progress];
}

/// State when incident is successfully posted
class IncidentPosted extends IncidentState {
  final IncidentEntity incident;

  const IncidentPosted({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// State when community incidents are loaded
class CommunityIncidentsLoaded extends IncidentState {
  final List<IncidentWithVisibilityEntity> incidents;
  final bool hasReachedMax;
  final String? currentCategoryFilter;
  final DateTime? lastLoadedTimestamp;

  const CommunityIncidentsLoaded({
    required this.incidents,
    this.hasReachedMax = false,
    this.currentCategoryFilter,
    this.lastLoadedTimestamp,
  });

  @override
  List<Object?> get props => [incidents, hasReachedMax, currentCategoryFilter, lastLoadedTimestamp];

  CommunityIncidentsLoaded copyWith({
    List<IncidentWithVisibilityEntity>? incidents,
    bool? hasReachedMax,
    String? currentCategoryFilter,
    DateTime? lastLoadedTimestamp,
  }) {
    return CommunityIncidentsLoaded(
      incidents: incidents ?? this.incidents,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentCategoryFilter: currentCategoryFilter ?? this.currentCategoryFilter,
      lastLoadedTimestamp: lastLoadedTimestamp ?? this.lastLoadedTimestamp,
    );
  }
}

/// State when user incidents are loaded
class UserIncidentsLoaded extends IncidentState {
  final GetUserIncidentsResult result;
  final IncidentStatus? currentStatusFilter;
  final String? currentCategoryFilter;

  const UserIncidentsLoaded({
    required this.result,
    this.currentStatusFilter,
    this.currentCategoryFilter,
  });

  @override
  List<Object?> get props => [result, currentStatusFilter, currentCategoryFilter];

  UserIncidentsLoaded copyWith({
    GetUserIncidentsResult? result,
    IncidentStatus? currentStatusFilter,
    String? currentCategoryFilter,
  }) {
    return UserIncidentsLoaded(
      result: result ?? this.result,
      currentStatusFilter: currentStatusFilter ?? this.currentStatusFilter,
      currentCategoryFilter: currentCategoryFilter ?? this.currentCategoryFilter,
    );
  }
}

/// State when incidents near location are loaded
class IncidentsNearLocationLoaded extends IncidentState {
  final List<IncidentWithVisibilityEntity> incidents;
  final double latitude;
  final double longitude;
  final double radiusInMeters;
  final String? currentCategoryFilter;

  const IncidentsNearLocationLoaded({
    required this.incidents,
    required this.latitude,
    required this.longitude,
    required this.radiusInMeters,
    this.currentCategoryFilter,
  });

  @override
  List<Object?> get props => [incidents, latitude, longitude, radiusInMeters, currentCategoryFilter];
}

/// State when a specific incident is loaded
class IncidentDetailsLoaded extends IncidentState {
  final IncidentEntity incident;

  const IncidentDetailsLoaded({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// State when search results are loaded
class IncidentSearchResultsLoaded extends IncidentState {
  final List<IncidentEntity> incidents;
  final String query;
  final String? categoryFilter;
  final bool isUserIncidentsSearch;

  const IncidentSearchResultsLoaded({
    required this.incidents,
    required this.query,
    this.categoryFilter,
    this.isUserIncidentsSearch = false,
  });

  @override
  List<Object?> get props => [incidents, query, categoryFilter, isUserIncidentsSearch];
}

/// State when incident statistics are loaded
class IncidentStatisticsLoaded extends IncidentState {
  final Map<String, dynamic> statistics;

  const IncidentStatisticsLoaded({
    required this.statistics,
  });

  @override
  List<Object?> get props => [statistics];
}

/// State when an incident is successfully updated
class IncidentUpdated extends IncidentState {
  final IncidentEntity incident;

  const IncidentUpdated({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// State when an incident is successfully deleted
class IncidentDeleted extends IncidentState {
  final String incidentId;

  const IncidentDeleted({
    required this.incidentId,
  });

  @override
  List<Object?> get props => [incidentId];
}

/// State when an incident is successfully reported
class IncidentReported extends IncidentState {
  final IncidentEntity incident;

  const IncidentReported({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// State when an incident is successfully blocked
class IncidentBlocked extends IncidentState {
  final IncidentEntity incident;

  const IncidentBlocked({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// Error state
class IncidentError extends IncidentState {
  final String message;
  final String? errorCode;

  const IncidentError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// State when refreshing incidents
class IncidentRefreshing extends IncidentState {
  const IncidentRefreshing();
}

/// State when loading more incidents (pagination)
class IncidentLoadingMore extends IncidentState {
  final List<IncidentWithVisibilityEntity> currentIncidents;

  const IncidentLoadingMore({
    required this.currentIncidents,
  });

  @override
  List<Object?> get props => [currentIncidents];
}

/// Combined state for multiple incident lists
class CombinedIncidentsLoaded extends IncidentState {
  final List<IncidentWithVisibilityEntity> communityIncidents;
  final GetUserIncidentsResult userIncidents;
  final Map<String, dynamic>? statistics;
  final String? currentCategoryFilter;
  final bool showCommunityVisible;
  final bool showSelfVisible;

  const CombinedIncidentsLoaded({
    required this.communityIncidents,
    required this.userIncidents,
    this.statistics,
    this.currentCategoryFilter,
    this.showCommunityVisible = true,
    this.showSelfVisible = true,
  });

  @override
  List<Object?> get props => [
        communityIncidents,
        userIncidents,
        statistics,
        currentCategoryFilter,
        showCommunityVisible,
        showSelfVisible,
      ];

  CombinedIncidentsLoaded copyWith({
    List<IncidentWithVisibilityEntity>? communityIncidents,
    GetUserIncidentsResult? userIncidents,
    Map<String, dynamic>? statistics,
    String? currentCategoryFilter,
    bool? showCommunityVisible,
    bool? showSelfVisible,
  }) {
    return CombinedIncidentsLoaded(
      communityIncidents: communityIncidents ?? this.communityIncidents,
      userIncidents: userIncidents ?? this.userIncidents,
      statistics: statistics ?? this.statistics,
      currentCategoryFilter: currentCategoryFilter ?? this.currentCategoryFilter,
      showCommunityVisible: showCommunityVisible ?? this.showCommunityVisible,
      showSelfVisible: showSelfVisible ?? this.showSelfVisible,
    );
  }

  /// Get filtered incidents based on visibility settings
  List<IncidentEntity> get filteredUserIncidents {
    final List<IncidentEntity> filtered = [];
    
    if (showCommunityVisible) {
      filtered.addAll(userIncidents.incidents.where(
        (incident) => incident.visibilityStatus == IncidentVisibilityStatus.visibleToCommunity,
      ));
    }
    
    if (showSelfVisible) {
      filtered.addAll(userIncidents.incidents.where(
        (incident) => incident.visibilityStatus == IncidentVisibilityStatus.visibleToSelf,
      ));
    }
    
    return filtered;
  }
}
