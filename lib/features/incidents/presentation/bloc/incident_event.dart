import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

/// Base class for all incident events
abstract class IncidentEvent extends Equatable {
  const IncidentEvent();

  @override
  List<Object?> get props => [];
}

/// Event to post a new incident
class PostIncidentEvent extends IncidentEvent {
  final String userId;
  final String categoryKey;
  final String categoryTitle;
  final String? subcategoryKey;
  final String? subcategoryTitle;
  final String? severity;
  final String title;
  final String description;
  final LocationEntity location;
  final List<MediaEntity> media;
  final bool isAnonymous;
  final String? zoneId;
  final Map<String, dynamic>? metadata;

  const PostIncidentEvent({
    required this.userId,
    required this.categoryKey,
    required this.categoryTitle,
    this.subcategoryKey,
    this.subcategoryTitle,
    this.severity,
    required this.title,
    required this.description,
    required this.location,
    this.media = const [],
    this.isAnonymous = false,
    this.zoneId,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        userId,
        categoryKey,
        categoryTitle,
        subcategoryKey,
        subcategoryTitle,
        severity,
        title,
        description,
        location,
        media,
        isAnonymous,
        zoneId,
        metadata,
      ];
}

/// Event to load community incidents
class LoadCommunityIncidentsEvent extends IncidentEvent {
  final String userId;
  final String? categoryFilter;
  final int? limit;
  final DateTime? lastIncidentTimestamp;
  final bool refresh;

  const LoadCommunityIncidentsEvent({
    required this.userId,
    this.categoryFilter,
    this.limit,
    this.lastIncidentTimestamp,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [userId, categoryFilter, limit, lastIncidentTimestamp, refresh];
}

/// Event to load user's own incidents
class LoadUserIncidentsEvent extends IncidentEvent {
  final String userId;
  final IncidentStatus? statusFilter;
  final String? categoryFilter;
  final bool includeStatistics;
  final bool refresh;

  const LoadUserIncidentsEvent({
    required this.userId,
    this.statusFilter,
    this.categoryFilter,
    this.includeStatistics = false,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [userId, statusFilter, categoryFilter, includeStatistics, refresh];
}

/// Event to load incidents near a location
class LoadIncidentsNearLocationEvent extends IncidentEvent {
  final String userId;
  final double latitude;
  final double longitude;
  final double radiusInMeters;
  final String? categoryFilter;

  const LoadIncidentsNearLocationEvent({
    required this.userId,
    required this.latitude,
    required this.longitude,
    required this.radiusInMeters,
    this.categoryFilter,
  });

  @override
  List<Object?> get props => [userId, latitude, longitude, radiusInMeters, categoryFilter];
}

/// Event to load a specific incident by ID
class LoadIncidentByIdEvent extends IncidentEvent {
  final String incidentId;
  final String userId;

  const LoadIncidentByIdEvent({
    required this.incidentId,
    required this.userId,
  });

  @override
  List<Object?> get props => [incidentId, userId];
}

/// Event to search incidents
class SearchIncidentsEvent extends IncidentEvent {
  final String userId;
  final String query;
  final String? categoryFilter;
  final bool searchUserIncidents;

  const SearchIncidentsEvent({
    required this.userId,
    required this.query,
    this.categoryFilter,
    this.searchUserIncidents = false,
  });

  @override
  List<Object?> get props => [userId, query, categoryFilter, searchUserIncidents];
}

/// Event to report an incident
class ReportIncidentEvent extends IncidentEvent {
  final String incidentId;
  final String reporterUserId;
  final ReportReason reason;
  final String? additionalInfo;

  const ReportIncidentEvent({
    required this.incidentId,
    required this.reporterUserId,
    required this.reason,
    this.additionalInfo,
  });

  @override
  List<Object?> get props => [incidentId, reporterUserId, reason, additionalInfo];
}

/// Event to block an incident (admin action)
class BlockIncidentEvent extends IncidentEvent {
  final String incidentId;
  final String adminUserId;
  final String? reason;

  const BlockIncidentEvent({
    required this.incidentId,
    required this.adminUserId,
    this.reason,
  });

  @override
  List<Object?> get props => [incidentId, adminUserId, reason];
}

/// Event to update an incident
class UpdateIncidentEvent extends IncidentEvent {
  final IncidentEntity incident;

  const UpdateIncidentEvent({
    required this.incident,
  });

  @override
  List<Object?> get props => [incident];
}

/// Event to delete an incident
class DeleteIncidentEvent extends IncidentEvent {
  final String incidentId;
  final String userId;

  const DeleteIncidentEvent({
    required this.incidentId,
    required this.userId,
  });

  @override
  List<Object?> get props => [incidentId, userId];
}

/// Event to load incident statistics
class LoadIncidentStatisticsEvent extends IncidentEvent {
  final String userId;

  const LoadIncidentStatisticsEvent({
    required this.userId,
  });

  @override
  List<Object?> get props => [userId];
}

/// Event to refresh incidents (pull-to-refresh)
class RefreshIncidentsEvent extends IncidentEvent {
  final String userId;
  final String? categoryFilter;

  const RefreshIncidentsEvent({
    required this.userId,
    this.categoryFilter,
  });

  @override
  List<Object?> get props => [userId, categoryFilter];
}

/// Event to clear incident state
class ClearIncidentStateEvent extends IncidentEvent {
  const ClearIncidentStateEvent();
}

/// Event to load more incidents (pagination)
class LoadMoreIncidentsEvent extends IncidentEvent {
  final String userId;
  final String? categoryFilter;

  const LoadMoreIncidentsEvent({
    required this.userId,
    this.categoryFilter,
  });

  @override
  List<Object?> get props => [userId, categoryFilter];
}

/// Event to filter incidents by category
class FilterIncidentsByCategoryEvent extends IncidentEvent {
  final String? categoryFilter;

  const FilterIncidentsByCategoryEvent({
    this.categoryFilter,
  });

  @override
  List<Object?> get props => [categoryFilter];
}

/// Event to toggle incident visibility filter
class ToggleVisibilityFilterEvent extends IncidentEvent {
  final bool showCommunityVisible;
  final bool showSelfVisible;

  const ToggleVisibilityFilterEvent({
    required this.showCommunityVisible,
    required this.showSelfVisible,
  });

  @override
  List<Object?> get props => [showCommunityVisible, showSelfVisible];
}
