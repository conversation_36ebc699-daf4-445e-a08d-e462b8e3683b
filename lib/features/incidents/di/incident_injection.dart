import 'package:get_it/get_it.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_firebase_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_marker_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/repositories/incident_repository_impl.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';
import 'package:respublicaseguridad/features/incidents/domain/services/incident_visibility_service.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/post_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/add_incident_update_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incidents_near_location_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/search_user_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/search_community_incidents_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_statistics_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/share_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/view_incident_update_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/report_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/block_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/validate_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/determine_visibility_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/build_incident_usecase.dart';

// Category system imports
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_category_remote_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_category_local_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/repositories/incident_category_repository_impl.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_category_repository.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_categories_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/services/incident_category_service.dart';

import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_update_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/add_incident_update_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/my_incidents_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_report_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/incident_category_selection_cubit.dart';
import 'package:respublicaseguridad/features/incidents/presentation/cubit/delete_incident_cubit.dart';

/// Dependency injection setup for incident feature
class IncidentInjection {
  static void init() {
    final getIt = GetIt.instance;

    // Data Sources
    getIt.registerLazySingleton<IncidentFirebaseDataSource>(
      () =>
          IncidentFirebaseDataSourceImpl(firestore: getIt<FirebaseFirestore>()),
    );

    // Incident Marker Data Source for Realtime DB
    getIt.registerLazySingleton<IncidentMarkerDataSource>(
      () => IncidentMarkerDataSourceImpl(getIt<FirebaseDatabase>()),
    );

    // Category Data Sources
    getIt.registerLazySingleton<IncidentCategoryRemoteDataSource>(
      () => IncidentCategoryFirebaseDataSource(
        firestore: getIt<FirebaseFirestore>(),
      ),
    );

    getIt.registerLazySingleton<IncidentCategoryLocalDataSource>(
      () => IncidentCategorySharedPrefsDataSource(
        sharedPreferences: getIt<SharedPreferences>(),
      ),
    );

    // Repositories
    getIt.registerLazySingleton<IncidentRepository>(
      () => IncidentRepositoryImpl(
        firebaseDataSource: getIt<IncidentFirebaseDataSource>(),
        markerDataSource: getIt<IncidentMarkerDataSource>(),
      ),
    );

    // Category Repository
    getIt.registerLazySingleton<IncidentCategoryRepository>(
      () => IncidentCategoryRepositoryImpl(
        remoteDataSource: getIt<IncidentCategoryRemoteDataSource>(),
        localDataSource: getIt<IncidentCategoryLocalDataSource>(),
      ),
    );

    // Services
    getIt.registerLazySingleton<IncidentVisibilityService>(
      () => IncidentVisibilityService(
        identityRepository: getIt(), // IdentityVerificationRepository
        zoneRepository: getIt(), // ZoneRepository
      ),
    );

    // Category Use Cases
    getIt.registerLazySingleton<GetIncidentCategoriesUseCase>(
      () => GetIncidentCategoriesUseCase(getIt<IncidentCategoryRepository>()),
    );

    // Category Service
    getIt.registerLazySingleton<IncidentCategoryService>(
      () => IncidentCategoryService(getIt<GetIncidentCategoriesUseCase>()),
    );

    // Use Cases

    getIt.registerLazySingleton<AddIncidentUpdateUseCase>(
      () => AddIncidentUpdateUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetIncidentUpdatesUseCase>(
      () => GetIncidentUpdatesUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetIncidentByIdUseCase>(
      () => GetIncidentByIdUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<UpdateIncidentUseCase>(
      () => UpdateIncidentUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<DeleteIncidentUseCase>(
      () => DeleteIncidentUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetCommunityIncidentsUseCase>(
      () => GetCommunityIncidentsUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetUserIncidentsUseCase>(
      () => GetUserIncidentsUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetIncidentsNearLocationUseCase>(
      () => GetIncidentsNearLocationUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<SearchUserIncidentsUseCase>(
      () => SearchUserIncidentsUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<SearchCommunityIncidentsUseCase>(
      () => SearchCommunityIncidentsUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<GetIncidentStatisticsUseCase>(
      () => GetIncidentStatisticsUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<ReportIncidentUseCase>(
      () => ReportIncidentUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<BlockIncidentUseCase>(
      () => BlockIncidentUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<ShareIncidentUseCase>(
      () => ShareIncidentUseCase(),
    );

    getIt.registerLazySingleton<ViewIncidentUpdateUseCase>(
      () => ViewIncidentUpdateUseCase(getIt<IncidentRepository>()),
    );

    // Incident creation use cases
    getIt.registerLazySingleton<ValidateIncidentUseCase>(
      () => ValidateIncidentUseCase(),
    );

    getIt.registerLazySingleton<DetermineVisibilityUseCase>(
      () => DetermineVisibilityUseCase(getIt<IncidentVisibilityService>()),
    );

    getIt.registerLazySingleton<BuildIncidentUseCase>(
      () => BuildIncidentUseCase(),
    );

    getIt.registerLazySingleton<CreateIncidentUseCase>(
      () => CreateIncidentUseCase(getIt<IncidentRepository>()),
    );

    getIt.registerLazySingleton<PostIncidentOrchestratorUseCase>(
      () => PostIncidentOrchestratorUseCase(
        validateUseCase: getIt<ValidateIncidentUseCase>(),
        visibilityUseCase: getIt<DetermineVisibilityUseCase>(),
        buildUseCase: getIt<BuildIncidentUseCase>(),
        createUseCase: getIt<CreateIncidentUseCase>(),
      ),
    );

    // BLoCs and Cubits - simplified for now
    getIt.registerFactory<IncidentUpdateCubit>(
      () => IncidentUpdateCubit(
        addIncidentUpdateUseCase: getIt<AddIncidentUpdateUseCase>(),
        getIncidentUpdatesUseCase: getIt<GetIncidentUpdatesUseCase>(),
      ),
    );

    getIt.registerFactory<AddIncidentUpdateCubit>(
      () => AddIncidentUpdateCubit(
        addIncidentUpdateUseCase: getIt<AddIncidentUpdateUseCase>(),
      ),
    );

    getIt.registerFactory<MyIncidentsCubit>(
      () => MyIncidentsCubit(
        getUserIncidentsUseCase: getIt<GetUserIncidentsUseCase>(),
        deleteIncidentUseCase: getIt<DeleteIncidentUseCase>(),
      ),
    );

    getIt.registerFactory<IncidentReportCubit>(
      () => IncidentReportCubit(
        postIncidentUseCase: getIt<PostIncidentOrchestratorUseCase>(),
        visibilityService: getIt<IncidentVisibilityService>(),
      ),
    );

    getIt.registerFactory<IncidentCategorySelectionCubit>(
      () => IncidentCategorySelectionCubit(
        getCategoriesUseCase: getIt<GetIncidentCategoriesUseCase>(),
      ),
    );

    getIt.registerFactory<DeleteIncidentCubit>(
      () => DeleteIncidentCubit(
        deleteIncidentUseCase: getIt<DeleteIncidentUseCase>(),
      ),
    );
  }
}
