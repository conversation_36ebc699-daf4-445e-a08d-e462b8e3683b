class IncidentConstants {
  static const String incidentsCollection = 'incidents';
  static const String incidentUpdatesCollection = 'incident_updates';
  static const String incidentReportsCollection = 'incident_reports';
  static const String incidentStatisticsCollection = 'incident_statistics';
  
  static const String userIdField = 'userId';
  static const String postedAtField = 'postedAt';
  static const String visibilityStatusField = 'visibilityStatus';
  static const String zoneIdField = 'zoneId';
  static const String typeField = 'type';
  static const String statusField = 'status';
  static const String isBlockedField = 'isBlocked';
  static const String locationField = 'location';
  static const String incidentIdField = 'incidentId';
  
  static const int visibilityWindowHours = 72;
  static const int maxIncidentsPerQuery = 50;
  static const int maxUserIncidentsPerDay = 10;
}
