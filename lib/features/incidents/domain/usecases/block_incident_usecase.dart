import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class BlockIncidentParams {
  final String incidentId;
  final String adminUserId;
  final String reason;

  const BlockIncidentParams({
    required this.incidentId,
    required this.adminUserId,
    required this.reason,
  });
}

class BlockIncidentUseCase implements UseCase<IncidentEntity, BlockIncidentParams> {
  final IncidentRepository _repository;

  BlockIncidentUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentEntity>> call(BlockIncidentParams params) async {
    return await _repository.blockIncident(
      incidentId: params.incidentId,
      adminUserId: params.adminUserId,
      reason: params.reason,
    );
  }
}
