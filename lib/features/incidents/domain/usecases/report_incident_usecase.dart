import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class ReportIncidentParams {
  final String incidentId;
  final String reporterUserId;
  final ReportReason reason;
  final String? additionalInfo;

  const ReportIncidentParams({
    required this.incidentId,
    required this.reporterUserId,
    required this.reason,
    this.additionalInfo,
  });
}

class ReportIncidentUseCase implements UseCase<IncidentEntity, ReportIncidentParams> {
  final IncidentRepository _repository;

  ReportIncidentUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentEntity>> call(ReportIncidentParams params) async {
    return await _repository.reportIncident(
      incidentId: params.incidentId,
      reporterUserId: params.reporterUserId,
      reason: params.reason,
      additionalInfo: params.additionalInfo,
    );
  }
}
