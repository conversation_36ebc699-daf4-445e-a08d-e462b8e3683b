export 'create_incident_usecase.dart';
export 'get_incident_by_id_usecase.dart';
export 'update_incident_usecase.dart';
export 'delete_incident_usecase.dart';
export 'post_incident_orchestrator_usecase.dart';

import 'post_incident_orchestrator_usecase.dart';

// Alias for the orchestrator usecase to match bloc expectations
typedef PostIncidentWithVisibilityInfoUseCase = PostIncidentOrchestratorUseCase;
