import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class ShareIncidentParams {
  final IncidentEntity incident;
  final ShareType shareType;
  final String? customMessage;

  const ShareIncidentParams({
    required this.incident,
    required this.shareType,
    this.customMessage,
  });
}

enum ShareType {
  text,
  link,
  full,
}

class ShareIncidentResult {
  final String shareText;
  final String? shareLink;
  final bool success;

  const ShareIncidentResult({
    required this.shareText,
    this.shareLink,
    required this.success,
  });
}

class ShareIncidentUseCase implements UseCase<ShareIncidentResult, ShareIncidentParams> {
  @override
  Future<Either<Failure, ShareIncidentResult>> call(ShareIncidentParams params) async {
    try {
      final shareText = _buildShareText(params.incident, params.shareType, params.customMessage);
      final shareLink = _buildShareLink(params.incident);

      return Right(ShareIncidentResult(
        shareText: shareText,
        shareLink: shareLink,
        success: true,
      ));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to prepare share content: ${e.toString()}'));
    }
  }

  String _buildShareText(IncidentEntity incident, ShareType shareType, String? customMessage) {
    switch (shareType) {
      case ShareType.text:
        return _buildTextOnlyShare(incident, customMessage);
      case ShareType.link:
        return _buildLinkShare(incident, customMessage);
      case ShareType.full:
        return _buildFullShare(incident, customMessage);
    }
  }

  String _buildTextOnlyShare(IncidentEntity incident, String? customMessage) {
    final buffer = StringBuffer();
    
    if (customMessage != null && customMessage.isNotEmpty) {
      buffer.writeln(customMessage);
      buffer.writeln();
    }

    buffer.writeln('🚨 Incident Report');
    buffer.writeln();
    buffer.writeln('📍 ${incident.location.address}');
    buffer.writeln('🏷️ ${incident.categoryTitle}');
    
    if (incident.subcategoryTitle != null) {
      buffer.writeln('📂 ${incident.subcategoryTitle}');
    }
    
    if (incident.severity != null) {
      final severityIcon = _getSeverityIcon(incident.severity!);
      buffer.writeln('$severityIcon ${incident.severity}');
    }
    
    buffer.writeln();
    buffer.writeln('📝 ${incident.title}');
    
    if (incident.description.isNotEmpty) {
      buffer.writeln();
      buffer.writeln(incident.description);
    }
    
    buffer.writeln();
    buffer.writeln('⏰ ${incident.timeSincePosted}');
    
    if (incident.isAnonymous) {
      buffer.writeln('👤 Posted anonymously');
    }

    return buffer.toString();
  }

  String _buildLinkShare(IncidentEntity incident, String? customMessage) {
    final buffer = StringBuffer();
    
    if (customMessage != null && customMessage.isNotEmpty) {
      buffer.writeln(customMessage);
      buffer.writeln();
    }

    buffer.writeln('🚨 Incident in ${incident.location.address}');
    buffer.writeln();
    buffer.writeln('${incident.categoryTitle} - ${incident.title}');
    buffer.writeln();
    buffer.writeln('View details: ${_buildShareLink(incident)}');

    return buffer.toString();
  }

  String _buildFullShare(IncidentEntity incident, String? customMessage) {
    final buffer = StringBuffer();
    
    if (customMessage != null && customMessage.isNotEmpty) {
      buffer.writeln(customMessage);
      buffer.writeln();
    }

    buffer.writeln('🚨 Community Safety Alert');
    buffer.writeln();
    buffer.writeln('📍 Location: ${incident.location.address}');
    buffer.writeln('🏷️ Category: ${incident.categoryTitle}');
    
    if (incident.subcategoryTitle != null) {
      buffer.writeln('📂 Subcategory: ${incident.subcategoryTitle}');
    }
    
    if (incident.severity != null) {
      final severityIcon = _getSeverityIcon(incident.severity!);
      buffer.writeln('$severityIcon Severity: ${incident.severity}');
    }
    
    buffer.writeln();
    buffer.writeln('📝 ${incident.title}');
    
    if (incident.description.isNotEmpty) {
      buffer.writeln();
      buffer.writeln(incident.description);
    }
    
    if (incident.hasMedia) {
      buffer.writeln();
      buffer.writeln('📷 ${incident.mediaCount} media file(s) attached');
    }
    
    buffer.writeln();
    buffer.writeln('⏰ Reported ${incident.timeSincePosted}');
    
    if (incident.isAnonymous) {
      buffer.writeln('👤 Posted anonymously');
    }

    buffer.writeln();
    buffer.writeln('View full details: ${_buildShareLink(incident)}');
    buffer.writeln();
    buffer.writeln('Stay safe! 🛡️');

    return buffer.toString();
  }

  String? _buildShareLink(IncidentEntity incident) {
    // Generate a deep link to the incident
    return 'https://respublicaseguridad.app/incidents/${incident.incidentId}';
  }

  String _getSeverityIcon(String severity) {
    final severityLower = severity.toLowerCase();
    if (severityLower.contains('grave') || severityLower.contains('high')) {
      return '🔴';
    } else if (severityLower.contains('leve') || severityLower.contains('low')) {
      return '🟡';
    }
    return '⚪';
  }
}


