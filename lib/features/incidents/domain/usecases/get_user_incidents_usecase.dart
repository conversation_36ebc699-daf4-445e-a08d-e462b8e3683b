import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class GetUserIncidentsParams {
  final String userId;
  final IncidentStatus? statusFilter;
  final String? categoryFilter;
  final bool includeStatistics;

  const GetUserIncidentsParams({
    required this.userId,
    this.statusFilter,
    this.categoryFilter,
    this.includeStatistics = false,
  });
}

class GetUserIncidentsResult {
  final List<IncidentEntity> incidents;
  final Map<String, dynamic>? statistics;

  const GetUserIncidentsResult({
    required this.incidents,
    this.statistics,
  });
}

class GetUserIncidentsUseCase implements UseCase<GetUserIncidentsResult, GetUserIncidentsParams> {
  final IncidentRepository _repository;

  GetUserIncidentsUseCase(this._repository);

  @override
  Future<Either<Failure, GetUserIncidentsResult>> call(GetUserIncidentsParams params) async {
    final incidentsResult = await _repository.getUserIncidents(params.userId);

    return incidentsResult.fold(
      (failure) => Left(failure),
      (incidents) async {
        // Filter incidents based on parameters
        var filteredIncidents = incidents;

        if (params.statusFilter != null) {
          filteredIncidents = filteredIncidents.where((incident) =>
            incident.status == params.statusFilter).toList();
        }

        if (params.categoryFilter != null) {
          filteredIncidents = filteredIncidents.where((incident) =>
            incident.categoryKey == params.categoryFilter).toList();
        }

        Map<String, dynamic>? statistics;
        if (params.includeStatistics) {
          final statsResult = await _repository.getUserIncidentStatistics(params.userId);
          statistics = statsResult.fold((failure) => null, (stats) => stats);
        }

        return Right(GetUserIncidentsResult(
          incidents: filteredIncidents,
          statistics: statistics,
        ));
      },
    );
  }
}
