import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/validate_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/determine_visibility_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/build_incident_usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/create_incident_usecase.dart';

class PostIncidentParams {
  final String userId;
  final String categoryKey;
  final String categoryTitle;
  final String? subcategoryKey;
  final String? subcategoryTitle;
  final String? severity;
  final String title;
  final String description;
  final LocationEntity location;
  final List<MediaEntity> media;
  final bool isAnonymous;
  final String? zoneId;
  final Map<String, dynamic>? metadata;

  const PostIncidentParams({
    required this.userId,
    required this.categoryKey,
    required this.categoryTitle,
    this.subcategoryKey,
    this.subcategoryTitle,
    this.severity,
    required this.title,
    required this.description,
    required this.location,
    this.media = const [],
    this.isAnonymous = false,
    this.zoneId,
    this.metadata,
  });
}

class PostIncidentOrchestratorUseCase implements UseCase<IncidentEntity, PostIncidentParams> {
  final ValidateIncidentUseCase _validateUseCase;
  final DetermineVisibilityUseCase _visibilityUseCase;
  final BuildIncidentUseCase _buildUseCase;
  final CreateIncidentUseCase _createUseCase;

  PostIncidentOrchestratorUseCase({
    required ValidateIncidentUseCase validateUseCase,
    required DetermineVisibilityUseCase visibilityUseCase,
    required BuildIncidentUseCase buildUseCase,
    required CreateIncidentUseCase createUseCase,
  }) : _validateUseCase = validateUseCase,
       _visibilityUseCase = visibilityUseCase,
       _buildUseCase = buildUseCase,
       _createUseCase = createUseCase;

  @override
  Future<Either<Failure, IncidentEntity>> call(PostIncidentParams params) async {
    final visibilityResult = await _visibilityUseCase(DetermineVisibilityParams(
      userId: params.userId,
      location: params.location,
    ));

    if (visibilityResult.isLeft()) {
      return Left(visibilityResult.fold((l) => l, (r) => throw Exception()));
    }

    final visibility = visibilityResult.fold((l) => throw Exception(), (r) => r);

    final buildResult = await _buildUseCase(BuildIncidentParams(
      userId: params.userId,
      categoryKey: params.categoryKey,
      categoryTitle: params.categoryTitle,
      subcategoryKey: params.subcategoryKey,
      subcategoryTitle: params.subcategoryTitle,
      severity: params.severity,
      title: params.title,
      description: params.description,
      location: params.location,
      visibilityStatus: visibility,
      media: params.media,
      isAnonymous: params.isAnonymous,
      zoneId: params.zoneId,
      metadata: params.metadata,
    ));

    if (buildResult.isLeft()) {
      return Left(buildResult.fold((l) => l, (r) => throw Exception()));
    }

    final incident = buildResult.fold((l) => throw Exception(), (r) => r);

    final validationResult = await _validateUseCase(incident);
    if (validationResult.isLeft()) {
      return Left(validationResult.fold((l) => l, (r) => throw Exception()));
    }

    return await _createUseCase(incident);
  }
}
