import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/services/incident_visibility_service.dart';

class DetermineVisibilityParams {
  final String userId;
  final LocationEntity location;

  const DetermineVisibilityParams({
    required this.userId,
    required this.location,
  });
}

class DetermineVisibilityUseCase implements UseCase<IncidentVisibilityStatus, DetermineVisibilityParams> {
  final IncidentVisibilityService _visibilityService;

  DetermineVisibilityUseCase(this._visibilityService);

  @override
  Future<Either<Failure, IncidentVisibilityStatus>> call(DetermineVisibilityParams params) async {
    return await _visibilityService.determineIncidentVisibility(
      userId: params.userId,
      incidentLocation: params.location,
    );
  }
}
