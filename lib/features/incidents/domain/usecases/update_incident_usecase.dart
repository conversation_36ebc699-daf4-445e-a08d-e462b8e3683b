import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class UpdateIncidentParams {
  final IncidentEntity incident;
  final String userId;

  const UpdateIncidentParams({
    required this.incident,
    required this.userId,
  });
}

class UpdateIncidentUseCase implements UseCase<IncidentEntity, UpdateIncidentParams> {
  final IncidentRepository _repository;

  UpdateIncidentUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentEntity>> call(UpdateIncidentParams params) async {
    return await _repository.updateIncident(params.incident);
  }
}
