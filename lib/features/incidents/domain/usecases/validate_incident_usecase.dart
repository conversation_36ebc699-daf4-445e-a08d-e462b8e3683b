import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class ValidateIncidentUseCase implements UseCase<void, IncidentEntity> {
  @override
  Future<Either<Failure, void>> call(IncidentEntity incident) async {
    if (incident.userId.isEmpty) {
      return Left(ValidationFailure('User ID is required'));
    }

    if (incident.title.trim().isEmpty) {
      return Left(ValidationFailure('Title is required'));
    }

    if (incident.title.length > 100) {
      return Left(ValidationFailure('Title must be 100 characters or less'));
    }

    if (incident.description.trim().isEmpty) {
      return Left(ValidationFailure('Description is required'));
    }

    if (incident.description.length > 1000) {
      return Left(ValidationFailure('Description must be 1000 characters or less'));
    }

    if (!incident.location.isValid) {
      return Left(ValidationFailure('Valid location is required'));
    }

    for (final media in incident.media) {
      if (!media.isValid) {
        return Left(ValidationFailure('All media files must be valid'));
      }
    }

    if (incident.media.length > 5) {
      return Left(ValidationFailure('Maximum 5 media files allowed per incident'));
    }

    return const Right(null);
  }
}
