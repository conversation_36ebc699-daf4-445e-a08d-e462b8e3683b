import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class DeleteIncidentParams {
  final String incidentId;
  final String userId;

  const DeleteIncidentParams({
    required this.incidentId,
    required this.userId,
  });
}

class DeleteIncidentUseCase implements UseCase<void, DeleteIncidentParams> {
  final IncidentRepository _repository;

  DeleteIncidentUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(DeleteIncidentParams params) async {
    return await _repository.deleteIncident(params.incidentId);
  }
}
