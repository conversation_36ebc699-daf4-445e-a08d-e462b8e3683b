import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class SearchUserIncidentsParams {
  final String userId;
  final String query;
  final String? categoryFilter;

  const SearchUserIncidentsParams({
    required this.userId,
    required this.query,
    this.categoryFilter,
  });
}

class SearchUserIncidentsUseCase implements UseCase<List<IncidentEntity>, SearchUserIncidentsParams> {
  final IncidentRepository _repository;

  SearchUserIncidentsUseCase(this._repository);

  @override
  Future<Either<Failure, List<IncidentEntity>>> call(SearchUserIncidentsParams params) async {
    return await _repository.searchUserIncidents(
      userId: params.userId,
      query: params.query,
      categoryFilter: params.categoryFilter,
    );
  }
}
