import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class GetIncidentStatisticsUseCase implements UseCase<Map<String, dynamic>, String> {
  final IncidentRepository _repository;

  GetIncidentStatisticsUseCase(this._repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(String userId) async {
    return await _repository.getUserIncidentStatistics(userId);
  }
}
