import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class SearchCommunityIncidentsParams {
  final String userId;
  final String query;
  final String? categoryFilter;

  const SearchCommunityIncidentsParams({
    required this.userId,
    required this.query,
    this.categoryFilter,
  });
}

class SearchCommunityIncidentsUseCase implements UseCase<List<IncidentEntity>, SearchCommunityIncidentsParams> {
  final IncidentRepository _repository;

  SearchCommunityIncidentsUseCase(this._repository);

  @override
  Future<Either<Failure, List<IncidentEntity>>> call(SearchCommunityIncidentsParams params) async {
    // Get user's validated zones first (this would need to be implemented)
    // For now, we'll use an empty list as a placeholder
    final userValidatedZoneIds = <String>[];
    
    return await _repository.searchIncidents(
      userId: params.userId,
      query: params.query,
      userValidatedZoneIds: userValidatedZoneIds,
      categoryFilter: params.categoryFilter,
    );
  }
}
