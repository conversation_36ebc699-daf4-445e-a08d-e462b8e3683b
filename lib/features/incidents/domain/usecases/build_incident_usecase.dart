import 'package:dartz/dartz.dart';
import 'package:uuid/uuid.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

class BuildIncidentParams {
  final String userId;
  final String categoryKey;
  final String categoryTitle;
  final String? subcategoryKey;
  final String? subcategoryTitle;
  final String? severity;
  final String title;
  final String description;
  final LocationEntity location;
  final List<MediaEntity> media;
  final bool isAnonymous;
  final IncidentVisibilityStatus visibilityStatus;
  final String? zoneId;
  final Map<String, dynamic>? metadata;

  const BuildIncidentParams({
    required this.userId,
    required this.categoryKey,
    required this.categoryTitle,
    this.subcategoryKey,
    this.subcategoryTitle,
    this.severity,
    required this.title,
    required this.description,
    required this.location,
    required this.visibilityStatus,
    this.media = const [],
    this.isAnonymous = false,
    this.zoneId,
    this.metadata,
  });
}

class BuildIncidentUseCase implements UseCase<IncidentEntity, BuildIncidentParams> {
  final Uuid _uuid;

  BuildIncidentUseCase({Uuid? uuid}) : _uuid = uuid ?? const Uuid();

  @override
  Future<Either<Failure, IncidentEntity>> call(BuildIncidentParams params) async {
    try {
      final incident = IncidentEntity(
        incidentId: _uuid.v4(),
        userId: params.userId,
        categoryKey: params.categoryKey,
        categoryTitle: params.categoryTitle,
        subcategoryKey: params.subcategoryKey,
        subcategoryTitle: params.subcategoryTitle,
        severity: params.severity,
        title: params.title,
        description: params.description,
        location: params.location,
        media: params.media,
        postedAt: DateTime.now(),
        visibilityStatus: params.visibilityStatus,
        isAnonymous: params.isAnonymous,
        zoneId: params.zoneId,
        metadata: params.metadata,
      );

      return Right(incident);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to build incident: ${e.toString()}'));
    }
  }
}
