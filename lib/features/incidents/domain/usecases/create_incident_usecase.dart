import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class CreateIncidentUseCase implements UseCase<IncidentEntity, IncidentEntity> {
  final IncidentRepository _repository;

  CreateIncidentUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentEntity>> call(IncidentEntity incident) async {
    return await _repository.createIncident(incident);
  }
}
