import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class GetIncidentByIdParams {
  final String incidentId;
  final String userId;

  const GetIncidentByIdParams({
    required this.incidentId,
    required this.userId,
  });
}

class GetIncidentByIdUseCase implements UseCase<IncidentEntity, GetIncidentByIdParams> {
  final IncidentRepository _repository;

  GetIncidentByIdUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentEntity>> call(GetIncidentByIdParams params) async {
    return await _repository.getIncidentById(params.incidentId);
  }
}
