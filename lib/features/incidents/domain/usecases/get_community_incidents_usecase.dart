import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class GetCommunityIncidentsParams {
  final String userId;
  final List<String> userValidatedZoneIds;
  final String? categoryFilter;
  final int? limit;
  final DateTime? lastIncidentTimestamp;

  const GetCommunityIncidentsParams({
    required this.userId,
    required this.userValidatedZoneIds,
    this.categoryFilter,
    this.limit,
    this.lastIncidentTimestamp,
  });
}

class GetCommunityIncidentsUseCase implements UseCase<List<IncidentEntity>, GetCommunityIncidentsParams> {
  final IncidentRepository _repository;

  GetCommunityIncidentsUseCase(this._repository);

  @override
  Future<Either<Failure, List<IncidentEntity>>> call(GetCommunityIncidentsParams params) async {
    return await _repository.getCommunityIncidents(
      userId: params.userId,
      userValidatedZoneIds: params.userValidatedZoneIds,
      categoryFilter: params.categoryFilter,
      limit: params.limit,
      lastIncidentTimestamp: params.lastIncidentTimestamp,
    );
  }
}
