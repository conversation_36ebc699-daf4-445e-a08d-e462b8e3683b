import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class GetIncidentsNearLocationParams {
  final String userId;
  final double latitude;
  final double longitude;
  final double radiusInMeters;
  final String? categoryFilter;

  const GetIncidentsNearLocationParams({
    required this.userId,
    required this.latitude,
    required this.longitude,
    required this.radiusInMeters,
    this.categoryFilter,
  });
}

class GetIncidentsNearLocationUseCase implements UseCase<List<IncidentEntity>, GetIncidentsNearLocationParams> {
  final IncidentRepository _repository;

  GetIncidentsNearLocationUseCase(this._repository);

  @override
  Future<Either<Failure, List<IncidentEntity>>> call(GetIncidentsNearLocationParams params) async {
    return await _repository.getIncidentsNearLocation(
      userId: params.userId,
      latitude: params.latitude,
      longitude: params.longitude,
      radiusInMeters: params.radiusInMeters,
      categoryFilter: params.categoryFilter,
    );
  }
}
