import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class AddIncidentUpdateParams {
  final String incidentId;
  final IncidentUpdateEntity update;

  const AddIncidentUpdateParams({
    required this.incidentId,
    required this.update,
  });
}

class AddIncidentUpdateUseCase implements UseCase<IncidentUpdateEntity, AddIncidentUpdateParams> {
  final IncidentRepository _repository;

  AddIncidentUpdateUseCase(this._repository);

  @override
  Future<Either<Failure, IncidentUpdateEntity>> call(AddIncidentUpdateParams params) async {
    return await _repository.addIncidentUpdate(
      incidentId: params.incidentId,
      update: params.update,
    );
  }
}

class GetIncidentUpdatesUseCase implements UseCase<List<IncidentUpdateEntity>, String> {
  final IncidentRepository _repository;

  GetIncidentUpdatesUseCase(this._repository);

  @override
  Future<Either<Failure, List<IncidentUpdateEntity>>> call(String incidentId) async {
    return await _repository.getIncidentUpdates(incidentId);
  }
}
