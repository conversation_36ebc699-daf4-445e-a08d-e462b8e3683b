import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/incident_category_entity.dart';
import '../repositories/incident_category_repository.dart';

/// Parameters for getting incident categories (no parameters needed)
class GetIncidentCategoriesParams {
  const GetIncidentCategoriesParams();
}

/// Use case for fetching incident categories
class GetIncidentCategoriesUseCase implements UseCase<List<IncidentCategoryEntity>, GetIncidentCategoriesParams> {
  final IncidentCategoryRepository repository;

  const GetIncidentCategoriesUseCase(this.repository);

  @override
  Future<Either<Failure, List<IncidentCategoryEntity>>> call(GetIncidentCategoriesParams params) async {
    try {
      // Try to fetch from API first
      final categories = await repository.getCategories();

      // Cache the fetched categories
      await repository.cacheCategories(categories);

      return Right(categories);
    } catch (e) {
      try {
        // If API fails, try to get cached categories
        final cachedCategories = await repository.getCachedCategories();

        if (cachedCategories.isNotEmpty) {
          return Right(cachedCategories);
        }

        // If no cached categories, return default fallback
        return Right(_getDefaultCategories());
      } catch (cacheError) {
        // If both API and cache fail, return default categories
        return Right(_getDefaultCategories());
      }
    }
  }

  /// Fallback categories in case API and cache both fail
  List<IncidentCategoryEntity> _getDefaultCategories() {
    final now = DateTime.now();
    return [
      IncidentCategoryEntity(
        id: 'crime',
        key: 'crime',
        title: 'Crime & Security',
        description: 'Theft, robbery, vandalism, break-ins',
        iconName: 'shield_error',
        color: '#E53E3E',
        sortOrder: 1,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'medical',
        key: 'medicalEmergency',
        title: 'Medical Emergency',
        description: 'Health emergencies, injuries, medical assistance needed',
        iconName: 'heart_pulse',
        color: '#D53F8C',
        sortOrder: 2,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'fire',
        key: 'fire',
        title: 'Fire & Hazards',
        description: 'Fire incidents, gas leaks, dangerous materials',
        iconName: 'fire',
        color: '#DD6B20',
        sortOrder: 3,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'accident',
        key: 'accident',
        title: 'Traffic & Accidents',
        description: 'Vehicle accidents, collisions, road incidents',
        iconName: 'vehicle_car_collision',
        color: '#3182CE',
        sortOrder: 4,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'suspicious',
        key: 'suspiciousActivity',
        title: 'Suspicious Activity',
        description: 'Unusual behavior, security concerns, suspicious persons',
        iconName: 'eye',
        color: '#805AD5',
        sortOrder: 5,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'natural',
        key: 'naturalDisaster',
        title: 'Natural Disaster',
        description: 'Earthquakes, floods, storms, natural emergencies',
        iconName: 'cloud',
        color: '#38A169',
        sortOrder: 6,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'infrastructure',
        key: 'infrastructure',
        title: 'Infrastructure',
        description: 'Power outages, water issues, road damage, public utilities',
        iconName: 'building',
        color: '#319795',
        sortOrder: 7,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'disturbance',
        key: 'publicDisturbance',
        title: 'Public Disturbance',
        description: 'Noise complaints, gatherings, public order issues',
        iconName: 'people',
        color: '#D69E2E',
        sortOrder: 8,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'environmental',
        key: 'environmental',
        title: 'Environmental',
        description: 'Pollution, waste issues, environmental hazards',
        iconName: 'globe',
        color: '#48BB78',
        sortOrder: 9,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'animal',
        key: 'animalRelated',
        title: 'Animal Related',
        description: 'Stray animals, animal attacks, wildlife issues',
        iconName: 'heart',
        color: '#ED8936',
        sortOrder: 10,
        createdAt: now,
      ),
      IncidentCategoryEntity(
        id: 'other',
        key: 'other',
        title: 'Other',
        description: 'Other incidents not covered by specific categories',
        iconName: 'more_horizontal',
        color: '#718096',
        sortOrder: 99,
        createdAt: now,
      ),
    ];
  }
}
