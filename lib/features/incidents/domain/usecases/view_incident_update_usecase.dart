import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

class ViewIncidentUpdateParams {
  final String incidentId;
  final String updateId;
  final String userId; // For authorization check

  const ViewIncidentUpdateParams({
    required this.incidentId,
    required this.updateId,
    required this.userId,
  });
}

class ViewIncidentUpdateResult {
  final IncidentUpdateEntity update;
  final IncidentEntity incident;
  final bool canEdit;
  final bool canDelete;

  const ViewIncidentUpdateResult({
    required this.update,
    required this.incident,
    required this.canEdit,
    required this.canDelete,
  });
}

class ViewIncidentUpdateUseCase implements UseCase<ViewIncidentUpdateResult, ViewIncidentUpdateParams> {
  final IncidentRepository _repository;

  ViewIncidentUpdateUseCase(this._repository);

  @override
  Future<Either<Failure, ViewIncidentUpdateResult>> call(ViewIncidentUpdateParams params) async {
    try {
      // Get the incident first to check authorization
      final incidentResult = await _repository.getIncidentById(params.incidentId);
      if (incidentResult.isLeft()) {
        return Left(incidentResult.fold((l) => l, (r) => throw Exception()));
      }

      final incident = incidentResult.fold((l) => throw Exception(), (r) => r);

      // Get all updates for the incident
      final updatesResult = await _repository.getIncidentUpdates(params.incidentId);
      if (updatesResult.isLeft()) {
        return Left(updatesResult.fold((l) => l, (r) => throw Exception()));
      }

      final updates = updatesResult.fold((l) => throw Exception(), (r) => r);

      // Find the specific update
      final update = updates.firstWhere(
        (u) => u.updateId == params.updateId,
        orElse: () => IncidentUpdateEntity.empty,
      );

      if (update.isEmpty) {
        return Left(NotFoundFailure('Update not found'));
      }

      // Determine permissions
      final canEdit = _canUserEditUpdate(params.userId, incident, update);
      final canDelete = _canUserDeleteUpdate(params.userId, incident, update);

      return Right(ViewIncidentUpdateResult(
        update: update,
        incident: incident,
        canEdit: canEdit,
        canDelete: canDelete,
      ));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to load update: ${e.toString()}'));
    }
  }

  bool _canUserEditUpdate(String userId, IncidentEntity incident, IncidentUpdateEntity update) {
    // Only the incident reporter can edit updates
    // And only their own updates
    return userId.isNotEmpty && 
           userId == incident.userId && 
           userId == update.authorId;
  }

  bool _canUserDeleteUpdate(String userId, IncidentEntity incident, IncidentUpdateEntity update) {
    // Only the incident reporter can delete updates
    // And only their own updates
    return userId.isNotEmpty && 
           userId == incident.userId && 
           userId == update.authorId;
  }
}
