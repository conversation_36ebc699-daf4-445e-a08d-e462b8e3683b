import 'package:equatable/equatable.dart';

/// Entity representing an incident subcategory
class IncidentSubcategoryEntity extends Equatable {
  final String key; // e.g., 'animal_suelto', 'maltrato_animal', etc.
  final String title;
  final List<String> severities; // e.g., ["Leve: Perro/gato suelto", "Grave: Animal agresivo/rabioso, jauría, ataque"]

  const IncidentSubcategoryEntity({
    required this.key,
    required this.title,
    required this.severities,
  });

  @override
  List<Object?> get props => [
        key,
        title,
        severities,
      ];

  IncidentSubcategoryEntity copyWith({
    String? key,
    String? title,
    List<String>? severities,
  }) {
    return IncidentSubcategoryEntity(
      key: key ?? this.key,
      title: title ?? this.title,
      severities: severities ?? this.severities,
    );
  }
}
