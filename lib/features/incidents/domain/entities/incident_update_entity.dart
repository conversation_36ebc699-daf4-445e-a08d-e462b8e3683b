import 'package:equatable/equatable.dart';
import 'incident_enums.dart';

class IncidentUpdateEntity extends Equatable {
  final String updateId;
  final String incidentId;
  final String content;
  final DateTime timestamp;
  final String authorId;
  final IncidentUpdateType type;
  final String? authorDisplayName;
  final bool isAnonymous;
  final Map<String, dynamic>? metadata;

  const IncidentUpdateEntity({
    required this.updateId,
    required this.incidentId,
    required this.content,
    required this.timestamp,
    required this.authorId,
    required this.type,
    this.authorDisplayName,
    this.isAnonymous = false,
    this.metadata,
  });

  /// Check if this update is a text update
  bool get isTextUpdate => type == IncidentUpdateType.textUpdate;

  /// Check if this update is a link
  bool get isLink => type == IncidentUpdateType.link;

  /// Check if this update is a status change
  bool get isStatusChange => type == IncidentUpdateType.statusChange;

  /// Check if this update is a community observation
  bool get isCommunityObservation => type == IncidentUpdateType.communityObservation;

  /// Get the display name for the author
  String get displayAuthorName {
    if (isAnonymous) return 'Anonymous';
    return authorDisplayName ?? 'Unknown User';
  }

  /// Check if the content is a valid URL (for link type updates)
  bool get isValidUrl {
    if (!isLink) return true; // Not applicable for non-link updates
    
    try {
      final uri = Uri.parse(content);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get formatted timestamp
  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// Check if update is valid
  bool get isValid {
    return updateId.isNotEmpty &&
           incidentId.isNotEmpty &&
           content.isNotEmpty &&
           authorId.isNotEmpty &&
           (isLink ? isValidUrl : true);
  }

  /// Check if update is recent (within last 24 hours)
  bool get isRecent {
    final now = DateTime.now();
    return now.difference(timestamp).inHours < 24;
  }

  IncidentUpdateEntity copyWith({
    String? updateId,
    String? incidentId,
    String? content,
    DateTime? timestamp,
    String? authorId,
    IncidentUpdateType? type,
    String? authorDisplayName,
    bool? isAnonymous,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentUpdateEntity(
      updateId: updateId ?? this.updateId,
      incidentId: incidentId ?? this.incidentId,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      authorId: authorId ?? this.authorId,
      type: type ?? this.type,
      authorDisplayName: authorDisplayName ?? this.authorDisplayName,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        updateId,
        incidentId,
        content,
        timestamp,
        authorId,
        type,
        authorDisplayName,
        isAnonymous,
        metadata,
      ];

  static final empty = IncidentUpdateEntity(
    updateId: '',
    incidentId: '',
    content: '',
    timestamp: DateTime.now(),
    authorId: '',
    type: IncidentUpdateType.textUpdate,
  );

  bool get isEmpty => this == IncidentUpdateEntity.empty;
  bool get isNotEmpty => this != IncidentUpdateEntity.empty;

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'updateId': updateId,
      'incidentId': incidentId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'authorId': authorId,
      'type': type.name,
      'authorDisplayName': authorDisplayName,
      'isAnonymous': isAnonymous,
      'metadata': metadata,
    };
  }

  /// Create from JSON for deserialization
  factory IncidentUpdateEntity.fromJson(Map<String, dynamic> json) {
    return IncidentUpdateEntity(
      updateId: json['updateId'] as String,
      incidentId: json['incidentId'] as String,
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      authorId: json['authorId'] as String,
      type: IncidentUpdateType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => IncidentUpdateType.textUpdate,
      ),
      authorDisplayName: json['authorDisplayName'] as String?,
      isAnonymous: json['isAnonymous'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
