import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';

class LocationEntity extends Equatable {
  final double latitude;
  final double longitude;
  final String address;

  const LocationEntity({
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  /// Calculate distance to another location in meters using geolocator package
  double distanceTo(LocationEntity other) {
    return Geolocator.distanceBetween(
      latitude,
      longitude,
      other.latitude,
      other.longitude,
    );
  }

  /// Check if this location is within a certain radius of another location
  bool isWithinRadius(LocationEntity other, double radiusInMeters) {
    return distanceTo(other) <= radiusInMeters;
  }

  /// Get a formatted string representation of coordinates
  String get coordinatesString => '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';

  /// Check if coordinates are valid
  bool get isValid {
    return latitude >= -90 && latitude <= 90 && 
           longitude >= -180 && longitude <= 180 &&
           address.isNotEmpty;
  }

  LocationEntity copyWith({
    double? latitude,
    double? longitude,
    String? address,
  }) {
    return LocationEntity(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
    );
  }

  @override
  List<Object?> get props => [latitude, longitude, address];

  static const empty = LocationEntity(
    latitude: 0.0,
    longitude: 0.0,
    address: '',
  );

  bool get isEmpty => this == LocationEntity.empty;
  bool get isNotEmpty => this != LocationEntity.empty;

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() => toMap();

  /// Create from JSON for deserialization
  factory LocationEntity.fromJson(Map<String, dynamic> json) {
    return LocationEntity(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String? ?? '',
    );
  }
}

extension LocationEntityExtension on LocationEntity {
  /// Convert to a map for serialization
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
    };
  }

  /// Create from a map
  static LocationEntity fromMap(Map<String, dynamic> map) {
    return LocationEntity(
      latitude: (map['latitude'] as num).toDouble(),
      longitude: (map['longitude'] as num).toDouble(),
      address: map['address'] as String? ?? '',
    );
  }
}
