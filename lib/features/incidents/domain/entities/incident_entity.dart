import 'package:equatable/equatable.dart';
import 'incident_enums.dart';
import 'location_entity.dart';
import 'media_entity.dart';
import 'incident_update_entity.dart';

class IncidentEntity extends Equatable {
  final String incidentId;
  final String userId;
  final String categoryKey; 
  final String categoryTitle;
  final String? subcategoryKey;
  final String? subcategoryTitle;
  final String? severity;
  final String title;
  final String description;
  final LocationEntity location;
  final List<MediaEntity> media;
  final DateTime postedAt;
  final IncidentVisibilityStatus visibilityStatus;
  final bool isAnonymous;
  final List<IncidentUpdateEntity> updates;
  final String? zoneId;
  final DateTime? lastUpdatedAt;
  final IncidentStatus status;
  final List<ReportReason> reportReasons;
  final int reportCount;
  final bool isBlocked;
  final Map<String, dynamic>? metadata;

  const IncidentEntity({
    required this.incidentId,
    required this.userId,
    required this.categoryKey,
    required this.categoryTitle,
    this.subcategoryKey,
    this.subcategoryTitle,
    this.severity,
    required this.title,
    required this.description,
    required this.location,
    this.media = const [],
    required this.postedAt,
    this.visibilityStatus = IncidentVisibilityStatus.visibleToSelf,
    this.isAnonymous = false,
    this.updates = const [],
    this.zoneId,
    this.lastUpdatedAt,
    this.status = IncidentStatus.active,
    this.reportReasons = const [],
    this.reportCount = 0,
    this.isBlocked = false,
    this.metadata,
  });

  /// Check if incident is visible to community
  bool get isVisibleToCommunity => visibilityStatus == IncidentVisibilityStatus.visibleToCommunity;

  /// Check if incident is only visible to self
  bool get isVisibleToSelfOnly => visibilityStatus == IncidentVisibilityStatus.visibleToSelf;

  /// Check if incident has media attachments
  bool get hasMedia => media.isNotEmpty;

  /// Check if incident has updates
  bool get hasUpdates => updates.isNotEmpty;

  /// Get the number of media attachments
  int get mediaCount => media.length;

  /// Get the number of updates
  int get updateCount => updates.length;

  /// Get media by type
  List<MediaEntity> getMediaByType(MediaType type) {
    return media.where((m) => m.type == type).toList();
  }

  /// Get photos only
  List<MediaEntity> get photos => getMediaByType(MediaType.photo);

  /// Get videos only
  List<MediaEntity> get videos => getMediaByType(MediaType.video);

  /// Get audio files only
  List<MediaEntity> get audioFiles => getMediaByType(MediaType.audio);

  /// Check if incident has photos
  bool get hasPhotos => photos.isNotEmpty;

  /// Check if incident has videos
  bool get hasVideos => videos.isNotEmpty;

  /// Check if incident has audio
  bool get hasAudio => audioFiles.isNotEmpty;

  /// Get the most recent update
  IncidentUpdateEntity? get latestUpdate {
    if (updates.isEmpty) return null;
    return updates.reduce((a, b) => a.timestamp.isAfter(b.timestamp) ? a : b);
  }

  /// Get updates sorted by timestamp (newest first)
  List<IncidentUpdateEntity> get sortedUpdates {
    final sortedList = List<IncidentUpdateEntity>.from(updates);
    sortedList.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedList;
  }

  /// Check if incident is recent (within last 72 hours for time-decay visibility)
  bool get isRecent {
    final now = DateTime.now();
    return now.difference(postedAt).inHours < 72;
  }

  /// Check if incident should be visible based on time-decay (72 hours)
  bool get isWithinVisibilityWindow => isRecent;

  /// Check if incident is reported
  bool get isReported => reportCount > 0 || reportReasons.isNotEmpty;

  /// Check if incident is under review
  bool get isUnderReview => status == IncidentStatus.underReview;

  /// Check if incident is resolved
  bool get isResolved => status == IncidentStatus.resolved;

  /// Check if incident is archived
  bool get isArchived => status == IncidentStatus.archived;

  /// Check if incident is active (not blocked, reported, or archived)
  bool get isActive => status == IncidentStatus.active && !isBlocked;

  /// Get formatted time since posted
  String get timeSincePosted {
    final now = DateTime.now();
    final difference = now.difference(postedAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${postedAt.day}/${postedAt.month}/${postedAt.year}';
    }
  }

  /// Check if incident is valid
  bool get isValid {
    return incidentId.isNotEmpty &&
           userId.isNotEmpty &&
           title.isNotEmpty &&
           description.isNotEmpty &&
           location.isValid;
  }

  /// Add a media attachment
  IncidentEntity addMedia(MediaEntity mediaEntity) {
    final newMedia = List<MediaEntity>.from(media)..add(mediaEntity);
    return copyWith(
      media: newMedia,
      lastUpdatedAt: DateTime.now(),
    );
  }

  /// Remove a media attachment
  IncidentEntity removeMedia(String mediaId) {
    final newMedia = media.where((m) => m.mediaId != mediaId).toList();
    return copyWith(
      media: newMedia,
      lastUpdatedAt: DateTime.now(),
    );
  }

  /// Add an update
  IncidentEntity addUpdate(IncidentUpdateEntity update) {
    final newUpdates = List<IncidentUpdateEntity>.from(updates)..add(update);
    return copyWith(
      updates: newUpdates,
      lastUpdatedAt: DateTime.now(),
    );
  }

  /// Update visibility status
  IncidentEntity updateVisibility(IncidentVisibilityStatus newStatus) {
    return copyWith(
      visibilityStatus: newStatus,
      lastUpdatedAt: DateTime.now(),
    );
  }

  IncidentEntity copyWith({
    String? incidentId,
    String? userId,
    String? categoryKey,
    String? categoryTitle,
    String? subcategoryKey,
    String? subcategoryTitle,
    String? severity,
    String? title,
    String? description,
    LocationEntity? location,
    List<MediaEntity>? media,
    DateTime? postedAt,
    IncidentVisibilityStatus? visibilityStatus,
    bool? isAnonymous,
    List<IncidentUpdateEntity>? updates,
    String? zoneId,
    DateTime? lastUpdatedAt,
    IncidentStatus? status,
    List<ReportReason>? reportReasons,
    int? reportCount,
    bool? isBlocked,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentEntity(
      incidentId: incidentId ?? this.incidentId,
      userId: userId ?? this.userId,
      categoryKey: categoryKey ?? this.categoryKey,
      categoryTitle: categoryTitle ?? this.categoryTitle,
      subcategoryKey: subcategoryKey ?? this.subcategoryKey,
      subcategoryTitle: subcategoryTitle ?? this.subcategoryTitle,
      severity: severity ?? this.severity,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      media: media ?? this.media,
      postedAt: postedAt ?? this.postedAt,
      visibilityStatus: visibilityStatus ?? this.visibilityStatus,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      updates: updates ?? this.updates,
      zoneId: zoneId ?? this.zoneId,
      lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt,
      status: status ?? this.status,
      reportReasons: reportReasons ?? this.reportReasons,
      reportCount: reportCount ?? this.reportCount,
      isBlocked: isBlocked ?? this.isBlocked,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        incidentId,
        userId,
        categoryKey,
        categoryTitle,
        subcategoryKey,
        subcategoryTitle,
        severity,
        title,
        description,
        location,
        media,
        postedAt,
        visibilityStatus,
        isAnonymous,
        updates,
        zoneId,
        lastUpdatedAt,
        status,
        reportReasons,
        reportCount,
        isBlocked,
        metadata,
      ];

  static final empty = IncidentEntity(
    incidentId: '',
    userId: '',
    categoryKey: 'other',
    categoryTitle: 'Other',
    title: '',
    description: '',
    location: LocationEntity.empty,
    postedAt: DateTime.now(),
  );

  bool get isEmpty => this == IncidentEntity.empty;
  bool get isNotEmpty => this != IncidentEntity.empty;

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'incidentId': incidentId,
      'userId': userId,
      'categoryKey': categoryKey,
      'categoryTitle': categoryTitle,
      'subcategoryKey': subcategoryKey,
      'subcategoryTitle': subcategoryTitle,
      'severity': severity,
      'title': title,
      'description': description,
      'location': location.toJson(),
      'media': media.map((m) => m.toJson()).toList(),
      'postedAt': postedAt.toIso8601String(),
      'visibilityStatus': visibilityStatus.name,
      'isAnonymous': isAnonymous,
      'updates': updates.map((u) => u.toJson()).toList(),
      'zoneId': zoneId,
      'lastUpdatedAt': lastUpdatedAt?.toIso8601String(),
      'status': status.name,
      'reportReasons': reportReasons.map((r) => r.name).toList(),
      'reportCount': reportCount,
      'isBlocked': isBlocked,
      'metadata': metadata,
    };
  }

  /// Create from JSON for deserialization
  factory IncidentEntity.fromJson(Map<String, dynamic> json) {
    return IncidentEntity(
      incidentId: json['incidentId'] as String,
      userId: json['userId'] as String,
      categoryKey: json['categoryKey'] as String,
      categoryTitle: json['categoryTitle'] as String,
      subcategoryKey: json['subcategoryKey'] as String?,
      subcategoryTitle: json['subcategoryTitle'] as String?,
      severity: json['severity'] as String?,
      title: json['title'] as String,
      description: json['description'] as String,
      location: LocationEntity.fromJson(json['location'] as Map<String, dynamic>),
      media: (json['media'] as List<dynamic>?)
          ?.map((m) => MediaEntity.fromJson(m as Map<String, dynamic>))
          .toList() ?? [],
      postedAt: DateTime.parse(json['postedAt'] as String),
      visibilityStatus: IncidentVisibilityStatus.values.firstWhere(
        (e) => e.name == json['visibilityStatus'],
        orElse: () => IncidentVisibilityStatus.visibleToSelf,
      ),
      isAnonymous: json['isAnonymous'] as bool? ?? false,
      updates: (json['updates'] as List<dynamic>?)
          ?.map((u) => IncidentUpdateEntity.fromJson(u as Map<String, dynamic>))
          .toList() ?? [],
      zoneId: json['zoneId'] as String?,
      lastUpdatedAt: json['lastUpdatedAt'] != null
          ? DateTime.parse(json['lastUpdatedAt'] as String)
          : null,
      status: IncidentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => IncidentStatus.active,
      ),
      reportReasons: (json['reportReasons'] as List<dynamic>?)
          ?.map((r) => ReportReason.values.firstWhere((e) => e.name == r))
          .toList() ?? [],
      reportCount: json['reportCount'] as int? ?? 0,
      isBlocked: json['isBlocked'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
