import 'package:equatable/equatable.dart';
import 'incident_enums.dart';

class MediaEntity extends Equatable {
  final String mediaId;
  final MediaType type;
  final String url;
  final DateTime uploadedAt;
  final String? fileName;
  final int? fileSizeBytes;
  final String? thumbnailUrl;
  final Map<String, dynamic>? metadata;

  const MediaEntity({
    required this.mediaId,
    required this.type,
    required this.url,
    required this.uploadedAt,
    this.fileName,
    this.fileSizeBytes,
    this.thumbnailUrl,
    this.metadata,
  });

  /// Get file extension from fileName or url
  String? get fileExtension {
    final name = fileName ?? url;
    if (name.isEmpty) return null;
    
    final lastDot = name.lastIndexOf('.');
    if (lastDot == -1 || lastDot == name.length - 1) return null;
    
    return name.substring(lastDot + 1).toLowerCase();
  }

  /// Check if the file extension is valid for the media type
  bool get hasValidExtension {
    final extension = fileExtension;
    if (extension == null) return false;
    
    return type.allowedExtensions.contains(extension);
  }

  /// Get human-readable file size
  String get fileSizeFormatted {
    if (fileSizeBytes == null) return 'Unknown size';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Check if media is an image
  bool get isImage => type == MediaType.photo;

  /// Check if media is a video
  bool get isVideo => type == MediaType.video;

  /// Check if media is audio
  bool get isAudio => type == MediaType.audio;

  /// Check if media has a thumbnail
  bool get hasThumbnail => thumbnailUrl != null && thumbnailUrl!.isNotEmpty;

  /// Get display URL (thumbnail for videos, original for images)
  String get displayUrl {
    if (isVideo && hasThumbnail) return thumbnailUrl!;
    return url;
  }

  /// Check if media is valid
  bool get isValid {
    return mediaId.isNotEmpty && 
           url.isNotEmpty && 
           hasValidExtension;
  }

  MediaEntity copyWith({
    String? mediaId,
    MediaType? type,
    String? url,
    DateTime? uploadedAt,
    String? fileName,
    int? fileSizeBytes,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  }) {
    return MediaEntity(
      mediaId: mediaId ?? this.mediaId,
      type: type ?? this.type,
      url: url ?? this.url,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        mediaId,
        type,
        url,
        uploadedAt,
        fileName,
        fileSizeBytes,
        thumbnailUrl,
        metadata,
      ];

  static final empty = MediaEntity(
    mediaId: '',
    type: MediaType.photo,
    url: '',
    uploadedAt: DateTime.now(),
  );

  bool get isEmpty => this == MediaEntity.empty;
  bool get isNotEmpty => this != MediaEntity.empty;

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'mediaId': mediaId,
      'type': type.name,
      'url': url,
      'uploadedAt': uploadedAt.toIso8601String(),
      'fileName': fileName,
      'fileSizeBytes': fileSizeBytes,
      'thumbnailUrl': thumbnailUrl,
      'metadata': metadata,
    };
  }

  /// Create from JSON for deserialization
  factory MediaEntity.fromJson(Map<String, dynamic> json) {
    return MediaEntity(
      mediaId: json['mediaId'] as String,
      type: MediaType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MediaType.photo,
      ),
      url: json['url'] as String,
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      fileName: json['fileName'] as String?,
      fileSizeBytes: json['fileSizeBytes'] as int?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
