import 'package:equatable/equatable.dart';
import 'incident_subcategory_entity.dart';

/// Entity representing an incident category
class IncidentCategoryEntity extends Equatable {
  final String id;
  final String key; // e.g., 'crime', 'fire', etc.
  final String title;
  final String description;
  final String iconName;
  final String color;
  final String? imageUrl; // Optional image URL for the category
  final bool isActive;
  final int sortOrder;
  final List<IncidentSubcategoryEntity> subcategories; // List of subcategories
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const IncidentCategoryEntity({
    required this.id,
    required this.key,
    required this.title,
    required this.description,
    required this.iconName,
    required this.color,
    this.imageUrl,
    this.isActive = true,
    this.sortOrder = 0,
    this.subcategories = const [],
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        key,
        title,
        description,
        iconName,
        color,
        imageUrl,
        isActive,
        sortOrder,
        subcategories,
        createdAt,
        updatedAt,
        metadata,
      ];

  IncidentCategoryEntity copyWith({
    String? id,
    String? key,
    String? title,
    String? description,
    String? iconName,
    String? color,
    String? imageUrl,
    bool? isActive,
    int? sortOrder,
    List<IncidentSubcategoryEntity>? subcategories,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentCategoryEntity(
      id: id ?? this.id,
      key: key ?? this.key,
      title: title ?? this.title,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      subcategories: subcategories ?? this.subcategories,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

}
