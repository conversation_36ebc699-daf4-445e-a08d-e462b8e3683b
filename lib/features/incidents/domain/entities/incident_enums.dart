/// Enums for incident reporting system
enum IncidentVisibilityStatus { visibleToSelf, visibleToCommunity }

/// Detailed validation status for better user feedback
enum UserValidationStatus {
  notValidated, // User hasn't completed ID validation
  validatedButOutOfRange, // User is validated but not within 600m of validated zones
  validatedAndInRange, // User is validated and within range of validated zones
}

enum MediaType { photo, video, audio }

enum IncidentUpdateType { textUpdate, link, statusChange, communityObservation }

enum IncidentStatus {
  active,
  blocked,
  reported,
  underReview,
  resolved,
  archived,
}

enum ReportReason {
  inappropriate,
  spam,
  falseInformation,
  harassment,
  violence,
  other,
}

extension MediaTypeExtension on MediaType {
  String get displayName {
    switch (this) {
      case MediaType.photo:
        return 'Photo';
      case MediaType.video:
        return 'Video';
      case MediaType.audio:
        return 'Audio';
    }
  }

  List<String> get allowedExtensions {
    switch (this) {
      case MediaType.photo:
        return ['jpg', 'jpeg', 'png', 'webp'];
      case MediaType.video:
        return ['mp4', 'mov', 'avi', 'mkv'];
      case MediaType.audio:
        return ['mp3', 'wav', 'aac', 'm4a'];
    }
  }
}

extension IncidentUpdateTypeExtension on IncidentUpdateType {
  String get displayName {
    switch (this) {
      case IncidentUpdateType.textUpdate:
        return 'Text Update';
      case IncidentUpdateType.link:
        return 'Link';
      case IncidentUpdateType.statusChange:
        return 'Status Change';
      case IncidentUpdateType.communityObservation:
        return 'Community Observation';
    }
  }
}

extension UserValidationStatusExtension on UserValidationStatus {
  String get displayName {
    switch (this) {
      case UserValidationStatus.notValidated:
        return 'Not Validated';
      case UserValidationStatus.validatedButOutOfRange:
        return 'Validated (Out of Range)';
      case UserValidationStatus.validatedAndInRange:
        return 'Validated (In Range)';
    }
  }

  String get description {
    switch (this) {
      case UserValidationStatus.notValidated:
        return 'Complete identity and zone validation to make public reports';
      case UserValidationStatus.validatedButOutOfRange:
        return 'You are validated, but need to be within 600m of your validated zone to make public reports';
      case UserValidationStatus.validatedAndInRange:
        return 'You can make public reports visible to the community';
    }
  }

  String get actionMessage {
    switch (this) {
      case UserValidationStatus.notValidated:
        return 'Complete validation to make public reports';
      case UserValidationStatus.validatedButOutOfRange:
        return 'Move closer to your validated zone or create a new zone here';
      case UserValidationStatus.validatedAndInRange:
        return 'Your reports will be visible to the community';
    }
  }
}

extension IncidentStatusExtension on IncidentStatus {
  String get displayName {
    switch (this) {
      case IncidentStatus.active:
        return 'Active';
      case IncidentStatus.blocked:
        return 'Blocked';
      case IncidentStatus.reported:
        return 'Reported';
      case IncidentStatus.underReview:
        return 'Under Review';
      case IncidentStatus.resolved:
        return 'Resolved';
      case IncidentStatus.archived:
        return 'Archived';
    }
  }

  String get description {
    switch (this) {
      case IncidentStatus.active:
        return 'Incident is active and visible';
      case IncidentStatus.blocked:
        return 'Incident has been blocked by moderators';
      case IncidentStatus.reported:
        return 'Incident has been reported by community';
      case IncidentStatus.underReview:
        return 'Incident is being reviewed by moderators';
      case IncidentStatus.resolved:
        return 'Incident has been resolved';
      case IncidentStatus.archived:
        return 'Incident has been archived';
    }
  }
}

extension ReportReasonExtension on ReportReason {
  String get displayName {
    switch (this) {
      case ReportReason.inappropriate:
        return 'Inappropriate Content';
      case ReportReason.spam:
        return 'Spam';
      case ReportReason.falseInformation:
        return 'False Information';
      case ReportReason.harassment:
        return 'Harassment';
      case ReportReason.violence:
        return 'Violence or Threats';
      case ReportReason.other:
        return 'Other';
    }
  }
}
