import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/entities/user_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';
import 'package:respublicaseguridad/features/identity_validation/domain/repositories/identity_verification_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';

/// Service to determine incident visibility based on user verification status
class IncidentVisibilityService {
  final IdentityVerificationRepository _identityRepository;
  final ZoneRepository _zoneRepository;

  IncidentVisibilityService({
    required IdentityVerificationRepository identityRepository,
    required ZoneRepository zoneRepository,
  }) : _identityRepository = identityRepository,
       _zoneRepository = zoneRepository;

  /// Determines the visibility status for a new incident based on user's verification status
  /// and their current location (presence-based validation)
  Future<Either<Failure, IncidentVisibilityStatus>>
  determineIncidentVisibility({
    required String userId,
    required LocationEntity incidentLocation,
  }) async {
    try {
      // Get user's identity verification status
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      final hasValidatedIdentity =
          user.validationStatus == ValidationStatus.validated;

      if (!hasValidatedIdentity) {
        return const Right(IncidentVisibilityStatus.visibleToSelf);
      }

      // Get user's zones to check if they have a validated zone at the incident location
      final zonesResult = await _zoneRepository.getUserZones(userId);
      if (zonesResult.isLeft()) {
        return Left(zonesResult.fold((l) => l, (r) => throw Exception()));
      }

      final userZones = zonesResult.fold((l) => throw Exception(), (r) => r);

      // Check if user has a validated zone that covers the incident location
      final hasValidatedZoneAtLocation = userZones.any(
        (zone) =>
            zone.validationStatus == ZoneStatus.validated &&
            _isLocationInZone(incidentLocation, zone),
      );

      // Both validated identity AND validated zone at location are required
      // This ensures the user is actually present at the incident location
      if (hasValidatedIdentity && hasValidatedZoneAtLocation) {
        return const Right(IncidentVisibilityStatus.visibleToCommunity);
      } else {
        return const Right(IncidentVisibilityStatus.visibleToSelf);
      }
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to determine incident visibility: ${e.toString()}',
        ),
      );
    }
  }

  /// Checks if user can view a specific incident based on their verification status
  Future<Either<Failure, bool>> canUserViewIncident({
    required String userId,
    required IncidentEntity incident,
  }) async {
    try {
      // User can always view their own incidents
      if (incident.userId == userId) {
        return const Right(true);
      }

      // If incident is only visible to self, only the author can view it
      if (incident.visibilityStatus == IncidentVisibilityStatus.visibleToSelf) {
        return const Right(false);
      }

      // For community-visible incidents, check if user has validated identity
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);
      return Right(user.validationStatus != ValidationStatus.validated);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to check if user can view incident: ${e.toString()}',
        ),
      );
    }
  }

  /// Gets the list of zone IDs where the user can view community incidents
  Future<Either<Failure, List<String>>> getUserValidatedZoneIds(
    String userId,
  ) async {
    try {
      // Get user's identity verification status
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      // User must have validated identity to view community incidents
      if (user.validationStatus != ValidationStatus.validated) {
        return const Right([]);
      }

      // Get user's validated zones
      final zonesResult = await _zoneRepository.getUserZones(userId);
      if (zonesResult.isLeft()) {
        return Left(zonesResult.fold((l) => l, (r) => throw Exception()));
      }

      final userZones = zonesResult.fold((l) => throw Exception(), (r) => r);
      final validatedZoneIds =
          userZones
              .where((zone) => zone.validationStatus == ZoneStatus.validated)
              .map((zone) => zone.id)
              .toList();

      return Right(validatedZoneIds);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get user validated zone IDs: ${e.toString()}',
        ),
      );
    }
  }

  /// Calculates the current visibility percentage based on time decay (72 hours)
  double calculateVisibilityPercentage(DateTime postedAt) {
    final now = DateTime.now();
    final timeDifference = now.difference(postedAt);
    const visibilityWindow = Duration(hours: 72);

    if (timeDifference >= visibilityWindow) {
      return 0.0; // Completely faded out
    }

    // Linear decay from 100% to 0% over 72 hours
    final decayPercentage =
        timeDifference.inMilliseconds / visibilityWindow.inMilliseconds;
    return (1.0 - decayPercentage).clamp(0.0, 1.0);
  }

  /// Checks if incident is within the visibility window (72 hours)
  bool isIncidentWithinVisibilityWindow(DateTime postedAt) {
    final now = DateTime.now();
    const visibilityWindow = Duration(hours: 72);
    return now.difference(postedAt) < visibilityWindow;
  }

  /// Checks if user can post anonymously (requires validated identity)
  Future<Either<Failure, bool>> canUserPostAnonymously(String userId) async {
    try {
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);
      return Right(user.validationStatus == ValidationStatus.validated);
    } catch (e) {
      return Left(
        ServerFailure(
          message:
              'Failed to check if user can post anonymously: ${e.toString()}',
        ),
      );
    }
  }

  /// Gets visibility status display text for UI
  String getVisibilityStatusDisplayText(IncidentVisibilityStatus status) {
    switch (status) {
      case IncidentVisibilityStatus.visibleToSelf:
        return 'Visible only to you';
      case IncidentVisibilityStatus.visibleToCommunity:
        return 'Visible to community';
    }
  }

  /// Gets suggestions for improving incident visibility
  Future<Either<Failure, List<String>>> getVisibilitySuggestions({
    required String userId,
    required LocationEntity incidentLocation,
  }) async {
    try {
      final suggestions = <String>[];

      // Check user's identity verification status
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      if (user.validationStatus != ValidationStatus.validated) {
        suggestions.add(
          'Complete identity verification to make your reports visible to the community',
        );
        return Right(suggestions);
      }

      // Check zone validation status
      final zonesResult = await _zoneRepository.getUserZones(userId);
      if (zonesResult.isLeft()) {
        return Left(zonesResult.fold((l) => l, (r) => throw Exception()));
      }

      final userZones = zonesResult.fold((l) => throw Exception(), (r) => r);
      final hasValidatedZoneAtLocation = userZones.any(
        (zone) =>
            zone.validationStatus == ZoneStatus.validated &&
            _isLocationInZone(incidentLocation, zone),
      );

      if (!hasValidatedZoneAtLocation) {
        // Check if user has any validated zones at all
        final hasAnyValidatedZone = userZones.any(
          (zone) => zone.validationStatus == ZoneStatus.validated,
        );

        if (hasAnyValidatedZone) {
          // User is validated but not in range of their zones
          suggestions.add(
            'You are validated, but need to be within 600m of one of your validated zones to make public reports. Move closer to your validated zone or create a new zone at this location.',
          );
        } else {
          // User has no validated zones
          suggestions.add(
            'Create and validate a zone at this location to make your reports visible to the community',
          );
        }
      } else {
        suggestions.add(
          'Your reports are visible to the community because you have validated identity and a validated zone at this location',
        );
      }

      return Right(suggestions);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get visibility suggestions: ${e.toString()}',
        ),
      );
    }
  }

  /// Get detailed validation status for better user feedback
  Future<Either<Failure, UserValidationStatus>> getUserValidationStatus({
    required String userId,
    required LocationEntity currentLocation,
  }) async {
    try {
      // Check identity validation status
      final userResult = await _identityRepository
          .getIdentityVerificationStatus(userId);
      if (userResult.isLeft()) {
        return Left(userResult.fold((l) => l, (r) => throw Exception()));
      }

      final user = userResult.fold((l) => throw Exception(), (r) => r);

      // If identity not validated, return not validated
      if (user.validationStatus != ValidationStatus.validated) {
        return const Right(UserValidationStatus.notValidated);
      }

      // Check zone validation status
      final zonesResult = await _zoneRepository.getUserZones(userId);
      if (zonesResult.isLeft()) {
        return Left(zonesResult.fold((l) => l, (r) => throw Exception()));
      }

      final userZones = zonesResult.fold((l) => throw Exception(), (r) => r);
      final hasValidatedZoneAtLocation = userZones.any(
        (zone) =>
            zone.validationStatus == ZoneStatus.validated &&
            _isLocationInZone(currentLocation, zone),
      );

      if (hasValidatedZoneAtLocation) {
        return const Right(UserValidationStatus.validatedAndInRange);
      } else {
        // Check if user has any validated zones at all
        final hasAnyValidatedZone = userZones.any(
          (zone) => zone.validationStatus == ZoneStatus.validated,
        );

        if (hasAnyValidatedZone) {
          return const Right(UserValidationStatus.validatedButOutOfRange);
        } else {
          return const Right(UserValidationStatus.notValidated);
        }
      }
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get user validation status: ${e.toString()}',
        ),
      );
    }
  }

  /// Helper method to check if a location is within a zone's boundaries
  bool _isLocationInZone(LocationEntity location, ZoneEntity zone) {
    // Calculate distance between location and zone center
    final distance = location.distanceTo(
      LocationEntity(
        latitude: zone.centerCoordinates.latitude,
        longitude: zone.centerCoordinates.longitude,
        address: '',
      ),
    );

    // Check if location is within zone radius (600 meters)
    return distance <= zone.radiusInMeters;
  }
}
