import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_category_entity.dart';
import 'package:respublicaseguridad/features/incidents/domain/usecases/get_incident_categories_usecase.dart';

/// Service for managing incident categories
class IncidentCategoryService {
  final GetIncidentCategoriesUseCase _getCategoriesUseCase;

  IncidentCategoryService(this._getCategoriesUseCase);

  /// Get all available incident categories
  Future<Either<Failure, List<IncidentCategoryEntity>>> getCategories() async {
    return await _getCategoriesUseCase(const GetIncidentCategoriesParams());
  }

  /// Get category by key
  Future<Either<Failure, IncidentCategoryEntity?>> getCategoryByKey(String key) async {
    final result = await getCategories();
    return result.fold(
      (failure) => Left(failure),
      (categories) {
        final category = categories.where((c) => c.key == key).firstOrNull;
        return Right(category);
      },
    );
  }

  /// Get category display name by key
  String getCategoryDisplayName(String categoryKey) {
    switch (categoryKey.toLowerCase()) {
      case 'robo':
        return 'Robo';
      case 'violencia':
        return 'Violencia';
      case 'sospecha':
        return 'Actividad Sospechosa';
      case 'desamparo':
        return 'Desamparo';
      case 'infraestructura':
        return 'Infraestructura';
      case 'transito':
        return 'Tránsito';
      case 'drogas':
        return 'Drogas';
      case 'animales':
        return 'Animales';
      case 'convivencia':
        return 'Convivencia';
      default:
        return 'Otro';
    }
  }

  /// Get category color by key
  String getCategoryColor(String categoryKey) {
    switch (categoryKey.toLowerCase()) {
      case 'robo':
        return '#E53E3E'; // Red
      case 'violencia':
        return '#D53F8C'; // Pink
      case 'sospecha':
        return '#805AD5'; // Purple
      case 'desamparo':
        return '#3182CE'; // Blue
      case 'infraestructura':
        return '#319795'; // Teal
      case 'transito':
        return '#D69E2E'; // Yellow
      case 'drogas':
        return '#DD6B20'; // Orange
      case 'animales':
        return '#9F7AEA'; // Light Purple
      case 'convivencia':
        return '#48BB78'; // Green
      default:
        return '#718096'; // Gray
    }
  }
}
