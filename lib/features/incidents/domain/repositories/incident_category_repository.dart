import '../entities/incident_category_entity.dart';

/// Repository interface for incident categories
abstract class IncidentCategoryRepository {
  /// Fetch all active incident categories from the API
  Future<List<IncidentCategoryEntity>> getCategories();

  /// Fetch a specific category by its key
  Future<IncidentCategoryEntity?> getCategoryByKey(String key);

  /// Search categories by title or description
  Future<List<IncidentCategoryEntity>> searchCategories(String query);

  /// Cache categories locally for offline access
  Future<void> cacheCategories(List<IncidentCategoryEntity> categories);

  /// Get cached categories (for offline mode)
  Future<List<IncidentCategoryEntity>> getCachedCategories();

  /// Clear category cache
  Future<void> clearCategoryCache();

  /// Check if categories are cached and fresh
  Future<bool> areCategoriesCached();
}
