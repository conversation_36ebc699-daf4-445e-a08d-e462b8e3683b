import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';

/// Repository interface for incident management operations
abstract class IncidentRepository {
  /// Create a new incident
  Future<Either<Failure, IncidentEntity>> createIncident(IncidentEntity incident);

  /// Get incident by ID
  Future<Either<Failure, IncidentEntity>> getIncidentById(String incidentId);

  /// Update an existing incident
  Future<Either<Failure, IncidentEntity>> updateIncident(IncidentEntity incident);

  /// Delete an incident
  Future<Either<Failure, void>> deleteIncident(String incidentId);

  /// Get incidents for a specific user
  Future<Either<Failure, List<IncidentEntity>>> getUserIncidents(String userId);

  /// Get community incidents visible to user based on their zones and validation status
  Future<Either<Failure, List<IncidentEntity>>> getCommunityIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
    int? limit,
    DateTime? lastIncidentTimestamp,
  });

  /// Get incidents near a location within specified radius
  Future<Either<Failure, List<IncidentEntity>>> getIncidentsNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
    required String userId,
    String? categoryFilter,
  });

  /// Add an update to an incident
  Future<Either<Failure, IncidentUpdateEntity>> addIncidentUpdate({
    required String incidentId,
    required IncidentUpdateEntity update,
  });

  /// Get updates for an incident
  Future<Either<Failure, List<IncidentUpdateEntity>>> getIncidentUpdates(String incidentId);

  /// Report an incident
  Future<Either<Failure, IncidentEntity>> reportIncident({
    required String incidentId,
    required String reporterUserId,
    required ReportReason reason,
    String? additionalInfo,
  });

  /// Block an incident (admin action)
  Future<Either<Failure, IncidentEntity>> blockIncident({
    required String incidentId,
    required String adminUserId,
    String? reason,
  });

  /// Search incidents by text query
  Future<Either<Failure, List<IncidentEntity>>> searchIncidents({
    required String query,
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
  });

  /// Search user's own incidents by text query
  Future<Either<Failure, List<IncidentEntity>>> searchUserIncidents({
    required String userId,
    required String query,
    String? categoryFilter,
  });

  /// Get incident statistics for a user
  Future<Either<Failure, Map<String, dynamic>>> getUserIncidentStatistics(String userId);

  /// Stream of incident updates for real-time UI updates
  Stream<Either<Failure, List<IncidentEntity>>> watchUserIncidents(String userId);

  /// Stream of community incidents for real-time updates
  Stream<Either<Failure, List<IncidentEntity>>> watchCommunityIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
  });

  /// Stream of specific incident updates
  Stream<Either<Failure, IncidentEntity>> watchIncident(String incidentId);
}
