import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/media_entity.dart';
import '../../domain/entities/incident_enums.dart';

class MediaModel extends MediaEntity {
  const MediaModel({
    required super.mediaId,
    required super.type,
    required super.url,
    required super.uploadedAt,
    super.fileName,
    super.fileSizeBytes,
    super.thumbnailUrl,
    super.metadata,
  });

  factory MediaModel.fromEntity(MediaEntity entity) {
    return MediaModel(
      mediaId: entity.mediaId,
      type: entity.type,
      url: entity.url,
      uploadedAt: entity.uploadedAt,
      fileName: entity.fileName,
      fileSizeBytes: entity.fileSizeBytes,
      thumbnailUrl: entity.thumbnailUrl,
      metadata: entity.metadata,
    );
  }

  factory MediaModel.fromFirestore(Map<String, dynamic> data) {
    return MediaModel(
      mediaId: data['mediaId'] as String? ?? '',
      type: _parseMediaType(data['type']),
      url: data['url'] as String? ?? '',
      uploadedAt: (data['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      fileName: data['fileName'] as String?,
      fileSizeBytes: data['fileSizeBytes'] as int?,
      thumbnailUrl: data['thumbnailUrl'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory MediaModel.fromMap(Map<String, dynamic> map) {
    return MediaModel(
      mediaId: map['mediaId'] as String? ?? '',
      type: _parseMediaType(map['type']),
      url: map['url'] as String? ?? '',
      uploadedAt: map['uploadedAt'] is DateTime 
          ? map['uploadedAt'] as DateTime
          : DateTime.parse(map['uploadedAt'] as String? ?? DateTime.now().toIso8601String()),
      fileName: map['fileName'] as String?,
      fileSizeBytes: map['fileSizeBytes'] as int?,
      thumbnailUrl: map['thumbnailUrl'] as String?,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'mediaId': mediaId,
      'type': type.name,
      'url': url,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'fileName': fileName,
      'fileSizeBytes': fileSizeBytes,
      'thumbnailUrl': thumbnailUrl,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'mediaId': mediaId,
      'type': type.name,
      'url': url,
      'uploadedAt': uploadedAt.toIso8601String(),
      'fileName': fileName,
      'fileSizeBytes': fileSizeBytes,
      'thumbnailUrl': thumbnailUrl,
      'metadata': metadata,
    };
  }

  static MediaType _parseMediaType(dynamic value) {
    if (value == null) return MediaType.photo;
    
    if (value is MediaType) return value;
    
    final stringValue = value.toString().toLowerCase();
    switch (stringValue) {
      case 'photo':
        return MediaType.photo;
      case 'video':
        return MediaType.video;
      case 'audio':
        return MediaType.audio;
      default:
        return MediaType.photo;
    }
  }

  MediaModel copyWith({
    String? mediaId,
    MediaType? type,
    String? url,
    DateTime? uploadedAt,
    String? fileName,
    int? fileSizeBytes,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  }) {
    return MediaModel(
      mediaId: mediaId ?? this.mediaId,
      type: type ?? this.type,
      url: url ?? this.url,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  static final empty = MediaModel(
    mediaId: '',
    type: MediaType.photo,
    url: '',
    uploadedAt: DateTime.now(),
  );
}
