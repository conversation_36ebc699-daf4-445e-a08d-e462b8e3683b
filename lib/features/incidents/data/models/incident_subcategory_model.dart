import '../../domain/entities/incident_subcategory_entity.dart';

class IncidentSubcategoryModel extends IncidentSubcategoryEntity {
  const IncidentSubcategoryModel({
    required super.key,
    required super.title,
    required super.severities,
  });

  factory IncidentSubcategoryModel.fromEntity(IncidentSubcategoryEntity entity) {
    return IncidentSubcategoryModel(
      key: entity.key,
      title: entity.title,
      severities: entity.severities,
    );
  }

  factory IncidentSubcategoryModel.fromMap(Map<String, dynamic> map) {
    return IncidentSubcategoryModel(
      key: map['key'] as String? ?? '',
      title: map['title'] as String? ?? '',
      severities: (map['severities'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'title': title,
      'severities': severities,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      'key': key,
      'title': title,
      'severities': severities,
    };
  }

  IncidentSubcategoryModel copyWith({
    String? key,
    String? title,
    List<String>? severities,
  }) {
    return IncidentSubcategoryModel(
      key: key ?? this.key,
      title: title ?? this.title,
      severities: severities ?? this.severities,
    );
  }
}
