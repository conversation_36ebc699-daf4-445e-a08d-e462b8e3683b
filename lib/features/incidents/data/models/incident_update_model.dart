import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/incident_update_entity.dart';
import '../../domain/entities/incident_enums.dart';

class IncidentUpdateModel extends IncidentUpdateEntity {
  const IncidentUpdateModel({
    required super.updateId,
    required super.incidentId,
    required super.content,
    required super.timestamp,
    required super.authorId,
    required super.type,
    super.authorDisplayName,
    super.isAnonymous,
    super.metadata,
  });

  factory IncidentUpdateModel.fromEntity(IncidentUpdateEntity entity) {
    return IncidentUpdateModel(
      updateId: entity.updateId,
      incidentId: entity.incidentId,
      content: entity.content,
      timestamp: entity.timestamp,
      authorId: entity.authorId,
      type: entity.type,
      authorDisplayName: entity.authorDisplayName,
      isAnonymous: entity.isAnonymous,
      metadata: entity.metadata,
    );
  }

  factory IncidentUpdateModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return IncidentUpdateModel(
        updateId: doc.id,
        incidentId: '',
        content: '',
        timestamp: DateTime.now(),
        authorId: '',
        type: IncidentUpdateType.textUpdate,
      );
    }

    return IncidentUpdateModel(
      updateId: doc.id,
      incidentId: data['incidentId'] as String? ?? '',
      content: data['content'] as String? ?? '',
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      authorId: data['authorId'] as String? ?? '',
      type: _parseIncidentUpdateType(data['type']),
      authorDisplayName: data['authorDisplayName'] as String?,
      isAnonymous: data['isAnonymous'] as bool? ?? false,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory IncidentUpdateModel.fromMap(Map<String, dynamic> map) {
    return IncidentUpdateModel(
      updateId: map['updateId'] as String? ?? '',
      incidentId: map['incidentId'] as String? ?? '',
      content: map['content'] as String? ?? '',
      timestamp: map['timestamp'] is DateTime
          ? map['timestamp'] as DateTime
          : map['timestamp'] is Timestamp
              ? (map['timestamp'] as Timestamp).toDate()
              : DateTime.parse(map['timestamp'] as String? ?? DateTime.now().toIso8601String()),
      authorId: map['authorId'] as String? ?? '',
      type: _parseIncidentUpdateType(map['type']),
      authorDisplayName: map['authorDisplayName'] as String?,
      isAnonymous: map['isAnonymous'] as bool? ?? false,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'updateId': updateId,
      'incidentId': incidentId,
      'content': content,
      'timestamp': Timestamp.fromDate(timestamp),
      'authorId': authorId,
      'type': type.name,
      'authorDisplayName': authorDisplayName,
      'isAnonymous': isAnonymous,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'updateId': updateId,
      'incidentId': incidentId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'authorId': authorId,
      'type': type.name,
      'authorDisplayName': authorDisplayName,
      'isAnonymous': isAnonymous,
      'metadata': metadata,
    };
  }

  static IncidentUpdateType _parseIncidentUpdateType(dynamic value) {
    if (value == null) return IncidentUpdateType.textUpdate;
    
    if (value is IncidentUpdateType) return value;
    
    final stringValue = value.toString().toLowerCase();
    switch (stringValue) {
      case 'textupdate':
        return IncidentUpdateType.textUpdate;
      case 'link':
        return IncidentUpdateType.link;
      case 'statuschange':
        return IncidentUpdateType.statusChange;
      case 'communityobservation':
        return IncidentUpdateType.communityObservation;
      default:
        return IncidentUpdateType.textUpdate;
    }
  }

  IncidentUpdateModel copyWith({
    String? updateId,
    String? incidentId,
    String? content,
    DateTime? timestamp,
    String? authorId,
    IncidentUpdateType? type,
    String? authorDisplayName,
    bool? isAnonymous,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentUpdateModel(
      updateId: updateId ?? this.updateId,
      incidentId: incidentId ?? this.incidentId,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      authorId: authorId ?? this.authorId,
      type: type ?? this.type,
      authorDisplayName: authorDisplayName ?? this.authorDisplayName,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      metadata: metadata ?? this.metadata,
    );
  }

  static final empty = IncidentUpdateModel(
    updateId: '',
    incidentId: '',
    content: '',
    timestamp: DateTime.now(),
    authorId: '',
    type: IncidentUpdateType.textUpdate,
  );
}
