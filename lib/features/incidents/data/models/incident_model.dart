import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/incident_entity.dart';
import '../../domain/entities/incident_enums.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/entities/media_entity.dart';
import '../../domain/entities/incident_update_entity.dart';
import 'location_model.dart';
import 'media_model.dart';
import 'incident_update_model.dart';

class IncidentModel extends IncidentEntity {
  const IncidentModel({
    required super.incidentId,
    required super.userId,
    required super.categoryKey,
    required super.categoryTitle,
    super.subcategoryKey,
    super.subcategoryTitle,
    super.severity,
    required super.title,
    required super.description,
    required super.location,
    super.media,
    required super.postedAt,
    super.visibilityStatus,
    super.isAnonymous,
    super.updates,
    super.zoneId,
    super.lastUpdatedAt,
    super.status,
    super.reportReasons,
    super.reportCount,
    super.isBlocked,
    super.metadata,
  });

  factory IncidentModel.fromEntity(IncidentEntity entity) {
    return IncidentModel(
      incidentId: entity.incidentId,
      userId: entity.userId,
      categoryKey: entity.categoryKey,
      categoryTitle: entity.categoryTitle,
      subcategoryKey: entity.subcategoryKey,
      subcategoryTitle: entity.subcategoryTitle,
      severity: entity.severity,
      title: entity.title,
      description: entity.description,
      location: entity.location,
      media: entity.media,
      postedAt: entity.postedAt,
      visibilityStatus: entity.visibilityStatus,
      isAnonymous: entity.isAnonymous,
      updates: entity.updates,
      zoneId: entity.zoneId,
      lastUpdatedAt: entity.lastUpdatedAt,
      metadata: entity.metadata,
    );
  }

  factory IncidentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      return IncidentModel(
        incidentId: doc.id,
        userId: '',
        categoryKey: 'other',
        categoryTitle: 'Other',
        title: '',
        description: '',
        location: LocationEntity.empty,
        postedAt: DateTime.now(),
      );
    }

    return IncidentModel(
      incidentId: doc.id,
      userId: data['userId'] as String? ?? '',
      categoryKey: data['categoryKey'] as String? ?? data['type'] as String? ?? 'other',
      categoryTitle: data['categoryTitle'] as String? ?? 'Other',
      subcategoryKey: data['subcategoryKey'] as String?,
      subcategoryTitle: data['subcategoryTitle'] as String?,
      severity: data['severity'] as String?,
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      location: LocationModel.fromFirestore(data['location'] as Map<String, dynamic>? ?? {}),
      media: _parseMediaList(data['media']),
      postedAt: (data['postedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      visibilityStatus: _parseVisibilityStatus(data['visibilityStatus']),
      isAnonymous: data['isAnonymous'] as bool? ?? false,
      updates: _parseUpdatesList(data['updates']),
      zoneId: data['zoneId'] as String?,
      lastUpdatedAt: (data['lastUpdatedAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory IncidentModel.fromMap(Map<String, dynamic> map) {
    return IncidentModel(
      incidentId: map['incidentId'] as String? ?? '',
      userId: map['userId'] as String? ?? '',
      categoryKey: map['categoryKey'] as String? ?? map['type'] as String? ?? 'other',
      categoryTitle: map['categoryTitle'] as String? ?? 'Other',
      subcategoryKey: map['subcategoryKey'] as String?,
      subcategoryTitle: map['subcategoryTitle'] as String?,
      severity: map['severity'] as String?,
      title: map['title'] as String? ?? '',
      description: map['description'] as String? ?? '',
      location: LocationModel.fromMap(map['location'] as Map<String, dynamic>? ?? {}),
      media: _parseMediaListFromMap(map['media']),
      postedAt: map['postedAt'] is DateTime
          ? map['postedAt'] as DateTime
          : DateTime.parse(map['postedAt'] as String? ?? DateTime.now().toIso8601String()),
      visibilityStatus: _parseVisibilityStatus(map['visibilityStatus']),
      isAnonymous: map['isAnonymous'] as bool? ?? false,
      updates: _parseUpdatesListFromMap(map['updates']),
      zoneId: map['zoneId'] as String?,
      lastUpdatedAt: map['lastUpdatedAt'] is DateTime
          ? map['lastUpdatedAt'] as DateTime?
          : (map['lastUpdatedAt'] as String?)?.isNotEmpty == true
              ? DateTime.parse(map['lastUpdatedAt'] as String)
              : null,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'categoryKey': categoryKey,
      'categoryTitle': categoryTitle,
      'subcategoryKey': subcategoryKey,
      'subcategoryTitle': subcategoryTitle,
      'severity': severity,
      'title': title,
      'description': description,
      'location': LocationModel.fromEntity(location).toFirestore(),
      'media': media.map((m) => MediaModel.fromEntity(m).toFirestore()).toList(),
      'postedAt': Timestamp.fromDate(postedAt),
      'visibilityStatus': visibilityStatus.name,
      'isAnonymous': isAnonymous,
      'updates': updates.map((u) => IncidentUpdateModel.fromEntity(u).toFirestore()).toList(),
      'zoneId': zoneId,
      'lastUpdatedAt': lastUpdatedAt != null ? Timestamp.fromDate(lastUpdatedAt!) : FieldValue.serverTimestamp(),
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'incidentId': incidentId,
      'userId': userId,
      'categoryKey': categoryKey,
      'categoryTitle': categoryTitle,
      'subcategoryKey': subcategoryKey,
      'subcategoryTitle': subcategoryTitle,
      'severity': severity,
      'title': title,
      'description': description,
      'location': LocationModel.fromEntity(location).toMap(),
      'media': media.map((m) => MediaModel.fromEntity(m).toMap()).toList(),
      'postedAt': postedAt.toIso8601String(),
      'visibilityStatus': visibilityStatus.name,
      'isAnonymous': isAnonymous,
      'updates': updates.map((u) => IncidentUpdateModel.fromEntity(u).toMap()).toList(),
      'zoneId': zoneId,
      'lastUpdatedAt': lastUpdatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  static IncidentVisibilityStatus _parseVisibilityStatus(dynamic value) {
    if (value == null) return IncidentVisibilityStatus.visibleToSelf;

    if (value is IncidentVisibilityStatus) return value;

    final stringValue = value.toString().toLowerCase();
    switch (stringValue) {
      case 'visibletoself':
        return IncidentVisibilityStatus.visibleToSelf;
      case 'visibletocommunity':
        return IncidentVisibilityStatus.visibleToCommunity;
      default:
        return IncidentVisibilityStatus.visibleToSelf;
    }
  }

  static List<MediaEntity> _parseMediaList(dynamic value) {
    if (value == null) return [];

    if (value is! List) return [];

    return value
        .map((item) => MediaModel.fromFirestore(item as Map<String, dynamic>))
        .cast<MediaEntity>()
        .toList();
  }

  static List<MediaEntity> _parseMediaListFromMap(dynamic value) {
    if (value == null) return [];

    if (value is! List) return [];

    return value
        .map((item) => MediaModel.fromMap(item as Map<String, dynamic>))
        .cast<MediaEntity>()
        .toList();
  }

  static List<IncidentUpdateEntity> _parseUpdatesList(dynamic value) {
    if (value == null) return [];

    if (value is! List) return [];

    return value
        .map((item) {
          final map = item as Map<String, dynamic>;
          // Convert Firestore Timestamp to DateTime if needed
          if (map['timestamp'] is Timestamp) {
            map['timestamp'] = (map['timestamp'] as Timestamp).toDate();
          }
          return IncidentUpdateModel.fromMap(map);
        })
        .cast<IncidentUpdateEntity>()
        .toList();
  }

  static List<IncidentUpdateEntity> _parseUpdatesListFromMap(dynamic value) {
    if (value == null) return [];

    if (value is! List) return [];

    return value
        .map((item) => IncidentUpdateModel.fromMap(item as Map<String, dynamic>))
        .cast<IncidentUpdateEntity>()
        .toList();
  }

  IncidentModel copyWith({
    String? incidentId,
    String? userId,
    String? categoryKey,
    String? categoryTitle,
    String? subcategoryKey,
    String? subcategoryTitle,
    String? severity,
    String? title,
    String? description,
    LocationEntity? location,
    List<MediaEntity>? media,
    DateTime? postedAt,
    IncidentVisibilityStatus? visibilityStatus,
    bool? isAnonymous,
    List<IncidentUpdateEntity>? updates,
    String? zoneId,
    DateTime? lastUpdatedAt,
    IncidentStatus? status,
    List<ReportReason>? reportReasons,
    int? reportCount,
    bool? isBlocked,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentModel(
      incidentId: incidentId ?? this.incidentId,
      userId: userId ?? this.userId,
      categoryKey: categoryKey ?? this.categoryKey,
      categoryTitle: categoryTitle ?? this.categoryTitle,
      subcategoryKey: subcategoryKey ?? this.subcategoryKey,
      subcategoryTitle: subcategoryTitle ?? this.subcategoryTitle,
      severity: severity ?? this.severity,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      media: media ?? this.media,
      postedAt: postedAt ?? this.postedAt,
      visibilityStatus: visibilityStatus ?? this.visibilityStatus,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      updates: updates ?? this.updates,
      zoneId: zoneId ?? this.zoneId,
      lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt,
      status: status ?? this.status,
      reportReasons: reportReasons ?? this.reportReasons,
      reportCount: reportCount ?? this.reportCount,
      isBlocked: isBlocked ?? this.isBlocked,
      metadata: metadata ?? this.metadata,
    );
  }

  static final empty = IncidentModel(
    incidentId: '',
    userId: '',
    categoryKey: 'other',
    categoryTitle: 'Other',
    title: '',
    description: '',
    location: LocationEntity.empty,
    postedAt: DateTime.now(),
  );
}
