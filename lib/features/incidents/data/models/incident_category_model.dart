import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/incident_category_entity.dart';
import '../../domain/entities/incident_subcategory_entity.dart';
import 'incident_subcategory_model.dart';

class IncidentCategoryModel extends IncidentCategoryEntity {
  const IncidentCategoryModel({
    required super.id,
    required super.key,
    required super.title,
    required super.description,
    required super.iconName,
    required super.color,
    super.imageUrl,
    super.isActive,
    super.sortOrder,
    super.subcategories,
    required super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory IncidentCategoryModel.fromEntity(IncidentCategoryEntity entity) {
    return IncidentCategoryModel(
      id: entity.id,
      key: entity.key,
      title: entity.title,
      description: entity.description,
      iconName: entity.iconName,
      color: entity.color,
      imageUrl: entity.imageUrl,
      isActive: entity.isActive,
      sortOrder: entity.sortOrder,
      subcategories: entity.subcategories,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      metadata: entity.metadata,
    );
  }

  factory IncidentCategoryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;

    if (data == null) {
      return IncidentCategoryModel(
        id: doc.id,
        key: '',
        title: '',
        description: '',
        iconName: 'more_horizontal',
        color: '#718096',
        createdAt: DateTime.now(),
      );
    }

    // Parse subcategories from metadata.subcategories
    final metadata = data['metadata'] as Map<String, dynamic>?;
    final subcategoriesData = metadata?['subcategories'] as List<dynamic>? ?? [];
    final subcategories = subcategoriesData
        .map((subcat) => IncidentSubcategoryModel.fromMap(subcat as Map<String, dynamic>))
        .toList();

    return IncidentCategoryModel(
      id: doc.id,
      key: data['key'] as String? ?? '',
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      iconName: data['iconName'] as String? ?? 'more_horizontal',
      color: data['color'] as String? ?? '#718096',
      imageUrl: data['imageUrl'] as String?,
      isActive: data['isActive'] as bool? ?? true,
      sortOrder: data['sortOrder'] as int? ?? 0,
      subcategories: subcategories,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  factory IncidentCategoryModel.fromMap(Map<String, dynamic> map) {
    // Parse subcategories from metadata.subcategories
    final metadata = map['metadata'] as Map<String, dynamic>?;
    final subcategoriesData = metadata?['subcategories'] as List<dynamic>? ?? [];
    final subcategories = subcategoriesData
        .map((subcat) => IncidentSubcategoryModel.fromMap(subcat as Map<String, dynamic>))
        .toList();

    return IncidentCategoryModel(
      id: map['id'] as String? ?? '',
      key: map['key'] as String? ?? '',
      title: map['title'] as String? ?? '',
      description: map['description'] as String? ?? '',
      iconName: map['iconName'] as String? ?? 'more_horizontal',
      color: map['color'] as String? ?? '#718096',
      imageUrl: map['imageUrl'] as String?,
      isActive: map['isActive'] as bool? ?? true,
      sortOrder: map['sortOrder'] as int? ?? 0,
      subcategories: subcategories,
      createdAt: map['createdAt'] is DateTime
          ? map['createdAt'] as DateTime
          : DateTime.parse(map['createdAt'] as String? ?? DateTime.now().toIso8601String()),
      updatedAt: map['updatedAt'] is DateTime
          ? map['updatedAt'] as DateTime?
          : (map['updatedAt'] as String?)?.isNotEmpty == true
              ? DateTime.parse(map['updatedAt'] as String)
              : null,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    // Create metadata with subcategories
    final metadataWithSubcategories = Map<String, dynamic>.from(metadata ?? {});
    metadataWithSubcategories['subcategories'] = subcategories
        .map((subcat) => IncidentSubcategoryModel.fromEntity(subcat).toFirestore())
        .toList();

    return {
      'key': key,
      'title': title,
      'description': description,
      'iconName': iconName,
      'color': color,
      'imageUrl': imageUrl,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : FieldValue.serverTimestamp(),
      'metadata': metadataWithSubcategories,
    };
  }

  Map<String, dynamic> toMap() {
    // Create metadata with subcategories
    final metadataWithSubcategories = Map<String, dynamic>.from(metadata ?? {});
    metadataWithSubcategories['subcategories'] = subcategories
        .map((subcat) => IncidentSubcategoryModel.fromEntity(subcat).toMap())
        .toList();

    return {
      'id': id,
      'key': key,
      'title': title,
      'description': description,
      'iconName': iconName,
      'color': color,
      'imageUrl': imageUrl,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadataWithSubcategories,
    };
  }

  @override
  IncidentCategoryModel copyWith({
    String? id,
    String? key,
    String? title,
    String? description,
    String? iconName,
    String? color,
    String? imageUrl,
    bool? isActive,
    int? sortOrder,
    List<IncidentSubcategoryEntity>? subcategories,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return IncidentCategoryModel(
      id: id ?? this.id,
      key: key ?? this.key,
      title: title ?? this.title,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      subcategories: subcategories ?? this.subcategories,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

 
}
