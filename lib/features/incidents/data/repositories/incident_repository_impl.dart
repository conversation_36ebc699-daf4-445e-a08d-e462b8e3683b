import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/error/exceptions.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_firebase_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_marker_datasource.dart';
import 'package:respublicaseguridad/features/incidents/data/models/incident_models.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/incident_entities.dart';
import 'package:respublicaseguridad/features/incidents/domain/repositories/incident_repository.dart';

/// Implementation of IncidentRepository
class IncidentRepositoryImpl implements IncidentRepository {
  final IncidentFirebaseDataSource firebaseDataSource;
  final IncidentMarkerDataSource markerDataSource;

  IncidentRepositoryImpl({
    required this.firebaseDataSource,
    required this.markerDataSource,
  });

  @override
  Future<Either<Failure, IncidentEntity>> createIncident(IncidentEntity incident) async {
    try {
      final incidentModel = IncidentModel.fromEntity(incident);
      final result = await firebaseDataSource.createIncident(incidentModel);
      
      // Also save to Realtime DB for map markers
      await markerDataSource.saveIncidentMarker(result);
      
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IncidentEntity>> getIncidentById(String incidentId) async {
    try {
      final result = await firebaseDataSource.getIncidentById(incidentId);
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IncidentEntity>> updateIncident(IncidentEntity incident) async {
    try {
      final incidentModel = IncidentModel.fromEntity(incident);
      final result = await firebaseDataSource.updateIncident(incidentModel);
      
      // Also update in Realtime DB for map markers
      await markerDataSource.updateIncidentMarker(result);
      
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> deleteIncident(String incidentId) async {
    try {
      await firebaseDataSource.deleteIncident(incidentId);
      
      // Also delete from Realtime DB
      await markerDataSource.deleteIncidentMarker(incidentId);
      
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentEntity>>> getUserIncidents(String userId) async {
    try {
      final result = await firebaseDataSource.getUserIncidents(userId);
      return Right(result.cast<IncidentEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentEntity>>> getCommunityIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
    int? limit,
    DateTime? lastIncidentTimestamp,
  }) async {
    try {
      final result = await firebaseDataSource.getCommunityIncidents(
        zoneIds: userValidatedZoneIds,
        typeFilter: categoryFilter,
        limit: limit,
        lastDocument: null, // TODO: Implement proper pagination with DocumentSnapshot
      );
      return Right(result.cast<IncidentEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentEntity>>> getIncidentsNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
    required String userId,
    String? categoryFilter,
  }) async {
    try {
      final result = await firebaseDataSource.getIncidentsNearLocation(
        latitude: latitude,
        longitude: longitude,
        radiusInMeters: radiusInMeters,
        typeFilter: categoryFilter,
      );
      return Right(result.cast<IncidentEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IncidentUpdateEntity>> addIncidentUpdate({
    required String incidentId,
    required IncidentUpdateEntity update,
  }) async {
    try {
      final updateModel = IncidentUpdateModel.fromEntity(update);
      final result = await firebaseDataSource.addIncidentUpdate(
        incidentId: incidentId,
        update: updateModel,
      );
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentUpdateEntity>>> getIncidentUpdates(String incidentId) async {
    try {
      final result = await firebaseDataSource.getIncidentUpdates(incidentId);
      return Right(result.cast<IncidentUpdateEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IncidentEntity>> reportIncident({
    required String incidentId,
    required String reporterUserId,
    required ReportReason reason,
    String? additionalInfo,
  }) async {
    try {
      final result = await firebaseDataSource.reportIncident(
        incidentId: incidentId,
        reporterUserId: reporterUserId,
        reason: reason.name,
        additionalInfo: additionalInfo,
      );
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, IncidentEntity>> blockIncident({
    required String incidentId,
    required String adminUserId,
    String? reason,
  }) async {
    try {
      final result = await firebaseDataSource.blockIncident(
        incidentId: incidentId,
        adminUserId: adminUserId,
        reason: reason,
      );
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentEntity>>> searchIncidents({
    required String query,
    required String userId,
    required List<String> userValidatedZoneIds,
    String? categoryFilter,
  }) async {
    try {
      final result = await firebaseDataSource.searchIncidents(
        query: query,
        zoneIds: userValidatedZoneIds,
        typeFilter: categoryFilter,
      );
      return Right(result.cast<IncidentEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<IncidentEntity>>> searchUserIncidents({
    required String userId,
    required String query,
    String? categoryFilter,
  }) async {
    try {
      final result = await firebaseDataSource.searchUserIncidents(
        userId: userId,
        query: query,
        typeFilter: categoryFilter,
      );
      return Right(result.cast<IncidentEntity>());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserIncidentStatistics(String userId) async {
    try {
      final result = await firebaseDataSource.getUserIncidentStatistics(userId);
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Stream<Either<Failure, List<IncidentEntity>>> watchUserIncidents(String userId) {
    return firebaseDataSource.watchUserIncidents(userId).map(
      (incidents) => Right<Failure, List<IncidentEntity>>(incidents.cast<IncidentEntity>()),
    ).handleError(
      (error) => Left<Failure, List<IncidentEntity>>(_mapExceptionToFailure(error)),
    );
  }

  @override
  Stream<Either<Failure, List<IncidentEntity>>> watchCommunityIncidents({
    required String userId,
    required List<String> userValidatedZoneIds,
  }) {
    return firebaseDataSource.watchCommunityIncidents(
      zoneIds: userValidatedZoneIds,
    ).map(
      (incidents) => Right<Failure, List<IncidentEntity>>(incidents.cast<IncidentEntity>()),
    ).handleError(
      (error) => Left<Failure, List<IncidentEntity>>(_mapExceptionToFailure(error)),
    );
  }

  @override
  Stream<Either<Failure, IncidentEntity>> watchIncident(String incidentId) {
    return firebaseDataSource.watchIncident(incidentId).map(
      (incident) => Right<Failure, IncidentEntity>(incident),
    ).handleError(
      (error) => Left<Failure, IncidentEntity>(_mapExceptionToFailure(error)),
    );
  }

  /// Maps exceptions to appropriate failures
  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(message: exception.message);
    } else if (exception is NotFoundException) {
      return NotFoundFailure(exception.message);
    } else if (exception is ValidationException) {
      return ValidationFailure(exception.message);
    } else {
      return ServerFailure(message: 'An unexpected error occurred: ${exception.toString()}');
    }
  }
}
