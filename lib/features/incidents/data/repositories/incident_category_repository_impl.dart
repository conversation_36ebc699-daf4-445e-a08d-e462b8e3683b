import '../../domain/entities/incident_category_entity.dart';
import '../../domain/repositories/incident_category_repository.dart';
import '../datasources/incident_category_remote_datasource.dart';
import '../datasources/incident_category_local_datasource.dart';
import '../models/incident_category_model.dart';

class IncidentCategoryRepositoryImpl implements IncidentCategoryRepository {
  final IncidentCategoryRemoteDataSource remoteDataSource;
  final IncidentCategoryLocalDataSource localDataSource;

  const IncidentCategoryRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<List<IncidentCategoryEntity>> getCategories() async {
    try {
      // Check if we have fresh cached data first
      if (await localDataSource.areCategoriesCached()) {
        final cachedCategories = await localDataSource.getCachedCategories();
        if (cachedCategories.isNotEmpty) {
          return cachedCategories;
        }
      }

      // Fetch from remote if no fresh cache
      final categories = await remoteDataSource.getCategories();
      
      // Cache the fresh data
      await localDataSource.cacheCategories(categories);
      
      return categories;
    } catch (e) {
      // If remote fails, try to get any cached data (even if stale)
      final cachedCategories = await localDataSource.getCachedCategories();
      if (cachedCategories.isNotEmpty) {
        return cachedCategories;
      }
      
      rethrow;
    }
  }

  @override
  Future<IncidentCategoryEntity?> getCategoryByKey(String key) async {
    try {
      // Try remote first
      final category = await remoteDataSource.getCategoryByKey(key);
      return category;
    } catch (e) {
      // If remote fails, search in cached categories
      final cachedCategories = await localDataSource.getCachedCategories();
      try {
        return cachedCategories.firstWhere((category) => category.key == key);
      } catch (e) {
        return null;
      }
    }
  }

  @override
  Future<List<IncidentCategoryEntity>> searchCategories(String query) async {
    try {
      // Try remote search first
      final categories = await remoteDataSource.searchCategories(query);
      return categories;
    } catch (e) {
      // If remote fails, search in cached categories
      final cachedCategories = await localDataSource.getCachedCategories();
      
      if (query.isEmpty) {
        return cachedCategories;
      }

      final lowercaseQuery = query.toLowerCase();
      return cachedCategories.where((category) {
        return category.title.toLowerCase().contains(lowercaseQuery) ||
               category.description.toLowerCase().contains(lowercaseQuery) ||
               category.key.toLowerCase().contains(lowercaseQuery);
      }).toList();
    }
  }

  @override
  Future<void> cacheCategories(List<IncidentCategoryEntity> categories) async {
    final categoryModels = categories
        .map((category) => IncidentCategoryModel.fromEntity(category))
        .toList();

    await localDataSource.cacheCategories(categoryModels);
  }

  @override
  Future<List<IncidentCategoryEntity>> getCachedCategories() async {
    return await localDataSource.getCachedCategories();
  }

  @override
  Future<void> clearCategoryCache() async {
    await localDataSource.clearCache();
  }

  @override
  Future<bool> areCategoriesCached() async {
    return await localDataSource.areCategoriesCached();
  }
}
