import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/incident_category_model.dart';

/// Local data source for incident categories caching
abstract class IncidentCategoryLocalDataSource {
  Future<List<IncidentCategoryModel>> getCachedCategories();
  Future<void> cacheCategories(List<IncidentCategoryModel> categories);
  Future<void> clearCache();
  Future<bool> areCategoriesCached();
}

class IncidentCategorySharedPrefsDataSource implements IncidentCategoryLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const String _categoriesKey = 'cached_incident_categories';
  static const String _timestampKey = 'categories_cache_timestamp';
  static const Duration _cacheValidDuration = Duration(hours: 24); 

  const IncidentCategorySharedPrefsDataSource({
    required this.sharedPreferences,
  });

  @override
  Future<List<IncidentCategoryModel>> getCachedCategories() async {
    try {
      final categoriesJson = sharedPreferences.getString(_categoriesKey);
      if (categoriesJson == null) {
        return [];
      }

      final categoriesList = json.decode(categoriesJson) as List<dynamic>;
      return categoriesList
          .map((categoryMap) => IncidentCategoryModel.fromMap(categoryMap as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // If there's an error reading cache, return empty list
      return [];
    }
  }

  @override
  Future<void> cacheCategories(List<IncidentCategoryModel> categories) async {
    try {
      final categoriesList = categories.map((category) => category.toMap()).toList();
      final categoriesJson = json.encode(categoriesList);
      
      await sharedPreferences.setString(_categoriesKey, categoriesJson);
      await sharedPreferences.setInt(_timestampKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // Silently fail if caching fails
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      await sharedPreferences.remove(_categoriesKey);
      await sharedPreferences.remove(_timestampKey);
    } catch (e) {
      // Silently fail if clearing cache fails
    }
  }

  @override
  Future<bool> areCategoriesCached() async {
    try {
      final categoriesJson = sharedPreferences.getString(_categoriesKey);
      final timestamp = sharedPreferences.getInt(_timestampKey);
      
      if (categoriesJson == null || timestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      
      // Check if cache is still valid
      return now.difference(cacheTime) < _cacheValidDuration;
    } catch (e) {
      return false;
    }
  }
}
