import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/incident_category_model.dart';

/// Remote data source for incident categories
abstract class IncidentCategoryRemoteDataSource {
  Future<List<IncidentCategoryModel>> getCategories();
  Future<IncidentCategoryModel?> getCategoryByKey(String key);
  Future<List<IncidentCategoryModel>> searchCategories(String query);
}

class IncidentCategoryFirebaseDataSource implements IncidentCategoryRemoteDataSource {
  final FirebaseFirestore firestore;
  static const String _collection = 'incident_categories';

  const IncidentCategoryFirebaseDataSource({
    required this.firestore,
  });

  @override
  Future<List<IncidentCategoryModel>> getCategories() async {
    try {
      final querySnapshot = await firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .get();

      // Convert to models and sort by title as secondary sort on client side
      final categories = querySnapshot.docs
          .map((doc) => IncidentCategoryModel.fromFirestore(doc))
          .toList();

      // Sort by sortOrder first, then by title
      categories.sort((a, b) {
        final sortOrderComparison = a.sortOrder.compareTo(b.sortOrder);
        if (sortOrderComparison != 0) return sortOrderComparison;
        return a.title.compareTo(b.title);
      });

      return categories;
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  @override
  Future<IncidentCategoryModel?> getCategoryByKey(String key) async {
    try {
      final querySnapshot = await firestore
          .collection(_collection)
          .where('key', isEqualTo: key)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return null;
      }

      return IncidentCategoryModel.fromFirestore(querySnapshot.docs.first);
    } catch (e) {
      throw Exception('Failed to fetch category by key: $e');
    }
  }

  @override
  Future<List<IncidentCategoryModel>> searchCategories(String query) async {
    try {
      if (query.isEmpty) {
        return getCategories();
      }

      // Firestore doesn't support full-text search, so we'll fetch all and filter
      final allCategories = await getCategories();
      
      final lowercaseQuery = query.toLowerCase();
      return allCategories.where((category) {
        return category.title.toLowerCase().contains(lowercaseQuery) ||
               category.description.toLowerCase().contains(lowercaseQuery) ||
               category.key.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search categories: $e');
    }
  }
}
