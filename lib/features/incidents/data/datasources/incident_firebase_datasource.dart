import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/core/error/exceptions.dart';
import 'package:respublicaseguridad/features/incidents/data/models/incident_models.dart';
import 'package:respublicaseguridad/features/incidents/core/constants/incident_constants.dart';

/// Abstract data source for incident operations
abstract class IncidentFirebaseDataSource {
  /// Create a new incident
  Future<IncidentModel> createIncident(IncidentModel incident);

  /// Get incident by ID
  Future<IncidentModel> getIncidentById(String incidentId);

  /// Update an existing incident
  Future<IncidentModel> updateIncident(IncidentModel incident);

  /// Delete an incident
  Future<void> deleteIncident(String incidentId);

  /// Get incidents for a specific user
  Future<List<IncidentModel>> getUserIncidents(String userId);

  /// Get community incidents with filtering
  Future<List<IncidentModel>> getCommunityIncidents({
    required List<String> zoneIds,
    String? typeFilter,
    int? limit,
    DocumentSnapshot? lastDocument,
  });

  /// Get incidents near a location
  Future<List<IncidentModel>> getIncidentsNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
    String? typeFilter,
  });

  /// Add an update to an incident
  Future<IncidentUpdateModel> addIncidentUpdate({
    required String incidentId,
    required IncidentUpdateModel update,
  });

  /// Get updates for an incident
  Future<List<IncidentUpdateModel>> getIncidentUpdates(String incidentId);

  /// Report an incident
  Future<IncidentModel> reportIncident({
    required String incidentId,
    required String reporterUserId,
    required String reason,
    String? additionalInfo,
  });

  /// Block an incident
  Future<IncidentModel> blockIncident({
    required String incidentId,
    required String adminUserId,
    String? reason,
  });

  /// Search incidents by text query
  Future<List<IncidentModel>> searchIncidents({
    required String query,
    required List<String> zoneIds,
    String? typeFilter,
  });

  /// Search user's own incidents by text query
  Future<List<IncidentModel>> searchUserIncidents({
    required String userId,
    required String query,
    String? typeFilter,
  });

  /// Get incident statistics for a user
  Future<Map<String, dynamic>> getUserIncidentStatistics(String userId);

  /// Stream of incident updates for real-time UI updates
  Stream<List<IncidentModel>> watchUserIncidents(String userId);

  /// Stream of community incidents for real-time updates
  Stream<List<IncidentModel>> watchCommunityIncidents({
    required List<String> zoneIds,
  });

  /// Stream of specific incident updates
  Stream<IncidentModel> watchIncident(String incidentId);

  /// Check if user can post incident (rate limiting)
  Future<bool> canUserPostIncident(String userId);

  /// Get incidents that are within visibility window
  Future<List<IncidentModel>> getActiveIncidents({
    required List<String> zoneIds,
    String? typeFilter,
  });
}

/// Implementation of IncidentFirebaseDataSource
class IncidentFirebaseDataSourceImpl implements IncidentFirebaseDataSource {
  final FirebaseFirestore _firestore;

  IncidentFirebaseDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<IncidentModel> createIncident(IncidentModel incident) async {
    try {
      final docRef = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc();

      final incidentWithId = incident.copyWith(incidentId: docRef.id);

      await docRef.set(incidentWithId.toFirestore());

      final doc = await docRef.get();
      if (!doc.exists) {
        throw ServerException('Failed to create incident');
      }

      return IncidentModel.fromFirestore(doc);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to create incident: ${e.toString()}');
    }
  }

  @override
  Future<IncidentModel> getIncidentById(String incidentId) async {
    try {
      final doc = await _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incidentId)
          .get();

      if (!doc.exists) {
        throw NotFoundException('Incident with ID $incidentId not found');
      }

      return IncidentModel.fromFirestore(doc);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to get incident: ${e.toString()}');
    }
  }

  @override
  Future<IncidentModel> updateIncident(IncidentModel incident) async {
    try {
      final docRef = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incident.incidentId);

      await docRef.update({
        ...incident.toFirestore(),
        'lastUpdatedAt': FieldValue.serverTimestamp(),
      });

      return await getIncidentById(incident.incidentId);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to update incident: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteIncident(String incidentId) async {
    try {
      final batch = _firestore.batch();

      // Delete the incident
      final incidentRef = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incidentId);
      batch.delete(incidentRef);

      // Delete all related updates
      final updatesSnapshot = await _firestore
          .collection(IncidentConstants.incidentUpdatesCollection)
          .where(IncidentConstants.incidentIdField, isEqualTo: incidentId)
          .get();

      for (final doc in updatesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to delete incident: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> getUserIncidents(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.userIdField, isEqualTo: userId)
          .orderBy(IncidentConstants.postedAtField, descending: true)
          .limit(IncidentConstants.maxIncidentsPerQuery)
          .get();

      return querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get user incidents: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> getCommunityIncidents({
    required List<String> zoneIds,
    String? typeFilter,
    int? limit,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      if (zoneIds.isEmpty) {
        return [];
      }

      // Build base query with proper indexing strategy
      Query<Map<String, dynamic>> query = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.visibilityStatusField, isEqualTo: 'visibleToCommunity')
          .where(IncidentConstants.isBlockedField, isEqualTo: false)
          .where(IncidentConstants.zoneIdField, whereIn: zoneIds.take(10).toList()) // Firestore limit
          .orderBy(IncidentConstants.postedAtField, descending: true);

      // Add type filter if provided
      if (typeFilter != null) {
        query = query.where(IncidentConstants.typeField, isEqualTo: typeFilter);
      }

      // Add pagination cursor
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      // Apply limit
      query = query.limit(limit ?? IncidentConstants.maxIncidentsPerQuery);

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get community incidents: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> getIncidentsNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInMeters,
    String? typeFilter,
  }) async {
    try {
      // For now, get all community incidents and filter client-side
      // TODO: Implement proper geospatial queries with GeoFirestore
      Query<Map<String, dynamic>> query = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.visibilityStatusField, isEqualTo: 'visibleToCommunity')
          .where(IncidentConstants.isBlockedField, isEqualTo: false)
          .orderBy(IncidentConstants.postedAtField, descending: true)
          .limit(100); // Limit to avoid large data transfer

      if (typeFilter != null) {
        query = query.where(IncidentConstants.typeField, isEqualTo: typeFilter);
      }

      final querySnapshot = await query.get();

      // Client-side distance filtering (should be server-side in production)
      final incidents = querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .where((incident) {
            // TODO: Implement proper distance calculation
            // For now, return all incidents within a reasonable area
            return true;
          })
          .toList();

      return incidents;
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get incidents near location: ${e.toString()}');
    }
  }

  @override
  Future<IncidentUpdateModel> addIncidentUpdate({
    required String incidentId,
    required IncidentUpdateModel update,
  }) async {
    try {
      final batch = _firestore.batch();

      // Add the update to the incident_updates collection
      final updateRef = _firestore
          .collection(IncidentConstants.incidentUpdatesCollection)
          .doc();

      final updateWithId = update.copyWith(updateId: updateRef.id);
      batch.set(updateRef, updateWithId.toFirestore());

      // Update the incident's lastUpdatedAt timestamp
      final incidentRef = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incidentId);

      // Update the incident document with the new update in the updates array
      batch.update(incidentRef, {
        'lastUpdatedAt': FieldValue.serverTimestamp(),
        'updateCount': FieldValue.increment(1),
        'updates': FieldValue.arrayUnion([updateWithId.toFirestore()]),
      });

      await batch.commit();

      final doc = await updateRef.get();
      return IncidentUpdateModel.fromFirestore(doc);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to add incident update: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentUpdateModel>> getIncidentUpdates(String incidentId) async {
    try {
      final querySnapshot = await _firestore
          .collection(IncidentConstants.incidentUpdatesCollection)
          .where(IncidentConstants.incidentIdField, isEqualTo: incidentId)
          .orderBy('timestamp', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => IncidentUpdateModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get incident updates: ${e.toString()}');
    }
  }

  @override
  Future<IncidentModel> reportIncident({
    required String incidentId,
    required String reporterUserId,
    required String reason,
    String? additionalInfo,
  }) async {
    try {
      final batch = _firestore.batch();

      // Add report to reports collection
      final reportRef = _firestore
          .collection(IncidentConstants.incidentReportsCollection)
          .doc();

      batch.set(reportRef, {
        'incidentId': incidentId,
        'reporterUserId': reporterUserId,
        'reason': reason,
        'additionalInfo': additionalInfo,
        'reportedAt': FieldValue.serverTimestamp(),
      });

      // Update incident report count and reasons
      final incidentRef = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incidentId);

      batch.update(incidentRef, {
        'reportCount': FieldValue.increment(1),
        'reportReasons': FieldValue.arrayUnion([reason]),
        'lastReportedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      return await getIncidentById(incidentId);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to report incident: ${e.toString()}');
    }
  }

  @override
  Future<IncidentModel> blockIncident({
    required String incidentId,
    required String adminUserId,
    String? reason,
  }) async {
    try {
      await _firestore
          .collection(IncidentConstants.incidentsCollection)
          .doc(incidentId)
          .update({
            'isBlocked': true,
            'status': 'blocked',
            'blockedBy': adminUserId,
            'blockedAt': FieldValue.serverTimestamp(),
            'blockReason': reason,
          });

      return await getIncidentById(incidentId);
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to block incident: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> searchIncidents({
    required String query,
    required List<String> zoneIds,
    String? typeFilter,
  }) async {
    try {
      if (zoneIds.isEmpty) {
        return [];
      }

      Query<Map<String, dynamic>> firestoreQuery = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.visibilityStatusField, isEqualTo: 'visibleToCommunity')
          .where(IncidentConstants.isBlockedField, isEqualTo: false)
          .where(IncidentConstants.zoneIdField, whereIn: zoneIds.take(10).toList())
          .orderBy(IncidentConstants.postedAtField, descending: true)
          .limit(50);

      if (typeFilter != null) {
        firestoreQuery = firestoreQuery.where(IncidentConstants.typeField, isEqualTo: typeFilter);
      }

      final querySnapshot = await firestoreQuery.get();

      // Client-side text search (Firestore doesn't support full-text search natively)
      final incidents = querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .where((incident) {
            final lowerQuery = query.toLowerCase();
            return incident.title.toLowerCase().contains(lowerQuery) ||
                   incident.description.toLowerCase().contains(lowerQuery);
          })
          .toList();

      return incidents;
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to search incidents: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> searchUserIncidents({
    required String userId,
    required String query,
    String? typeFilter,
  }) async {
    try {
      Query<Map<String, dynamic>> firestoreQuery = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.userIdField, isEqualTo: userId)
          .orderBy(IncidentConstants.postedAtField, descending: true)
          .limit(50);

      if (typeFilter != null) {
        firestoreQuery = firestoreQuery.where(IncidentConstants.typeField, isEqualTo: typeFilter);
      }

      final querySnapshot = await firestoreQuery.get();

      // Client-side text search (Firestore doesn't support full-text search natively)
      final incidents = querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .where((incident) {
            final lowerQuery = query.toLowerCase();
            return incident.title.toLowerCase().contains(lowerQuery) ||
                   incident.description.toLowerCase().contains(lowerQuery);
          })
          .toList();

      return incidents;
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to search user incidents: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserIncidentStatistics(String userId) async {
    try {
      final userIncidents = await getUserIncidents(userId);

      final stats = {
        'totalIncidents': userIncidents.length,
        'activeIncidents': userIncidents.where((i) => i.status == 'active').length,
        'resolvedIncidents': userIncidents.where((i) => i.status == 'resolved').length,
        'communityVisibleIncidents': userIncidents.where((i) =>
          i.visibilityStatus == 'visibleToCommunity').length,
        'incidentsByType': <String, int>{},
      };

      // Count incidents by type
      final incidentsByType = stats['incidentsByType'] as Map<String, int>;
      for (final incident in userIncidents) {
        final typeName = incident.categoryTitle.toString();
        incidentsByType[typeName] = (incidentsByType[typeName] ?? 0) + 1;
      }

      return stats;
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get user incident statistics: ${e.toString()}');
    }
  }

  @override
  Stream<List<IncidentModel>> watchUserIncidents(String userId) {
    return _firestore
        .collection(IncidentConstants.incidentsCollection)
        .where(IncidentConstants.userIdField, isEqualTo: userId)
        .orderBy(IncidentConstants.postedAtField, descending: true)
        .limit(IncidentConstants.maxIncidentsPerQuery)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => IncidentModel.fromFirestore(doc))
            .toList());
  }

  @override
  Stream<List<IncidentModel>> watchCommunityIncidents({
    required List<String> zoneIds,
  }) {
    if (zoneIds.isEmpty) {
      return Stream.value([]);
    }

    Query<Map<String, dynamic>> query = _firestore
        .collection(IncidentConstants.incidentsCollection)
        .where(IncidentConstants.visibilityStatusField, isEqualTo: 'visibleToCommunity')
        .where(IncidentConstants.isBlockedField, isEqualTo: false)
        .where(IncidentConstants.zoneIdField, whereIn: zoneIds.take(10).toList())
        .orderBy(IncidentConstants.postedAtField, descending: true)
        .limit(IncidentConstants.maxIncidentsPerQuery);

    return query
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => IncidentModel.fromFirestore(doc))
            .toList());
  }

  @override
  Stream<IncidentModel> watchIncident(String incidentId) {
    return _firestore
        .collection(IncidentConstants.incidentsCollection)
        .doc(incidentId)
        .snapshots()
        .map((doc) {
          if (!doc.exists) {
            throw NotFoundException('Incident not found');
          }
          return IncidentModel.fromFirestore(doc);
        });
  }

  @override
  Future<bool> canUserPostIncident(String userId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      final querySnapshot = await _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.userIdField, isEqualTo: userId)
          .where(IncidentConstants.postedAtField, isGreaterThan: startOfDay)
          .get();

      return querySnapshot.docs.length < IncidentConstants.maxUserIncidentsPerDay;
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to check if user can post incident: ${e.toString()}');
    }
  }

  @override
  Future<List<IncidentModel>> getActiveIncidents({
    required List<String> zoneIds,
    String? typeFilter,
  }) async {
    try {
      if (zoneIds.isEmpty) {
        return [];
      }

      Query<Map<String, dynamic>> query = _firestore
          .collection(IncidentConstants.incidentsCollection)
          .where(IncidentConstants.visibilityStatusField, isEqualTo: 'visibleToCommunity')
          .where(IncidentConstants.isBlockedField, isEqualTo: false)
          .where(IncidentConstants.statusField, isEqualTo: 'active')
          .where(IncidentConstants.zoneIdField, whereIn: zoneIds.take(10).toList())
          .orderBy(IncidentConstants.postedAtField, descending: true)
          .limit(IncidentConstants.maxIncidentsPerQuery);

      if (typeFilter != null) {
        query = query.where(IncidentConstants.typeField, isEqualTo: typeFilter);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      throw ServerException('Firebase error: ${e.message}');
    } catch (e) {
      throw ServerException('Failed to get active incidents: ${e.toString()}');
    }
  }
}
