import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:respublicaseguridad/features/home/<USER>/models/incident_marker_model.dart';
import 'package:respublicaseguridad/features/incidents/data/models/incident_model.dart';
import 'package:respublicaseguridad/features/incidents/domain/entities/location_entity.dart';

/// Data source for incident markers in Firebase Realtime Database
abstract class IncidentMarkerDataSource {
  /// Save an incident marker to Realtime Database
  Future<void> saveIncidentMarker(IncidentModel incident);
  
  /// Delete an incident marker from Realtime Database
  Future<void> deleteIncidentMarker(String incidentId);
  
  /// Update an incident marker in Realtime Database
  Future<void> updateIncidentMarker(IncidentModel incident);
  
  /// Clean up expired markers (older than 72 hours)
  Future<void> cleanupExpiredMarkers();
}

/// Implementation of IncidentMarkerDataSource using Firebase Realtime Database
class IncidentMarkerDataSourceImpl implements IncidentMarkerDataSource {
  final FirebaseDatabase _database;
  
  // Database paths
  static const String _markersPath = 'incident_markers';
  static const String _zoneMarkersPath = 'zone_incident_markers';

  IncidentMarkerDataSourceImpl(this._database);
  
  @override
  Future<void> saveIncidentMarker(IncidentModel incident) async {
    try {
      // Create marker model from incident
      final marker = _createMarkerFromIncident(incident);
      
      // Add expiration timestamp (72 hours from now)
      final expiresAt = DateTime.now().add(const Duration(hours: 72)).millisecondsSinceEpoch;
      final markerData = {
        ...marker.toRealtimeDB(),
        'expiresAt': expiresAt,
        'visibilityStatus': incident.visibilityStatus.name,
      };
      
      // Write to main markers collection
      await _database.ref()
          .child(_markersPath)
          .child(incident.incidentId)
          .set(markerData);
      
      // Write to zone-specific collection if zoneId exists
      if (incident.zoneId != null) {
        await _database.ref()
            .child(_zoneMarkersPath)
            .child(incident.zoneId!)
            .child(incident.incidentId)
            .set(markerData);
      }
      
      debugPrint('✅ IncidentMarkerDataSource: Saved marker for incident ${incident.incidentId}');
    } catch (e) {
      debugPrint('❌ IncidentMarkerDataSource: Error saving marker: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteIncidentMarker(String incidentId) async {
    try {
      // Remove from main markers collection
      await _database.ref()
          .child(_markersPath)
          .child(incidentId)
          .remove();
      
      // Note: We can't easily remove from zone collection without knowing the zoneId
      // In a production app, you might want to store a mapping of incidentId -> zoneId
      
      debugPrint('✅ IncidentMarkerDataSource: Deleted marker for incident $incidentId');
    } catch (e) {
      debugPrint('❌ IncidentMarkerDataSource: Error deleting marker: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updateIncidentMarker(IncidentModel incident) async {
    try {
      // Create marker model from incident
      final marker = _createMarkerFromIncident(incident);
      
      // Prepare update data with visibility status
      final updateData = {
        ...marker.toRealtimeDB(),
        'visibilityStatus': incident.visibilityStatus.name,
      };

      // Update in main markers collection
      await _database.ref()
          .child(_markersPath)
          .child(incident.incidentId)
          .update(updateData);

      // Update in zone-specific collection if zoneId exists
      if (incident.zoneId != null) {
        await _database.ref()
            .child(_zoneMarkersPath)
            .child(incident.zoneId!)
            .child(incident.incidentId)
            .update(updateData);
      }
      
      debugPrint('✅ IncidentMarkerDataSource: Updated marker for incident ${incident.incidentId}');
    } catch (e) {
      debugPrint('❌ IncidentMarkerDataSource: Error updating marker: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> cleanupExpiredMarkers() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Query markers with expiresAt less than current time
      final snapshot = await _database.ref()
          .child(_markersPath)
          .orderByChild('expiresAt')
          .endBefore(now)
          .get();
      
      if (!snapshot.exists) {
        debugPrint('✅ IncidentMarkerDataSource: No expired markers to clean up');
        return;
      }
      
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final expiredCount = data.length;
      
      // Delete expired markers
      for (final entry in data.entries) {
        await _database.ref()
            .child(_markersPath)
            .child(entry.key)
            .remove();
      }
      
      debugPrint('✅ IncidentMarkerDataSource: Cleaned up $expiredCount expired markers');
    } catch (e) {
      debugPrint('❌ IncidentMarkerDataSource: Error cleaning up expired markers: $e');
      rethrow;
    }
  }
  
  /// Create a marker model from an incident
  IncidentMarkerModel _createMarkerFromIncident(IncidentModel incident) {
    // Map category and severity
    final categoryInfo = _mapCategoryInfo(incident.categoryKey);

    // Get the first image from incident media for the marker
    String? markerImageUrl;
    if (incident.media.isNotEmpty) {
      // Find the first image in the media list
      final firstImage = incident.media.firstWhere(
        (media) => media.isImage,
        orElse: () => incident.media.first, // Fallback to first media if no images
      );
      markerImageUrl = firstImage.displayUrl; // Use displayUrl which handles thumbnails for videos
    }

    return IncidentMarkerModel(
      incidentId: incident.incidentId,
      categoryKey: incident.categoryKey,
      categoryTitle: categoryInfo.title,
      subcategoryKey: incident.subcategoryKey,
      subcategoryTitle: incident.subcategoryTitle,
      severity: _mapSeverity(incident.severity),
      location: LocationEntity(
        latitude: incident.location.latitude,
        longitude: incident.location.longitude,
        address: incident.location.address,
      ),
      postedAt: incident.postedAt,
      isAnonymous: incident.isAnonymous,
      zoneId: incident.zoneId,
      currentVisibilityPercentage: 100.0, // Will be recalculated based on time
      isHighSeverity: incident.severity == 'high' || incident.severity == 'critical',
      iconName: categoryInfo.iconName,
      color: categoryInfo.color,
      userId: incident.userId,
      imageUrl: markerImageUrl, // Use incident's media instead of category imageUrl
      visibilityStatus: incident.visibilityStatus,
    );
  }
  
  /// Map incident type to category info
  _CategoryInfo _mapCategoryInfo(String type) {
    switch (type.toLowerCase()) {
      case 'theft':
      case 'robo':
        return _CategoryInfo(
          title: 'Robo',
          iconName: 'security',
          color: '#FF0000',
          imageUrl: null,
        );
      case 'violence':
      case 'violencia':
        return _CategoryInfo(
          title: 'Violencia',
          iconName: 'warning',
          color: '#FF4500',
          imageUrl: null,
        );
      case 'suspicious':
      case 'sospecha':
        return _CategoryInfo(
          title: 'Sospecha',
          iconName: 'visibility',
          color: '#FFA500',
          imageUrl: null,
        );
      case 'infrastructure':
      case 'infraestructura':
        return _CategoryInfo(
          title: 'Infraestructura',
          iconName: 'construction',
          color: '#4682B4',
          imageUrl: null,
        );
      case 'traffic':
      case 'transito':
      case 'tránsito':
        return _CategoryInfo(
          title: 'Tránsito',
          iconName: 'traffic',
          color: '#1E90FF',
          imageUrl: null,
        );
      case 'drugs':
      case 'drogas':
        return _CategoryInfo(
          title: 'Drogas',
          iconName: 'dangerous',
          color: '#8B008B',
          imageUrl: null,
        );
      case 'animals':
      case 'animales':
        return _CategoryInfo(
          title: 'Animales',
          iconName: 'pets',
          color: '#228B22',
          imageUrl: null,
        );
      case 'coexistence':
      case 'convivencia':
        return _CategoryInfo(
          title: 'Convivencia',
          iconName: 'people',
          color: '#4169E1',
          imageUrl: null,
        );
      default:
        return _CategoryInfo(
          title: 'Otro',
          iconName: 'report_problem',
          color: '#808080',
          imageUrl: null,
        );
    }
  }
  
  /// Map incident severity to marker severity
  String _mapSeverity(String? severity) {
    if (severity == null) return 'Leve';
    
    switch (severity.toLowerCase()) {
      case 'high':
      case 'critical':
      case 'grave':
        return 'Grave';
      case 'medium':
      case 'low':
      case 'leve':
      default:
        return 'Leve';
    }
  }
}

/// Helper class for category information
class _CategoryInfo {
  final String title;
  final String iconName;
  final String color;
  final String? imageUrl;

  _CategoryInfo({
    required this.title,
    required this.iconName,
    required this.color,
    this.imageUrl,
  });
}