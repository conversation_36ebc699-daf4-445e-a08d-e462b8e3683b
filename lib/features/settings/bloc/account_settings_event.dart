import 'package:equatable/equatable.dart';

abstract class AccountSettingsEvent extends Equatable {
  const AccountSettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadLinkedProviders extends AccountSettingsEvent {
  const LoadLinkedProviders();
}

class LinkGoogleAccount extends AccountSettingsEvent {
  const LinkGoogleAccount();
}

class LinkFacebookAccount extends AccountSettingsEvent {
  const LinkFacebookAccount();
}

class UnlinkProvider extends AccountSettingsEvent {
  final String providerId;

  const UnlinkProvider(this.providerId);

  @override
  List<Object?> get props => [providerId];
}
