import 'package:equatable/equatable.dart';

class AccountSettingsState extends Equatable {
  final bool isLoading;
  final List<String> linkedProviders;
  final String? errorMessage;
  final String? successMessage;

  const AccountSettingsState({
    this.isLoading = false,
    this.linkedProviders = const [],
    this.errorMessage,
    this.successMessage,
  });

  AccountSettingsState copyWith({
    bool? isLoading,
    List<String>? linkedProviders,
    String? errorMessage,
    String? successMessage,
  }) {
    return AccountSettingsState(
      isLoading: isLoading ?? this.isLoading,
      linkedProviders: linkedProviders ?? this.linkedProviders,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        linkedProviders,
        errorMessage,
        successMessage,
      ];
}
