import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/get_linked_providers_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/link_facebook_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/link_google_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/unlink_provider_use_case.dart';
import 'package:respublicaseguridad/features/settings/bloc/account_settings_event.dart';
import 'package:respublicaseguridad/features/settings/bloc/account_settings_state.dart';

@injectable
class AccountSettingsBloc extends Bloc<AccountSettingsEvent, AccountSettingsState> {
  final GetLinkedProvidersUseCase _getLinkedProvidersUseCase;
  final LinkGoogleUseCase _linkGoogleUseCase;
  final LinkFacebookUseCase _linkFacebookUseCase;
  final UnlinkProviderUseCase _unlinkProviderUseCase;

  AccountSettingsBloc(
    this._getLinkedProvidersUseCase,
    this._linkGoogleUseCase,
    this._linkFacebookUseCase,
    this._unlinkProviderUseCase,
  ) : super(const AccountSettingsState()) {
    on<LoadLinkedProviders>(_onLoadLinkedProviders);
    on<LinkGoogleAccount>(_onLinkGoogleAccount);
    on<LinkFacebookAccount>(_onLinkFacebookAccount);
    on<UnlinkProvider>(_onUnlinkProvider);
  }

  Future<void> _onLoadLinkedProviders(
    LoadLinkedProviders event,
    Emitter<AccountSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _getLinkedProvidersUseCase();
    
    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      (providers) => emit(state.copyWith(
        isLoading: false,
        linkedProviders: providers,
      )),
    );
  }

  Future<void> _onLinkGoogleAccount(
    LinkGoogleAccount event,
    Emitter<AccountSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _linkGoogleUseCase();
    
    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      (user) async {
        // Reload linked providers after successful linking
        final providersResult = await _getLinkedProvidersUseCase();
        providersResult.fold(
          (failure) => emit(state.copyWith(
            isLoading: false,
            errorMessage: failure.message,
          )),
          (providers) => emit(state.copyWith(
            isLoading: false,
            linkedProviders: providers,
            successMessage: 'Cuenta de Google vinculada exitosamente',
          )),
        );
      },
    );
  }

  Future<void> _onLinkFacebookAccount(
    LinkFacebookAccount event,
    Emitter<AccountSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _linkFacebookUseCase();
    
    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      (user) async {
        // Reload linked providers after successful linking
        final providersResult = await _getLinkedProvidersUseCase();
        providersResult.fold(
          (failure) => emit(state.copyWith(
            isLoading: false,
            errorMessage: failure.message,
          )),
          (providers) => emit(state.copyWith(
            isLoading: false,
            linkedProviders: providers,
            successMessage: 'Cuenta de Facebook vinculada exitosamente',
          )),
        );
      },
    );
  }

  Future<void> _onUnlinkProvider(
    UnlinkProvider event,
    Emitter<AccountSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _unlinkProviderUseCase(event.providerId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      (user) async {
        // Reload linked providers after successful unlinking
        final providersResult = await _getLinkedProvidersUseCase();
        providersResult.fold(
          (failure) => emit(state.copyWith(
            isLoading: false,
            errorMessage: failure.message,
          )),
          (providers) => emit(state.copyWith(
            isLoading: false,
            linkedProviders: providers,
            successMessage: 'Cuenta desvinculada exitosamente',
          )),
        );
      },
    );
  }
}
