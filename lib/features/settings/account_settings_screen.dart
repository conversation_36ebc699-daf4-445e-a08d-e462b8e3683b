import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/widgets/ios_dialog.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';

import 'package:respublicaseguridad/features/settings/bloc/account_settings_bloc.dart';
import 'package:respublicaseguridad/features/settings/bloc/account_settings_event.dart';
import 'package:respublicaseguridad/features/settings/bloc/account_settings_state.dart';

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<AccountSettingsBloc>()
        ..add(const LoadLinkedProviders()),
      child: const _AccountSettingsContent(),
    );
  }
}

class _AccountSettingsContent extends StatelessWidget {
  const _AccountSettingsContent();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return BlocListener<AccountSettingsBloc, AccountSettingsState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
        if (state.successMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.successMessage!),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Configuración de Cuenta'),
          centerTitle: true,
          leading: IconButton(
            onPressed: () => NavigationService.navigateTo(context, 'settings'),
            icon: const Icon(Icons.arrow_back),
          ),
        ),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, authState) {
            final user = authState.user;
            
            return BlocBuilder<AccountSettingsBloc, AccountSettingsState>(
              builder: (context, state) {
                if (state.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User Info Section
                      _buildUserInfoSection(context, user),
                      
                      SizedBox(height: 24.h),
                      
                      // Linked Accounts Section
                      _buildLinkedAccountsSection(context, state),
                      
                      SizedBox(height: 24.h),
                      
                      // Available Accounts to Link Section
                      _buildAvailableAccountsSection(context, state),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildUserInfoSection(BuildContext context, user) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: user.photoURL != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: Image.network(
                          user.photoURL!,
                          fit: BoxFit.cover,
                          width: 40.w,
                          height: 40.h,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.person,
                            size: 20.sp,
                            color: Colors.white,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 20.sp,
                        color: Colors.white,
                      ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayName ?? 'Usuario',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      user.email ?? 'No disponible',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),

            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLinkedAccountsSection(BuildContext context, AccountSettingsState state) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final linkedProviders = state.linkedProviders;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 28.w,
                height: 28.h,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.link,
                  size: 16.sp,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(width: 10.w),
              Text(
                context.l10n.linkedAccounts,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (linkedProviders.isEmpty)
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(4.r),
                border: Border.all(
                  color: theme.dividerColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 20.sp,
                    color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      context.l10n.noLinkedAccounts,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            Column(
              children: linkedProviders.asMap().entries.map((entry) {
                final index = entry.key;
                final provider = entry.value;
                return Padding(
                  padding: EdgeInsets.only(bottom: index < linkedProviders.length - 1 ? 12.h : 0),
                  child: _buildLinkedProviderTile(context, provider),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildLinkedProviderTile(BuildContext context, String providerId) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    IconData icon;
    String name;
    String description;
    Color color;

    switch (providerId) {
      case 'google.com':
        icon = FontAwesomeIcons.google;
        name = 'Google';
        description = context.l10n.googleAccountLinked;
        color = Colors.red;
        break;
      case 'facebook.com':
        icon = FontAwesomeIcons.facebook;
        name = 'Facebook';
        description = context.l10n.facebookAccountLinked;
        color = const Color(0xFF1877F2);
        break;
      case 'password':
        icon = Icons.email;
        name = context.l10n.emailPasswordMethod;
        description = context.l10n.primaryLoginMethod;
        color = theme.colorScheme.primary;
        break;
      default:
        icon = Icons.account_circle;
        name = providerId;
        description = 'Proveedor vinculado';
        color = theme.colorScheme.primary;
    }

    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: isDark ? theme.colorScheme.surface : theme.colorScheme.surface.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: theme.dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 44.w,
            height: 44.h,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(icon, color: color, size: 22.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          if (providerId != 'password') // Don't allow unlinking email/password
            Container(
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: TextButton(
                onPressed: () => _showUnlinkConfirmation(context, providerId, name),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  minimumSize: Size.zero,
                ),
                child: Text(
                  context.l10n.unlink,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            )
          else
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                context.l10n.primary,
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvailableAccountsSection(BuildContext context, AccountSettingsState state) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final linkedProviders = state.linkedProviders;

    final availableProviders = <Map<String, dynamic>>[];

    if (!linkedProviders.contains('google.com')) {
      availableProviders.add({
        'id': 'google.com',
        'name': 'Google',
        'description': context.l10n.quickLoginWithGoogle,
        'icon': FontAwesomeIcons.google,
        'color': Colors.red,
      });
    }

    if (!linkedProviders.contains('facebook.com')) {
      availableProviders.add({
        'id': 'facebook.com',
        'name': 'Facebook',
        'description': context.l10n.quickLoginWithFacebook,
        'icon': FontAwesomeIcons.facebook,
        'color': const Color(0xFF1877F2),
      });
    }

    if (availableProviders.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.add_link,
                  size: 18.sp,
                  color: Colors.green,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.l10n.linkAdditionalAccounts,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      context.l10n.addMoreLoginOptions,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Column(
            children: availableProviders.asMap().entries.map((entry) {
              final index = entry.key;
              final provider = entry.value;
              return Padding(
                padding: EdgeInsets.only(bottom: index < availableProviders.length - 1 ? 12.h : 0),
                child: _buildAvailableProviderTile(context, provider),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableProviderTile(BuildContext context, Map<String, dynamic> provider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _linkProvider(context, provider['id']),
        borderRadius: BorderRadius.circular(4.r),
        child: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: isDark ? theme.colorScheme.surface : theme.colorScheme.surface.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(
              color: provider['color'].withValues(alpha: 0.2),
              width: 1.5,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 44.w,
                height: 44.h,
                decoration: BoxDecoration(
                  color: provider['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(provider['icon'], color: provider['color'], size: 22.sp),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Vincular ${provider['name']}',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      provider['description'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: provider['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.add,
                  size: 18.sp,
                  color: provider['color'],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _linkProvider(BuildContext context, String providerId) {
    switch (providerId) {
      case 'google.com':
        context.read<AccountSettingsBloc>().add(const LinkGoogleAccount());
        break;
      case 'facebook.com':
        context.read<AccountSettingsBloc>().add(const LinkFacebookAccount());
        break;
    }
  }

  void _showUnlinkConfirmation(BuildContext context, String providerId, String providerName) {
    IosDialog.showAlertDialog(
      context: context,
      title: '${context.l10n.unlink} $providerName',
      message: context.l10n.unlinkConfirmationMessage(providerName),
      cancelText: context.l10n.cancel,
      confirmText: context.l10n.unlink,
      onCancel: () {},
      onConfirm: () {
        context.read<AccountSettingsBloc>().add(UnlinkProvider(providerId));
      },
    );
  }

}
