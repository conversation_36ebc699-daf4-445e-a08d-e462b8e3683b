import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import 'battery_indicator.dart';
import 'driving_status_indicator.dart';

class FamilyMemberCard extends StatefulWidget {
  final Map<String, dynamic> member;
  final VoidCallback onTap;
  final VoidCallback onCall;
  final VoidCallback onMessage;

  const FamilyMemberCard({
    super.key,
    required this.member,
    required this.onTap,
    required this.onCall,
    required this.onMessage,
  });

  @override
  State<FamilyMemberCard> createState() => _FamilyMemberCardState();
}

class _FamilyMemberCardState extends State<FamilyMemberCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // Start pulse animation if member is in emergency
    if (widget.member['isInEmergency'] == true) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isOnline = widget.member['isOnline'] as bool;
    final isInEmergency = widget.member['isInEmergency'] as bool? ?? false;
    final isDriving = widget.member['isDriving'] as bool? ?? false;

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isInEmergency ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: isInEmergency 
                    ? AppColors.error.withValues(alpha: 0.1)
                    : isDark 
                        ? const Color(0xFF2A2A2A) 
                        : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(16.r),
                border: isInEmergency 
                    ? Border.all(
                        color: AppColors.error.withValues(alpha: 0.3),
                        width: 1.w,
                      )
                    : null,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: isDark ? 0.2 : 0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 8.r,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Main member info row
                  Row(
                    children: [
                      // Avatar with status indicators
                      _buildMemberAvatar(isDark, isOnline, isInEmergency),
                      
                      SizedBox(width: 12.w),
                      
                      // Member details
                      Expanded(
                        child: _buildMemberDetails(context, isDark),
                      ),
                      
                      // Action buttons
                      _buildActionButtons(isDark),
                    ],
                  ),
                  
                  // Additional status indicators
                  if (isDriving || isInEmergency) ...[
                    SizedBox(height: 12.h),
                    _buildStatusIndicators(isDark, isDriving, isInEmergency),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMemberAvatar(bool isDark, bool isOnline, bool isInEmergency) {
    return Stack(
      children: [
        // Main avatar
        Container(
          width: 56.w,
          height: 56.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isInEmergency 
                  ? AppColors.error
                  : isOnline 
                      ? AppColors.success 
                      : AppColors.getTextSecondary(context),
              width: 2.w,
            ),
          ),
          child: CircleAvatar(
            radius: 26.r,
            backgroundImage: NetworkImage(widget.member['avatar']),
          ),
        ),
        
        // Online/Emergency status indicator
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 18.w,
            height: 18.h,
            decoration: BoxDecoration(
              color: isInEmergency 
                  ? AppColors.error
                  : isOnline 
                      ? AppColors.success 
                      : AppColors.getTextSecondary(context),
              shape: BoxShape.circle,
              border: Border.all(
                color: isDark ? const Color(0xFF2A2A2A) : Colors.grey.shade50,
                width: 2.w,
              ),
            ),
            child: isInEmergency 
                ? Icon(
                    FluentIcons.warning_24_filled,
                    size: 10.sp,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
      ],
    );
  }

  Widget _buildMemberDetails(BuildContext context, bool isDark) {
    final lastSeen = widget.member['lastSeen'] as String;
    final location = widget.member['currentLocation'] as String;
    final batteryLevel = widget.member['batteryLevel'] as int;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name and battery
        Row(
          children: [
            Expanded(
              child: Text(
                widget.member['name'],
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.getTextPrimary(context),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            BatteryIndicator(
              batteryLevel: batteryLevel,
              isCharging: widget.member['isCharging'] ?? false,
            ),
          ],
        ),
        
        SizedBox(height: 4.h),
        
        // Location
        Row(
          children: [
            Icon(
              FluentIcons.location_24_filled,
              size: 12.sp,
              color: AppColors.getTextSecondary(context),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Text(
                location,
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.getTextSecondary(context),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        
        SizedBox(height: 2.h),
        
        // Last seen
        Text(
          lastSeen,
          style: GoogleFonts.outfit(
            fontSize: 11.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.getTextSecondary(context).withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isDark) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Call button
        GestureDetector(
          onTap: widget.onCall,
          child: Container(
            width: 36.w,
            height: 36.h,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              FluentIcons.call_24_filled,
              size: 16.sp,
              color: AppColors.success,
            ),
          ),
        ),
        
        SizedBox(width: 8.w),
        
        // Message button
        GestureDetector(
          onTap: widget.onMessage,
          child: Container(
            width: 36.w,
            height: 36.h,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              FluentIcons.chat_24_filled,
              size: 16.sp,
              color: AppColors.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIndicators(bool isDark, bool isDriving, bool isInEmergency) {
    return Row(
      children: [
        if (isDriving) ...[
          DrivingStatusIndicator(
            speed: widget.member['currentSpeed'] ?? 0,
            speedLimit: widget.member['speedLimit'] ?? 0,
            isUsingPhone: widget.member['isUsingPhone'] ?? false,
          ),
        ],
        
        if (isInEmergency) ...[
          if (isDriving) SizedBox(width: 8.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FluentIcons.warning_24_filled,
                  size: 12.sp,
                  color: AppColors.error,
                ),
                SizedBox(width: 4.w),
                Text(
                  'EMERGENCY',
                  style: GoogleFonts.outfit(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
