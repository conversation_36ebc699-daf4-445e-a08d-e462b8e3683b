import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import 'member_emoji_avatar.dart';
import 'member_status_indicator.dart';
import 'member_share_button.dart';

class FamilyMapMarker extends StatefulWidget {
  final Map<String, dynamic> member;
  final VoidCallback? onTap;
  final VoidCallback? onShare;
  final bool isSelected;
  final bool showShareButton;

  const FamilyMapMarker({
    super.key,
    required this.member,
    this.onTap,
    this.onShare,
    this.isSelected = false,
    this.showShareButton = true,
  });

  @override
  State<FamilyMapMarker> createState() => _FamilyMapMarkerState();
}

class _FamilyMapMarkerState extends State<FamilyMapMarker>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _emergencyController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _emergencyAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAppropriateAnimation();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _emergencyController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _emergencyAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _emergencyController, curve: Curves.elasticOut),
    );
  }

  void _startAppropriateAnimation() {
    if (widget.member['isInEmergency'] == true) {
      _emergencyController.repeat(reverse: true);
    } else if (widget.isSelected) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FamilyMapMarker oldWidget) {
    super.didUpdateWidget(oldWidget);
    _handleAnimationChanges(oldWidget);
  }

  void _handleAnimationChanges(FamilyMapMarker oldWidget) {
    final wasInEmergency = oldWidget.member['isInEmergency'] == true;
    final isInEmergency = widget.member['isInEmergency'] == true;

    if (isInEmergency != wasInEmergency) {
      if (isInEmergency) {
        _pulseController.stop();
        _emergencyController.repeat(reverse: true);
      } else {
        _emergencyController.stop();
        if (widget.isSelected) {
          _pulseController.repeat(reverse: true);
        }
      }
    } else if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected && !isInEmergency) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _emergencyController.dispose();
    super.dispose();
  }

  Color _getStatusColor() {
    final isInEmergency = widget.member['isInEmergency'] as bool? ?? false;
    final isOnline = widget.member['isOnline'] as bool? ?? false;
    final batteryLevel = widget.member['batteryLevel'] as int? ?? 100;

    if (isInEmergency) return AppColors.error;
    if (!isOnline) return Colors.grey;
    if (batteryLevel <= 15) return AppColors.error;
    if (batteryLevel <= 30) return Colors.orange;
    return AppColors.success;
  }

  @override
  Widget build(BuildContext context) {
    final isInEmergency = widget.member['isInEmergency'] as bool? ?? false;

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: isInEmergency ? _emergencyAnimation : _pulseAnimation,
        builder: (context, child) {
          final scale = _getAnimationScale(isInEmergency);

          return Transform.scale(
            scale: scale,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [_buildMarkerContainer(), _buildPointerTriangle()],
            ),
          );
        },
      ),
    );
  }

  double _getAnimationScale(bool isInEmergency) {
    if (isInEmergency) return _emergencyAnimation.value;
    if (widget.isSelected) return _pulseAnimation.value;
    return 1.0;
  }

  Widget _buildMarkerContainer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      decoration: _buildMarkerDecoration(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildAvatarWithIndicators(),
          SizedBox(width: 8.w),
          _buildMemberInfo(),
          if (widget.showShareButton) ...[
            SizedBox(width: 8.w),
            MemberShareButton(
              member: widget.member,
              onShare: widget.onShare,
              isCompact: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMemberInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [_buildMemberName(), _buildMemberStatus()],
    );
  }

  Widget _buildMemberStatus() {
    final isDriving = widget.member['isDriving'] as bool? ?? false;
    final batteryLevel = widget.member['batteryLevel'] as int? ?? 100;
    final isCharging = widget.member['isCharging'] as bool? ?? false;

    String statusText = '';
    Color statusColor = AppColors.getTextSecondary(context);

    if (isDriving) {
      final speed = widget.member['currentSpeed'] as int? ?? 0;
      statusText = '🚗 ${speed}mph';
      statusColor = AppColors.primary;
    } else if (isCharging) {
      statusText = '⚡ Charging $batteryLevel%';
      statusColor = AppColors.success;
    } else if (batteryLevel <= 15) {
      statusText = '🔋 $batteryLevel%';
      statusColor = AppColors.error;
    } else {
      statusText = '📍 ${widget.member['currentLocation'] ?? 'Unknown'}';
    }

    return Text(
      statusText,
      style: GoogleFonts.outfit(
        fontSize: 10.sp,
        fontWeight: FontWeight.w500,
        color: statusColor,
      ),
    );
  }

  BoxDecoration _buildMarkerDecoration() {
    final isInEmergency = widget.member['isInEmergency'] as bool? ?? false;

    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(20.r),
      border: Border.all(color: _getStatusColor(), width: 2.w),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.2),
          offset: const Offset(0, 2),
          blurRadius: 8.r,
          spreadRadius: 0,
        ),
        if (isInEmergency)
          BoxShadow(
            color: AppColors.error.withValues(alpha: 0.3),
            offset: const Offset(0, 0),
            blurRadius: 12.r,
            spreadRadius: 2.r,
          ),
      ],
    );
  }

  Widget _buildAvatarWithIndicators() {
    return Stack(
      children: [
        MemberEmojiAvatar(
          member: widget.member,
          size: 40.w,
          borderColor: _getStatusColor(),
          borderWidth: 3.0,
        ),
        Positioned(
          top: -2.h,
          right: -2.w,
          child: MemberStatusIndicator(member: widget.member, size: 18.0),
        ),
      ],
    );
  }

  Widget _buildMemberName() {
    return Text(
      widget.member['name'],
      style: GoogleFonts.outfit(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildPointerTriangle() {
    return CustomPaint(
      size: Size(12.w, 6.h),
      painter: TrianglePainter(color: _getStatusColor()),
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  const TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, size.height);
    path.lineTo(0, 0);
    path.lineTo(size.width, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
