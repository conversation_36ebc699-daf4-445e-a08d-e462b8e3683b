import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../core/theme/app_colors.dart';

class IncidentsHeatmapCard extends StatelessWidget {
  final bool isDark;
  final List<Map<String, dynamic>> incidentData;

  const IncidentsHeatmapCard({
    super.key,
    required this.isDark,
    required this.incidentData,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 8.r,
                ),
              ],
      ),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  FluentIcons.map_24_filled,
                  size: 18.sp,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Incidents & Alerts Trends',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '3km radius • Last 24 hours',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getThreatLevelColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  '${incidentData.length} incidents',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getThreatLevelColor(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Compact heatmap visualization
          Container(
            height: 120.h, // Reduced from 200.h
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: theme.dividerColor.withValues(alpha: 0.2),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Stack(
                children: [
                  // Mini Google Map
                  GoogleMap(
                    initialCameraPosition: const CameraPosition(
                      target: LatLng(37.7749, -122.4194),
                      zoom: 13.0,
                    ),
                    markers: _buildIncidentMarkers(),
                    mapType: MapType.normal,
                    myLocationEnabled: false,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    scrollGesturesEnabled: false,
                    zoomGesturesEnabled: false,
                    tiltGesturesEnabled: false,
                    rotateGesturesEnabled: false,
                    mapToolbarEnabled: false,
                    compassEnabled: false,
                  ),

                  // Compact overlay with incident stats
                  Positioned(
                    top: 8.h,
                    right: 8.w,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '${incidentData.length} incidents',
                        style: GoogleFonts.outfit(
                          fontSize: 10.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 12.h), // Reduced spacing

          // Compact incident breakdown
          _buildIncidentBreakdown(context),
        ],
      ),
    );
  }

  // Helper methods
  Set<Marker> _buildIncidentMarkers() {
    return incidentData.map((incident) {
      return Marker(
        markerId: MarkerId('incident_${incident['lat']}_${incident['lng']}'),
        position: LatLng(incident['lat'], incident['lng']),
        icon: BitmapDescriptor.defaultMarkerWithHue(
          _getMarkerColor(incident['severity']),
        ),
        infoWindow: InfoWindow(
          title: incident['type'].toString().toUpperCase(),
          snippet: '${incident['severity']} • ${incident['time']}',
        ),
      );
    }).toSet();
  }

  double _getMarkerColor(String severity) {
    switch (severity) {
      case 'high':
        return BitmapDescriptor.hueRed;
      case 'medium':
        return BitmapDescriptor.hueOrange;
      case 'low':
        return BitmapDescriptor.hueYellow;
      default:
        return BitmapDescriptor.hueBlue;
    }
  }

  Color _getThreatLevelColor() {
    // Determine overall threat level based on incidents
    final highCount = incidentData.where((i) => i['severity'] == 'high').length;
    final mediumCount = incidentData.where((i) => i['severity'] == 'medium').length;

    if (highCount >= 2) {
      return AppColors.error;
    } else if (highCount >= 1 || mediumCount >= 2) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  Widget _buildIncidentBreakdown(BuildContext context) {
    // Count incidents by severity
    final Map<String, int> severityCounts = {};
    for (var incident in incidentData) {
      final severity = incident['severity'] as String;
      severityCounts[severity] = (severityCounts[severity] ?? 0) + 1;
    }

    return Row(
      children: [
        Expanded(
          child: _buildSeverityChip(
            context,
            'High',
            severityCounts['high'] ?? 0,
            AppColors.error,
          ),
        ),
        SizedBox(width: 6.w), // Reduced spacing
        Expanded(
          child: _buildSeverityChip(
            context,
            'Med',
            severityCounts['medium'] ?? 0,
            Colors.orange,
          ),
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: _buildSeverityChip(
            context,
            'Low',
            severityCounts['low'] ?? 0,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildSeverityChip(
    BuildContext context,
    String label,
    int count,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 8.w), // Reduced padding
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.w,
        ),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: theme.textTheme.titleSmall?.copyWith( // Smaller text
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 10.sp, // Smaller text
            ),
          ),
        ],
      ),
    );
  }
}
