import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import '../../../core/theme/app_colors.dart';

class MemberStatusIndicator extends StatelessWidget {
  final Map<String, dynamic> member;
  final double size;

  const MemberStatusIndicator({
    super.key,
    required this.member,
    this.size = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    final indicator = _getPrimaryStatusIndicator();
    if (indicator == null) return const SizedBox.shrink();

    return Container(
      width: size.w,
      height: size.h,
      decoration: BoxDecoration(
        color: indicator.backgroundColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 1.w,
        ),
      ),
      child: Icon(
        indicator.icon,
        size: (size * 0.5).sp,
        color: indicator.iconColor,
      ),
    );
  }

  StatusIndicatorData? _getPrimaryStatusIndicator() {
    final isDriving = member['isDriving'] as bool? ?? false;
    final isCharging = member['isCharging'] as bool? ?? false;
    final batteryLevel = member['batteryLevel'] as int? ?? 100;
    final isInEmergency = member['isInEmergency'] as bool? ?? false;

    // Priority order: Emergency > Driving > Low Battery > Charging
    if (isInEmergency) {
      return StatusIndicatorData(
        icon: FluentIcons.warning_24_filled,
        backgroundColor: AppColors.error,
        iconColor: Colors.white,
      );
    }

    if (isDriving) {
      return StatusIndicatorData(
        icon: FluentIcons.vehicle_car_24_filled,
        backgroundColor: AppColors.primary,
        iconColor: Colors.white,
      );
    }

    if (batteryLevel <= 15) {
      return StatusIndicatorData(
        icon: FluentIcons.battery_0_24_filled,
        backgroundColor: AppColors.error,
        iconColor: Colors.white,
      );
    }

    if (isCharging) {
      return StatusIndicatorData(
        icon: FluentIcons.battery_charge_24_filled,
        backgroundColor: AppColors.success,
        iconColor: Colors.white,
      );
    }

    return null; // No status indicator needed
  }
}

class StatusIndicatorData {
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;

  const StatusIndicatorData({
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
  });
}

class MultiStatusIndicator extends StatelessWidget {
  final Map<String, dynamic> member;
  final double spacing;

  const MultiStatusIndicator({
    super.key,
    required this.member,
    this.spacing = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    final indicators = _getAllStatusIndicators();
    if (indicators.isEmpty) return const SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: indicators
          .map((indicator) => Padding(
                padding: EdgeInsets.only(right: spacing.w),
                child: _buildIndicatorChip(indicator),
              ))
          .toList(),
    );
  }

  Widget _buildIndicatorChip(StatusIndicatorData indicator) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: indicator.backgroundColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            indicator.icon,
            size: 10.sp,
            color: indicator.backgroundColor,
          ),
          SizedBox(width: 2.w),
          Text(
            _getIndicatorText(indicator),
            style: TextStyle(
              fontSize: 8.sp,
              fontWeight: FontWeight.w600,
              color: indicator.backgroundColor,
            ),
          ),
        ],
      ),
    );
  }

  String _getIndicatorText(StatusIndicatorData indicator) {
    if (indicator.icon == FluentIcons.warning_24_filled) return 'SOS';
    if (indicator.icon == FluentIcons.vehicle_car_24_filled) {
      final speed = member['currentSpeed'] as int? ?? 0;
      return '${speed}mph';
    }
    if (indicator.icon == FluentIcons.battery_0_24_filled) {
      final battery = member['batteryLevel'] as int? ?? 0;
      return '$battery%';
    }
    if (indicator.icon == FluentIcons.battery_charge_24_filled) return 'Charging';
    return '';
  }

  List<StatusIndicatorData> _getAllStatusIndicators() {
    final List<StatusIndicatorData> indicators = [];
    
    final isDriving = member['isDriving'] as bool? ?? false;
    final isCharging = member['isCharging'] as bool? ?? false;
    final batteryLevel = member['batteryLevel'] as int? ?? 100;
    final isInEmergency = member['isInEmergency'] as bool? ?? false;

    if (isInEmergency) {
      indicators.add(StatusIndicatorData(
        icon: FluentIcons.warning_24_filled,
        backgroundColor: AppColors.error,
        iconColor: Colors.white,
      ));
    }

    if (isDriving) {
      indicators.add(StatusIndicatorData(
        icon: FluentIcons.vehicle_car_24_filled,
        backgroundColor: AppColors.primary,
        iconColor: Colors.white,
      ));
    }

    if (batteryLevel <= 15 && !isCharging) {
      indicators.add(StatusIndicatorData(
        icon: FluentIcons.battery_0_24_filled,
        backgroundColor: AppColors.error,
        iconColor: Colors.white,
      ));
    }

    if (isCharging) {
      indicators.add(StatusIndicatorData(
        icon: FluentIcons.battery_charge_24_filled,
        backgroundColor: AppColors.success,
        iconColor: Colors.white,
      ));
    }

    return indicators;
  }
}
