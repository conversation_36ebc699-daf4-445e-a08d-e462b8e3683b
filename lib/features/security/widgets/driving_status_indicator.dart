import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';

class DrivingStatusIndicator extends StatefulWidget {
  final int speed;
  final int speedLimit;
  final bool isUsingPhone;
  final bool showDetails;

  const DrivingStatusIndicator({
    super.key,
    required this.speed,
    required this.speedLimit,
    this.isUsingPhone = false,
    this.showDetails = true,
  });

  @override
  State<DrivingStatusIndicator> createState() => _DrivingStatusIndicatorState();
}

class _DrivingStatusIndicatorState extends State<DrivingStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _warningController;
  late Animation<double> _warningAnimation;

  @override
  void initState() {
    super.initState();
    
    _warningController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _warningAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _warningController,
      curve: Curves.easeInOut,
    ));
    
    if (_shouldShowWarning()) {
      _warningController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(DrivingStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (_shouldShowWarning() != _shouldShowWarningForWidget(oldWidget)) {
      if (_shouldShowWarning()) {
        _warningController.repeat(reverse: true);
      } else {
        _warningController.stop();
        _warningController.reset();
      }
    }
  }

  @override
  void dispose() {
    _warningController.dispose();
    super.dispose();
  }

  bool _shouldShowWarning() {
    return widget.isUsingPhone || widget.speed > widget.speedLimit;
  }

  bool _shouldShowWarningForWidget(DrivingStatusIndicator widget) {
    return widget.isUsingPhone || widget.speed > widget.speedLimit;
  }

  Color _getSpeedColor() {
    if (widget.speed > widget.speedLimit) {
      return AppColors.error;
    } else if (widget.speed > widget.speedLimit * 0.9) {
      return Colors.orange;
    } else {
      return AppColors.success;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showDetails) {
      return _buildCompactIndicator();
    }
    
    return _buildDetailedIndicator();
  }

  Widget _buildCompactIndicator() {
    return AnimatedBuilder(
      animation: _shouldShowWarning() ? _warningAnimation : 
          const AlwaysStoppedAnimation(1.0),
      builder: (context, child) {
        return Opacity(
          opacity: _shouldShowWarning() ? _warningAnimation.value : 1.0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
            decoration: BoxDecoration(
              color: _getSpeedColor().withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FluentIcons.vehicle_car_24_filled,
                  size: 10.sp,
                  color: _getSpeedColor(),
                ),
                SizedBox(width: 2.w),
                Text(
                  '${widget.speed}',
                  style: GoogleFonts.outfit(
                    fontSize: 9.sp,
                    fontWeight: FontWeight.w600,
                    color: _getSpeedColor(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailedIndicator() {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: _getSpeedColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: _shouldShowWarning() 
            ? Border.all(
                color: _getSpeedColor().withValues(alpha: 0.3),
                width: 1.w,
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Driving status header
          Row(
            children: [
              AnimatedBuilder(
                animation: _shouldShowWarning() ? _warningAnimation : 
                    const AlwaysStoppedAnimation(1.0),
                builder: (context, child) {
                  return Opacity(
                    opacity: _shouldShowWarning() ? _warningAnimation.value : 1.0,
                    child: Icon(
                      FluentIcons.vehicle_car_24_filled,
                      size: 16.sp,
                      color: _getSpeedColor(),
                    ),
                  );
                },
              ),
              SizedBox(width: 6.w),
              Text(
                'Driving',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.getTextPrimary(context),
                ),
              ),
              const Spacer(),
              if (widget.isUsingPhone)
                AnimatedBuilder(
                  animation: _warningAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _warningAnimation.value,
                      child: Icon(
                        FluentIcons.phone_24_filled,
                        size: 14.sp,
                        color: AppColors.error,
                      ),
                    );
                  },
                ),
            ],
          ),
          
          SizedBox(height: 6.h),
          
          // Speed information
          Row(
            children: [
              // Current speed
              Text(
                '${widget.speed}',
                style: GoogleFonts.outfit(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w700,
                  color: _getSpeedColor(),
                ),
              ),
              Text(
                ' mph',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.getTextSecondary(context),
                ),
              ),
              
              SizedBox(width: 8.w),
              
              // Speed limit
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: AppColors.getTextSecondary(context).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  'Limit ${widget.speedLimit}',
                  style: GoogleFonts.outfit(
                    fontSize: 9.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.getTextSecondary(context),
                  ),
                ),
              ),
            ],
          ),
          
          // Warning messages
          if (_shouldShowWarning()) ...[
            SizedBox(height: 6.h),
            _buildWarningMessages(),
          ],
        ],
      ),
    );
  }

  Widget _buildWarningMessages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.speed > widget.speedLimit)
          _buildWarningMessage(
            'Speeding detected',
            FluentIcons.warning_24_filled,
            AppColors.error,
          ),
        
        if (widget.isUsingPhone) ...[
          if (widget.speed > widget.speedLimit) SizedBox(height: 4.h),
          _buildWarningMessage(
            'Phone usage while driving',
            FluentIcons.phone_24_filled,
            AppColors.error,
          ),
        ],
      ],
    );
  }

  Widget _buildWarningMessage(String message, IconData icon, Color color) {
    return AnimatedBuilder(
      animation: _warningAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _warningAnimation.value,
          child: Row(
            children: [
              Icon(
                icon,
                size: 10.sp,
                color: color,
              ),
              SizedBox(width: 4.w),
              Text(
                message,
                style: GoogleFonts.outfit(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class DrivingTripCard extends StatelessWidget {
  final String memberName;
  final String startLocation;
  final String destination;
  final int currentSpeed;
  final int speedLimit;
  final String eta;
  final bool isUsingPhone;
  final VoidCallback? onTap;

  const DrivingTripCard({
    super.key,
    required this.memberName,
    required this.startLocation,
    required this.destination,
    required this.currentSpeed,
    required this.speedLimit,
    required this.eta,
    this.isUsingPhone = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final hasWarning = isUsingPhone || currentSpeed > speedLimit;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: hasWarning 
              ? AppColors.error.withValues(alpha: 0.1)
              : isDark 
                  ? const Color(0xFF2A2A2A) 
                  : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12.r),
          border: hasWarning 
              ? Border.all(
                  color: AppColors.error.withValues(alpha: 0.3),
                  width: 1.w,
                )
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with member name and driving status
            Row(
              children: [
                Icon(
                  FluentIcons.vehicle_car_24_filled,
                  size: 16.sp,
                  color: AppColors.primary,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '$memberName is driving',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.getTextPrimary(context),
                    ),
                  ),
                ),
                Text(
                  'ETA $eta',
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.getTextSecondary(context),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Route information
            Row(
              children: [
                Icon(
                  FluentIcons.location_24_filled,
                  size: 12.sp,
                  color: AppColors.getTextSecondary(context),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    '$startLocation → $destination',
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.getTextSecondary(context),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Driving status indicator
            DrivingStatusIndicator(
              speed: currentSpeed,
              speedLimit: speedLimit,
              isUsingPhone: isUsingPhone,
              showDetails: false,
            ),
          ],
        ),
      ),
    );
  }
}
