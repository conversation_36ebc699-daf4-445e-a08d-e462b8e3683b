import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';

class MemberEmojiAvatar extends StatelessWidget {
  final Map<String, dynamic> member;
  final double size;
  final Color? backgroundColor;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const MemberEmojiAvatar({
    super.key,
    required this.member,
    required this.size,
    this.backgroundColor,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    final avatarUrl = member['avatar'] as String?;
    final hasPhoto = avatarUrl != null && avatarUrl.isNotEmpty;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border:
            showBorder
                ? Border.all(
                  color: borderColor ?? _getStatusBorderColor(),
                  width: borderWidth.w,
                )
                : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 2),
            blurRadius: 4.r,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipOval(
        child: hasPhoto ? _buildPhotoAvatar(avatarUrl) : _buildEmojiAvatar(),
      ),
    );
  }

  Widget _buildPhotoAvatar(String avatarUrl) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey.shade100,
        shape: BoxShape.circle,
      ),
      child: Image.network(
        avatarUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildEmojiAvatar(),
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingAvatar();
        },
      ),
    );
  }

  Widget _buildEmojiAvatar() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: _getGradientBackground(),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          _getMemberEmoji(),
          style: TextStyle(fontSize: size * 0.5, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  Widget _buildLoadingAvatar() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2.w,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      ),
    );
  }

  LinearGradient _getGradientBackground() {
    final name = member['name'].toString().toLowerCase();

    // Create unique gradients based on name
    final gradients = [
      [const Color(0xFF667eea), const Color(0xFF764ba2)], // Purple-Blue
      [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Pink-Red
      [const Color(0xFF4facfe), const Color(0xFF00f2fe)], // Blue-Cyan
      [const Color(0xFF43e97b), const Color(0xFF38f9d7)], // Green-Teal
      [const Color(0xFFfa709a), const Color(0xFFfee140)], // Pink-Yellow
      [const Color(0xFF30cfd0), const Color(0xFF91a7ff)], // Cyan-Purple
      [const Color(0xFFa8edea), const Color(0xFFfed6e3)], // Mint-Pink
      [const Color(0xFFffecd2), const Color(0xFFfcb69f)], // Peach-Orange
    ];

    final index = name.hashCode.abs() % gradients.length;
    final colors = gradients[index];

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: colors,
    );
  }

  Color _getStatusBorderColor() {
    final isInEmergency = member['isInEmergency'] as bool? ?? false;
    final isOnline = member['isOnline'] as bool? ?? false;
    final batteryLevel = member['batteryLevel'] as int? ?? 100;

    if (isInEmergency) return AppColors.error;
    if (!isOnline) return Colors.grey.shade400;
    if (batteryLevel <= 15) return AppColors.error;
    if (batteryLevel <= 30) return Colors.orange;
    return AppColors.success;
  }

  String _getMemberEmoji() {
    final name = member['name'].toString().toLowerCase();
    final role = member['role']?.toString().toLowerCase();

    // Check role first if available
    if (role != null) {
      switch (role) {
        case 'mother':
        case 'mom':
          return '👩';
        case 'father':
        case 'dad':
          return '👨';
        case 'daughter':
          return '👧';
        case 'son':
          return '👦';
        case 'grandmother':
        case 'grandma':
          return '👵';
        case 'grandfather':
        case 'grandpa':
          return '👴';
      }
    }

    // Fallback to name-based detection
    if (name.contains('mom') || name.contains('mother')) return '👩';
    if (name.contains('dad') || name.contains('father')) return '👨';
    if (name.contains('sarah') || name.contains('daughter')) return '👧';
    if (name.contains('son') || name.contains('boy')) return '👦';
    if (name.contains('grandma') || name.contains('grandmother')) return '👵';
    if (name.contains('grandpa') || name.contains('grandfather')) return '👴';

    // Gender-based fallback
    final gender = member['gender']?.toString().toLowerCase();
    if (gender == 'female') return '👩';
    if (gender == 'male') return '👨';

    return '👤'; // Default person emoji
  }
}

class MemberPhotoAvatar extends StatelessWidget {
  final Map<String, dynamic> member;
  final double size;
  final Color? borderColor;
  final double borderWidth;

  const MemberPhotoAvatar({
    super.key,
    required this.member,
    required this.size,
    this.borderColor,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    final avatarUrl = member['avatar'] as String?;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border:
            borderColor != null
                ? Border.all(color: borderColor!, width: borderWidth.w)
                : null,
      ),
      child: CircleAvatar(
        radius: (size - borderWidth) / 2,
        backgroundImage:
            avatarUrl != null && avatarUrl.isNotEmpty
                ? NetworkImage(avatarUrl)
                : null,
        child:
            avatarUrl == null || avatarUrl.isEmpty
                ? MemberEmojiAvatar(member: member, size: size * 0.8)
                : null,
      ),
    );
  }
}

class MemberInitialsAvatar extends StatelessWidget {
  final Map<String, dynamic> member;
  final double size;
  final Color? backgroundColor;
  final Color? textColor;

  const MemberInitialsAvatar({
    super.key,
    required this.member,
    required this.size,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.blue.shade100,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          _getInitials(),
          style: TextStyle(
            fontSize: size * 0.4,
            fontWeight: FontWeight.w600,
            color: textColor ?? Colors.blue.shade800,
          ),
        ),
      ),
    );
  }

  String _getInitials() {
    final name = member['name']?.toString() ?? '';
    if (name.isEmpty) return '?';

    final parts = name.split(' ');
    if (parts.length == 1) {
      return parts[0].substring(0, 1).toUpperCase();
    } else {
      return '${parts[0].substring(0, 1)}${parts[1].substring(0, 1)}'
          .toUpperCase();
    }
  }
}
