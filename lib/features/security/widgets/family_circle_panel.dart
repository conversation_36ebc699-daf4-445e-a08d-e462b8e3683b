import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import 'family_member_card.dart';

class FamilyCirclePanel extends StatefulWidget {
  final List<Map<String, dynamic>> familyMembers;
  final Function(String memberId) onMemberTap;
  final Function(String memberId) onMemberCall;
  final Function(String memberId) onMemberMessage;

  const FamilyCirclePanel({
    super.key,
    required this.familyMembers,
    required this.onMemberTap,
    required this.onMemberCall,
    required this.onMemberMessage,
  });

  @override
  State<FamilyCirclePanel> createState() => _FamilyCirclePanelState();
}

class _FamilyCirclePanelState extends State<FamilyCirclePanel>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  bool _isExpanded = false;
  double _panelHeight = 120.h; // Collapsed height
  final double _maxPanelHeight = 400.h; // Expanded height

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _togglePanel() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _slideController.forward();
      _fadeController.forward();
    } else {
      _slideController.reverse();
      _fadeController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        final currentHeight =
            _panelHeight +
            (_maxPanelHeight - _panelHeight) * _slideAnimation.value;

        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: currentHeight,
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1A1A1A) : Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24.r),
                topRight: Radius.circular(24.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
                  offset: const Offset(0, -4),
                  blurRadius: 20.r,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                // Handle and Header
                _buildPanelHeader(theme, isDark),

                // Family Members List
                Expanded(child: _buildFamilyMembersList(theme, isDark)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPanelHeader(ThemeData theme, bool isDark) {
    return GestureDetector(
      onTap: _togglePanel,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: Column(
          children: [
            // Drag Handle
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color:
                    isDark
                        ? Colors.white.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            SizedBox(height: 16.h),

            // Header Row
            Row(
              children: [
                Container(
                  width: 32.w,
                  height: 32.h,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    FluentIcons.people_24_filled,
                    size: 18.sp,
                    color: AppColors.primary,
                  ),
                ),

                SizedBox(width: 12.w),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Family Circle',
                        style: GoogleFonts.outfit(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.getTextPrimary(context),
                        ),
                      ),
                      Text(
                        '${widget.familyMembers.length} members online',
                        style: GoogleFonts.outfit(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.getTextSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ),

                // Expand/Collapse Icon
                AnimatedRotation(
                  turns: _isExpanded ? 0.5 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    FluentIcons.chevron_up_24_filled,
                    size: 20.sp,
                    color: AppColors.getTextSecondary(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFamilyMembersList(ThemeData theme, bool isDark) {
    if (!_isExpanded) {
      // Collapsed view - horizontal scroll of member avatars
      return _buildCollapsedView(theme, isDark);
    }

    // Expanded view - full member cards
    return _buildExpandedView(theme, isDark);
  }

  Widget _buildCollapsedView(ThemeData theme, bool isDark) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children:
              widget.familyMembers.map((member) {
                return Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: GestureDetector(
                    onTap: () => widget.onMemberTap(member['id']),
                    child: _buildMemberAvatar(member, isDark),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildExpandedView(ThemeData theme, bool isDark) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: ListView.builder(
          itemCount: widget.familyMembers.length,
          itemBuilder: (context, index) {
            final member = widget.familyMembers[index];
            return Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: FamilyMemberCard(
                member: member,
                onTap: () => widget.onMemberTap(member['id']),
                onCall: () => widget.onMemberCall(member['id']),
                onMessage: () => widget.onMemberMessage(member['id']),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMemberAvatar(Map<String, dynamic> member, bool isDark) {
    final isOnline = member['isOnline'] as bool;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            // Avatar
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isOnline
                          ? AppColors.success
                          : AppColors.getTextSecondary(context),
                  width: 2.w,
                ),
              ),
              child: CircleAvatar(
                radius: 22.r,
                backgroundImage: NetworkImage(member['avatar']),
              ),
            ),

            // Online/Offline indicator
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 14.w,
                height: 14.h,
                decoration: BoxDecoration(
                  color:
                      isOnline
                          ? AppColors.success
                          : AppColors.getTextSecondary(context),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isDark ? const Color(0xFF1A1A1A) : Colors.white,
                    width: 2.w,
                  ),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 4.h),

        // Name
        Text(
          member['name'],
          style: GoogleFonts.outfit(
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.getTextPrimary(context),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
