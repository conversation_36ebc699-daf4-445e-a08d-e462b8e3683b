import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';

class IncidentListItem extends StatelessWidget {
  final Map<String, dynamic> incident;
  final bool isDark;
  final bool isLast;

  const IncidentListItem({
    super.key,
    required this.incident,
    required this.isDark,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Incident severity indicator
              Container(
                width: 8.w,
                height: 8.h,
                margin: EdgeInsets.only(top: 6.h, right: 12.w),
                decoration: BoxDecoration(
                  color: _getSeverityColor(incident['severity']),
                  shape: BoxShape.circle,
                ),
              ),

              // Incident content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Incident type and time
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            incident['type']?.toString().toUpperCase() ??
                                'UNKNOWN',
                            style: GoogleFonts.outfit(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.getTextPrimary(context),
                            ),
                          ),
                        ),
                        Text(
                          incident['time']?.toString() ?? '',
                          style: GoogleFonts.outfit(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.getTextSecondary(context),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 4.h),

                    // Incident location (generated from lat/lng if available)
                    if (incident['lat'] != null && incident['lng'] != null)
                      Row(
                        children: [
                          Icon(
                            FluentIcons.location_24_regular,
                            size: 12.sp,
                            color: AppColors.getTextSecondary(context),
                          ),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: Text(
                              _getLocationText(incident),
                              style: GoogleFonts.outfit(
                                fontSize: 11.sp,
                                fontWeight: FontWeight.w400,
                                color: AppColors.getTextSecondary(context),
                              ),
                            ),
                          ),
                        ],
                      ),

                    if (incident['lat'] != null && incident['lng'] != null)
                      SizedBox(height: 4.h),

                    // Incident distance (calculated from coordinates)
                    if (incident['lat'] != null && incident['lng'] != null)
                      Text(
                        _getDistanceText(incident),
                        style: GoogleFonts.outfit(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.getTextSecondary(context),
                        ),
                      ),
                  ],
                ),
              ),

              // Status indicator (show severity if no status)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: _getSeverityColor(
                    incident['severity']?.toString() ?? 'low',
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  incident['severity']?.toString().toUpperCase() ?? 'LOW',
                  style: GoogleFonts.outfit(
                    fontSize: 9.sp,
                    fontWeight: FontWeight.w500,
                    color: _getSeverityColor(
                      incident['severity']?.toString() ?? 'low',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Broken line separator (only if not last item)
        if (!isLast) _buildBrokenLineSeparator(context),
      ],
    );
  }

  Widget _buildBrokenLineSeparator(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      child: CustomPaint(
        size: Size(double.infinity, 1.h),
        painter: BrokenLinePainter(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          dashWidth: 4.w,
          dashSpace: 3.w,
        ),
      ),
    );
  }

  String _getLocationText(Map<String, dynamic> incident) {
    final lat = incident['lat']?.toString() ?? '';
    final lng = incident['lng']?.toString() ?? '';
    return 'Lat: ${lat.substring(0, lat.length > 6 ? 6 : lat.length)}, Lng: ${lng.substring(0, lng.length > 6 ? 6 : lng.length)}';
  }

  String _getDistanceText(Map<String, dynamic> incident) {
    // Simple distance calculation - in a real app you'd calculate from user's location
    return '~0.5km away';
  }

  Color _getSeverityColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'high':
        return const Color(0xFFFF4757);
      case 'medium':
        return const Color(0xFFFF9F43);
      case 'low':
        return const Color(0xFF2ED573);
      default:
        return const Color(0xFF747D8C);
    }
  }
}

class BrokenLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  BrokenLinePainter({
    required this.color,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

    double startX = 0;
    final y = size.height / 2;

    while (startX < size.width) {
      final endX = (startX + dashWidth).clamp(0.0, size.width);
      canvas.drawLine(Offset(startX, y), Offset(endX, y), paint);
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! BrokenLinePainter ||
        oldDelegate.color != color ||
        oldDelegate.dashWidth != dashWidth ||
        oldDelegate.dashSpace != dashSpace;
  }
}
