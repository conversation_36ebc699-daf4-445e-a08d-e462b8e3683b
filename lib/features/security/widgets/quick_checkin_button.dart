import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';

class QuickCheckInButton extends StatefulWidget {
  final VoidCallback? onCheckIn;
  final String currentLocation;
  final bool isVisible;

  const QuickCheckInButton({
    super.key,
    this.onCheckIn,
    required this.currentLocation,
    this.isVisible = true,
  });

  @override
  State<QuickCheckInButton> createState() => _QuickCheckInButtonState();
}

class _QuickCheckInButtonState extends State<QuickCheckInButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _isCheckedIn = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(QuickCheckInButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleCheckIn() {
    setState(() {
      _isCheckedIn = true;
    });
    
    widget.onCheckIn?.call();
    
    // Auto-hide after check-in
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible && !_animationController.isAnimating) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: GestureDetector(
              onTap: _isCheckedIn ? null : _handleCheckIn,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  color: _isCheckedIn 
                      ? AppColors.success.withValues(alpha: 0.9)
                      : Colors.black.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(24.r),
                  border: Border.all(
                    color: _isCheckedIn 
                        ? AppColors.success
                        : Colors.white.withValues(alpha: 0.3),
                    width: 1.w,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      offset: const Offset(0, 4),
                      blurRadius: 12.r,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Check-in icon
                    Container(
                      width: 24.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        color: _isCheckedIn 
                            ? Colors.white
                            : AppColors.primary.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _isCheckedIn 
                            ? FluentIcons.checkmark_24_filled
                            : FluentIcons.location_24_filled,
                        size: 12.sp,
                        color: _isCheckedIn 
                            ? AppColors.success
                            : AppColors.primary,
                      ),
                    ),
                    
                    SizedBox(width: 8.w),
                    
                    // Check-in text
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isCheckedIn ? 'Checked In!' : 'Check In',
                          style: GoogleFonts.outfit(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          widget.currentLocation,
                          style: GoogleFonts.outfit(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w400,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class CheckInHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> checkInHistory;
  final VoidCallback? onViewAll;

  const CheckInHistoryCard({
    super.key,
    required this.checkInHistory,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF2A2A2A) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.2 : 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  FluentIcons.location_24_filled,
                  size: 16.sp,
                  color: AppColors.primary,
                ),
              ),
              
              SizedBox(width: 12.w),
              
              Expanded(
                child: Text(
                  'Recent Check-ins',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.getTextPrimary(context),
                  ),
                ),
              ),
              
              if (onViewAll != null)
                GestureDetector(
                  onTap: onViewAll,
                  child: Text(
                    'View All',
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // Check-in list
          ...checkInHistory.take(3).map((checkIn) {
            return Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: _buildCheckInItem(context, checkIn, isDark),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCheckInItem(BuildContext context, Map<String, dynamic> checkIn, bool isDark) {
    return Row(
      children: [
        // Location icon
        Container(
          width: 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            color: AppColors.success,
            shape: BoxShape.circle,
          ),
        ),
        
        SizedBox(width: 12.w),
        
        // Check-in details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                checkIn['location'],
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.getTextPrimary(context),
                ),
              ),
              Text(
                checkIn['time'],
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.getTextSecondary(context),
                ),
              ),
            ],
          ),
        ),
        
        // Member who checked in
        if (checkIn['member'] != null)
          Text(
            checkIn['member'],
            style: GoogleFonts.outfit(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.getTextSecondary(context),
            ),
          ),
      ],
    );
  }
}
