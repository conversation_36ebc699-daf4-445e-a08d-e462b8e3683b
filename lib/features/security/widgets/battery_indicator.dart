import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';

class BatteryIndicator extends StatefulWidget {
  final int batteryLevel;
  final bool isCharging;
  final bool showPercentage;
  final double? width;
  final double? height;

  const BatteryIndicator({
    super.key,
    required this.batteryLevel,
    this.isCharging = false,
    this.showPercentage = true,
    this.width,
    this.height,
  });

  @override
  State<BatteryIndicator> createState() => _BatteryIndicatorState();
}

class _BatteryIndicatorState extends State<BatteryIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _chargingController;
  late Animation<double> _chargingAnimation;

  @override
  void initState() {
    super.initState();

    _chargingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _chargingAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _chargingController, curve: Curves.easeInOut),
    );

    if (widget.isCharging) {
      _chargingController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(BatteryIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isCharging != oldWidget.isCharging) {
      if (widget.isCharging) {
        _chargingController.repeat(reverse: true);
      } else {
        _chargingController.stop();
        _chargingController.reset();
      }
    }
  }

  @override
  void dispose() {
    _chargingController.dispose();
    super.dispose();
  }

  Color _getBatteryColor() {
    if (widget.batteryLevel > 50) {
      return AppColors.success;
    } else if (widget.batteryLevel > 20) {
      return Colors.orange;
    } else {
      return AppColors.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    final batteryColor = _getBatteryColor();
    final batteryWidth = widget.width ?? 24.w;
    final batteryHeight = widget.height ?? 12.h;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Battery icon with level
        Stack(
          alignment: Alignment.center,
          children: [
            // Battery outline
            Container(
              width: batteryWidth,
              height: batteryHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: batteryColor.withValues(alpha: 0.6),
                  width: 1.w,
                ),
                borderRadius: BorderRadius.circular(2.r),
              ),
              child: Align(
                alignment: Alignment.centerLeft,
                child: AnimatedBuilder(
                  animation:
                      widget.isCharging
                          ? _chargingAnimation
                          : const AlwaysStoppedAnimation(1.0),
                  builder: (context, child) {
                    return Container(
                      width:
                          (batteryWidth - 2.w) *
                          (widget.batteryLevel / 100) *
                          (widget.isCharging ? _chargingAnimation.value : 1.0),
                      height: batteryHeight - 2.h,
                      decoration: BoxDecoration(
                        color: batteryColor,
                        borderRadius: BorderRadius.circular(1.r),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Battery tip
            Positioned(
              right: -2.w,
              child: Container(
                width: 2.w,
                height: 6.h,
                decoration: BoxDecoration(
                  color: batteryColor.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(1.r),
                    bottomRight: Radius.circular(1.r),
                  ),
                ),
              ),
            ),

            // Charging lightning bolt
            if (widget.isCharging)
              AnimatedBuilder(
                animation: _chargingAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _chargingAnimation.value,
                    child: Icon(
                      FluentIcons.flash_24_filled,
                      size: 8.sp,
                      color: Colors.white,
                    ),
                  );
                },
              ),
          ],
        ),

        // Battery percentage text
        if (widget.showPercentage) ...[
          SizedBox(width: 4.w),
          Text(
            '${widget.batteryLevel}%',
            style: GoogleFonts.outfit(
              fontSize: 11.sp,
              fontWeight: FontWeight.w500,
              color: batteryColor,
            ),
          ),
        ],
      ],
    );
  }
}

class BatteryLevelCard extends StatelessWidget {
  final String memberName;
  final int batteryLevel;
  final bool isCharging;
  final String lastUpdated;
  final VoidCallback? onTap;

  const BatteryLevelCard({
    super.key,
    required this.memberName,
    required this.batteryLevel,
    this.isCharging = false,
    required this.lastUpdated,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isLowBattery = batteryLevel <= 20;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color:
              isLowBattery
                  ? AppColors.error.withValues(alpha: 0.1)
                  : isDark
                  ? const Color(0xFF2A2A2A)
                  : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12.r),
          border:
              isLowBattery
                  ? Border.all(
                    color: AppColors.error.withValues(alpha: 0.3),
                    width: 1.w,
                  )
                  : null,
        ),
        child: Row(
          children: [
            // Warning icon for low battery
            if (isLowBattery) ...[
              Icon(
                FluentIcons.warning_24_filled,
                size: 16.sp,
                color: AppColors.error,
              ),
              SizedBox(width: 8.w),
            ],

            // Member name
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    memberName,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.getTextPrimary(context),
                    ),
                  ),
                  Text(
                    lastUpdated,
                    style: GoogleFonts.outfit(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.getTextSecondary(context),
                    ),
                  ),
                ],
              ),
            ),

            // Battery indicator
            BatteryIndicator(
              batteryLevel: batteryLevel,
              isCharging: isCharging,
              width: 32.w,
              height: 16.h,
            ),
          ],
        ),
      ),
    );
  }
}
