import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../core/theme/app_colors.dart';

class FamilyMapMarkerGenerator {
  /// Creates a Google Maps marker icon based on family member data
  static Future<BitmapDescriptor> createMarkerFromMember(
    Map<String, dynamic> member,
    BuildContext context, {
    bool isSelected = false,
  }) async {
    final markerColor = _getMarkerColor(member);
    return BitmapDescriptor.defaultMarkerWithHue(markerColor);
  }

  /// Gets the appropriate marker color based on member status
  static double _getMarkerColor(Map<String, dynamic> member) {
    final isInEmergency = member['isInEmergency'] as bool? ?? false;
    final isOnline = member['isOnline'] as bool? ?? false;
    final batteryLevel = member['batteryLevel'] as int? ?? 100;
    final isDriving = member['isDriving'] as bool? ?? false;

    if (isInEmergency) {
      return BitmapDescriptor.hueRed;
    }
    
    if (!isOnline) {
      return BitmapDescriptor.hueViolet;
    }
    
    if (batteryLevel <= 15) {
      return BitmapDescriptor.hueOrange;
    }
    
    if (isDriving) {
      return BitmapDescriptor.hueBlue;
    }
    
    return BitmapDescriptor.hueGreen;
  }

  /// Creates a marker info window snippet text
  static String createMarkerSnippet(Map<String, dynamic> member) {
    final batteryLevel = member['batteryLevel'] as int? ?? 100;
    final isDriving = member['isDriving'] as bool? ?? false;
    final isInEmergency = member['isInEmergency'] as bool? ?? false;
    final currentLocation = member['currentLocation'] as String? ?? 'Unknown';

    if (isInEmergency) {
      return '🚨 EMERGENCY ALERT';
    }

    if (isDriving) {
      final speed = member['currentSpeed'] as int? ?? 0;
      return '🚗 Driving ${speed}mph • $batteryLevel% battery';
    }

    return '📍 $currentLocation • $batteryLevel% battery';
  }

  /// Gets status color for UI elements
  static Color getStatusColor(Map<String, dynamic> member) {
    final isInEmergency = member['isInEmergency'] as bool? ?? false;
    final isOnline = member['isOnline'] as bool? ?? false;
    final batteryLevel = member['batteryLevel'] as int? ?? 100;

    if (isInEmergency) return AppColors.error;
    if (!isOnline) return Colors.grey;
    if (batteryLevel <= 15) return AppColors.error;
    if (batteryLevel <= 30) return Colors.orange;
    return AppColors.success;
  }

  /// Creates a marker with custom styling
  static Marker createFamilyMarker({
    required String markerId,
    required Map<String, dynamic> member,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    final lat = member['lat'] as double;
    final lng = member['lng'] as double;
    final position = LatLng(lat, lng);

    return Marker(
      markerId: MarkerId(markerId),
      position: position,
      infoWindow: InfoWindow(
        title: member['name'] as String,
        snippet: createMarkerSnippet(member),
      ),
      onTap: onTap,
    );
  }

  /// Batch creates markers for multiple family members
  static Future<Set<Marker>> createFamilyMarkers({
    required List<Map<String, dynamic>> familyMembers,
    required BuildContext context,
    Function(Map<String, dynamic>)? onMemberTap,
  }) async {
    final Set<Marker> markers = {};

    for (final member in familyMembers) {
      final icon = await createMarkerFromMember(member, context);
      
      markers.add(
        Marker(
          markerId: MarkerId('family_${member['id']}'),
          position: LatLng(
            member['lat'] as double,
            member['lng'] as double,
          ),
          icon: icon,
          infoWindow: InfoWindow(
            title: member['name'] as String,
            snippet: createMarkerSnippet(member),
          ),
          onTap: onMemberTap != null ? () => onMemberTap(member) : null,
        ),
      );
    }

    return markers;
  }

  /// Updates marker based on member status changes
  static Future<Marker> updateMemberMarker({
    required Marker existingMarker,
    required Map<String, dynamic> updatedMember,
    required BuildContext context,
  }) async {
    final newIcon = await createMarkerFromMember(updatedMember, context);
    
    return existingMarker.copyWith(
      iconParam: newIcon,
      infoWindowParam: InfoWindow(
        title: updatedMember['name'] as String,
        snippet: createMarkerSnippet(updatedMember),
      ),
    );
  }
}
