import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../widgets/emergency_alert_fab.dart';
import '../widgets/family_map_marker.dart';
import '../widgets/quick_checkin_button.dart';
import '../widgets/member_share_button.dart';
import '../utils/family_map_marker_generator.dart';

class SOSMapScreen extends StatefulWidget {
  const SOSMapScreen({super.key});

  @override
  State<SOSMapScreen> createState() => _SOSMapScreenState();
}

class _SOSMapScreenState extends State<SOSMapScreen> {
  bool _isEmergencyActive = false;
  bool _showHeartbeat = false;
  int _countdown = 10;

  static const LatLng _defaultLocation = LatLng(
    37.7749,
    -122.4194,
  ); // <PERSON>
  LatLng _currentLocation = _defaultLocation;
  Set<Marker> _markers = {};

  // Sample family members data
  final List<Map<String, dynamic>> _familyMembers = [
    {
      'id': '1',
      'name': 'Mom',
      'avatar':
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      'isOnline': true,
      'batteryLevel': 85,
      'isCharging': false,
      'currentLocation': 'Home',
      'lastSeen': 'Active now',
      'isDriving': false,
      'isInEmergency': false,
      'lat': 37.7749,
      'lng': -122.4194,
    },
    {
      'id': '2',
      'name': 'Dad',
      'avatar':
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      'isOnline': true,
      'batteryLevel': 23,
      'isCharging': false,
      'currentLocation': 'Office',
      'lastSeen': '2 min ago',
      'isDriving': true,
      'currentSpeed': 55,
      'speedLimit': 45,
      'isUsingPhone': false,
      'isInEmergency': false,
      'lat': 37.7849,
      'lng': -122.4094,
    },
    {
      'id': '3',
      'name': 'Sarah',
      'avatar':
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      'isOnline': true,
      'batteryLevel': 67,
      'isCharging': true,
      'currentLocation': 'School',
      'lastSeen': 'Active now',
      'isDriving': false,
      'isInEmergency': false,
      'lat': 37.7649,
      'lng': -122.4294,
    },
  ];

  @override
  void initState() {
    super.initState();
    _startCountdown();
    _initializeMarkers();
  }

  void _initializeMarkers() async {
    Set<Marker> markers = {
      // Emergency location marker
      Marker(
        markerId: const MarkerId('emergency_location'),
        position: _currentLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: const InfoWindow(
          title: 'Emergency Location',
          snippet: 'Your current location',
        ),
      ),
    };

    // Add family member markers
    for (final member in _familyMembers) {
      final lat = member['lat'] as double;
      final lng = member['lng'] as double;
      final position = LatLng(lat, lng);

      final icon = await FamilyMapMarkerGenerator.createMarkerFromMember(
        member,
        context,
      );

      markers.add(
        Marker(
          markerId: MarkerId('family_${member['id']}'),
          position: position,
          icon: icon,
          infoWindow: InfoWindow(
            title: member['name'],
            snippet: _getFamilyMemberSnippet(member),
          ),
          onTap: () => _onFamilyMemberTap(member),
        ),
      );
    }

    setState(() {
      _markers = markers;
    });
  }

  String _getFamilyMemberSnippet(Map<String, dynamic> member) {
    final batteryLevel = member['batteryLevel'] as int;
    final isDriving = member['isDriving'] as bool? ?? false;
    final isInEmergency = member['isInEmergency'] as bool? ?? false;

    if (isInEmergency) return '🚨 EMERGENCY';
    if (isDriving)
      return '🚗 Driving • ${member['currentSpeed']}mph • $batteryLevel%';
    return '📍 ${member['currentLocation']} • $batteryLevel%';
  }

  void _onFamilyMemberTap(Map<String, dynamic> member) {
    // Handle family member marker tap
    // Could show detailed info, call, message, etc.
    print('Tapped on ${member['name']}');
  }

  void _handleCheckIn() {
    // Handle check-in functionality
    print('User checked in at current location');
    // You could send this to your backend, notify family members, etc.
  }

  void _onShareMember(Map<String, dynamic> member) {
    // Show share dialog
    showDialog(
      context: context,
      builder:
          (context) => ShareLocationDialog(
            member: member,
            onShareLive: () {
              Navigator.of(context).pop();
              _shareLiveLocation(member);
            },
            onShareOnce: () {
              Navigator.of(context).pop();
              _shareCurrentLocation(member);
            },
          ),
    );
  }

  void _shareLiveLocation(Map<String, dynamic> member) {
    // Implement live location sharing
    print('Sharing live location for ${member['name']}');
    // You could start a real-time location sharing session
  }

  void _shareCurrentLocation(Map<String, dynamic> member) {
    // Implement one-time location sharing
    print('Sharing current location for ${member['name']}');
    // You could send current location via SMS, email, etc.
  }

  void _call911() {
    // Implement 911 calling functionality
    print('Calling 911...');
    // You could use url_launcher to make the call: launch('tel:911')
  }

  void _callFireDepartment() {
    // Implement fire department calling functionality
    print('Calling Fire Department...');
    // You could use url_launcher to make the call: launch('tel:fire-department-number')
  }

  Widget _buildFamilyInfoPanel() {
    return Positioned(
      bottom: 200.h, // Above the FAB
      left: 20.w,
      right: 20.w,
      child: Container(
        height: 80.h,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1.w,
          ),
        ),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          itemCount: _familyMembers.length,
          itemBuilder: (context, index) {
            final member = _familyMembers[index];
            return Padding(
              padding: EdgeInsets.only(right: 12.w),
              child: FamilyMapMarker(
                member: member,
                onTap: () => _onFamilyMemberTap(member),
                onShare: () => _onShareMember(member),
                showShareButton: !_isEmergencyActive,
              ),
            );
          },
        ),
      ),
    );
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _countdown > 0) {
        setState(() {
          _countdown--;
        });
        if (_countdown > 0) {
          _startCountdown();
        } else {
          setState(() {
            _showHeartbeat = true;
            _isEmergencyActive = true;
          });
        }
      }
    });
  }

  void _cancelEmergency() {
    Navigator.of(context).pop();
  }

  void _activateEmergency() {
    setState(() {
      _isEmergencyActive = !_isEmergencyActive;
    });

    if (_isEmergencyActive) {
      // Add emergency pulse animation to map marker
      _updateEmergencyMarker();
    }
  }

  void _updateEmergencyMarker() {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('emergency_location'),
          position: _currentLocation,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: const InfoWindow(
            title: '🚨 EMERGENCY ALERT ACTIVE',
            snippet: 'Emergency services notified',
          ),
        ),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              // Map controller ready - could be used for future features
            },
            initialCameraPosition: CameraPosition(
              target: _currentLocation,
              zoom: 16.0,
            ),
            markers: _markers,
            mapType: MapType.normal,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            compassEnabled: false,
            mapToolbarEnabled: false,
            style: '''
            [
              {
                "elementType": "geometry",
                "stylers": [{"color": "#1d2c4d"}]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [{"color": "#8ec3b9"}]
              },
              {
                "elementType": "labels.text.stroke",
                "stylers": [{"color": "#1a3646"}]
              },
              {
                "featureType": "road",
                "elementType": "geometry",
                "stylers": [{"color": "#304a7d"}]
              },
              {
                "featureType": "water",
                "elementType": "geometry",
                "stylers": [{"color": "#0e1626"}]
              }
            ]
            ''',
          ),

          // Family member info panel (bottom overlay)
          _buildFamilyInfoPanel(),

          // Quick check-in button
          Positioned(
            top: 120.h,
            right: 20.w,
            child: QuickCheckInButton(
              currentLocation: 'Current Location',
              isVisible: !_isEmergencyActive,
              onCheckIn: _handleCheckIn,
            ),
          ),

          // Back button
          Positioned(
            top: 60.h,
            left: 20.w,
            child: GestureDetector(
              onTap: _cancelEmergency,
              child: Container(
                width: 44.w,
                height: 44.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.5),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1.w,
                  ),
                ),
                child: Icon(
                  FluentIcons.arrow_left_24_filled,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton:
          _showHeartbeat
              ? Padding(
                padding: EdgeInsets.only(bottom: 120.h),
                child: EmergencyAlertFab(
                  isActive: _isEmergencyActive,
                  onPressed:
                      _isEmergencyActive
                          ? _cancelEmergency
                          : _activateEmergency,
                  onCall911: _call911,
                  onCallFire: _callFireDepartment,
                  locationInfo:
                      _isEmergencyActive
                          ? 'Emergency Active - Location Shared'
                          : 'Tap to activate emergency',
                ),
              )
              : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
