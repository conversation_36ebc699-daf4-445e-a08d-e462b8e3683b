import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:rxdart/rxdart.dart';

import '../../data/models/incident_model.dart';
import '../../data/models/incident_summary_model.dart';
import '../../data/repositories/security_incidents_repository.dart';
import '../../../../core/services/location_service.dart';

// Events
abstract class SecurityIncidentsEvent extends Equatable {
  const SecurityIncidentsEvent();

  @override
  List<Object?> get props => [];
}

class LoadIncidents extends SecurityIncidentsEvent {
  final String zoneId;
  
  const LoadIncidents(this.zoneId);
  
  @override
  List<Object?> get props => [zoneId];
}

class LoadSummary extends SecurityIncidentsEvent {
  final String zoneId;
  
  const LoadSummary(this.zoneId);
  
  @override
  List<Object?> get props => [zoneId];
}

class LoadNearbyIncidents extends SecurityIncidentsEvent {
  final Position position;
  final double radiusKm;
  
  const LoadNearbyIncidents(this.position, this.radiusKm);
  
  @override
  List<Object?> get props => [position, radiusKm];
}

class ReportIncident extends SecurityIncidentsEvent {
  final IncidentModel incident;
  
  const ReportIncident(this.incident);
  
  @override
  List<Object?> get props => [incident];
}

class UpdateIncident extends SecurityIncidentsEvent {
  final String incidentId;
  final Map<String, dynamic> updates;
  
  const UpdateIncident(this.incidentId, this.updates);
  
  @override
  List<Object?> get props => [incidentId, updates];
}

class RefreshData extends SecurityIncidentsEvent {
  final String zoneId;
  
  const RefreshData(this.zoneId);
  
  @override
  List<Object?> get props => [zoneId];
}

class SyncOfflineData extends SecurityIncidentsEvent {
  const SyncOfflineData();
}

class FilterIncidents extends SecurityIncidentsEvent {
  final List<String>? types;
  final List<String>? severities;
  final DateTime? startDate;
  final DateTime? endDate;
  
  const FilterIncidents({
    this.types,
    this.severities,
    this.startDate,
    this.endDate,
  });
  
  @override
  List<Object?> get props => [types, severities, startDate, endDate];
}

// States
abstract class SecurityIncidentsState extends Equatable {
  const SecurityIncidentsState();

  @override
  List<Object?> get props => [];
}

class SecurityIncidentsInitial extends SecurityIncidentsState {}

class SecurityIncidentsLoading extends SecurityIncidentsState {}

class SecurityIncidentsLoaded extends SecurityIncidentsState {
  final List<IncidentModel> incidents;
  final IncidentSummaryModel? summary;
  final List<IncidentTrendModel> trends;
  final bool isOffline;
  final DateTime lastUpdated;
  final List<IncidentModel> filteredIncidents;
  
  const SecurityIncidentsLoaded({
    required this.incidents,
    this.summary,
    required this.trends,
    required this.isOffline,
    required this.lastUpdated,
    required this.filteredIncidents,
  });
  
  @override
  List<Object?> get props => [
    incidents,
    summary,
    trends,
    isOffline,
    lastUpdated,
    filteredIncidents,
  ];
  
  SecurityIncidentsLoaded copyWith({
    List<IncidentModel>? incidents,
    IncidentSummaryModel? summary,
    List<IncidentTrendModel>? trends,
    bool? isOffline,
    DateTime? lastUpdated,
    List<IncidentModel>? filteredIncidents,
  }) {
    return SecurityIncidentsLoaded(
      incidents: incidents ?? this.incidents,
      summary: summary ?? this.summary,
      trends: trends ?? this.trends,
      isOffline: isOffline ?? this.isOffline,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      filteredIncidents: filteredIncidents ?? this.filteredIncidents,
    );
  }
}

class SecurityIncidentsError extends SecurityIncidentsState {
  final String message;
  final bool isOffline;
  
  const SecurityIncidentsError(this.message, {this.isOffline = false});
  
  @override
  List<Object?> get props => [message, isOffline];
}

class IncidentReported extends SecurityIncidentsState {
  final IncidentModel incident;
  
  const IncidentReported(this.incident);
  
  @override
  List<Object?> get props => [incident];
}

class IncidentUpdated extends SecurityIncidentsState {
  final String incidentId;
  
  const IncidentUpdated(this.incidentId);
  
  @override
  List<Object?> get props => [incidentId];
}

// BLoC
class SecurityIncidentsBloc extends Bloc<SecurityIncidentsEvent, SecurityIncidentsState> {
  final SecurityIncidentsRepository _repository;
  final LocationService _locationService;
  
  StreamSubscription<List<IncidentModel>>? _incidentsSubscription;
  StreamSubscription<IncidentSummaryModel?>? _summarySubscription;
  StreamSubscription<List<IncidentTrendModel>>? _trendsSubscription;
  
  // Current data
  List<IncidentModel> _currentIncidents = [];
  IncidentSummaryModel? _currentSummary;
  List<IncidentTrendModel> _currentTrends = [];
  
  // Filters
  List<String>? _typeFilters;
  List<String>? _severityFilters;
  DateTime? _startDateFilter;
  DateTime? _endDateFilter;
  
  SecurityIncidentsBloc({
    required SecurityIncidentsRepository repository,
    required LocationService locationService,
  }) : _repository = repository,
       _locationService = locationService,
       super(SecurityIncidentsInitial()) {
    
    on<LoadIncidents>(_onLoadIncidents);
    on<LoadSummary>(_onLoadSummary);
    on<LoadNearbyIncidents>(_onLoadNearbyIncidents);
    on<ReportIncident>(_onReportIncident);
    on<UpdateIncident>(_onUpdateIncident);
    on<RefreshData>(_onRefreshData);
    on<SyncOfflineData>(_onSyncOfflineData);
    on<FilterIncidents>(_onFilterIncidents);
  }

  Future<void> _onLoadIncidents(
    LoadIncidents event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    emit(SecurityIncidentsLoading());
    
    try {
      // Cancel existing subscriptions
      await _cancelSubscriptions();
      
      // Set up real-time streams
      _incidentsSubscription = _repository
          .getIncidentsStream(event.zoneId)
          .listen(
            (incidents) {
              _currentIncidents = incidents;
              _emitLoadedState(emit);
            },
            onError: (error) {
              emit(SecurityIncidentsError(
                'Failed to load incidents: $error',
                isOffline: true,
              ));
            },
          );
      
      _summarySubscription = _repository
          .getSummaryStream(event.zoneId)
          .listen(
            (summary) {
              _currentSummary = summary;
              _emitLoadedState(emit);
            },
            onError: (error) {
              print('Summary stream error: $error');
            },
          );
      
      _trendsSubscription = _repository
          .getTrendsStream(event.zoneId)
          .listen(
            (trends) {
              _currentTrends = trends;
              _emitLoadedState(emit);
            },
            onError: (error) {
              print('Trends stream error: $error');
            },
          );
      
    } catch (e) {
      emit(SecurityIncidentsError('Failed to initialize streams: $e'));
    }
  }

  Future<void> _onLoadSummary(
    LoadSummary event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    // Summary is loaded as part of LoadIncidents
    // This event can be used for manual refresh
    add(LoadIncidents(event.zoneId));
  }

  Future<void> _onLoadNearbyIncidents(
    LoadNearbyIncidents event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    try {
      final result = await _repository.getNearbyIncidents(
        event.position,
        event.radiusKm,
      );
      
      result.fold(
        (failure) => emit(SecurityIncidentsError(failure.toString())),
        (incidents) {
          _currentIncidents = incidents;
          _emitLoadedState(emit);
        },
      );
    } catch (e) {
      emit(SecurityIncidentsError('Failed to load nearby incidents: $e'));
    }
  }

  Future<void> _onReportIncident(
    ReportIncident event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    try {
      final result = await _repository.reportIncident(event.incident);
      
      result.fold(
        (failure) => emit(SecurityIncidentsError(
          'Failed to report incident: ${failure.toString()}',
          isOffline: true,
        )),
        (_) => emit(IncidentReported(event.incident)),
      );
    } catch (e) {
      emit(SecurityIncidentsError('Failed to report incident: $e'));
    }
  }

  Future<void> _onUpdateIncident(
    UpdateIncident event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    try {
      final result = await _repository.updateIncident(
        event.incidentId,
        event.updates,
      );
      
      result.fold(
        (failure) => emit(SecurityIncidentsError(
          'Failed to update incident: ${failure.toString()}'
        )),
        (_) => emit(IncidentUpdated(event.incidentId)),
      );
    } catch (e) {
      emit(SecurityIncidentsError('Failed to update incident: $e'));
    }
  }

  Future<void> _onRefreshData(
    RefreshData event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    // Reload all data
    add(LoadIncidents(event.zoneId));
  }

  Future<void> _onSyncOfflineData(
    SyncOfflineData event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    try {
      final result = await _repository.syncOfflineData();
      
      result.fold(
        (failure) => emit(SecurityIncidentsError(
          'Failed to sync offline data: ${failure.toString()}'
        )),
        (_) {
          // Refresh current data after sync
          if (state is SecurityIncidentsLoaded) {
            final currentState = state as SecurityIncidentsLoaded;
            _emitLoadedState(emit);
          }
        },
      );
    } catch (e) {
      emit(SecurityIncidentsError('Failed to sync offline data: $e'));
    }
  }

  Future<void> _onFilterIncidents(
    FilterIncidents event,
    Emitter<SecurityIncidentsState> emit,
  ) async {
    _typeFilters = event.types;
    _severityFilters = event.severities;
    _startDateFilter = event.startDate;
    _endDateFilter = event.endDate;
    
    _emitLoadedState(emit);
  }

  void _emitLoadedState(Emitter<SecurityIncidentsState> emit) {
    final filteredIncidents = _applyFilters(_currentIncidents);
    
    emit(SecurityIncidentsLoaded(
      incidents: _currentIncidents,
      summary: _currentSummary,
      trends: _currentTrends,
      isOffline: false, // TODO: Implement offline detection
      lastUpdated: DateTime.now(),
      filteredIncidents: filteredIncidents,
    ));
  }

  List<IncidentModel> _applyFilters(List<IncidentModel> incidents) {
    var filtered = incidents;
    
    if (_typeFilters != null && _typeFilters!.isNotEmpty) {
      filtered = filtered.where((incident) => 
        _typeFilters!.contains(incident.type)).toList();
    }
    
    if (_severityFilters != null && _severityFilters!.isNotEmpty) {
      filtered = filtered.where((incident) => 
        _severityFilters!.contains(incident.severity)).toList();
    }
    
    if (_startDateFilter != null) {
      filtered = filtered.where((incident) => 
        incident.timestamp.isAfter(_startDateFilter!)).toList();
    }
    
    if (_endDateFilter != null) {
      filtered = filtered.where((incident) => 
        incident.timestamp.isBefore(_endDateFilter!)).toList();
    }
    
    return filtered;
  }

  Future<void> _cancelSubscriptions() async {
    await _incidentsSubscription?.cancel();
    await _summarySubscription?.cancel();
    await _trendsSubscription?.cancel();
  }

  @override
  Future<void> close() async {
    await _cancelSubscriptions();
    return super.close();
  }
}
