import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:geolocator/geolocator.dart';

import '../../../../core/services/localization_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/services/location_service.dart';
import '../bloc/security_incidents_bloc.dart';
import '../../widgets/emergency_alert_fab.dart';
import '../../widgets/incidents_heatmap_card.dart';
import '../../widgets/recent_incidents_card.dart';
import '../../screens/sos_map_screen.dart';

class RealtimeSecurityScreen extends StatefulWidget {
  const RealtimeSecurityScreen({Key? key}) : super(key: key);

  @override
  State<RealtimeSecurityScreen> createState() => _RealtimeSecurityScreenState();
}

class _RealtimeSecurityScreenState extends State<RealtimeSecurityScreen>
    with TickerProviderStateMixin {
  bool _isHolding = false;
  int _holdProgress = 0;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  String? _currentZoneId;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeOut));
    
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    try {
      final locationService = context.read<LocationService>();
      final position = await locationService.getCurrentPosition();
      
      setState(() {
        _currentPosition = position;
        // TODO: Determine zone ID from position
        _currentZoneId = 'default_zone'; // Placeholder
      });
      
      if (_currentZoneId != null) {
        // Load incidents for current zone
        context.read<SecurityIncidentsBloc>().add(LoadIncidents(_currentZoneId!));
      }
    } catch (e) {
      print('Error getting location: $e');
      // Use default zone if location fails
      setState(() {
        _currentZoneId = 'default_zone';
      });
      context.read<SecurityIncidentsBloc>().add(LoadIncidents(_currentZoneId!));
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startHold() {
    setState(() {
      _isHolding = true;
      _holdProgress = 0;
    });

    _pulseController.repeat();
    _incrementHoldProgress();
  }

  void _incrementHoldProgress() {
    Future.delayed(const Duration(milliseconds: 50), () {
      if (_isHolding && mounted) {
        setState(() {
          _holdProgress++;
        });

        if (_holdProgress >= 100) {
          _completeHold();
        } else {
          _incrementHoldProgress();
        }
      }
    });
  }

  void _completeHold() {
    setState(() {
      _isHolding = false;
      _holdProgress = 0;
    });

    _pulseController.stop();
    _pulseController.reset();

    // Navigate to SOS map screen
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SOSMapScreen())
    );
  }

  void _cancelHold() {
    setState(() {
      _isHolding = false;
      _holdProgress = 0;
    });

    _pulseController.stop();
    _pulseController.reset();
  }

  void _refreshData() {
    if (_currentZoneId != null) {
      context.read<SecurityIncidentsBloc>().add(RefreshData(_currentZoneId!));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          context.l10n.security,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(FluentIcons.arrow_clockwise_24_regular),
          ),
          IconButton(
            onPressed: () => _showSecuritySettings(),
            icon: const Icon(FluentIcons.settings_24_regular),
          ),
        ],
      ),
      body: BlocConsumer<SecurityIncidentsBloc, SecurityIncidentsState>(
        listener: (context, state) {
          if (state is SecurityIncidentsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
                action: state.isOffline
                    ? SnackBarAction(
                        label: 'Retry',
                        onPressed: _refreshData,
                      )
                    : null,
              ),
            );
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async => _refreshData(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // Offline indicator
                  if (state is SecurityIncidentsLoaded && state.isOffline)
                    _buildOfflineIndicator(isDark),

                  // Security status banner
                  if (state is SecurityIncidentsLoaded && state.summary != null)
                    _buildSecurityStatusBanner(state.summary!, isDark),
                  
                  SizedBox(height: 16.h),

                  // Real-time Incidents Heatmap
                  _buildHeatmapCard(state, isDark),
                  
                  SizedBox(height: 16.h),

                  // Real-time Incidents List
                  _buildIncidentsCard(state, isDark),
                  
                  SizedBox(height: 69.h), // Bottom padding for FAB
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 30.h),
        child: SizedBox(
          width: 170.w,
          height: 170.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Pulse wave animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Container(
                    width: _isHolding
                        ? (70.w + 100.w * _pulseAnimation.value)
                        : 70.w,
                    height: _isHolding
                        ? (70.h + 100.h * _pulseAnimation.value)
                        : 70.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: _isHolding
                          ? Border.all(
                              color: AppColors.error.withValues(
                                alpha: 0.3 * (1 - _pulseAnimation.value),
                              ),
                              width: 2.w,
                            )
                          : null,
                    ),
                  );
                },
              ),

              // Progress indicator
              if (_isHolding)
                SizedBox(
                  width: 90.w,
                  height: 90.h,
                  child: CircularProgressIndicator(
                    value: _holdProgress / 100,
                    strokeWidth: 3.w,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.error),
                    backgroundColor: AppColors.error.withValues(alpha: 0.2),
                  ),
                ),

              // Emergency FAB
              GestureDetector(
                onLongPressStart: (_) => _startHold(),
                onLongPressEnd: (_) => _cancelHold(),
                child: EmergencyAlertFab(
                  isActive: _isHolding,
                  onPressed: null, // Disabled tap, only long press works
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildHeatmapCard(SecurityIncidentsState state, bool isDark) {
    if (state is SecurityIncidentsLoading) {
      return _buildLoadingCard(isDark, 'Loading heatmap...');
    }
    
    if (state is SecurityIncidentsLoaded) {
      // Convert IncidentModel to Map format for existing widget
      final incidentData = state.filteredIncidents.map((incident) => {
        'lat': incident.latitude,
        'lng': incident.longitude,
        'type': incident.type,
        'severity': incident.severity,
        'time': _formatTimeAgo(incident.timestamp),
      }).toList();

      return IncidentsHeatmapCard(
        isDark: isDark,
        incidentData: incidentData,
      );
    }
    
    if (state is SecurityIncidentsError) {
      return _buildErrorCard(isDark, 'Failed to load heatmap');
    }
    
    return _buildLoadingCard(isDark, 'Initializing...');
  }

  Widget _buildIncidentsCard(SecurityIncidentsState state, bool isDark) {
    if (state is SecurityIncidentsLoading) {
      return _buildLoadingCard(isDark, 'Loading incidents...');
    }
    
    if (state is SecurityIncidentsLoaded) {
      // Convert IncidentModel to Map format for existing widget
      final incidentData = state.filteredIncidents.map((incident) => {
        'lat': incident.latitude,
        'lng': incident.longitude,
        'type': incident.type,
        'severity': incident.severity,
        'time': _formatTimeAgo(incident.timestamp),
      }).toList();

      return RecentIncidentsCard(
        isDark: isDark,
        incidentData: incidentData,
      );
    }
    
    if (state is SecurityIncidentsError) {
      return _buildErrorCard(isDark, 'Failed to load incidents');
    }
    
    return _buildLoadingCard(isDark, 'Initializing...');
  }

  Widget _buildLoadingCard(bool isDark, String message) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark ? [] : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          SizedBox(height: 16.h),
          Text(
            message,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(bool isDark, String message) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        boxShadow: isDark ? [] : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            FluentIcons.error_circle_24_regular,
            color: AppColors.error,
            size: 32.sp,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineIndicator(bool isDark) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            FluentIcons.wifi_off_24_regular,
            color: AppColors.warning,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Offline mode - showing cached data',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityStatusBanner(dynamic summary, bool isDark) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardColor : Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: isDark ? [] : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8.r,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: _getThreatLevelColor(summary.threatLevel).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              FluentIcons.shield_24_filled,
              color: _getThreatLevelColor(summary.threatLevel),
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Security Status: ${summary.threatLevel.toUpperCase()}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _getThreatLevelColor(summary.threatLevel),
                  ),
                ),
                Text(
                  '${summary.activeIncidents} active incidents in your area',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getThreatLevelColor(String threatLevel) {
    switch (threatLevel.toLowerCase()) {
      case 'critical':
        return AppColors.error;
      case 'high':
        return Colors.red;
      case 'medium':
        return AppColors.warning;
      case 'low':
      default:
        return Colors.green;
    }
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _showSecuritySettings() {
    // Navigate to security settings
    // TODO: Implement security settings screen
    print('Show security settings');
  }
}
