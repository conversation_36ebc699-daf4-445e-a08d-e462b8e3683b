import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';

import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/theme/app_colors.dart';
import 'widgets/emergency_alert_fab.dart';
import 'widgets/incidents_heatmap_card.dart';
import 'widgets/recent_incidents_card.dart';
import 'screens/sos_map_screen.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({Key? key}) : super(key: key);

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen>
    with TickerProviderStateMixin {
  bool _isHolding = false;
  int _holdProgress = 0;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // Sample incident data for heatmap (3km radius)
  final List<Map<String, dynamic>> _incidentData = [
    {
      'lat': 37.7749,
      'lng': -122.4194,
      'type': 'theft',
      'severity': 'medium',
      'time': '2h ago',
    },
    {
      'lat': 37.7759,
      'lng': -122.4184,
      'type': 'assault',
      'severity': 'high',
      'time': '4h ago',
    },
    {
      'lat': 37.7739,
      'lng': -122.4204,
      'type': 'vandalism',
      'severity': 'low',
      'time': '6h ago',
    },
    {
      'lat': 37.7769,
      'lng': -122.4174,
      'type': 'burglary',
      'severity': 'high',
      'time': '8h ago',
    },
    {
      'lat': 37.7729,
      'lng': -122.4214,
      'type': 'suspicious',
      'severity': 'low',
      'time': '12h ago',
    },
    {
      'lat': 37.7779,
      'lng': -122.4164,
      'type': 'emergency',
      'severity': 'high',
      'time': '1d ago',
    },
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startHold() {
    setState(() {
      _isHolding = true;
      _holdProgress = 0;
    });

    _pulseController.repeat();
    _incrementHoldProgress();
  }

  void _incrementHoldProgress() {
    Future.delayed(const Duration(milliseconds: 50), () {
      if (_isHolding && mounted) {
        setState(() {
          _holdProgress++;
        });

        if (_holdProgress >= 100) {
          _completeHold();
        } else {
          _incrementHoldProgress();
        }
      }
    });
  }

  void _completeHold() {
    setState(() {
      _isHolding = false;
      _holdProgress = 0;
    });

    _pulseController.stop();
    _pulseController.reset();

    // Navigate to SOS map screen
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SOSMapScreen()));
  }

  void _cancelHold() {
    setState(() {
      _isHolding = false;
      _holdProgress = 0;
    });

    _pulseController.stop();
    _pulseController.reset();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          context.l10n.security,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showSecuritySettings(),
            icon: const Icon(FluentIcons.settings_24_regular),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Incidents Heatmap - 3km radius trends
            IncidentsHeatmapCard(isDark: isDark, incidentData: _incidentData),
            SizedBox(height: 16.h),

            // Real-time Incidents - Inspired by Citizen's incident feed
            RecentIncidentsCard(isDark: isDark, incidentData: _incidentData),
            SizedBox(height: 69.h), // Bottom padding for FAB (moved lower)
          ],
        ),
      ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 30.h), // Moved FAB lower
        child: SizedBox(
          width: 170.w, // Fixed size to prevent jumping
          height: 170.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Pulse wave animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Container(
                    width:
                        _isHolding
                            ? (70.w + 100.w * _pulseAnimation.value)
                            : 70.w,
                    height:
                        _isHolding
                            ? (70.h + 100.h * _pulseAnimation.value)
                            : 70.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                          _isHolding
                              ? Border.all(
                                color: AppColors.error.withValues(
                                  alpha: 0.3 * (1 - _pulseAnimation.value),
                                ),
                                width: 2.w,
                              )
                              : null,
                    ),
                  );
                },
              ),

              // Progress indicator
              if (_isHolding)
                SizedBox(
                  width: 90.w,
                  height: 90.h,
                  child: CircularProgressIndicator(
                    value: _holdProgress / 100,
                    strokeWidth: 3.w,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.error),
                    backgroundColor: AppColors.error.withValues(alpha: 0.2),
                  ),
                ),

              // Emergency FAB
              GestureDetector(
                onLongPressStart: (_) => _startHold(),
                onLongPressEnd: (_) => _cancelHold(),
                child: EmergencyAlertFab(
                  isActive: _isHolding,
                  onPressed: null, // Disabled tap, only long press works
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  void _showSecuritySettings() {
    // Navigate to security settings
  }
}
