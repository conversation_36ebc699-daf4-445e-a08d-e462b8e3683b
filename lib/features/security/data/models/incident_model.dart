import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'incident_model.g.dart';

@HiveType(typeId: 0)
class IncidentModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String type;
  
  @HiveField(2)
  final String severity;
  
  @HiveField(3)
  final double latitude;
  
  @HiveField(4)
  final double longitude;
  
  @HiveField(5)
  final String zoneId;
  
  @HiveField(6)
  final String geohash;
  
  @HiveField(7)
  final DateTime timestamp;
  
  @HiveField(8)
  final DateTime? resolvedAt;
  
  @HiveField(9)
  final String status;
  
  @HiveField(10)
  final String? description;
  
  @HiveField(11)
  final String? reporterId;
  
  @HiveField(12)
  final Map<String, dynamic>? metadata;
  
  @HiveField(13)
  final List<String>? mediaUrls;
  
  @HiveField(14)
  final int priority;
  
  @HiveField(15)
  final bool isAnonymous;
  
  @HiveField(16)
  final DateTime? lastUpdated;

  const IncidentModel({
    required this.id,
    required this.type,
    required this.severity,
    required this.latitude,
    required this.longitude,
    required this.zoneId,
    required this.geohash,
    required this.timestamp,
    this.resolvedAt,
    required this.status,
    this.description,
    this.reporterId,
    this.metadata,
    this.mediaUrls,
    required this.priority,
    required this.isAnonymous,
    this.lastUpdated,
  });

  factory IncidentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IncidentModel(
      id: doc.id,
      type: data['type'] ?? '',
      severity: data['severity'] ?? 'low',
      latitude: (data['location']['latitude'] ?? 0.0).toDouble(),
      longitude: (data['location']['longitude'] ?? 0.0).toDouble(),
      zoneId: data['zoneId'] ?? '',
      geohash: data['geohash'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      resolvedAt: data['resolvedAt'] != null 
          ? (data['resolvedAt'] as Timestamp).toDate() 
          : null,
      status: data['status'] ?? 'active',
      description: data['description'],
      reporterId: data['reporterId'],
      metadata: data['metadata'] != null 
          ? Map<String, dynamic>.from(data['metadata']) 
          : null,
      mediaUrls: data['mediaUrls'] != null 
          ? List<String>.from(data['mediaUrls']) 
          : null,
      priority: data['priority'] ?? 1,
      isAnonymous: data['isAnonymous'] ?? false,
      lastUpdated: data['lastUpdated'] != null 
          ? (data['lastUpdated'] as Timestamp).toDate() 
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'type': type,
      'severity': severity,
      'location': {
        'latitude': latitude,
        'longitude': longitude,
      },
      'zoneId': zoneId,
      'geohash': geohash,
      'timestamp': Timestamp.fromDate(timestamp),
      'resolvedAt': resolvedAt != null ? Timestamp.fromDate(resolvedAt!) : null,
      'status': status,
      'description': description,
      'reporterId': reporterId,
      'metadata': metadata,
      'mediaUrls': mediaUrls,
      'priority': priority,
      'isAnonymous': isAnonymous,
      'lastUpdated': lastUpdated != null ? Timestamp.fromDate(lastUpdated!) : null,
    };
  }

  IncidentModel copyWith({
    String? id,
    String? type,
    String? severity,
    double? latitude,
    double? longitude,
    String? zoneId,
    String? geohash,
    DateTime? timestamp,
    DateTime? resolvedAt,
    String? status,
    String? description,
    String? reporterId,
    Map<String, dynamic>? metadata,
    List<String>? mediaUrls,
    int? priority,
    bool? isAnonymous,
    DateTime? lastUpdated,
  }) {
    return IncidentModel(
      id: id ?? this.id,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      zoneId: zoneId ?? this.zoneId,
      geohash: geohash ?? this.geohash,
      timestamp: timestamp ?? this.timestamp,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      status: status ?? this.status,
      description: description ?? this.description,
      reporterId: reporterId ?? this.reporterId,
      metadata: metadata ?? this.metadata,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      priority: priority ?? this.priority,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        severity,
        latitude,
        longitude,
        zoneId,
        geohash,
        timestamp,
        resolvedAt,
        status,
        description,
        reporterId,
        metadata,
        mediaUrls,
        priority,
        isAnonymous,
        lastUpdated,
      ];
}

// Incident types enum
enum IncidentType {
  theft,
  assault,
  vandalism,
  burglary,
  suspicious,
  emergency,
  fire,
  medical,
  accident,
  other,
}

// Incident severity enum
enum IncidentSeverity {
  low,
  medium,
  high,
  critical,
}

// Incident status enum
enum IncidentStatus {
  active,
  investigating,
  resolved,
  dismissed,
}

extension IncidentTypeExtension on IncidentType {
  String get value {
    switch (this) {
      case IncidentType.theft:
        return 'theft';
      case IncidentType.assault:
        return 'assault';
      case IncidentType.vandalism:
        return 'vandalism';
      case IncidentType.burglary:
        return 'burglary';
      case IncidentType.suspicious:
        return 'suspicious';
      case IncidentType.emergency:
        return 'emergency';
      case IncidentType.fire:
        return 'fire';
      case IncidentType.medical:
        return 'medical';
      case IncidentType.accident:
        return 'accident';
      case IncidentType.other:
        return 'other';
    }
  }
}

extension IncidentSeverityExtension on IncidentSeverity {
  String get value {
    switch (this) {
      case IncidentSeverity.low:
        return 'low';
      case IncidentSeverity.medium:
        return 'medium';
      case IncidentSeverity.high:
        return 'high';
      case IncidentSeverity.critical:
        return 'critical';
    }
  }
  
  int get priority {
    switch (this) {
      case IncidentSeverity.low:
        return 1;
      case IncidentSeverity.medium:
        return 2;
      case IncidentSeverity.high:
        return 3;
      case IncidentSeverity.critical:
        return 4;
    }
  }
}

extension IncidentStatusExtension on IncidentStatus {
  String get value {
    switch (this) {
      case IncidentStatus.active:
        return 'active';
      case IncidentStatus.investigating:
        return 'investigating';
      case IncidentStatus.resolved:
        return 'resolved';
      case IncidentStatus.dismissed:
        return 'dismissed';
    }
  }
}
