// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'incident_summary_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class IncidentSummaryModelAdapter extends TypeAdapter<IncidentSummaryModel> {
  @override
  final int typeId = 1;

  @override
  IncidentSummaryModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return IncidentSummaryModel(
      zoneId: fields[0] as String,
      totalIncidents: fields[1] as int,
      activeIncidents: fields[2] as int,
      highSeverityCount: fields[3] as int,
      mediumSeverityCount: fields[4] as int,
      lowSeverityCount: fields[5] as int,
      incidentsByType: (fields[6] as Map).cast<String, int>(),
      lastUpdated: fields[7] as DateTime,
      timeframe: fields[8] as String,
      averageResponseTime: fields[9] as double,
      resolvedIncidents: fields[10] as int,
      threatLevel: fields[11] as String,
      hotspots: (fields[12] as List).cast<HotspotModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, IncidentSummaryModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.zoneId)
      ..writeByte(1)
      ..write(obj.totalIncidents)
      ..writeByte(2)
      ..write(obj.activeIncidents)
      ..writeByte(3)
      ..write(obj.highSeverityCount)
      ..writeByte(4)
      ..write(obj.mediumSeverityCount)
      ..writeByte(5)
      ..write(obj.lowSeverityCount)
      ..writeByte(6)
      ..write(obj.incidentsByType)
      ..writeByte(7)
      ..write(obj.lastUpdated)
      ..writeByte(8)
      ..write(obj.timeframe)
      ..writeByte(9)
      ..write(obj.averageResponseTime)
      ..writeByte(10)
      ..write(obj.resolvedIncidents)
      ..writeByte(11)
      ..write(obj.threatLevel)
      ..writeByte(12)
      ..write(obj.hotspots);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncidentSummaryModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HotspotModelAdapter extends TypeAdapter<HotspotModel> {
  @override
  final int typeId = 2;

  @override
  HotspotModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HotspotModel(
      latitude: fields[0] as double,
      longitude: fields[1] as double,
      incidentCount: fields[2] as int,
      dominantType: fields[3] as String,
      radius: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, HotspotModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude)
      ..writeByte(2)
      ..write(obj.incidentCount)
      ..writeByte(3)
      ..write(obj.dominantType)
      ..writeByte(4)
      ..write(obj.radius);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HotspotModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IncidentTrendModelAdapter extends TypeAdapter<IncidentTrendModel> {
  @override
  final int typeId = 3;

  @override
  IncidentTrendModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return IncidentTrendModel(
      zoneId: fields[0] as String,
      timeframe: fields[1] as String,
      timestamp: fields[2] as DateTime,
      dataPoints: (fields[3] as List).cast<TrendDataPoint>(),
      trendDirection: fields[4] as double,
      trendStrength: fields[5] as String,
    );
  }

  @override
  void write(BinaryWriter writer, IncidentTrendModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.zoneId)
      ..writeByte(1)
      ..write(obj.timeframe)
      ..writeByte(2)
      ..write(obj.timestamp)
      ..writeByte(3)
      ..write(obj.dataPoints)
      ..writeByte(4)
      ..write(obj.trendDirection)
      ..writeByte(5)
      ..write(obj.trendStrength);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncidentTrendModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TrendDataPointAdapter extends TypeAdapter<TrendDataPoint> {
  @override
  final int typeId = 4;

  @override
  TrendDataPoint read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TrendDataPoint(
      timestamp: fields[0] as DateTime,
      incidentCount: fields[1] as int,
      severityBreakdown: (fields[2] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, TrendDataPoint obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.timestamp)
      ..writeByte(1)
      ..write(obj.incidentCount)
      ..writeByte(2)
      ..write(obj.severityBreakdown);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrendDataPointAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
