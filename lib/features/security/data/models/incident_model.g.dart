// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'incident_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class IncidentModelAdapter extends TypeAdapter<IncidentModel> {
  @override
  final int typeId = 0;

  @override
  IncidentModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return IncidentModel(
      id: fields[0] as String,
      type: fields[1] as String,
      severity: fields[2] as String,
      latitude: fields[3] as double,
      longitude: fields[4] as double,
      zoneId: fields[5] as String,
      geohash: fields[6] as String,
      timestamp: fields[7] as DateTime,
      resolvedAt: fields[8] as DateTime?,
      status: fields[9] as String,
      description: fields[10] as String?,
      reporterId: fields[11] as String?,
      metadata: (fields[12] as Map?)?.cast<String, dynamic>(),
      mediaUrls: (fields[13] as List?)?.cast<String>(),
      priority: fields[14] as int,
      isAnonymous: fields[15] as bool,
      lastUpdated: fields[16] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, IncidentModel obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.severity)
      ..writeByte(3)
      ..write(obj.latitude)
      ..writeByte(4)
      ..write(obj.longitude)
      ..writeByte(5)
      ..write(obj.zoneId)
      ..writeByte(6)
      ..write(obj.geohash)
      ..writeByte(7)
      ..write(obj.timestamp)
      ..writeByte(8)
      ..write(obj.resolvedAt)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.description)
      ..writeByte(11)
      ..write(obj.reporterId)
      ..writeByte(12)
      ..write(obj.metadata)
      ..writeByte(13)
      ..write(obj.mediaUrls)
      ..writeByte(14)
      ..write(obj.priority)
      ..writeByte(15)
      ..write(obj.isAnonymous)
      ..writeByte(16)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncidentModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
