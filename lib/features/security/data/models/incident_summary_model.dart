import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'incident_summary_model.g.dart';

@HiveType(typeId: 1)
class IncidentSummaryModel extends Equatable {
  @HiveField(0)
  final String zoneId;
  
  @HiveField(1)
  final int totalIncidents;
  
  @HiveField(2)
  final int activeIncidents;
  
  @HiveField(3)
  final int highSeverityCount;
  
  @HiveField(4)
  final int mediumSeverityCount;
  
  @HiveField(5)
  final int lowSeverityCount;
  
  @HiveField(6)
  final Map<String, int> incidentsByType;
  
  @HiveField(7)
  final DateTime lastUpdated;
  
  @HiveField(8)
  final String timeframe; // '1h', '24h', '7d', '30d'
  
  @HiveField(9)
  final double averageResponseTime;
  
  @HiveField(10)
  final int resolvedIncidents;
  
  @HiveField(11)
  final String threatLevel; // 'low', 'medium', 'high', 'critical'
  
  @HiveField(12)
  final List<HotspotModel> hotspots;

  const IncidentSummaryModel({
    required this.zoneId,
    required this.totalIncidents,
    required this.activeIncidents,
    required this.highSeverityCount,
    required this.mediumSeverityCount,
    required this.lowSeverityCount,
    required this.incidentsByType,
    required this.lastUpdated,
    required this.timeframe,
    required this.averageResponseTime,
    required this.resolvedIncidents,
    required this.threatLevel,
    required this.hotspots,
  });

  factory IncidentSummaryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IncidentSummaryModel(
      zoneId: data['zoneId'] ?? '',
      totalIncidents: data['totalIncidents'] ?? 0,
      activeIncidents: data['activeIncidents'] ?? 0,
      highSeverityCount: data['highSeverityCount'] ?? 0,
      mediumSeverityCount: data['mediumSeverityCount'] ?? 0,
      lowSeverityCount: data['lowSeverityCount'] ?? 0,
      incidentsByType: Map<String, int>.from(data['incidentsByType'] ?? {}),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      timeframe: data['timeframe'] ?? '24h',
      averageResponseTime: (data['averageResponseTime'] ?? 0.0).toDouble(),
      resolvedIncidents: data['resolvedIncidents'] ?? 0,
      threatLevel: data['threatLevel'] ?? 'low',
      hotspots: (data['hotspots'] as List<dynamic>?)
          ?.map((h) => HotspotModel.fromMap(Map<String, dynamic>.from(h)))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'zoneId': zoneId,
      'totalIncidents': totalIncidents,
      'activeIncidents': activeIncidents,
      'highSeverityCount': highSeverityCount,
      'mediumSeverityCount': mediumSeverityCount,
      'lowSeverityCount': lowSeverityCount,
      'incidentsByType': incidentsByType,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'timeframe': timeframe,
      'averageResponseTime': averageResponseTime,
      'resolvedIncidents': resolvedIncidents,
      'threatLevel': threatLevel,
      'hotspots': hotspots.map((h) => h.toMap()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        zoneId,
        totalIncidents,
        activeIncidents,
        highSeverityCount,
        mediumSeverityCount,
        lowSeverityCount,
        incidentsByType,
        lastUpdated,
        timeframe,
        averageResponseTime,
        resolvedIncidents,
        threatLevel,
        hotspots,
      ];
}

@HiveType(typeId: 2)
class HotspotModel extends Equatable {
  @HiveField(0)
  final double latitude;
  
  @HiveField(1)
  final double longitude;
  
  @HiveField(2)
  final int incidentCount;
  
  @HiveField(3)
  final String dominantType;
  
  @HiveField(4)
  final double radius; // in meters

  const HotspotModel({
    required this.latitude,
    required this.longitude,
    required this.incidentCount,
    required this.dominantType,
    required this.radius,
  });

  factory HotspotModel.fromMap(Map<String, dynamic> map) {
    return HotspotModel(
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      incidentCount: map['incidentCount'] ?? 0,
      dominantType: map['dominantType'] ?? '',
      radius: (map['radius'] ?? 100.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'incidentCount': incidentCount,
      'dominantType': dominantType,
      'radius': radius,
    };
  }

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        incidentCount,
        dominantType,
        radius,
      ];
}

@HiveType(typeId: 3)
class IncidentTrendModel extends Equatable {
  @HiveField(0)
  final String zoneId;
  
  @HiveField(1)
  final String timeframe; // '5m', '15m', '1h', '1d'
  
  @HiveField(2)
  final DateTime timestamp;
  
  @HiveField(3)
  final List<TrendDataPoint> dataPoints;
  
  @HiveField(4)
  final double trendDirection; // -1 to 1, negative = decreasing, positive = increasing
  
  @HiveField(5)
  final String trendStrength; // 'weak', 'moderate', 'strong'

  const IncidentTrendModel({
    required this.zoneId,
    required this.timeframe,
    required this.timestamp,
    required this.dataPoints,
    required this.trendDirection,
    required this.trendStrength,
  });

  factory IncidentTrendModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IncidentTrendModel(
      zoneId: data['zoneId'] ?? '',
      timeframe: data['timeframe'] ?? '1h',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      dataPoints: (data['dataPoints'] as List<dynamic>?)
          ?.map((d) => TrendDataPoint.fromMap(Map<String, dynamic>.from(d)))
          .toList() ?? [],
      trendDirection: (data['trendDirection'] ?? 0.0).toDouble(),
      trendStrength: data['trendStrength'] ?? 'weak',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'zoneId': zoneId,
      'timeframe': timeframe,
      'timestamp': Timestamp.fromDate(timestamp),
      'dataPoints': dataPoints.map((d) => d.toMap()).toList(),
      'trendDirection': trendDirection,
      'trendStrength': trendStrength,
    };
  }

  @override
  List<Object?> get props => [
        zoneId,
        timeframe,
        timestamp,
        dataPoints,
        trendDirection,
        trendStrength,
      ];
}

@HiveType(typeId: 4)
class TrendDataPoint extends Equatable {
  @HiveField(0)
  final DateTime timestamp;
  
  @HiveField(1)
  final int incidentCount;
  
  @HiveField(2)
  final Map<String, int> severityBreakdown;

  const TrendDataPoint({
    required this.timestamp,
    required this.incidentCount,
    required this.severityBreakdown,
  });

  factory TrendDataPoint.fromMap(Map<String, dynamic> map) {
    return TrendDataPoint(
      timestamp: (map['timestamp'] as Timestamp).toDate(),
      incidentCount: map['incidentCount'] ?? 0,
      severityBreakdown: Map<String, int>.from(map['severityBreakdown'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timestamp': Timestamp.fromDate(timestamp),
      'incidentCount': incidentCount,
      'severityBreakdown': severityBreakdown,
    };
  }

  @override
  List<Object?> get props => [
        timestamp,
        incidentCount,
        severityBreakdown,
      ];
}
