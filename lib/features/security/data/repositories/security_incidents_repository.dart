import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:rxdart/rxdart.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/services/location_service.dart';
import '../models/incident_model.dart';
import '../models/incident_summary_model.dart';

abstract class SecurityIncidentsRepository {
  Stream<List<IncidentModel>> getIncidentsStream(String zoneId);
  Stream<IncidentSummaryModel?> getSummaryStream(String zoneId);
  Stream<List<IncidentTrendModel>> getTrendsStream(String zoneId);
  Future<Either<Failure, void>> reportIncident(IncidentModel incident);
  Future<Either<Failure, void>> updateIncident(String id, Map<String, dynamic> updates);
  Future<Either<Failure, List<IncidentModel>>> getNearbyIncidents(Position position, double radiusKm);
  Future<Either<Failure, void>> syncOfflineData();
  Future<void> cacheIncidents(List<IncidentModel> incidents);
  Future<List<IncidentModel>> getCachedIncidents();
}

class SecurityIncidentsRepositoryImpl implements SecurityIncidentsRepository {
  final FirebaseFirestore _firestore;
  final Box<IncidentModel> _incidentsBox;
  final Box<IncidentSummaryModel> _summariesBox;
  final Box<IncidentTrendModel> _trendsBox;
  final LocationService _locationService;
  
  // Stream controllers for real-time updates
  final _incidentsController = BehaviorSubject<List<IncidentModel>>();
  final _summaryController = BehaviorSubject<IncidentSummaryModel?>();
  final _trendsController = BehaviorSubject<List<IncidentTrendModel>>();
  
  // Cache management
  static const int _maxCacheSize = 100;
  static const Duration _cacheExpiry = Duration(hours: 24);
  
  SecurityIncidentsRepositoryImpl({
    required FirebaseFirestore firestore,
    required Box<IncidentModel> incidentsBox,
    required Box<IncidentSummaryModel> summariesBox,
    required Box<IncidentTrendModel> trendsBox,
    required LocationService locationService,
  }) : _firestore = firestore,
       _incidentsBox = incidentsBox,
       _summariesBox = summariesBox,
       _trendsBox = trendsBox,
       _locationService = locationService;

  @override
  Stream<List<IncidentModel>> getIncidentsStream(String zoneId) {
    // Combine Firestore stream with cached data
    final firestoreStream = _firestore
        .collection('security_incidents')
        .where('zoneId', isEqualTo: zoneId)
        .where('timestamp', isGreaterThan: Timestamp.fromDate(
          DateTime.now().subtract(const Duration(days: 7))
        ))
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => IncidentModel.fromFirestore(doc))
            .toList())
        .handleError((error) {
          print('Firestore stream error: $error');
          // Fall back to cached data on error
          _loadCachedIncidents();
        });

    // Start with cached data, then switch to live data
    final cachedIncidents = getCachedIncidents();
    
    return Rx.merge([
      Stream.fromFuture(cachedIncidents).where((incidents) => incidents.isNotEmpty),
      firestoreStream,
    ]).distinct().doOnData((incidents) {
      // Cache new data
      cacheIncidents(incidents);
      _incidentsController.add(incidents);
    });
  }

  @override
  Stream<IncidentSummaryModel?> getSummaryStream(String zoneId) {
    final firestoreStream = _firestore
        .collection('incident_realtime_summary')
        .doc(zoneId)
        .snapshots()
        .map((doc) => doc.exists 
            ? IncidentSummaryModel.fromFirestore(doc) 
            : null)
        .handleError((error) {
          print('Summary stream error: $error');
          _loadCachedSummary(zoneId);
        });

    // Start with cached data
    final cachedSummary = _summariesBox.get(zoneId);
    
    return Rx.merge([
      if (cachedSummary != null) Stream.value(cachedSummary),
      firestoreStream,
    ]).distinct().doOnData((summary) {
      if (summary != null) {
        _summariesBox.put(zoneId, summary);
        _summaryController.add(summary);
      }
    });
  }

  @override
  Stream<List<IncidentTrendModel>> getTrendsStream(String zoneId) {
    final firestoreStream = _firestore
        .collection('incident_trends')
        .where('zoneId', isEqualTo: zoneId)
        .where('timestamp', isGreaterThan: Timestamp.fromDate(
          DateTime.now().subtract(const Duration(hours: 24))
        ))
        .orderBy('timestamp', descending: true)
        .limit(20)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => IncidentTrendModel.fromFirestore(doc))
            .toList())
        .handleError((error) {
          print('Trends stream error: $error');
          _loadCachedTrends(zoneId);
        });

    // Start with cached data
    final cachedTrends = _trendsBox.values
        .where((trend) => trend.zoneId == zoneId)
        .toList();
    
    return Rx.merge([
      if (cachedTrends.isNotEmpty) Stream.value(cachedTrends),
      firestoreStream,
    ]).distinct().doOnData((trends) {
      // Cache trends
      for (final trend in trends) {
        final key = '${trend.zoneId}_${trend.timeframe}_${trend.timestamp.millisecondsSinceEpoch}';
        _trendsBox.put(key, trend);
      }
      _trendsController.add(trends);
    });
  }

  @override
  Future<Either<Failure, void>> reportIncident(IncidentModel incident) async {
    try {
      // Add to Firestore
      await _firestore
          .collection('security_incidents')
          .doc(incident.id)
          .set(incident.toFirestore());
      
      // Cache locally for offline access
      await _incidentsBox.put(incident.id, incident);
      
      return const Right(null);
    } catch (e) {
      // Store for offline sync
      await _storeForOfflineSync(incident);
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateIncident(String id, Map<String, dynamic> updates) async {
    try {
      await _firestore
          .collection('security_incidents')
          .doc(id)
          .update(updates);
      
      // Update cached version
      final cachedIncident = _incidentsBox.get(id);
      if (cachedIncident != null) {
        final updatedIncident = cachedIncident.copyWith(
          status: updates['status'],
          lastUpdated: updates['lastUpdated'] != null 
              ? DateTime.fromMillisecondsSinceEpoch(updates['lastUpdated'])
              : null,
        );
        await _incidentsBox.put(id, updatedIncident);
      }
      
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<IncidentModel>>> getNearbyIncidents(
    Position position, 
    double radiusKm
  ) async {
    try {
      // Use GeoFlutterFire for geospatial queries
      final center = GeoFirePoint(position.latitude, position.longitude);
      
      final query = _firestore
          .collection('security_incidents')
          .where('timestamp', isGreaterThan: Timestamp.fromDate(
            DateTime.now().subtract(const Duration(hours: 24))
          ));
      
      final snapshot = await query.get();
      
      final incidents = snapshot.docs
          .map((doc) => IncidentModel.fromFirestore(doc))
          .where((incident) {
            final distance = Geolocator.distanceBetween(
              position.latitude,
              position.longitude,
              incident.latitude,
              incident.longitude,
            );
            return distance <= radiusKm * 1000; // Convert km to meters
          })
          .toList();
      
      return Right(incidents);
    } catch (e) {
      // Fall back to cached data
      final cachedIncidents = await getCachedIncidents();
      final nearbyIncidents = cachedIncidents.where((incident) {
        final distance = Geolocator.distanceBetween(
          position.latitude,
          position.longitude,
          incident.latitude,
          incident.longitude,
        );
        return distance <= radiusKm * 1000;
      }).toList();
      
      return Right(nearbyIncidents);
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineData() async {
    try {
      // Get pending offline incidents
      final offlineBox = await Hive.openBox('offline_incidents');
      final pendingIncidents = offlineBox.values.cast<Map<String, dynamic>>();
      
      for (final incidentData in pendingIncidents) {
        try {
          await _firestore
              .collection('security_incidents')
              .doc(incidentData['id'])
              .set(incidentData);
          
          // Remove from offline queue
          await offlineBox.delete(incidentData['id']);
        } catch (e) {
          print('Failed to sync incident ${incidentData['id']}: $e');
        }
      }
      
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<void> cacheIncidents(List<IncidentModel> incidents) async {
    try {
      // Implement LRU cache
      if (_incidentsBox.length > _maxCacheSize) {
        await _cleanupOldCache();
      }
      
      for (final incident in incidents) {
        await _incidentsBox.put(incident.id, incident);
      }
    } catch (e) {
      print('Error caching incidents: $e');
    }
  }

  @override
  Future<List<IncidentModel>> getCachedIncidents() async {
    try {
      final now = DateTime.now();
      return _incidentsBox.values
          .where((incident) => 
            now.difference(incident.timestamp).inHours < _cacheExpiry.inHours)
          .toList()
        ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      print('Error getting cached incidents: $e');
      return [];
    }
  }

  // Private helper methods
  Future<void> _storeForOfflineSync(IncidentModel incident) async {
    try {
      final offlineBox = await Hive.openBox('offline_incidents');
      await offlineBox.put(incident.id, incident.toFirestore());
    } catch (e) {
      print('Error storing incident for offline sync: $e');
    }
  }

  Future<void> _cleanupOldCache() async {
    try {
      final incidents = _incidentsBox.values.toList()
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      // Remove oldest 20% of incidents
      final removeCount = (_maxCacheSize * 0.2).round();
      for (int i = 0; i < removeCount && i < incidents.length; i++) {
        await _incidentsBox.delete(incidents[i].id);
      }
    } catch (e) {
      print('Error cleaning up cache: $e');
    }
  }

  void _loadCachedIncidents() {
    getCachedIncidents().then((incidents) {
      if (incidents.isNotEmpty) {
        _incidentsController.add(incidents);
      }
    });
  }

  void _loadCachedSummary(String zoneId) {
    final cachedSummary = _summariesBox.get(zoneId);
    if (cachedSummary != null) {
      _summaryController.add(cachedSummary);
    }
  }

  void _loadCachedTrends(String zoneId) {
    final cachedTrends = _trendsBox.values
        .where((trend) => trend.zoneId == zoneId)
        .toList();
    if (cachedTrends.isNotEmpty) {
      _trendsController.add(cachedTrends);
    }
  }

  void dispose() {
    _incidentsController.close();
    _summaryController.close();
    _trendsController.close();
  }
}
