import 'dart:async';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/incident_model.dart';
import '../models/incident_summary_model.dart';

class SecurityCacheService {
  static const String _incidentsBoxName = 'security_incidents';
  static const String _summariesBoxName = 'incident_summaries';
  static const String _trendsBoxName = 'incident_trends';
  static const String _preferencesBoxName = 'security_preferences';
  static const String _offlineQueueBoxName = 'offline_queue';
  
  late Box<IncidentModel> _incidentsBox;
  late Box<IncidentSummaryModel> _summariesBox;
  late Box<IncidentTrendModel> _trendsBox;
  late Box<Map<String, dynamic>> _preferencesBox;
  late Box<Map<String, dynamic>> _offlineQueueBox;
  
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = true;
  final _connectivityController = StreamController<bool>.broadcast();
  
  Stream<bool> get connectivityStream => _connectivityController.stream;
  bool get isOnline => _isOnline;

  static Future<SecurityCacheService> create() async {
    final service = SecurityCacheService._();
    await service._initialize();
    return service;
  }
  
  SecurityCacheService._();

  Future<void> _initialize() async {
    // Initialize Hive boxes
    _incidentsBox = await Hive.openBox<IncidentModel>(_incidentsBoxName);
    _summariesBox = await Hive.openBox<IncidentSummaryModel>(_summariesBoxName);
    _trendsBox = await Hive.openBox<IncidentTrendModel>(_trendsBoxName);
    _preferencesBox = await Hive.openBox<Map<String, dynamic>>(_preferencesBoxName);
    _offlineQueueBox = await Hive.openBox<Map<String, dynamic>>(_offlineQueueBoxName);
    
    // Monitor connectivity
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        final wasOnline = _isOnline;
        _isOnline = results.isNotEmpty && !results.contains(ConnectivityResult.none);

        if (!wasOnline && _isOnline) {
          // Came back online - trigger sync
          _triggerOfflineSync();
        }

        _connectivityController.add(_isOnline);
      },
    );
    
    // Check initial connectivity
    final initialConnectivity = await _connectivity.checkConnectivity();
    _isOnline = initialConnectivity.isNotEmpty && !initialConnectivity.contains(ConnectivityResult.none);
  }

  // Incident caching methods
  Future<void> cacheIncident(IncidentModel incident) async {
    await _incidentsBox.put(incident.id, incident);
  }

  Future<void> cacheIncidents(List<IncidentModel> incidents) async {
    final Map<String, IncidentModel> incidentMap = {
      for (final incident in incidents) incident.id: incident
    };
    await _incidentsBox.putAll(incidentMap);
  }

  List<IncidentModel> getCachedIncidents({
    String? zoneId,
    Duration? maxAge,
  }) {
    var incidents = _incidentsBox.values.toList();
    
    if (zoneId != null) {
      incidents = incidents.where((i) => i.zoneId == zoneId).toList();
    }
    
    if (maxAge != null) {
      final cutoff = DateTime.now().subtract(maxAge);
      incidents = incidents.where((i) => i.timestamp.isAfter(cutoff)).toList();
    }
    
    // Sort by timestamp (newest first)
    incidents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return incidents;
  }

  Future<void> removeOldIncidents({Duration? olderThan}) async {
    final cutoff = DateTime.now().subtract(olderThan ?? const Duration(days: 30));
    
    final keysToRemove = <String>[];
    for (final entry in _incidentsBox.toMap().entries) {
      if (entry.value.timestamp.isBefore(cutoff)) {
        keysToRemove.add(entry.key);
      }
    }
    
    await _incidentsBox.deleteAll(keysToRemove);
  }

  // Summary caching methods
  Future<void> cacheSummary(String zoneId, IncidentSummaryModel summary) async {
    await _summariesBox.put(zoneId, summary);
  }

  IncidentSummaryModel? getCachedSummary(String zoneId) {
    return _summariesBox.get(zoneId);
  }

  // Trends caching methods
  Future<void> cacheTrend(IncidentTrendModel trend) async {
    final key = '${trend.zoneId}_${trend.timeframe}_${trend.timestamp.millisecondsSinceEpoch}';
    await _trendsBox.put(key, trend);
  }

  Future<void> cacheTrends(List<IncidentTrendModel> trends) async {
    final Map<String, IncidentTrendModel> trendMap = {
      for (final trend in trends)
        '${trend.zoneId}_${trend.timeframe}_${trend.timestamp.millisecondsSinceEpoch}': trend
    };
    await _trendsBox.putAll(trendMap);
  }

  List<IncidentTrendModel> getCachedTrends({
    String? zoneId,
    String? timeframe,
    Duration? maxAge,
  }) {
    var trends = _trendsBox.values.toList();
    
    if (zoneId != null) {
      trends = trends.where((t) => t.zoneId == zoneId).toList();
    }
    
    if (timeframe != null) {
      trends = trends.where((t) => t.timeframe == timeframe).toList();
    }
    
    if (maxAge != null) {
      final cutoff = DateTime.now().subtract(maxAge);
      trends = trends.where((t) => t.timestamp.isAfter(cutoff)).toList();
    }
    
    // Sort by timestamp (newest first)
    trends.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return trends;
  }

  // User preferences
  Future<void> savePreference(String key, dynamic value) async {
    await _preferencesBox.put(key, {'value': value, 'timestamp': DateTime.now().millisecondsSinceEpoch});
  }

  T? getPreference<T>(String key) {
    final data = _preferencesBox.get(key);
    return data?['value'] as T?;
  }

  Future<void> saveUserZones(List<String> zones) async {
    await savePreference('authorized_zones', zones);
  }

  List<String> getUserZones() {
    return getPreference<List<dynamic>>('authorized_zones')?.cast<String>() ?? [];
  }

  Future<void> saveNotificationSettings(Map<String, bool> settings) async {
    await savePreference('notification_settings', settings);
  }

  Map<String, bool> getNotificationSettings() {
    return getPreference<Map<String, dynamic>>('notification_settings')?.cast<String, bool>() ?? {};
  }

  // Offline queue management
  Future<void> queueForOfflineSync(String operation, Map<String, dynamic> data) async {
    final queueItem = {
      'operation': operation,
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'retryCount': 0,
    };
    
    final key = '${operation}_${DateTime.now().millisecondsSinceEpoch}';
    await _offlineQueueBox.put(key, queueItem);
  }

  List<Map<String, dynamic>> getOfflineQueue() {
    return _offlineQueueBox.values.toList();
  }

  Future<void> removeFromOfflineQueue(String key) async {
    await _offlineQueueBox.delete(key);
  }

  Future<void> clearOfflineQueue() async {
    await _offlineQueueBox.clear();
  }

  Future<void> incrementRetryCount(String key) async {
    final item = _offlineQueueBox.get(key);
    if (item != null) {
      item['retryCount'] = (item['retryCount'] ?? 0) + 1;
      await _offlineQueueBox.put(key, item);
    }
  }

  // Cache statistics
  Map<String, dynamic> getCacheStatistics() {
    return {
      'incidents_count': _incidentsBox.length,
      'summaries_count': _summariesBox.length,
      'trends_count': _trendsBox.length,
      'preferences_count': _preferencesBox.length,
      'offline_queue_count': _offlineQueueBox.length,
      'is_online': _isOnline,
      'cache_size_mb': _calculateCacheSize(),
    };
  }

  double _calculateCacheSize() {
    // Rough estimation of cache size in MB
    final incidentsSize = _incidentsBox.length * 2; // ~2KB per incident
    final summariesSize = _summariesBox.length * 1; // ~1KB per summary
    final trendsSize = _trendsBox.length * 0.5; // ~0.5KB per trend
    
    return (incidentsSize + summariesSize + trendsSize) / 1024; // Convert to MB
  }

  // Cache cleanup
  Future<void> performCacheCleanup() async {
    // Remove old incidents (older than 30 days)
    await removeOldIncidents(olderThan: const Duration(days: 30));
    
    // Remove old trends (older than 7 days)
    final cutoff = DateTime.now().subtract(const Duration(days: 7));
    final trendKeysToRemove = <String>[];
    
    for (final entry in _trendsBox.toMap().entries) {
      if (entry.value.timestamp.isBefore(cutoff)) {
        trendKeysToRemove.add(entry.key);
      }
    }
    
    await _trendsBox.deleteAll(trendKeysToRemove);
    
    // Remove failed offline queue items (retry count > 5)
    final queueKeysToRemove = <String>[];
    
    for (final entry in _offlineQueueBox.toMap().entries) {
      if ((entry.value['retryCount'] ?? 0) > 5) {
        queueKeysToRemove.add(entry.key);
      }
    }
    
    await _offlineQueueBox.deleteAll(queueKeysToRemove);
  }

  void _triggerOfflineSync() {
    // This would typically trigger a sync operation
    // Implementation depends on your sync strategy
    print('Connectivity restored - triggering offline sync');
  }

  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    await _connectivityController.close();
    
    // Close all boxes
    await _incidentsBox.close();
    await _summariesBox.close();
    await _trendsBox.close();
    await _preferencesBox.close();
    await _offlineQueueBox.close();
  }
}
