import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';

abstract class AuthRepository {
  /// Stream of [User] which will emit the current user when auth state changes
  Stream<User> get user;

  /// Returns the current cached user or empty user if not authenticated
  Future<Either<Failure, User>> getCurrentUser();

  /// Creates a new user with the provided [email] and [password]
  Future<Either<Failure, User>> signUp({
    required String email,
    required String password,
    String? displayName,
  });

  /// Starts the Sign In with Google process
  Future<Either<Failure, User>> signInWithGoogle();

  /// Starts the Sign In with Facebook process
  Future<Either<Failure, User>> signInWithFacebook();

  /// Signs in with the provided [email] and [password]
  Future<Either<Failure, User>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Signs out the current user
  Future<Either<Failure, void>> signOut();

  /// Sends a password reset email to the provided [email]
  Future<Either<Failure, void>> resetPassword(String email);

  /// Links Google account to the current user
  Future<Either<Failure, User>> linkWithGoogle();

  /// Links Facebook account to the current user
  Future<Either<Failure, User>> linkWithFacebook();

  /// Unlinks a provider from the current user
  Future<Either<Failure, User>> unlinkProvider(String providerId);

  /// Gets the list of linked providers for the current user
  Future<Either<Failure, List<String>>> getLinkedProviders();

  /// Updates the current user's profile photo
  Future<Either<Failure, User>> updateProfilePhoto(String photoURL);

  /// Updates the current user's display name
  Future<Either<Failure, User>> updateDisplayName(String displayName);
}
