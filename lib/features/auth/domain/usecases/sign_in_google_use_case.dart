import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class SignInGoogleUseCase {

  const SignInGoogleUseCase(this._authRepository);
  final AuthRepository _authRepository;

  Future<Either<Failure, User>> call() async {
    return await _authRepository.signInWithGoogle();
  }
} 
