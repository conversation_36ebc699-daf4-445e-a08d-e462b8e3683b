import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class SignUpUseCase {
  final AuthRepository _authRepository;

  const SignUpUseCase(this._authRepository);

  Future<Either<Failure, User>> call({
    required String email,
    required String password,
    String? displayName,
  }) {
    return _authRepository.signUp(
      email: email,
      password: password,
      displayName: displayName,
    );
  }
} 
