import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class SignOutUseCase {
  final AuthRepository _authRepository;

  const SignOutUseCase(this._authRepository);

  Future<Either<Failure, void>> call() async {
    return await _authRepository.signOut();
  }
} 