import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class UnlinkProviderUseCase {
  final AuthRepository _authRepository;

  const UnlinkProviderUseCase(this._authRepository);

  Future<Either<Failure, User>> call(String providerId) async {
    return await _authRepository.unlinkProvider(providerId);
  }
}
