import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class ResetPasswordUseCase {
  final AuthRepository _authRepository;

  const ResetPasswordUseCase(this._authRepository);

  Future<Either<Failure, void>> call({required String email}) async {
    return await _authRepository.resetPassword(email);
  }
} 
