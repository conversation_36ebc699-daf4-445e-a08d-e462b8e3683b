import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class GetLinkedProvidersUseCase {
  final AuthRepository _authRepository;

  const GetLinkedProvidersUseCase(this._authRepository);

  Future<Either<Failure, List<String>>> call() async {
    return await _authRepository.getLinkedProviders();
  }
}
