import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class LinkFacebookUseCase {
  final AuthRepository _authRepository;

  const LinkFacebookUseCase(this._authRepository);

  Future<Either<Failure, User>> call() async {
    return await _authRepository.linkWithFacebook();
  }
}
