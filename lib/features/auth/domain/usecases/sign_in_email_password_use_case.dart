import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@lazySingleton
class SignInEmailPasswordUseCase {

  const SignInEmailPasswordUseCase(this._authRepository);
  final AuthRepository _authRepository;

  Future<Either<Failure, User>> call({
    required String email,
    required String password,
  }) async {
    return _authRepository.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
  }
} 
