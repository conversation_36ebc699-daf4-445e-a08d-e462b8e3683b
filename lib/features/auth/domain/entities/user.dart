import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final bool isAnonymous;
  final bool emailVerified;
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    this.email,
    this.displayName,
    this.photoURL,
    this.isAnonymous = false,
    this.emailVerified = false,
    this.metadata,
  });

  User copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    bool? isAnonymous,
    bool? emailVerified,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      emailVerified: emailVerified ?? this.emailVerified,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoURL,
        isAnonymous,
        emailVerified,
        metadata,
      ];
  
  static const empty = User(id: '');
  
  bool get isEmpty => this == User.empty;
  bool get isNotEmpty => this != User.empty;
} 