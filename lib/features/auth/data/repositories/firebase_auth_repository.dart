// ignore_for_file: document_ignores, lines_longer_than_80_chars, prefer_const_constructors, avoid_catches_without_on_clauses

import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/features/auth/data/models/user_model.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart' as domain;
import 'package:respublicaseguridad/features/auth/domain/repositories/auth_repository.dart';

@LazySingleton(as: AuthRepository)
class FirebaseAuthRepository implements AuthRepository {

  FirebaseAuthRepository(
    this._firebaseAuth,
    this._googleSignIn,
    this._facebookAuth,
  );
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final FacebookAuth _facebookAuth;

  @override
  Stream<domain.User> get user {
    return _firebaseAuth.authStateChanges().map((firebaseUser) {
      return firebaseUser == null
          ? domain.User.empty
          : UserModel.fromFirebase(firebaseUser);
    });
  }

  @override
  Future<Either<Failure, domain.User>> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      return Right(
        firebaseUser == null
            ? domain.User.empty
            : UserModel.fromFirebase(firebaseUser),
      );
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.User>> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update user profile with name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await userCredential.user?.updateDisplayName(displayName);
        
        // Reload user to get updated profile data
        await userCredential.user?.reload();
      }
      
      final updatedUser = _firebaseAuth.currentUser;
      
      return Right(updatedUser == null
          ? domain.User.empty
          : UserModel.fromFirebase(updatedUser));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(message: e.message ?? 'Authentication failed', code: int.tryParse(e.code)));
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.User>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      return Right(userCredential.user == null
          ? domain.User.empty
          : UserModel.fromFirebase(userCredential.user!));
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(message: e.message ?? 'Authentication failed', code: int.tryParse(e.code)));
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, domain.User>> signInWithGoogle() async {
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return Left(AuthFailure(message: 'Google sign in was canceled'));
      }

      final googleAuth = await googleUser.authentication;
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      return Right(userCredential.user == null
          ? domain.User.empty
          : UserModel.fromFirebase(userCredential.user!));
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on Google sign in: ${e.code} - ${e.message}');

      // Handle specific error codes with user-friendly messages
      String errorMessage;
      switch (e.code) {
        case 'account-exists-with-different-credential':
          errorMessage = 'Ya existe una cuenta con este correo electrónico pero con un método de inicio de sesión diferente. Por favor, inicia sesión con tu método original (correo y contraseña) o contacta soporte para vincular las cuentas.';
          break;
        case 'invalid-credential':
          errorMessage = 'Las credenciales de Google no son válidas. Inténtalo de nuevo.';
          break;
        case 'operation-not-allowed':
          errorMessage = 'El inicio de sesión con Google no está habilitado. Contacta soporte.';
          break;
        case 'user-disabled':
          errorMessage = 'Esta cuenta ha sido deshabilitada. Contacta soporte.';
          break;
        case 'user-not-found':
          errorMessage = 'No se encontró ningún usuario con estas credenciales.';
          break;
        case 'wrong-password':
          errorMessage = 'Credenciales incorrectas. Inténtalo de nuevo.';
          break;
        case 'too-many-requests':
          errorMessage = 'Demasiados intentos fallidos. Inténtalo más tarde.';
          break;
        case 'network-request-failed':
          errorMessage = 'Error de conexión. Verifica tu conexión a Internet.';
          break;
        default:
          errorMessage = e.message ?? 'Error al iniciar sesión con Google.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on Google sign in: $e');
      return Left(AuthFailure(message: 'Error inesperado al iniciar sesión con Google.'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> signInWithFacebook() async {
    try {
      final result = await _facebookAuth.login();

      if (result.status != LoginStatus.success) {
        return Left(AuthFailure(message: 'Facebook sign in was canceled'));
      }

      final accessToken = result.accessToken;

      if (accessToken == null) {
        return Left(AuthFailure(message: 'Facebook access token is null'));
      }

      final credential = firebase_auth.FacebookAuthProvider.credential(
        accessToken.token,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      return Right(userCredential.user == null
          ? domain.User.empty
          : UserModel.fromFirebase(userCredential.user!));
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on Facebook sign in: ${e.code} - ${e.message}');

      // Handle specific error codes with user-friendly messages
      String errorMessage;
      switch (e.code) {
        case 'account-exists-with-different-credential':
          errorMessage = 'Ya existe una cuenta con este correo electrónico pero con un método de inicio de sesión diferente. Por favor, inicia sesión con tu método original (correo y contraseña) o contacta soporte para vincular las cuentas.';
          break;
        case 'invalid-credential':
          errorMessage = 'Las credenciales de Facebook no son válidas. Inténtalo de nuevo.';
          break;
        case 'operation-not-allowed':
          errorMessage = 'El inicio de sesión con Facebook no está habilitado. Contacta soporte.';
          break;
        case 'user-disabled':
          errorMessage = 'Esta cuenta ha sido deshabilitada. Contacta soporte.';
          break;
        case 'user-not-found':
          errorMessage = 'No se encontró ningún usuario con estas credenciales.';
          break;
        case 'wrong-password':
          errorMessage = 'Credenciales incorrectas. Inténtalo de nuevo.';
          break;
        case 'too-many-requests':
          errorMessage = 'Demasiados intentos fallidos. Inténtalo más tarde.';
          break;
        case 'network-request-failed':
          errorMessage = 'Error de conexión. Verifica tu conexión a Internet.';
          break;
        default:
          errorMessage = e.message ?? 'Error al iniciar sesión con Facebook.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on Facebook sign in: $e');
      return Left(AuthFailure(message: 'Error inesperado al iniciar sesión con Facebook.'));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
        _facebookAuth.logOut(),
      ]);
      return const Right(null);
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      print("domeee");
      return const Right(null);
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on reset password: ${e.code} - ${e.message}');

      // Handle specific error codes with user-friendly messages
      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'No hay ningún usuario registrado con este correo electrónico.';
          break;
        case 'invalid-email':
          errorMessage = 'El formato del correo electrónico no es válido.';
          break;
        case 'too-many-requests':
          errorMessage = 'Demasiados intentos fallidos. Por favor, inténtalo más tarde.';
          break;
        case 'network-request-failed':
          errorMessage = 'Error de conexión. Verifica tu conexión a Internet.';
          break;
        default:
          errorMessage = e.message ?? 'Ha ocurrido un error al restablecer la contraseña.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on reset password: $e');
      return Left(AuthFailure(message: 'Ha ocurrido un error inesperado. Inténtalo de nuevo.'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> linkWithGoogle() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(AuthFailure(message: 'No hay usuario autenticado'));
      }

      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return Left(AuthFailure(message: 'Google sign in was canceled'));
      }

      final googleAuth = await googleUser.authentication;
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await currentUser.linkWithCredential(credential);

      return Right(userCredential.user == null
          ? domain.User.empty
          : UserModel.fromFirebase(userCredential.user!));
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on Google link: ${e.code} - ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'provider-already-linked':
          errorMessage = 'Esta cuenta de Google ya está vinculada a tu cuenta.';
          break;
        case 'credential-already-in-use':
          errorMessage = 'Esta cuenta de Google ya está siendo utilizada por otro usuario.';
          break;
        case 'email-already-in-use':
          errorMessage = 'El correo electrónico de esta cuenta de Google ya está en uso.';
          break;
        case 'invalid-credential':
          errorMessage = 'Las credenciales de Google no son válidas.';
          break;
        case 'operation-not-allowed':
          errorMessage = 'La vinculación con Google no está habilitada.';
          break;
        case 'user-disabled':
          errorMessage = 'Esta cuenta ha sido deshabilitada.';
          break;
        default:
          errorMessage = e.message ?? 'Error al vincular cuenta de Google.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on Google link: $e');
      return Left(AuthFailure(message: 'Error inesperado al vincular cuenta de Google.'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> linkWithFacebook() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(AuthFailure(message: 'No hay usuario autenticado'));
      }

      final result = await _facebookAuth.login();

      if (result.status != LoginStatus.success) {
        return Left(AuthFailure(message: 'Facebook sign in was canceled'));
      }

      final accessToken = result.accessToken;

      if (accessToken == null) {
        return Left(AuthFailure(message: 'Facebook access token is null'));
      }

      final credential = firebase_auth.FacebookAuthProvider.credential(
        accessToken.token,
      );

      final userCredential = await currentUser.linkWithCredential(credential);

      return Right(userCredential.user == null
          ? domain.User.empty
          : UserModel.fromFirebase(userCredential.user!));
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on Facebook link: ${e.code} - ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'provider-already-linked':
          errorMessage = 'Esta cuenta de Facebook ya está vinculada a tu cuenta.';
          break;
        case 'credential-already-in-use':
          errorMessage = 'Esta cuenta de Facebook ya está siendo utilizada por otro usuario.';
          break;
        case 'email-already-in-use':
          errorMessage = 'El correo electrónico de esta cuenta de Facebook ya está en uso.';
          break;
        case 'invalid-credential':
          errorMessage = 'Las credenciales de Facebook no son válidas.';
          break;
        case 'operation-not-allowed':
          errorMessage = 'La vinculación con Facebook no está habilitada.';
          break;
        case 'user-disabled':
          errorMessage = 'Esta cuenta ha sido deshabilitada.';
          break;
        default:
          errorMessage = e.message ?? 'Error al vincular cuenta de Facebook.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on Facebook link: $e');
      return Left(AuthFailure(message: 'Error inesperado al vincular cuenta de Facebook.'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> unlinkProvider(String providerId) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(AuthFailure(message: 'No hay usuario autenticado'));
      }

      final updatedUser = await currentUser.unlink(providerId);

      return Right(UserModel.fromFirebase(updatedUser));
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('Firebase Auth Exception on unlink provider: ${e.code} - ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'no-such-provider':
          errorMessage = 'Este proveedor no está vinculado a tu cuenta.';
          break;
        case 'requires-recent-login':
          errorMessage = 'Por seguridad, necesitas iniciar sesión nuevamente antes de desvincular esta cuenta.';
          break;
        default:
          errorMessage = e.message ?? 'Error al desvincular proveedor.';
      }

      return Left(AuthFailure(message: errorMessage, code: int.tryParse(e.code)));
    } catch (e) {
      print('General exception on unlink provider: $e');
      return Left(AuthFailure(message: 'Error inesperado al desvincular proveedor.'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getLinkedProviders() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(AuthFailure(message: 'No hay usuario autenticado'));
      }

      final providers = currentUser.providerData.map((info) => info.providerId).toList();
      return Right(providers);
    } catch (e) {
      print('General exception on get linked providers: $e');
      return Left(AuthFailure(message: 'Error al obtener proveedores vinculados.'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> updateProfilePhoto(String photoURL) async {
    try {
      print('🔐 FirebaseAuthRepository: updateProfilePhoto called with URL: $photoURL');

      final user = _firebaseAuth.currentUser;
      if (user == null) {
        print('❌ No authenticated user found');
        return Left(AuthFailure(message: 'Usuario no autenticado'));
      }

      print('👤 Current user: ${user.uid}');
      print('🔄 Updating photo URL...');
      await user.updatePhotoURL(photoURL);

      print('🔄 Reloading user...');
      await user.reload();

      final updatedUser = _firebaseAuth.currentUser;
      print('✅ Profile photo updated successfully');
      print('🖼️ New photo URL: ${updatedUser?.photoURL}');

      return Right(
        updatedUser == null
            ? domain.User.empty
            : UserModel.fromFirebase(updatedUser),
      );
    } on firebase_auth.FirebaseAuthException catch (e) {
      print('❌ Firebase Auth Exception: ${e.code} - ${e.message}');
      return Left(AuthFailure(message: e.message ?? 'Error al actualizar foto de perfil'));
    } catch (e) {
      print('❌ General Exception in updateProfilePhoto: $e');
      return Left(AuthFailure(message: 'Error al actualizar foto de perfil'));
    }
  }

  @override
  Future<Either<Failure, domain.User>> updateDisplayName(String displayName) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'Usuario no autenticado'));
      }

      await user.updateDisplayName(displayName);
      await user.reload();

      final updatedUser = _firebaseAuth.currentUser;
      return Right(
        updatedUser == null
            ? domain.User.empty
            : UserModel.fromFirebase(updatedUser),
      );
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(AuthFailure(message: e.message ?? 'Error al actualizar nombre'));
    } catch (e) {
      return Left(AuthFailure(message: 'Error al actualizar nombre'));
    }
  }
}