import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

class UserModel extends User {
  const UserModel({
    required String id,
    String? email,
    String? displayName,
    String? photoURL,
    bool isAnonymous = false,
    bool emailVerified = false,
    Map<String, dynamic>? metadata,
  }) : super(
          id: id,
          email: email,
          displayName: displayName,
          photoURL: photoURL,
          isAnonymous: isAnonymous,
          emailVerified: emailVerified,
          metadata: metadata,
        );

  /// Factory method to create a UserModel from a Firebase user
  factory UserModel.fromFirebase(firebase_auth.User firebaseUser) {
    return UserModel(
      id: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      isAnonymous: firebaseUser.isAnonymous,
      emailVerified: firebaseUser.emailVerified,
      metadata: {
        'creationTime': firebaseUser.metadata.creationTime?.millisecondsSinceEpoch,
        'lastSignInTime': firebaseUser.metadata.lastSignInTime?.millisecondsSinceEpoch,
      },
    );
  }

  /// Empty user
  static const empty = UserModel(id: '');

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'isAnonymous': isAnonymous,
      'emailVerified': emailVerified,
      'metadata': metadata,
    };
  }

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      isAnonymous: json['isAnonymous'] as bool? ?? false,
      emailVerified: json['emailVerified'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Create a copy of this UserModel with given attributes
  @override
  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    bool? isAnonymous,
    bool? emailVerified,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      emailVerified: emailVerified ?? this.emailVerified,
      metadata: metadata ?? this.metadata,
    );
  }
} 