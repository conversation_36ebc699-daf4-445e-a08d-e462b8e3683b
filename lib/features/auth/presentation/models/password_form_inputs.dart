import 'package:formz/formz.dart';

/// Validation errors for password input
enum PasswordValidationError {
  empty('La contraseña no puede estar vacía'),
  tooShort('La contraseña debe tener al menos 8 caracteres'),
  noUppercase('La contraseña debe contener al menos una letra mayúscula'),
  noLowercase('La contraseña debe contener al menos una letra minúscula'),
  noNumber('La contraseña debe contener al menos un número');

  final String message;
  const PasswordValidationError(this.message);
}

/// Validation errors for the confirmed password
enum ConfirmedPasswordValidationError {
  invalid('Por favor confirma tu contraseña'),
  mismatch('Las contraseñas no coinciden');

  final String message;
  const ConfirmedPasswordValidationError(this.message);
}

/// Form input for password
class Password extends FormzInput<String, PasswordValidationError> {
  const Password.pure() : super.pure('');
  const Password.dirty([String value = '']) : super.dirty(value);

  @override
  PasswordValidationError? validator(String value) {
    if (value.isEmpty) return PasswordValidationError.empty;
    if (value.length < 5) return PasswordValidationError.tooShort;
    if (!value.contains(RegExp(r'[A-Z]'))) return PasswordValidationError.noUppercase;
    if (!value.contains(RegExp(r'[a-z]'))) return PasswordValidationError.noLowercase;
    if (!value.contains(RegExp(r'[0-9]'))) return PasswordValidationError.noNumber;
    return null;
  }
}

/// Form input for confirmed password
class ConfirmedPassword extends FormzInput<String, ConfirmedPasswordValidationError> {
  const ConfirmedPassword.pure({this.password = ''}) : super.pure('');
  
  const ConfirmedPassword.dirty({
    required this.password,
    String value = '',
  }) : super.dirty(value);

  final String password;

  @override
  ConfirmedPasswordValidationError? validator(String value) {
    if (value.isEmpty) return ConfirmedPasswordValidationError.invalid;
    return password == value ? null : ConfirmedPasswordValidationError.mismatch;
  }
} 