import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

class AuthUserRefreshRequested extends AuthEvent {
  const AuthUserRefreshRequested();
}

class AuthLoggedOut extends AuthEvent {
  const AuthLoggedOut();
}

class AuthEmailSignInRequested extends AuthEvent {

  const AuthEmailSignInRequested({
    required this.email,
    required this.password,
  });
  final String email;
  final String password;

  @override
  List<Object> get props => [email, password];
}

class AuthEmailSignUpRequested extends AuthEvent {

  const AuthEmailSignUpRequested({
    required this.email,
    required this.password,
    this.displayName,
  });
  final String email;
  final String password;
  final String? displayName;

  @override
  List<Object?> get props => [email, password, displayName];
}

class AuthGoogleSignInRequested extends AuthEvent {
  const AuthGoogleSignInRequested();
}

class AuthFacebookSignInRequested extends AuthEvent {
  const AuthFacebookSignInRequested();
}

class AuthPasswordResetRequested extends AuthEvent {

  const AuthPasswordResetRequested({required this.email});
  final String email;

  @override
  List<Object> get props => [email];
}

class AuthUserChanged extends AuthEvent {

  const AuthUserChanged(this.user);
  final User user;

  @override
  List<Object> get props => [user];
}

class AuthSignUpRequested extends AuthEvent {

  const AuthSignUpRequested({
    required this.email,
    required this.password,
    required this.name,
  });
  final String email;
  final String password;
  final String name;

  @override
  List<Object> get props => [email, password, name];
}

class AuthSignInEmailPasswordRequested extends AuthEvent {

  const AuthSignInEmailPasswordRequested({
    required this.email,
    required this.password,
  });
  final String email;
  final String password;

  @override
  List<Object> get props => [email, password];
}

class AuthSignInWithGoogleRequested extends AuthEvent {}

class AuthSignInWithFacebookRequested extends AuthEvent {}

class AuthSignOutRequested extends AuthEvent {} 