import 'package:equatable/equatable.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final User user;
  final String? errorMessage;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user = User.empty,
    this.errorMessage,
  });
  
  bool get isInitial => status == AuthStatus.initial;
  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, user, errorMessage];
  
  // Convenience factory constructors for different states
  factory AuthState.initial() => const AuthState();
  
  factory AuthState.authenticated(User user) => AuthState(
    status: AuthStatus.authenticated,
    user: user,
  );
  
  factory AuthState.unauthenticated([String? errorMessage]) => AuthState(
    status: AuthStatus.unauthenticated,
    user: User.empty,
    errorMessage: errorMessage,
  );
} 