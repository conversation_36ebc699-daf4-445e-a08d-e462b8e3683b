import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/get_user_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/reset_password_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_email_password_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_facebook_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_in_google_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_out_use_case.dart';
import 'package:respublicaseguridad/features/auth/domain/usecases/sign_up_use_case.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';

@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final GetUserUseCase _getUserUseCase;
  final SignInEmailPasswordUseCase _signInEmailPasswordUseCase;
  final SignUpUseCase _signUpUseCase;
  final SignInGoogleUseCase _signInGoogleUseCase;
  final SignInFacebookUseCase _signInFacebookUseCase;
  final SignOutUseCase _signOutUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;

  AuthBloc({
    required GetUserUseCase getUserUseCase,
    required SignInEmailPasswordUseCase signInEmailPasswordUseCase,
    required SignUpUseCase signUpUseCase,
    required SignInGoogleUseCase signInGoogleUseCase,
    required SignInFacebookUseCase signInFacebookUseCase,
    required SignOutUseCase signOutUseCase,
    required ResetPasswordUseCase resetPasswordUseCase,
  })  : _getUserUseCase = getUserUseCase,
        _signInEmailPasswordUseCase = signInEmailPasswordUseCase,
        _signUpUseCase = signUpUseCase,
        _signInGoogleUseCase = signInGoogleUseCase,
        _signInFacebookUseCase = signInFacebookUseCase,
        _signOutUseCase = signOutUseCase,
        _resetPasswordUseCase = resetPasswordUseCase,
        super(AuthState.initial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthUserRefreshRequested>(_onAuthUserRefreshRequested);
    on<AuthEmailSignInRequested>(_onAuthEmailSignInRequested);
    on<AuthEmailSignUpRequested>(_onAuthEmailSignUpRequested);
    on<AuthGoogleSignInRequested>(_onAuthGoogleSignInRequested);
    on<AuthFacebookSignInRequested>(_onAuthFacebookSignInRequested);
    on<AuthLoggedOut>(_onAuthLoggedOut);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    print('AuthBloc - Processing AuthCheckRequested');
    final userOrFailure = await _getUserUseCase();

    userOrFailure.fold(
      (failure) {
        print('AuthBloc - User check failed: ${failure.message}');
        emit(AuthState.unauthenticated());
      },
      (user) {
        if (user.isNotEmpty) {
          print('AuthBloc - User authenticated: ${user.email}');
          emit(AuthState.authenticated(user));
        } else {
          print('AuthBloc - User is empty, unauthenticated');
          emit(AuthState.unauthenticated());
        }
      },
    );
  }

  Future<void> _onAuthUserRefreshRequested(
    AuthUserRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    print('AuthBloc - Processing AuthUserRefreshRequested');
    final userOrFailure = await _getUserUseCase();

    userOrFailure.fold(
      (failure) {
        print('AuthBloc - User refresh failed: ${failure.message}');
        // Keep current state if refresh fails
      },
      (user) {
        if (user.isNotEmpty) {
          print('AuthBloc - User refreshed successfully: ${user.email}');
          print('AuthBloc - Updated photo URL: ${user.photoURL}');
          emit(AuthState.authenticated(user));
        } else {
          print('AuthBloc - User is empty after refresh, unauthenticated');
          emit(AuthState.unauthenticated());
        }
      },
    );
  }

  Future<void> _onAuthEmailSignInRequested(
    AuthEmailSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState(status: AuthStatus.initial));
    
    final result = await _signInEmailPasswordUseCase(
      email: event.email,
      password: event.password,
    );
    
    result.fold(
      (failure) => emit(AuthState.unauthenticated(failure.message)),
      (user) => emit(AuthState.authenticated(user)),
    );
  }

  Future<void> _onAuthEmailSignUpRequested(
    AuthEmailSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState(status: AuthStatus.initial));
    
    final result = await _signUpUseCase(
      email: event.email,
      password: event.password,
      displayName: event.displayName,
    );
    
    result.fold(
      (failure) => emit(AuthState.unauthenticated(failure.message)),
      (user) => emit(AuthState.authenticated(user)),
    );
  }

  Future<void> _onAuthGoogleSignInRequested(
    AuthGoogleSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState(status: AuthStatus.initial));
    
    final result = await _signInGoogleUseCase();
    
    result.fold(
      (failure) => emit(AuthState.unauthenticated(failure.message)),
      (user) => emit(AuthState.authenticated(user)),
    );
  }

  Future<void> _onAuthFacebookSignInRequested(
    AuthFacebookSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState(status: AuthStatus.initial));
    
    final result = await _signInFacebookUseCase();
    
    result.fold(
      (failure) => emit(AuthState.unauthenticated(failure.message)),
      (user) => emit(AuthState.authenticated(user)),
    );
  }

  Future<void> _onAuthLoggedOut(
    AuthLoggedOut event,
    Emitter<AuthState> emit,
  ) async {
    await _signOutUseCase();
    emit(AuthState.unauthenticated());
  }

  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState(status: AuthStatus.initial));
    
    try {
      final result = await _resetPasswordUseCase(email: event.email);
      
      result.fold(
        (failure) {
          print('Password reset failed: ${failure.message}');
          emit(AuthState.unauthenticated(failure.message));
        },
        (_) {
          print('Password reset email sent to ${event.email}');
          emit(const AuthState(status: AuthStatus.unauthenticated));
        },
      );
    } catch (e) {
      print('Unexpected error during password reset: $e');
      emit(AuthState.unauthenticated('An unexpected error occurred. Please try again.'));
    }
  }
} 