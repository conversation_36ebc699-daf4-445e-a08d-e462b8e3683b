import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:formz/formz.dart';
import 'package:respublicaseguridad/core/app_theme.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/auth/presentation/models/password_form_inputs.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_text_field.dart';


class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _acceptTerms = false;
  
  // Formz models
  Password _password = const Password.pure();
  ConfirmedPassword _confirmedPassword = const ConfirmedPassword.pure();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    
    // Start animation after frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }

  void _onPasswordChanged() {
    final password = Password.dirty(_passwordController.text);
    setState(() {
      _password = password;
      _confirmedPassword = ConfirmedPassword.dirty(
        password: _passwordController.text,
        value: _confirmPasswordController.text,
      );
    });
  }
  
  void _onConfirmPasswordChanged() {
    setState(() {
      _confirmedPassword = ConfirmedPassword.dirty(
        password: _passwordController.text,
        value: _confirmPasswordController.text,
      );
    });
  }

  void _handleSignup() {
    // Update password and confirmPassword one last time to ensure
    // they contain the latest values from controllers
    final password = Password.dirty(_passwordController.text);
    final confirmedPassword = ConfirmedPassword.dirty(
      password: _passwordController.text,
      value: _confirmPasswordController.text,
    );
    
    setState(() {
      _password = password;
      _confirmedPassword = confirmedPassword;
    });
    
    // Check if both password validations pass
    final isPasswordValid = _password.isValid;
    final isConfirmPasswordValid = _confirmedPassword.isValid;
    final passwordsValid = isPasswordValid && isConfirmPasswordValid;
    
    if (_formKey.currentState?.validate() ?? false && passwordsValid) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.l10n.mustAcceptTerms)),
        );
        return;
      }
      
      context.read<AuthBloc>().add(AuthEmailSignUpRequested(
        email: _emailController.text,
        password: _passwordController.text,
        displayName: _nameController.text,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark ? AppTheme.darkBackgroundColor : AppTheme.lightBackgroundColor;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.errorMessage!)),
          );
        }
      },
      child: SafeArea(
        top:false,
        child: Scaffold(
          backgroundColor: backgroundColor,
          appBar: AppBar(

            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: isDark ? Colors.white : Colors.black,
              ),
              onPressed: () {
                NavigationService.navigateTo(context, 'login');
              },
            ),
            title: Text(
              context.l10n.createAccount,
              style: TextStyle(
                color: isDark ? Colors.white : Colors.black,
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildHeaderSection(context, size),
                    SizedBox(height: 20.h),
                    _buildOAuthButtons(),
                    SizedBox(height: 20.h),
                    _buildDivider(),
                    SizedBox(height: 20.h),
                    _buildFormSection(context),
                    SizedBox(height: 20.h),
                    _buildTermsCheckbox(),
                    SizedBox(height: 20.h),
                    _buildSignupButton(),
                    SizedBox(height: 20.h),
                    _buildLoginPrompt(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.welcome,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: 24.sp,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Regístrate para comenzar a utilizar la plataforma',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.white60 
                : Colors.black54,
            fontSize: 14.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildOAuthButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSocialButton(
          icon: FontAwesomeIcons.google,
          label: 'Google',
          onPressed: () => context.read<AuthBloc>().add(const AuthGoogleSignInRequested()),
          color: Colors.red,
        ),
        SizedBox(width: 24.w),
        _buildSocialButton(
          icon: FontAwesomeIcons.facebook,
          label: 'Facebook',
          onPressed: () => context.read<AuthBloc>().add(const AuthFacebookSignInRequested()),
          color: const Color(0xFF1877F2),
        ),
      ],
    );
  }
  
  Widget _buildDivider() {
    return Row(
      children: [
        const Expanded(child: Divider()),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            'O regístrate con email',
            style: TextStyle(
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white60 
                  : Colors.black54,
              fontSize: 14.sp,
            ),
          ),
        ),
        const Expanded(child: Divider()),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.status == AuthStatus.initial &&
                         state.errorMessage == null &&
                         !state.isAuthenticated;
        return Expanded(
          child: ElevatedButton.icon(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? SizedBox(
                    width: 18.sp,
                    height: 18.sp,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : FaIcon(
                    icon,
                    size: 18.sp,
                    color: Colors.white,
                  ),
            label: Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: isLoading ? color.withValues(alpha: 0.7) : color,
              padding: EdgeInsets.symmetric(vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 0,
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormSection(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Full Name Field
          CustomTextField(
            controller: _nameController,
            hintText: 'Nombre completo',
            prefixIcon: Icon(
              Icons.person_outline,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Por favor ingresa tu nombre';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          // Email Field
          CustomTextField(
            controller: _emailController,
            hintText: 'Correo electrónico',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icon(
              Icons.email_outlined,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Por favor ingresa tu correo';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Ingresa un correo electrónico válido';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          // Password Field
          CustomTextField(
            controller: _passwordController,
            hintText: 'Contraseña',
            obscureText: _obscurePassword,
            prefixIcon: Icon(
              Icons.lock_outline,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.white54 
                    : Colors.black45,
              ),
              onPressed: _togglePasswordVisibility,
            ),
            validator: (_) => _password.error?.message,
            onChanged: (_) => _onPasswordChanged(),
          ),
          // Add password strength indicator
          if (_passwordController.text.isNotEmpty)
            _buildPasswordStrengthIndicator(),
          SizedBox(height: 16.h),
          // Confirm Password Field
          CustomTextField(
            controller: _confirmPasswordController,
            hintText: 'Confirmar contraseña',
            obscureText: _obscureConfirmPassword,
            prefixIcon: Icon(
              Icons.lock_outline,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscureConfirmPassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.white54 
                    : Colors.black45,
              ),
              onPressed: _toggleConfirmPasswordVisibility,
            ),
            validator: (_) => _confirmedPassword.error?.message,
            onChanged: (_) => _onConfirmPasswordChanged(),
          ),
        ],
      ),
    );
  }

  /// Builds a visual indicator of password strength
  Widget _buildPasswordStrengthIndicator() {
    // Define strength levels
    const int maxStrength = 5;
    int currentStrength = 0;
    String strengthText = 'Débil';
    Color strengthColor = Colors.red;
    
    // Calculate password strength
    final String pwd = _passwordController.text;
    if (pwd.isNotEmpty) currentStrength++;
    if (pwd.length >= 5) currentStrength++;
    if (pwd.contains(RegExp(r'[A-Z]'))) currentStrength++;
    if (pwd.contains(RegExp(r'[a-z]'))) currentStrength++;
    if (pwd.contains(RegExp(r'[0-9]'))) currentStrength++;
    
    // Determine text and color based on strength
    if (currentStrength <= 2) {
      strengthText = 'Muy débil';
      strengthColor = Colors.red;
    } else if (currentStrength == 3) {
      strengthText = 'Débil';
      strengthColor = Colors.orange;
    } else if (currentStrength == 4) {
      strengthText = 'Moderada';
      strengthColor = Colors.yellow;
    } else if (currentStrength == 5) {
      strengthText = 'Fuerte';
      strengthColor = Colors.lightGreen;
    } else {
      strengthText = 'Muy fuerte';
      strengthColor = Colors.green;
    }
    
    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Fortaleza de la contraseña: ',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.white60 
                      : Colors.black54,
                ),
              ),
              Text(
                strengthText,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: strengthColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          ClipRRect(
            borderRadius: BorderRadius.circular(4.r),
            child: LinearProgressIndicator(
              value: currentStrength / maxStrength,
              backgroundColor: Colors.grey[300],
              color: strengthColor,
              minHeight: 6.h,
            ),
          ),
        
        ],
      ),
    );
  }


  Widget _buildTermsCheckbox() {
    return Row(
      children: [
        SizedBox(
          width: 24.w,
          height: 24.h,
          child: Checkbox(
            value: _acceptTerms,
            activeColor: AppTheme.accentBlue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.r),
            ),
            onChanged: (bool? value) {
              setState(() {
                _acceptTerms = value ?? false;
              });
            },
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 14.sp,
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.white60 
                    : Colors.black54,
              ),
              children: [
                const TextSpan(
                  text: 'Acepto los ',
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () {
                      // TODO: Navigate to terms
                    },
                    child: Text(
                      'términos y condiciones',
                      style: TextStyle(
                        color: AppTheme.accentBlue,
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
                const TextSpan(
                  text: ' y la ',
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () {
                      // TODO: Navigate to privacy policy
                    },
                    child: Text(
                      'política de privacidad',
                      style: TextStyle(
                        color: AppTheme.accentBlue,
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignupButton() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.status == AuthStatus.initial &&
                         state.errorMessage == null &&
                         !state.isAuthenticated;
        return CustomButton(
          text: 'Registrarse',
          onPressed: () {
            if (!isLoading) {
              _handleSignup();
            }
          },
          isLoading: isLoading,
          suffixIcon: const Icon(
            Icons.arrow_forward,
            color: Colors.white,
          ),
        );
      },
    );
  }

  Widget _buildLoginPrompt(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '¿Ya tienes una cuenta? ',
          style: TextStyle(
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.white70 
                : Colors.black54,
            fontSize: 14.sp,
          ),
        ),
        TextButton(
          onPressed: () {
            NavigationService.navigateTo(context, 'login');
          },
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size(0, 36.h),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            foregroundColor: AppTheme.accentBlue,
          ),
          child: Text(
            'Iniciar Sesión',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14.sp,
            ),
          ),
        ),
      ],
    );
  }
} 