import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:formz/formz.dart';
import 'package:respublicaseguridad/core/app_theme.dart';
import 'package:respublicaseguridad/core/router/navigation_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_event.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/auth/presentation/models/password_form_inputs.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_button.dart';
import 'package:respublicaseguridad/features/auth/presentation/widgets/custom_text_field.dart'; 


class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  
  // Formz model
  Password _password = const Password.pure();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    
    // Start animation after frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _onPasswordChanged() {
    setState(() {
      _password = Password.dirty(_passwordController.text);
    });
  }

  void _handleLogin() {
    // Update password to ensure it contains the latest value from controller
    final password = Password.dirty(_passwordController.text);
    
    setState(() {
      _password = password;
    });
    
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(AuthEmailSignInRequested(
        email: _emailController.text,
        password: _passwordController.text,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark ? AppTheme.darkBackgroundColor : AppTheme.lightBackgroundColor;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.errorMessage!)),
          );
        }
      },
      child: SafeArea(
        top:false,
        child: Scaffold(
                  backgroundColor: backgroundColor,

          appBar: AppBar(
            
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top -
                      AppBar().preferredSize.height,
                ),
                child: Stack(
                  children: [
                    // Positioned logo
                    Positioned(
                      left: 20.w,
                      top: 10.h,
                      child: Hero(
                        tag: 'app_logo',
                        child: Image.asset(
                          'assets/logo.png',
                          width: 90.w,
                          height: 90.h,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    // Main content
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Empty space to position form below the logo
                          SizedBox(height: 110.h),
                          // Welcome text
                          Text(
                            context.l10n.welcome,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            context.l10n.enterCredentials,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: isDark ? Colors.white60 : Colors.black54,
                            ),
                          ),
                          SizedBox(height: 40.h),
                          _buildFormSection(context),
                          SizedBox(height: 20.h),
                          _buildLoginButton(),
                          SizedBox(height: 20.h),
                          _buildAdditionalButtons(),
                          SizedBox(height: 40.h),
                          _buildSignupPrompt(context),
                          // Add extra padding at the bottom for keyboard
                          SizedBox(height: 20.h),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, Size size) {
    // This function is no longer used since we're using a Stack with Positioned widget
    return const SizedBox.shrink();
  }

  Widget _buildFormSection(BuildContext context) {
    final FocusNode emailFocus = FocusNode();
    final FocusNode passwordFocus = FocusNode();
    
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _emailController,
            hintText: context.l10n.email,
            keyboardType: TextInputType.emailAddress,
            focusNode: emailFocus,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) {
              FocusScope.of(context).requestFocus(passwordFocus);
            },
            prefixIcon: Icon(
              Icons.email_outlined,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return context.l10n.pleaseEnterEmail;
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return context.l10n.pleaseEnterValidEmail;
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          CustomTextField(
            controller: _passwordController,
            hintText: context.l10n.password,
            obscureText: _obscurePassword,
            focusNode: passwordFocus,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _handleLogin(),
            prefixIcon: Icon(
              Icons.lock_outline,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white54 
                  : Colors.black45,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.white54 
                    : Colors.black45,
              ),
              onPressed: _togglePasswordVisibility,
            ),
            validator: (_) => _password.error?.message,
            onChanged: (_) => _onPasswordChanged(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.status == AuthStatus.initial &&
                         state.errorMessage == null &&
                         !state.isAuthenticated;
        return CustomButton(
          text: context.l10n.login,
          onPressed: () {
            if (!isLoading) {
              _handleLogin();
            }
          },
          isLoading: isLoading,
          suffixIcon: const Icon(
            Icons.arrow_forward,
            color: Colors.white,
          ),
        );
      },
    );
  }

  Widget _buildAdditionalButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        TextButton(
          onPressed: () {
            NavigationService.navigateTo(context, 'forgot-password');
          },
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            context.l10n.forgotPassword,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            const Expanded(
              child: Divider(thickness: 1),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                context.l10n.orContinueWith,
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.white54 
                      : Colors.black45,
                ),
              ),
            ),
            const Expanded(
              child: Divider(thickness: 1),
            ),
          ],
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialButton(
              icon: FontAwesomeIcons.google,
              label: context.l10n.google,
              onPressed: () => context.read<AuthBloc>().add(const AuthGoogleSignInRequested()),
              color: Colors.red,
            ),
            SizedBox(width: 16.w),
            _buildSocialButton(
              icon: FontAwesomeIcons.facebook,
              label: context.l10n.facebook,
              onPressed: () => context.read<AuthBloc>().add(const AuthFacebookSignInRequested()),
              color: const Color(0xFF1877F2),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.status == AuthStatus.initial &&
                         state.errorMessage == null &&
                         !state.isAuthenticated;
        return Expanded(
          child: ElevatedButton.icon(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? SizedBox(
                    width: 18.sp,
                    height: 18.sp,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : FaIcon(
                    icon,
                    size: 18.sp,
                    color: Colors.white,
                  ),
            label: Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: isLoading ? color.withValues(alpha: 0.7) : color,
              padding: EdgeInsets.symmetric(vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 1,
            ),
          ),
        );
      },
    );
  }

  Widget _buildSignupPrompt(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          context.l10n.dontHaveAccount,
          style: TextStyle(
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.white70 
                : Colors.black54,
            fontSize: 14.sp,
          ),
        ),
        TextButton(
          onPressed: () {
            NavigationService.navigateTo(context, 'signup');
          },
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size(0, 36.h),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            foregroundColor: AppTheme.accentBlue,
          ),
          child: Text(
            context.l10n.signUpPrompt,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14.sp,
            ),
          ),
        ),
      ],
    );
  }
} 