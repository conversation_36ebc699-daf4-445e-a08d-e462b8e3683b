import 'package:flutter/material.dart';
import 'package:respublicaseguridad/core/app_theme.dart';

class GradientContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final bool useAltGradient;
  final List<Color>? customGradient;
  final double borderRadius;
  final BoxBorder? border;
  final double? width;
  final double? height;
  
  const GradientContainer({
    Key? key,
    required this.child,
    this.padding = EdgeInsets.zero,
    this.useAltGradient = false,
    this.customGradient,
    this.borderRadius = 16,
    this.border,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Select gradient based on theme and parameters
    final gradient = customGradient ?? (isDark
        ? useAltGradient
            ? [
                AppTheme.darkSurfaceColor,
                AppTheme.darkBackgroundColor,
              ]
            : AppTheme.darkPrimaryGradient
        : useAltGradient
            ? [
                Colors.white,
                const Color(0xFFF8F9FF),
              ]
            : AppTheme.lightPrimaryGradient);
    
    return Container(
      width: width,
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        border: border,
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: child,
    );
  }
} 