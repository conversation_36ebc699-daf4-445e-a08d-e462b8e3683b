import 'package:flutter/material.dart';
import 'package:respublicaseguridad/core/app_theme.dart';

enum ButtonType { primary, secondary, outlined }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final ButtonType buttonType;
  final bool isLoading;
  final double width;
  final double height;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool disabled;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.buttonType = ButtonType.primary,
    this.isLoading = false,
    this.width = double.infinity,
    this.height = 54,
    this.prefixIcon,
    this.suffixIcon,
    this.borderRadius = 4.0,
    this.padding,
    this.disabled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Button styles based on type
    final backgroundColor = _getBackgroundColor(isDark);
    final textColor = _getTextColor(isDark);
    final borderSide = _getBorderSide(isDark);

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: disabled || isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 0),
          elevation: buttonType == ButtonType.outlined ? 0 : 2,
          shadowColor: buttonType == ButtonType.outlined 
              ? Colors.transparent 
              : isDark ? Colors.black54 : Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: borderSide,
          ),
          disabledBackgroundColor: buttonType == ButtonType.outlined 
              ? Colors.transparent 
              : isDark ? Colors.grey.shade800 : Colors.grey.shade300,
          disabledForegroundColor: isDark ? Colors.white60 : Colors.black45,
        ),
        child: isLoading 
            ? _buildLoadingIndicator(textColor)
            : _buildButtonContent(context, textColor),
      ),
    );
  }

  Color _getBackgroundColor(bool isDark) {
    switch (buttonType) {
      case ButtonType.primary:
        return AppTheme.primaryBlue;
      case ButtonType.secondary:
        return isDark ? AppTheme.darkSurfaceColor : Colors.white;
      case ButtonType.outlined:
        return Colors.transparent;
    }
  }

  Color _getTextColor(bool isDark) {
    switch (buttonType) {
      case ButtonType.primary:
        return Colors.white;
      case ButtonType.secondary:
        return isDark ? Colors.white : AppTheme.primaryBlue;
      case ButtonType.outlined:
        return isDark ? Colors.white : AppTheme.primaryBlue;
    }
  }

  BorderSide _getBorderSide(bool isDark) {
    if (buttonType == ButtonType.outlined) {
      return BorderSide(
        color: isDark ? Colors.white60 : AppTheme.primaryBlue,
        width: 1.5,
      );
    }
    return BorderSide.none;
  }

  Widget _buildLoadingIndicator(Color color) {
    return SizedBox(
      height: 24,
      width: 24,
      child: CircularProgressIndicator(
        strokeWidth: 2.5,
        valueColor: AlwaysStoppedAnimation<Color>(color),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context, Color textColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (prefixIcon != null) ...[
          prefixIcon!,
          const SizedBox(width: 8),
        ],
        Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (suffixIcon != null) ...[
          const SizedBox(width: 8),
          suffixIcon!,
        ],
      ],
    );
  }
} 