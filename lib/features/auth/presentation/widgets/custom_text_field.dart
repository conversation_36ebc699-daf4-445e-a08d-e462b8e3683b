import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:respublicaseguridad/core/app_theme.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String? labelText;
  final bool obscureText;
  final TextInputType keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final int? maxLines;
  final String? errorText;
  final bool autofocus;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? contentPadding;
  final bool? enabled;
  final TextInputAction? textInputAction;
  final Function(String)? onSubmitted;
  final bool showBorder;

  const CustomTextField({
    Key? key,
    required this.controller,
    required this.hintText,
    this.labelText,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.maxLines = 1,
    this.errorText,
    this.autofocus = false,
    this.focusNode,
    this.contentPadding,
    this.enabled,
    this.textInputAction,
    this.onSubmitted,
    this.showBorder = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final fillColor = isDark 
        ? AppTheme.darkInputFillColor 
        : AppTheme.lightFieldPrimary;
    final borderColor = isDark 
        ? AppTheme.darkFieldPrimary.withOpacity(0.6)
        : AppTheme.lightFieldPrimary;
    final textColor = isDark ? Colors.white : Colors.black87;
    final hintColor = isDark ? Colors.white60 : Colors.black45;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        
      ),
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        validator: validator,
        onChanged: onChanged,
        maxLines: maxLines,
        autofocus: autofocus,
        focusNode: focusNode,
        enabled: enabled,
        textInputAction: textInputAction ?? TextInputAction.next,
        onFieldSubmitted: onSubmitted,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: textColor,
          fontSize: 16.sp,
        ),
        decoration: InputDecoration(
          filled: true,
          fillColor: fillColor,
          hintText: hintText,
          labelText: labelText,
          errorText: errorText,
          hintStyle: TextStyle(
            color: hintColor,
            fontSize: 15.sp,
            fontWeight: FontWeight.w400,
          ),
          labelStyle: TextStyle(
            color: isDark ? Colors.white70 : AppTheme.accentBlue.withOpacity(0.8),
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
          ),
          errorStyle: TextStyle(
            fontSize: 12.sp,
            height: 1,
            fontWeight: FontWeight.w500,
          ),
          contentPadding: contentPadding ?? 
              EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          prefixIcon: prefixIcon != null 
              ? Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 8.w),
                  child: prefixIcon,
                ) 
              : null,
          prefixIconConstraints: BoxConstraints(minWidth: 40.w),
          suffixIcon: suffixIcon != null
              ? Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: suffixIcon,
                )
              : null,
          border: showBorder ? OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(color: borderColor, width: 1),
          ) : OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide.none,
          ),
          enabledBorder: showBorder ? OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: borderColor,
              width: 1,
            ),
          ) : OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: AppTheme.accentBlue,
              width: 1.5,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.error,
              width: 1,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.error,
              width: 1.5,
            ),
          ),
          isDense: true,
        ),
      ),
    );
  }
} 
