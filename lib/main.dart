import 'package:firebase_core/firebase_core.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:toastification/toastification.dart';
import 'package:respublicaseguridad/core/app_theme.dart';
import 'package:respublicaseguridad/core/di/injection.dart';
import 'package:respublicaseguridad/core/router/app_router.dart';
import 'package:respublicaseguridad/core/services/app_service_manager.dart';
import 'package:respublicaseguridad/core/services/crash_reporting_service.dart';
import 'package:respublicaseguridad/core/services/deep_link_service.dart';
import 'package:respublicaseguridad/core/services/environment_service.dart';
import 'package:respublicaseguridad/core/services/localization_service.dart';
import 'package:respublicaseguridad/core/localization/cubit/language_cubit.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_cubit.dart';
import 'package:respublicaseguridad/core/theme/cubit/theme_state.dart';
import 'package:respublicaseguridad/firebase_options.dart';
import 'package:respublicaseguridad/l10n/generated/app_localizations.dart';

// Global navigator key for the router
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize crash reporting (debug only)
  CrashReportingService.initialize();

  // Initialize environment service
  await EnvironmentService.initialize();

  // Initialize localization service
  await LocalizationService.init();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize HydratedBloc for state persistence
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );

  // Configure dependency injection
  await configureDependencies();

  // Initialize app service manager to handle auth-dependent services
  await AppServiceManager().initialize();

  // Initialize the app
  final app = MultiBlocProvider(
    providers: [
      BlocProvider(create: (_) => getIt<ThemeCubit>()),
      BlocProvider(create: (_) => LanguageCubit()),
    ],
    child: const MyApp(),
  );

  runApp(app);

  WidgetsBinding.instance.addPostFrameCallback((_) {
    DeepLinkService().initialize(AppRouter.router);
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        return BlocBuilder<LanguageCubit, Locale>(
          builder: (context, locale) {
            return ScreenUtilInit(
              designSize: const Size(375, 812),
              minTextAdapt: true,
              splitScreenMode: true,
              builder: (context, child) {
                return ToastificationWrapper(
                  child: GestureDetector(
                    // Dismiss keyboard when tapping outside input fields
                    onTap: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                    },
                    child: MaterialApp.router(
                      title: 'República Seguridad',
                      debugShowCheckedModeBanner: false,
                      theme: AppTheme.lightTheme,
                      darkTheme: AppTheme.darkTheme,
                      themeMode: themeState.themeMode,
                      routerConfig: AppRouter.router,
                      // Localization configuration
                      localizationsDelegates: const [
                        AppLocalizations.delegate,
                        GlobalMaterialLocalizations.delegate,
                        GlobalWidgetsLocalizations.delegate,
                        GlobalCupertinoLocalizations.delegate,
                      ],
                      supportedLocales:
                          LocalizationService.getSupportedLocales(),
                      locale: locale, // Use the locale from LanguageCubit
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
