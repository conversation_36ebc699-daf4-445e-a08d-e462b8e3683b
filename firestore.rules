rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for authentication and validation
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isValidUser() {
      return isAuthenticated() && request.auth.uid != null;
    }

    // Security incident helper functions
    function isValidZone(zoneId) {
      return zoneId is string && zoneId.size() > 0;
    }

    function getUserZones() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.authorizedZones;
    }

    function isUserAuthorizedForZone(zoneId) {
      return request.auth != null &&
             zoneId in getUserZones();
    }

    function isWithinTimeWindow(timestamp, windowHours) {
      return timestamp > request.time - duration.value(windowHours, 'h');
    }

    function isValidLocation(lat, lng) {
      return lat is number && lng is number &&
             lat >= -90 && lat <= 90 &&
             lng >= -180 && lng <= 180;
    }

    function isValidSeverity(severity) {
      return severity in ['low', 'medium', 'high', 'critical'];
    }

    function isValidIncidentType(type) {
      return type in ['theft', 'assault', 'vandalism', 'burglary', 'suspicious',
                     'emergency', 'fire', 'medical', 'accident', 'other'];
    }

    function isValidStatus(status) {
      return status in ['active', 'investigating', 'resolved', 'dismissed'];
    }

    function canViewPublicSafetyData() {
      // Allow viewing anonymized public safety data for users without zones
      // Only high-severity incidents that are anonymized
      return resource.data.isAnonymous == true &&
             resource.data.severity in ['high', 'critical'];
    }

    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Legacy Users collection (capital U) - maintain compatibility
    match /Users/<USER>
      allow read, write: if isOwner(userId);

      // User documents subcollection
      match /documentUser/{document} {
        allow read, write: if isOwner(userId);
      }
    }

    // Identity verification documents - strict security
    match /identity_documents/{documentId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.userId;
      allow read: if isAuthenticated()
                  && request.auth.uid == resource.data.userId;
      allow update: if isAuthenticated()
                    && request.auth.uid == resource.data.userId
                    && onlyUpdatingAllowedFields();
      allow delete: if false; // Never allow deletion of identity documents

      function onlyUpdatingAllowedFields() {
        let allowedFields = ['status', 'reviewTimestamp', 'reviewNotes', 'updatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }

    // Zones collection - users can manage their own zones, read others for validation
    match /zones/{zoneId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.userId;
      allow read: if isAuthenticated();
      allow update: if isAuthenticated()
                    && (request.auth.uid == resource.data.userId
                        || isValidCommunityValidation());
      allow delete: if isAuthenticated()
                    && request.auth.uid == resource.data.userId;

      function isValidCommunityValidation() {
        // Allow community validation updates
        let allowedFields = ['communityValidatedBy', 'communityValidationCount', 'updatedAt', 'validationStatus', 'validatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }

    // Zone validations collection - track validation history
    match /zone_validations/{validationId} {
      allow create: if isAuthenticated();
      allow read: if isAuthenticated();
      allow update: if false; // Validations are immutable once created
      allow delete: if false; // Never delete validation records
    }

    // Zone users collection - for zone membership
    match /zone_users/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Geofence events - for automatic validation tracking
    match /geofence_events/{eventId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.userId;
      allow read: if isAuthenticated()
                  && request.auth.uid == resource.data.userId;
      allow update: if false; // Events are immutable
      allow delete: if false; // Keep event history
    }

    // QR Validation Security Collections - Cloud Functions Only
    match /security_audit/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }

    match /security_threats/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }

    match /used_tokens/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }

    match /consumed_tokens/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }

    match /secure_tokens/{document} {
      allow read, write: if false; // Only Cloud Functions can access
    }

    // Legacy collections - maintain existing functionality
    match /CategoriaReportes/{parent}/TiposReporte/{document} {
      allow create: if isAuthenticated();
      allow read: if true; // Public read for report types
      allow write: if false;
      allow delete: if false;
    }

    match /{path=**}/TiposReporte/{document} {
      allow read: if true; // Public read for report types
    }

    match /zonas/{parent}/validaciones/{document} {
      allow create: if isAuthenticated();
      allow read: if isAuthenticated();
      allow write: if false;
      allow delete: if false;
    }

    match /{path=**}/validaciones/{document} {
      allow read: if isAuthenticated();
    }

    // Reports collection - if exists
    match /reports/{reportId} {
      allow create: if isAuthenticated();
      allow read: if isAuthenticated();
      allow update: if isAuthenticated()
                    && request.auth.uid == resource.data.createdBy;
      allow delete: if isAuthenticated()
                    && request.auth.uid == resource.data.createdBy;
    }

    // Notifications collection
    match /notifications/{userId} {
      allow read, write: if isOwner(userId);

      match /messages/{messageId} {
        allow read, write: if isOwner(userId);
      }
    }

    // Notification preferences collection - users can manage their own preferences
    match /notification_preferences/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Incident notifications collection - users can read their own notifications
    match /incident_notifications/{notificationId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.userId;
      allow read: if isAuthenticated()
                  && request.auth.uid == resource.data.userId;
      allow update: if isAuthenticated()
                    && request.auth.uid == resource.data.userId
                    && onlyUpdatingNotificationStatus();
      allow delete: if isAuthenticated()
                    && request.auth.uid == resource.data.userId;

      function onlyUpdatingNotificationStatus() {
        // Allow updating notification status fields only
        let allowedFields = ['status', 'readAt', 'dismissedAt', 'updatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }

    // Security incidents collection - real-time incident data
    match /security_incidents/{incidentId} {
      allow read: if request.auth != null &&
                     isWithinTimeWindow(resource.data.timestamp, 168); // 7 days - anyone can read safety data

      allow create: if request.auth != null &&
                       isValidZone(request.resource.data.zoneId) &&
                       isValidLocation(request.resource.data.location.latitude,
                                     request.resource.data.location.longitude) &&
                       isValidSeverity(request.resource.data.severity) &&
                       isValidIncidentType(request.resource.data.type) &&
                       isValidStatus(request.resource.data.status) &&
                       request.resource.data.timestamp is timestamp &&
                       request.resource.data.priority is int &&
                       request.resource.data.priority >= 1 &&
                       request.resource.data.priority <= 4 &&
                       request.resource.data.isAnonymous is bool;

      allow update: if request.auth != null &&
                       // Only allow status updates and resolution
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['status', 'resolvedAt', 'lastUpdated', 'metadata']) &&
                       isValidStatus(request.resource.data.status);

      allow delete: if false; // No deletions allowed for audit trail
    }

    // Incident realtime summaries - aggregated data per zone
    match /incident_realtime_summary/{zoneId} {
      allow read: if request.auth != null; // Anyone can read safety summaries

      // Only Cloud Functions can write summaries
      allow write: if false;
    }

    // Incident trends - historical trend data
    match /incident_trends/{trendId} {
      allow read: if request.auth != null; // Anyone can read safety trends

      // Only Cloud Functions can write trends
      allow write: if false;
    }

    // Incidents collection - for incident reporting system (now public for safety)
    match /incidents/{incidentId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.userId;
      allow read: if isAuthenticated(); // Anyone can read safety incidents
      allow update: if isAuthenticated()
                    && request.auth.uid == resource.data.userId
                    && onlyUpdatingAllowedIncidentFields();
      allow delete: if isAuthenticated()
                    && request.auth.uid == resource.data.userId;

      function isIncidentVisibleToCommunity() {
        // Allow reading incidents that are visible to community
        return resource.data.visibilityStatus == 'visibleToCommunity';
      }

      function onlyUpdatingAllowedIncidentFields() {
        // Allow updating specific fields only
        let allowedFields = ['title', 'description', 'status', 'reportCount', 'reportReasons', 'isBlocked','updates', 'updatedAt', 'lastUpdatedAt', 'updateCount'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }

    // Incident updates collection - for incident update system
    match /incident_updates/{updateId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.authorId
                    && isAuthorizedToUpdateIncident();
      allow read: if isAuthenticated()
                  && canReadIncidentUpdate();
      allow update: if false; // Updates are immutable once created
      allow delete: if isAuthenticated()
                    && request.auth.uid == resource.data.authorId;

      function isAuthorizedToUpdateIncident() {
        // Check if the user is the original author of the incident
        let incidentId = request.resource.data.incidentId;
        let incident = get(/databases/$(database)/documents/incidents/$(incidentId));
        return incident.data.userId == request.auth.uid;
      }

      function canReadIncidentUpdate() {
        // Users can read updates for incidents they can read
        let incidentId = resource.data.incidentId;
        let incident = get(/databases/$(database)/documents/incidents/$(incidentId));
        return request.auth.uid == incident.data.userId
               || incident.data.visibilityStatus == 'visibleToCommunity';
      }
    }

    


    // Incident categories collection - public read access for authenticated users
    match /incident_categories/{categoryId} {
      allow read: if isAuthenticated(); // All authenticated users can read categories
      allow write: if false; // Only admins can modify categories (via Cloud Functions)
    }

    // User reports collection - for dual verification system
    match /user_reports/{reportId} {
      allow create: if isAuthenticated()
                    && request.auth.uid == request.resource.data.reporterUserId;
      allow read: if isAuthenticated()
                  && (request.auth.uid == resource.data.reporterUserId
                      || request.auth.uid == resource.data.reportedUserId
                      || isSystemQuery());
      allow update: if isAuthenticated()
                    && request.auth.uid == resource.data.reporterUserId
                    && onlyUpdatingAllowedReportFields();
      allow delete: if false; // Never delete reports for audit trail

      function isSystemQuery() {
        // Allow system queries for dual verification workflow
        // This allows reading reports for verification decisions
        return true;
      }

      function onlyUpdatingAllowedReportFields() {
        // Allow updating specific fields only
        let allowedFields = ['status', 'reviewedAt', 'adminUserId', 'adminNotes', 'updatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }

    // Public safety data - anonymized incidents for general awareness
    match /public_safety_incidents/{incidentId} {
      allow read: if request.auth != null;
      allow write: if false; // Only Cloud Functions can write
    }

    // Admin access for FlutterFlow (maintain existing functionality)
    match /{document=**} {
      allow read, write: if request.auth.token.email.matches("<EMAIL>");
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
