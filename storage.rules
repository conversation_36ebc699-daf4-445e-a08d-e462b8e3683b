rules_version = '2';

// Firebase Storage Security Rules for ResPública Seguridad Identity Validation
// These rules protect sensitive identity documents while allowing proper upload access

service firebase.storage {
  match /b/{bucket}/o {
    
    // Identity verification documents - temporarily permissive for testing
    match /identity_verifications/{userId}/{allPaths=**} {
      // Allow uploads by authenticated users to their own folder
      allow write: if request.auth != null
                   && request.auth.uid == userId;

      // Allow reads by document owner (for testing)
      allow read: if request.auth != null
                  && request.auth.uid == userId;
    }

    // Profile photos - permissive for testing
    match /profile_photos/{userId}/{allPaths=**} {
      // Allow uploads by authenticated users to their own folder
      allow write: if request.auth != null
                   && request.auth.uid == userId;

      // Allow reads by document owner
      allow read: if request.auth != null
                  && request.auth.uid == userId;
    }
    
    // User documents (general) - existing pattern from current rules
    match /Users/<USER>/documentUser/{allPaths=**} {
      allow read, write: if request.auth != null 
                         && request.auth.uid == userId;
    }
    
    // General user files
    match /Users/<USER>/{allPaths=**} {
      allow read, write: if request.auth != null 
                         && request.auth.uid == userId;
    }
    
    // Report attachments - public read, authenticated write
    match /Reporte/{reportId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Zone-related files
    match /zonas/{zoneId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Temporary: Allow authenticated users to upload anywhere (for testing)
    // This bypasses App Check requirements for development
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}

// Helper function to validate image uploads
function isValidImageUpload() {
  return request.resource != null
         && request.resource.size < 10 * 1024 * 1024  // Max 10MB
         && request.resource.contentType.matches('image/.*')  // Only images
         && request.resource.contentType in ['image/jpeg', 'image/jpg', 'image/png'];  // Specific formats
}
